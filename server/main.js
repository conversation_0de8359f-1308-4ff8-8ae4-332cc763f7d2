import "../imports/startup/server";

import { WebApp } from "meteor/webapp";
import { toLower } from "lodash";
import { parseBoolean, shouldUseDevMode } from "../imports/api/utilities/utilities";

const allowedOrigins = [
  `https://app.${toLower(Meteor.settings.public.ENVIRONMENT)}.springmath.org`,
  `https://app.springmath.org`,
  "http://localhost:3000"
];

const CI = parseBoolean(process.env.CI);
const isLocalEnv = shouldUseDevMode(CI, ["LOCAL"]);

WebApp.connectHandlers.use((req, res, next) => {
  const { origin } = req.headers;

  res.setHeader("X-Frame-Options", "SAMEORIGIN");
  res.setHeader("X-Content-Type-Options", "nosniff");
  res.setHeader("X-Content-Type-Options", "no-referrer");

  if (req.headers["x-forwarded-proto"] === "https" || req.connection.encrypted) {
    res.setHeader("Strict-Transport-Security", "max-age=63072000; includeSubDomains; preload");
  }

  // NOTE(fmazur) - 'unsafe-inline' required for meteorjs
  res.setHeader(
    "Content-Security-Policy",
    [
      "default-src 'self'",
      `script-src 'self' 'unsafe-inline' ${
        isLocalEnv ? "'unsafe-eval'" : ""
      } https://fonts.googleapis.com http://www.youtube.com https://www.youtube.com`,
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      "media-src 'self' https://edspring-inta.s3.amazonaws.com",
      "img-src 'self' data:",
      "object-src 'none'",
      "base-uri 'self'",
      "connect-src 'self' https://apm-engine.meteor.com wss:",
      "frame-ancestors 'self'",
      "frame-src 'self' https://dialog.filepicker.io https://www.filepicker.io http://www.youtube.com https://www.youtube.com"
    ].join("; ")
  );

  res.setHeader("Cache-Control", "no-store, no-cache");
  res.setHeader("Pragma", "no-cache");

  if (origin && allowedOrigins.includes(origin)) {
    res.setHeader("Access-Control-Allow-Origin", origin);
    res.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With");
    res.setHeader("Access-Control-Allow-Credentials", "true");
  }

  if (req.method === "OPTIONS") {
    res.writeHead(204);
    res.end();
    return;
  }

  next();
});

WebApp.httpServer.on("upgrade", (req, socket) => {
  const { origin } = req.headers;

  if (origin && !allowedOrigins.includes(origin)) {
    socket?.destroy();
  }
});
