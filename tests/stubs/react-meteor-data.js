import React from "react";

export default function withTracker(options) {
  let expandedOptions = options;
  if (typeof options === "function") {
    expandedOptions = {
      getMeteorData: options
    };
  }

  const { getMeteorData } = expandedOptions;

  return WrappedComponent =>
    class ReactMeteorDataComponent extends React.PureComponent {
      render() {
        return <WrappedComponent {...this.props} {...getMeteorData(this.props)} />;
      }
    };
}

// Mock useTracker hook for testing
export function useTracker(reactiveFn) {
  // In tests, just call the reactive function synchronously
  return reactiveFn();
}

export { withTracker };
