import { assertSchoolOverviewHeader } from "../support/common/utils";

describe("New user", () => {
  it("sets new password and logs in the app", () => {
    cy.visit("http://localhost:3000/onboarding/welcome/setupAccountToken");

    cy.get("#txtPassword")
      .should("be.visible")
      .type("New-pass.9898");
    cy.get("#txtConfirmPassword").type("New-pass.9898");
    cy.findByText("Do we have the correct spelling of your name?").should("be.visible");
    cy.findByText("Set Password & Continue").click();
    assertSchoolOverviewHeader();
  });
});
