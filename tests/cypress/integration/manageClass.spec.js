import { generateNewUserEmail } from "../support/common/emailUtils";
import { goToSite } from "../support/common/navigating";
import { SCHOOL_ITEMS, TEST_GROUPS } from "../support/common/constants";
import { reactSelectByText } from "../support/common/utils";

/**
 * Groups used: grade5group2
 * Modifies: Primary teacher, adds new student group
 * Can't be rerun without test database restart
 * Requires Site with group
 */
describe("Manage Class:", () => {
  const newTeacherEmail = generateNewUserEmail("teacher");
  it("Data Admin adds new Teacher to class", () => {
    cy.loginAs({ role: "dataAdmin" });
    goToSite(SCHOOL_ITEMS.elementary);
    cy.findByText(TEST_GROUPS.grade5group2.name).click();
    cy.findByText("Manage Class").click();

    cy.findByText("Add new teacher")
      .should("be.visible")
      .click();
    cy.findByPlaceholderText("Last name").type("Smith");
    cy.findByPlaceholderText("First name").type("Alison");
    cy.findByPlaceholderText("Teacher ID").type("43215");
    cy.findByPlaceholderText("Email").type(newTeacherEmail);
    cy.findByText("Add teacher")
      .should("be.visible")
      .click();
    cy.findByText("Save").click();
    cy.findByTestId("Logan_row").should("be.visible");
  });

  it("Newly added teacher sees the welcome message", () => {
    cy.loginAs({ email: newTeacherEmail });
    cy.findByText("Welcome to SpringMath").should("be.visible");
  });

  it("Data Admin adds a class and a teacher to the school without permission to upload student roster", () => {
    cy.loginAs({ email: "<EMAIL>" });
    goToSite(SCHOOL_ITEMS.sunny);
    cy.findByText("Add Class & Teacher").click();

    cy.findByPlaceholderText("Class Name").type("Test 01#5");
    cy.findByPlaceholderText("Class Section ID").type("6ss1D-fqaFT_01_5-");
    cy.get("#select-grade").select("01");
    reactSelectByText("#select-teacher", "<EMAIL>");
    cy.findByText("Next").click();

    cy.findByTestId("newStudentLastName").should("be.visible");
  });
});
