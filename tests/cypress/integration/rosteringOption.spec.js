import { clickVisibleTestId, clickVisibleText, selectRosteringOption } from "../support/common/utils";
import { goToOrganizationById } from "../support/common/navigating";

/**
 * Groups used: -
 * Modifies: Organization rostering option
 * Can't be rerun without test database restart
 * Requires Organization with blocked file import
 */
describe("Rostering Option", () => {
  const blockedUploadOrgId = "unLLwgHQ4fHyHXgXt";

  it("DataAdmin uses blocked full roster import and verifies UI elements", () => {
    cy.loginAs({ email: "<EMAIL>" });
    clickVisibleText("Upload Roster file to SpringMath Support");
    cy.findByText("Upload Your Data for processing by SpringMath Support").should("be.visible");

    cy.fixture("fullRosterFirstUpload.csv", "utf-8").then(content => {
      cy.get("#dz1").upload(content, "fullRosterFirstUpload.csv");
      const date = new Date().toISOString().substring(0, 10);
      cy.findByText(`blocked_uploads_${date}_fullRosterFirstUpload.csv`).should("be.visible");
    });
    cy.findByText("Upload to SpringMath Support").click();
    cy.findByText("Successfully uploaded the roster file to the SpringMath Support").should("be.visible");
  });
  it("SuperAdmin uses blocked and default full roster import and verifies UI elements", () => {
    cy.loginAs({ role: "superAdmin" });
    cy.findByTestId("Test/Demo Organization").should("be.visible");
    clickVisibleText("Hide Empty Organizations");
    goToOrganizationById(blockedUploadOrgId);
    verifyDefaultRostering();

    cy.visit("http://localhost:3000/clients");
    cy.findByTestId("Test/Demo Organization").should("be.visible");
    cy.findByText("Hide Empty Organizations").click();
    selectRosteringOption(`${blockedUploadOrgId}_rostering`, "Roster Import");
    goToOrganizationById(blockedUploadOrgId);
    verifyDefaultRostering();
  });
  it("DataAdmin uses default full roster import and verifies UI elements", () => {
    cy.loginAs({ email: "<EMAIL>" });
    verifyDefaultRostering();
  });
  it("restore rostering option for BlockedUploads organization", () => {
    cy.loginAs({ role: "superAdmin" });
    cy.findByTestId("Test/Demo Organization").should("be.visible");
    cy.findByText("Hide Empty Organizations").click();
    selectRosteringOption(`${blockedUploadOrgId}_rostering`, "File Upload");
  });
});

function verifyDefaultRostering() {
  clickVisibleTestId("import-records-testid");
  cy.findByText("Upload Your Data", { exact: true }).should("be.visible");

  cy.fixture("fullRosterFirstUpload.csv", "utf-8").then(content => {
    cy.get("#dz1").upload(content, "fullRosterFirstUpload.csv");
    cy.findByText("fullRosterFirstUpload.csv", { exact: true }).should("be.visible");
  });
  cy.findByText("Finalize Upload").should("be.visible");
}
