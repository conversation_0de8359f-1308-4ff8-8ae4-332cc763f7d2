import { clickVisibleText, verifyInstructionalVideoPopup } from "../support/common/utils";
import { clickElementByTextWithinSideNav, clickDashboardTab } from "../support/common/navigating";
import { COACH_GRADE_LABELS, TEST_GROUPS } from "../support/common/constants";
import { inputScreeningScores, saveScreeningScores } from "../support/enterScoreHelper";

/**
 * Groups used: grade6group2
 * Modifies: Screening
 * Can't be rerun without test database restart
 * Requires Student group without screening
 */
describe("Forcing classwide intervention:", () => {
  describe("Admins", () => {
    beforeEach(() => {
      cy.loginAs({ role: "coach" });
    });
    it("should be able to force classwide intervention on groups that passed benchmark assessment.", () => {
      clickElementByTextWithinSideNav(COACH_GRADE_LABELS.sixth);
      cy.findByText(TEST_GROUPS.grade6group2.name, { exact: false }).click({ force: true });
      clickVisibleText("Begin Screening");
      verifyInstructionalVideoPopup("screening");
      inputScreeningScores(true, 0, true);
      saveScreeningScores();
      cy.get("#force-classwide-intervention-dropdown").click();
      cy.findByText("Override Recommendation and Schedule Classwide Intervention").click();
      cy.findByTestId("classwideInterventionTab").should("be.visible");
      clickDashboardTab(TEST_GROUPS.grade6group2.name, "classwideInterventionTab");
      cy.findByText("Your class has been scheduled for a classwide intervention.").should("be.visible");
    });
  });
});
