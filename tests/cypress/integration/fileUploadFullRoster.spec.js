import { addSchool, clickVisibleTestId, clickVisibleText, dropFile } from "../support/common/utils";
import { goToOrganizationById } from "../support/common/navigating";

/**
 * Groups used: Self uploaded groups
 * Modifies: Organization roster
 * Can't be rerun without test database restart
 * Requires Empty organization
 */
describe("File Upload- Full Roster:", () => {
  const fileUploadOrgId = "tuk2kibzQn7wieKRP";
  describe("Super Admin", () => {
    beforeEach(() => {
      cy.loginAs({ role: "superAdmin" });
      cy.findByTestId("test_organization_id_rostering").should("be.visible");
    });
    it("tries to import valid roster to empty organization and sees modal", () => {
      clickVisibleText("Include Inactive Clients");
      cy.findByText("Hide Empty Organizations").click();
      goToOrganizationById(fileUploadOrgId);
      cy.findByText("UPLOAD DATA", { exact: false }).click();

      dropFile("fullRosterFirstUpload.csv");
      clickVisibleText("Finalize Upload");
      cy.findByTestId("new-schools-modal").should("be.visible");
      cy.findByText("FirstSchool (1)").should("be.visible");
      clickVisibleText("Proceed with Import");
      cy.findByText("No Sites Uploaded").should("be.visible");
    });
    it("uploads valid roster to inactive organization", () => {
      clickVisibleText("Include Inactive Clients");
      cy.findByText("Hide Empty Organizations").click();
      goToOrganizationById(fileUploadOrgId);
      addSchool("FirstSchool", "1");

      clickVisibleTestId("import-records-testid");
      dropFile("fullRosterFirstUpload.csv");
      clickVisibleText("Finalize Upload");
      const newSchoolName = "FirstSchool";

      cy.findByTestId(`schoolItem_${newSchoolName}`)
        .should("be.visible")
        .within(() => {
          cy.findByText(newSchoolName).should("be.visible");
          cy.findByTestId(`${newSchoolName}-demographics`)
            .invoke("text")
            .then(text => {
              expect(text).to.contain("0 Teachers onboarded");
              expect(text).to.contain("1 Teachers yet to onboard");
              expect(text).to.contain("2 Students");
              expect(text).to.contain("2 Classes/Groups");
            });
        });
    });

    it("uploads valid high school roster to inactive organization with mixed HS grades (9-12 + HS) into the same class section", () => {
      clickVisibleText("Include Inactive Clients");
      cy.findByText("Hide Empty Organizations").click();
      goToOrganizationById(fileUploadOrgId);

      addSchool("SecondSchool", "2");

      clickVisibleTestId("import-records-testid");

      dropFile("fullRosterHSGrades.csv");
      clickVisibleText("Finalize Upload");
      const newSchoolName = "SecondSchool";

      cy.findByTestId(`schoolItem_${newSchoolName}`)
        .should("be.visible")
        .within(() => {
          cy.findByText(newSchoolName).should("be.visible");
          cy.findByTestId(`${newSchoolName}-demographics`)
            .invoke("text")
            .then(text => {
              expect(text).to.contain("0 Teachers onboarded");
              expect(text).to.contain("1 Teachers yet to onboard");
              expect(text).to.contain("9 Students");
              expect(text).to.contain("1 Classes/Groups");
            });
        });
    });

    it("sees validation errors when trying to upload roster with faulty or incomplete data", () => {
      goToOrganizationById(fileUploadOrgId);

      clickVisibleTestId("import-records-testid");

      // EMPTY ROSTER
      dropFile("emptyCSV.csv");
      cy.findByText("The upload tool found no parsable data.").should("be.visible");

      // VALIDATION ERRORS
      dropFile("fullRosterValidationErrors.csv");
      cy.get("textarea")
        .invoke("text")
        .then(text => {
          expect(text).to.contain(
            "The same Class Section ID, Teacher ID, Student Local ID was used in more than one row:\nClass Section ID: 2,\tTeacher ID: 1,\tStudent Local ID: 20"
          );
          expect(text).to.contain(
            "The same Class Section ID, Teacher ID, Student State ID was used in more than one row:\nClass Section ID: 2,\tTeacher ID: 1,\tStudent State ID: 200"
          );
          expect(text).to.contain(
            "More than one teacherID is present for TeacherEmail: <EMAIL>, found:\nTeacher ID: 1\nTeacher ID: 2"
          );
        });

      cy.reload();

      // UNSUPPORTED COLUMNS
      dropFile("unsupportedColumns.csv");
      cy.get("textarea")
        .invoke("text")
        .then(text => {
          expect(text).to.contain("Unsupported fields found:\nUnsupportedColumn1\nUnsupportedColumn2");
        });

      cy.reload();

      // MISSING COLUMNS
      dropFile("missingColumns.csv");
      cy.get("textarea")
        .invoke("text")
        .then(text => {
          expect(text).to.contain("Your CSV file is missing the following fields:\nSchoolName");
        });
    });

    it("sees that student group was archived when trying to upload a roster with missing currently active student group", () => {
      goToOrganizationById(fileUploadOrgId);

      clickVisibleTestId("import-records-testid");

      // MISSING STUDENT GROUP ARCHIVED
      dropFile("fullRosterMissingGroup.csv");
      clickVisibleText("Finalize Upload");
      cy.get("#loader", { timeout: 10000 }).should("not.exist");
      cy.findByTestId("schoolItem_FirstSchool").within(() => {
        clickVisibleText("Manage");
      });
      cy.get(".list-group-item")
        .should("be.visible")
        .should("have.length", 2);
    });
    it("sees unique email errors when trying to upload teachers already in a database with a different orgid", () => {
      goToOrganizationById(fileUploadOrgId);

      clickVisibleTestId("import-records-testid");

      // DUPLICATE EMAILS ACROSS ORGANIZATIONS ERROR
      dropFile("fullRosterDuplicateEmails.csv");
      clickVisibleText("Finalize Upload");
      cy.get("textarea")
        .invoke("text")
        .then(text => {
          expect(text).to.contain("Error validating teacher email addresses");
          expect(text).to.contain(
            "Teacher e-mail: <EMAIL> is already used by a different teacher: MultiSite Teacher (TeacherID: multiSiteTeacher) in a different organization. This teacher won't be uploaded."
          );
          expect(text).to.contain(
            "Teacher e-mail: <EMAIL> is already used by a different teacher: Teacher User (TeacherID: local_teacher_user_id) in a different organization. This teacher won't be uploaded."
          );
        });
    });
    it("successfully uploads a roster with mixed K-8, 9-12, HS school grades", () => {
      goToOrganizationById(fileUploadOrgId);
      clickVisibleTestId("import-records-testid");

      dropFile("fullRosterMixedSchoolGrades.csv");

      cy.findByTestId("schoolSubtotal").should("contain", 1);
      cy.findByTestId("teacherSubtotal").should("contain", 1);
      cy.findByTestId("classSubtotal").should("contain", 2);
      cy.findByTestId("studentSubtotal").should("contain", 2);
    });
    it("successfully uploads a roster with two teachers with the same class name and section ID in the same school", () => {
      goToOrganizationById(fileUploadOrgId);
      clickVisibleTestId("import-records-testid");

      dropFile("fullRosterTwoTeachersWithSameSectionIdInSameSchool.csv");

      cy.findByTestId("schoolSubtotal").should("contain", 1);
      cy.findByTestId("teacherSubtotal").should("contain", 2);
      cy.findByTestId("classSubtotal").should("contain", 2);
      cy.findByTestId("studentSubtotal").should("contain", 3);

      clickVisibleText("Finalize Upload");
      const newSchoolName = "FirstSchool";

      cy.findByTestId(`schoolItem_${newSchoolName}`)
        .should("be.visible")
        .within(() => {
          cy.findByText(newSchoolName).should("be.visible");
          cy.findByTestId(`${newSchoolName}-demographics`)
            .invoke("text")
            .then(text => {
              expect(text).to.contain("0 Teachers onboarded");
              expect(text).to.contain("2 Teachers yet to onboard");
              expect(text).to.contain("3 Students");
              expect(text).to.contain("2 Classes/Groups");
            });
          cy.findByText("Manage").click();
        });

      cy.findByTestId("manage-group-manage-class").click();
      cy.findByText("Teacher New").should("be.visible");
    });

    it("successfully uploads a roster with two teachers with the same class name and section ID in different schools", () => {
      goToOrganizationById(fileUploadOrgId);
      clickVisibleTestId("import-records-testid");

      dropFile("fullRosterTwoTeachersWithSameSectionIdInDifferentSchools.csv");

      cy.findByTestId("schoolSubtotal").should("contain", 2);
      cy.findByTestId("teacherSubtotal").should("contain", 2);
      cy.findByTestId("classSubtotal").should("contain", 3);
      cy.findByTestId("studentSubtotal").should("contain", 3);

      clickVisibleText("Finalize Upload");
      const newSchoolName = "SecondSchool";

      cy.findByTestId(`schoolItem_${newSchoolName}`)
        .should("be.visible")
        .within(() => {
          cy.findByText(newSchoolName).should("be.visible");
          cy.findByTestId(`${newSchoolName}-demographics`)
            .invoke("text")
            .then(text => {
              expect(text).to.contain("0 Teachers onboarded");
              expect(text).to.contain("1 Teachers yet to onboard");
              expect(text).to.contain("1 Students");
              expect(text).to.contain("1 Classes/Groups");
            });
        });
    });
  });
});
