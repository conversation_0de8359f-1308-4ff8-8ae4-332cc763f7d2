/* eslint-disable cypress/unsafe-to-chain-command */
import {
  clickDashboardTab,
  clickElementByTextWithinSideNav,
  goToGroupClasswideIntervention
} from "../support/common/navigating";
import { clearAllScores, inputAndCalculateClasswideScores, saveClasswideScores } from "../support/enterScoreHelper";
import {
  checkCurrentClasswideSkill,
  clickVisibleTestId,
  clickVisibleText,
  waitForNthScoreToBeSaved
} from "../support/common/utils";
import { INTERVENTION_SKILLS_BY_GRADE_BY_INDEX, COACH_GRADE_LABELS, TEST_GROUPS } from "../support/common/constants";

function verifyPlacementNotificationText(text) {
  cy.findByTestId("placementNotification")
    .should("be.visible")
    .should("contain", text);
}

function verifyCurrentSkillText(text) {
  cy.findByTestId("currentSkill")
    .scrollIntoView()
    .should("be.visible")
    .should("contain", text);
}

function verifyScoringTrend(groupName, score = "N/A") {
  cy.findByTestId(`scoringTrend_${groupName}`)
    .scrollIntoView()
    .should("be.visible")
    .should("have.text", score);
}

const placementTextByMedianScoreThreshold = {
  belowInstructionalTarget: "It appears your class needs to better learn this skill",
  atAboveInstructionalTarget: "Please save your scores and continue practicing this skill",
  // NOTE(fmazur) - majority means 80% of student scores are at/above instructional target for groups with less than 10 students
  majorityAtAboveInstructionalTarget: "majority of students in your small class are above the instructional target",
  // NOTE(fmazur) - majority overrides this placementNotification for groups with less than 10 students
  atAboveMasteryTarget: "Excellent work"
};

/**
 * Groups used: grade2group1
 * Modifies: Classwide Intervention
 * Can't be rerun without test database restart
 * Requires group with failed screening and classwide intervention with n-3/n skills completed
 */
// TODO(fmazur) - unit test placementNotification for 10+ students
describe("Classwide Intervention Assessment", () => {
  const classwideSkillNameByInterventionProgressIndex = INTERVENTION_SKILLS_BY_GRADE_BY_INDEX["02"];
  describe("Teacher enters classwide assessment", () => {
    it("should not display any score improvement trend for Test 02 (fqaFT_02) student group", () => {
      cy.loginAs({ role: "coach" });
      verifyScoringTrend(TEST_GROUPS.grade2group1.name);
    });

    it("and inputs an unusual high score followed by valid scores", () => {
      cy.loginAs({ role: "teacher" });
      goToGroupClasswideIntervention(TEST_GROUPS.grade2group1.name);

      inputAndCalculateClasswideScores({ isPassing: false });
      cy.get("#score_input_0")
        .clear()
        .should("have.value", "")
        .type("999");

      cy.findByText("Unusual High Score").should("be.visible");
      cy.findByText("Fix Unusual High Scores").should("be.visible");

      clearAllScores();
    });

    it("and inputs failing classwide intervention assessment scores", () => {
      cy.loginAs({ role: "teacher" });
      goToGroupClasswideIntervention(TEST_GROUPS.grade2group1.name);

      inputAndCalculateClasswideScores({ isPassing: false });
      verifyPlacementNotificationText(placementTextByMedianScoreThreshold.belowInstructionalTarget);

      saveClasswideScores();
      verifyCurrentSkillText(classwideSkillNameByInterventionProgressIndex[13]);
    });

    it("and inputs slightly improving classwide intervention assessment scores for some students", () => {
      cy.loginAs({ role: "teacher" });
      goToGroupClasswideIntervention(TEST_GROUPS.grade2group1.name);

      inputAndCalculateClasswideScores({ score: 2 });
      cy.get("#score_input_0")
        .clear()
        .type(0);
      waitForNthScoreToBeSaved({ scoreNumber: 0 });
      verifyPlacementNotificationText(placementTextByMedianScoreThreshold.belowInstructionalTarget);

      saveClasswideScores();
      verifyCurrentSkillText(classwideSkillNameByInterventionProgressIndex[13]);
    });

    it("and inputs at/above instructional target scores for classwide intervention", () => {
      cy.loginAs({ role: "teacher" });
      goToGroupClasswideIntervention(TEST_GROUPS.grade2group1.name);

      inputAndCalculateClasswideScores({ score: 5 });
      verifyPlacementNotificationText(placementTextByMedianScoreThreshold.majorityAtAboveInstructionalTarget);

      clearAllScores();
    });

    it("should display 80% score improvement rate for Test 02 (fqaFT_02) student group", () => {
      cy.loginAs({ role: "coach" });
      verifyScoringTrend(TEST_GROUPS.grade2group1.name, "80%");
    });

    it("and inputs passing classwide intervention assessment scores", () => {
      cy.loginAs({ role: "teacher" });
      goToGroupClasswideIntervention(TEST_GROUPS.grade2group1.name);
      inputAndCalculateClasswideScores({ isPassing: true });
      verifyPlacementNotificationText(placementTextByMedianScoreThreshold.atAboveMasteryTarget);

      saveClasswideScores();
      verifyCurrentSkillText(classwideSkillNameByInterventionProgressIndex[14]);
    });

    it("should display 100% score improvement rate for Test 02 (fqaFT_02) student group", () => {
      cy.loginAs({ role: "coach" });
      verifyScoringTrend(TEST_GROUPS.grade2group1.name, "100%");
      cy.findByTestId(`scoringTrend_${TEST_GROUPS.grade2group1.name}`).should("not.have.class", "text-danger");
    });

    it("and inputs failing classwide intervention assessment scores for the new intervention", () => {
      cy.loginAs({ role: "teacher" });
      goToGroupClasswideIntervention(TEST_GROUPS.grade2group1.name);

      inputAndCalculateClasswideScores({ isPassing: false });
      verifyPlacementNotificationText(placementTextByMedianScoreThreshold.belowInstructionalTarget);

      saveClasswideScores();
      verifyCurrentSkillText(classwideSkillNameByInterventionProgressIndex[14]);
    });

    it("should keep displaying 100% score improvement rate for Test 02 (fqaFT_02) student group (since not enough scores in current skill)", () => {
      cy.loginAs({ role: "coach" });
      verifyScoringTrend(TEST_GROUPS.grade2group1.name, "100%");
      cy.findByTestId(`scoringTrend_${TEST_GROUPS.grade2group1.name}`).should("not.have.class", "text-danger");
    });

    it("and inputs slightly improving classwide intervention assessment scores for some students", () => {
      cy.loginAs({ role: "teacher" });
      goToGroupClasswideIntervention(TEST_GROUPS.grade2group1.name);

      inputAndCalculateClasswideScores({ score: 3 });
      cy.get("#score_input_0")
        .clear()
        .type(0);
      waitForNthScoreToBeSaved({ scoreNumber: 0 });
      cy.get("#score_input_1")
        .clear()
        .type(0);
      waitForNthScoreToBeSaved({ scoreNumber: 1 });
      cy.get("#score_input_2")
        .clear()
        .type(0);
      waitForNthScoreToBeSaved({ scoreNumber: 2 });
      verifyPlacementNotificationText(placementTextByMedianScoreThreshold.belowInstructionalTarget);

      saveClasswideScores();
      verifyCurrentSkillText(classwideSkillNameByInterventionProgressIndex[14]);
    });

    it("should display 40% score improvement rate for Test 02 (fqaFT_02) student group", () => {
      cy.loginAs({ role: "coach" });
      verifyScoringTrend(TEST_GROUPS.grade2group1.name, "40%");
      cy.findByTestId(`scoringTrend_${TEST_GROUPS.grade2group1.name}`).should("have.class", "text-danger");
    });

    it("and is able to see previous assessment scores during the intervention", () => {
      cy.loginAs({ role: "teacher" });
      goToGroupClasswideIntervention(TEST_GROUPS.grade2group1.name);

      cy.get(".skill-progress")
        .first()
        .scrollIntoView()
        .within(() => {
          clickVisibleText(classwideSkillNameByInterventionProgressIndex[0]);
          cy.findByText(classwideSkillNameByInterventionProgressIndex[15])
            .scrollIntoView()
            .click(); // not active (future assessment)
        });

      checkCurrentClasswideSkill(classwideSkillNameByInterventionProgressIndex[0]);
    });

    it("and gets redirected to Classwide Intervention tab when clicking on active Classwide Intervention in student list view", () => {
      cy.loginAs({ role: "teacher" });
      goToGroupClasswideIntervention(TEST_GROUPS.grade2group1.name);
      clickDashboardTab(TEST_GROUPS.grade2group1.name, "growthTab");

      cy.get(".studentListIndividualInterventionList")
        .first()
        .within(() => {
          clickVisibleTestId("classwideDetails");
        });

      cy.url().should("contain", "classwide");
      cy.findByTestId("classwideInterventionTab").should("have.class", "active");
    });

    it("and sees the graph view after completing the intervention", () => {
      cy.loginAs({ role: "teacher" });
      goToGroupClasswideIntervention(TEST_GROUPS.grade2group1.name);
      inputAndCalculateClasswideScores({ isPassing: true });
      saveClasswideScores();
      verifyCurrentSkillText(classwideSkillNameByInterventionProgressIndex[15]);

      inputAndCalculateClasswideScores({ isPassing: true });
      clickVisibleTestId("saveScoreBtn");

      cy.findByTestId("classwide-pm-chart").should("be.visible");
      cy.url().should("contain", "/classwide");
    });

    it("and closes the completed intervention message and sees the Classwide Intervention tab disappear", () => {
      cy.loginAs({ role: "teacher" });
      goToGroupClasswideIntervention(TEST_GROUPS.grade2group1.name);

      cy.get(".skill-progress").should("exist");

      // clickVisibleTestId("dismissInterventionMessage");
      // cy.findByTestId("classwideInterventionTab").should("not.exist");
      // cy.findByTestId("studentsTab").should("have.class", "active");
    });

    it("and is able to see previous assessment scores in classwide view for completed intervention", () => {
      cy.loginAs({ role: "teacher" });
      clickElementByTextWithinSideNav(TEST_GROUPS.grade2group1.name);
      cy.findByTestId(`student-group_${TEST_GROUPS.grade2group1.name}`).should("be.visible");
      clickDashboardTab(TEST_GROUPS.grade2group1.name, "growthTab");

      cy.get(".studentListIndividualInterventionList")
        .first()
        .within(() => {
          clickVisibleTestId("classwideDetails");
        });
      cy.url().should("contain", "classwide");

      checkCurrentClasswideSkill(classwideSkillNameByInterventionProgressIndex[15]);

      cy.get(".skill-progress")
        .first()
        .within(() => {
          cy.findByText(classwideSkillNameByInterventionProgressIndex[1]).click();
        });

      checkCurrentClasswideSkill(classwideSkillNameByInterventionProgressIndex[1]);
    });
  });
  describe("Admin enters classwide assessment", () => {
    it("and is able to see previous assessment scores in completed intervention", () => {
      cy.loginAs({ role: "coach" });
      clickElementByTextWithinSideNav(COACH_GRADE_LABELS.second);
      cy.findByTestId("screeningNotice")
        .should("be.visible")
        .within(() => {
          cy.contains(TEST_GROUPS.grade2group1.name)
            .should("be.visible")
            .click();
        });

      clickDashboardTab(TEST_GROUPS.grade2group1.name, "growthTab");
      cy.get(".studentListIndividualInterventionList")
        .first()
        .within(() => {
          clickVisibleTestId("classwideDetails");
        });

      checkCurrentClasswideSkill(classwideSkillNameByInterventionProgressIndex[15]);

      cy.get(".skill-progress")
        .first()
        .within(() => {
          cy.findByText("Subtraction 0-12").click();
        });
      checkCurrentClasswideSkill("Subtraction 0-12");
    });
  });
});
