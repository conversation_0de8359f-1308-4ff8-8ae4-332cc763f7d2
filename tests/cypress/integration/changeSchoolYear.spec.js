import { assertSchoolOverviewHeader, clickVisibleTestId, getSchoolYearLabel } from "../support/common/utils";
import { NAVBAR_SCHOOL_YEAR_TEST_ID } from "../support/common/constants";

const CURRENT_SCHOOL_YEAR = Cypress.env("CURRENT_SCHOOL_YEAR");
const PREVIOUS_SCHOOL_YEAR = Cypress.env("PREVIOUS_SCHOOL_YEAR");

/**
 * Groups used: -
 * Modifies: Active schoolYear for coach
 * Can be rerun without test database restart
 * Requires: User with ability to change school year and siteAccess with multiple school years within site
 */
describe("Changing school years: ", () => {
  beforeEach(() => {
    cy.loginAs({ role: "coach" });
  });
  describe(`When admin starts with ${CURRENT_SCHOOL_YEAR} school year`, () => {
    it("should display the correct year below the SM logo", () => {
      cy.findByTestId(NAVBAR_SCHOOL_YEAR_TEST_ID).should("contain", getSchoolYearLabel(CURRENT_SCHOOL_YEAR));
    });
  });
  describe("When admin changes to the previous year", () => {
    it("should display the correct year below the SM logo", () => {
      cy.findByTestId(NAVBAR_SCHOOL_YEAR_TEST_ID).should("contain", getSchoolYearLabel(CURRENT_SCHOOL_YEAR));
      assertSchoolOverviewHeader();
      clickVisibleTestId("userContextMenu");
      cy.findByText(getSchoolYearLabel(PREVIOUS_SCHOOL_YEAR)).click();
      cy.findByTestId(NAVBAR_SCHOOL_YEAR_TEST_ID).should("contain", getSchoolYearLabel(PREVIOUS_SCHOOL_YEAR));
      assertSchoolOverviewHeader();
    });
  });
});
