import { clickVisibleTestId, logout, waitForLoadingToDisappear } from "../support/common/utils";
import { checkElementByTextWithinSideNav, changeActiveSite } from "../support/common/navigating";
import { CONTEXT_MENU_SITE_IDS, SCHOOL_NAMES } from "../support/common/constants";

const CURRENT_SCHOOL_YEAR = Cypress.env("CURRENT_SCHOOL_YEAR");
const PREVIOUS_SCHOOL_YEAR = Cypress.env("PREVIOUS_SCHOOL_YEAR");

/**
 * Groups used: -
 * Modifies: Active site for coach and multi-site teacher
 * Can be rerun without test database restart
 * Requires: User with ability to change site and siteAccess to multiple sites
 */
describe("changeActiveSite", () => {
  describe("Coach user", () => {
    beforeEach(() => {
      cy.loginAs({ role: "coach" });
    });
    it("should be able to switch to a different site with admin access", () => {
      checkElementByTextWithinSideNav(SCHOOL_NAMES.elementary);
      changeActiveSite(CONTEXT_MENU_SITE_IDS.dummy);
      checkElementByTextWithinSideNav(SCHOOL_NAMES.dummy);
    });

    it("should change default site", () => {
      checkElementByTextWithinSideNav(SCHOOL_NAMES.dummy);
      changeActiveSite(CONTEXT_MENU_SITE_IDS.elementary);
      logout();
      cy.loginAs({ role: "coach" });
      checkElementByTextWithinSideNav(SCHOOL_NAMES.elementary);
    });

    it("should get newest available year in default site", () => {
      checkElementByTextWithinSideNav(SCHOOL_NAMES.elementary);
      clickVisibleTestId("userContextMenu");
      cy.findByTestId(`schoolYear${PREVIOUS_SCHOOL_YEAR}`).click();
      logout();
      cy.loginAs({ role: "coach" });
      checkElementByTextWithinSideNav(SCHOOL_NAMES.elementary);
      clickVisibleTestId("userContextMenu");
      cy.findByTestId(`schoolYear${CURRENT_SCHOOL_YEAR}`).within(() => {
        cy.findByTestId("currentlySelectedYear").should("exist");
      });
    });
  });

  describe("Teacher user", () => {
    beforeEach(() => {
      cy.loginAs({ role: "multiSiteTeacher" });
    });
    it("should be able to use other sites when owning a group in more than one site", () => {
      checkElementByTextWithinSideNav(SCHOOL_NAMES.elementary);
      changeActiveSite(CONTEXT_MENU_SITE_IDS.dummy);
      waitForLoadingToDisappear();
      checkElementByTextWithinSideNav(SCHOOL_NAMES.dummy);
    });

    it("should change default site", () => {
      checkElementByTextWithinSideNav(SCHOOL_NAMES.dummy);
      changeActiveSite(CONTEXT_MENU_SITE_IDS.elementary);
      logout();
      cy.loginAs({ role: "multiSiteTeacher" });
      checkElementByTextWithinSideNav(SCHOOL_NAMES.elementary);
    });
  });
});
