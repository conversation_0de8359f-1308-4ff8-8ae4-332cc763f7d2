import { inputScreeningScores, verifyStudentScoreInputs } from "../support/enterScoreHelper";
import {
  clickElementByTextWithinSideNav,
  goToGroupClasswideIntervention,
  goToStudentListFor
} from "../support/common/navigating";
import {
  checkCurrentClasswideSkill,
  clickVisibleTestId,
  clickVisibleText,
  dropFile,
  getBenchmarkPeriodNameByDate,
  logout,
  moveSelectedStudentsToGroup,
  reactSelectByText,
  selectRosteringOption,
  waitForDetachedDom,
  waitForLoadingToDisappear
} from "../support/common/utils";
import { INTERVENTION_SKILLS_BY_GRADE_BY_INDEX, SCHOOL_ITEMS, TEST_GROUPS } from "../support/common/constants";

const benchmarkPeriodName = getBenchmarkPeriodNameByDate();
const numberOfScreeningScoresEnteredByBenchmarkPeriod = {
  Fall: "9",
  Winter: "8",
  Spring: "12"
};

/**
 * Groups used: grade4group1
 * Modifies:
 * Can't be rerun without test database restart
 * Requires superAdmin account
 * Requires grade at 9th classwide skill completed
 */
describe("Universal Data Admin", () => {
  const invalidEmail = "<EMAIL>";
  const validEmail = "<EMAIL>";
  const inviteSentText = "Invite Sent. Please contact the new user to inform them the invite has been sent.";
  const invalidEmailText =
    "Invalid email. The Universal Data Admin Email will only accept email addresses with a sourcewell-mn.gov domain.";
  const classwideSkillNameByInterventionProgressIndex = INTERVENTION_SKILLS_BY_GRADE_BY_INDEX["08"];

  it("can be added by a super admin", () => {
    cy.loginAs({ role: "superAdmin" });
    cy.findByText("Test/Demo Organization").should("be.visible");
    cy.get("#menu-nav-dropdown")
      .should("be.visible")
      .click();
    clickVisibleTestId("topNav_manage-users");
    clickVisibleTestId("addUniversalDataAdminButton");
    cy.findByTestId("email").type(invalidEmail);
    cy.findByTestId("firstName").type("John");
    cy.findByTestId("lastName").type("Dee");
    clickVisibleText("Send Invite Email");
    cy.findByText(invalidEmailText).should("be.visible");
    cy.findByText(inviteSentText, { timeout: 1000 }).should("not.exist");
    // eslint-disable-next-line cypress/unsafe-to-chain-command
    cy.findByTestId("email")
      .clear()
      .type(validEmail);
    clickVisibleText("Send Invite Email");
    cy.findByText(inviteSentText).should("be.visible");
  });

  it("can edit classwide intervention scores causing a student group to move down a skill", () => {
    cy.loginAs({ role: "teacher" });
    goToGroupClasswideIntervention(TEST_GROUPS.grade8group1.name);
    checkCurrentClasswideSkill(classwideSkillNameByInterventionProgressIndex[27]);

    logout();
    cy.loginAs({ role: "universalDataAdmin" });
    navigateToEditClasswideScores();
    editStudentScore(0, 0);
    editStudentScore(2, 1);
    editStudentScore(4, 2);

    logout(true);
    cy.loginAs({ role: "teacher" });
    goToGroupClasswideIntervention(TEST_GROUPS.grade8group1.name);
    checkCurrentClasswideSkill(classwideSkillNameByInterventionProgressIndex[26]);
    verifyStudentScoreInputs();
  });

  it("can edit classwide intervention scores causing a student group to move up a skill", () => {
    cy.loginAs({ role: "universalDataAdmin" });
    navigateToEditClasswideScores();
    editStudentScore(2, 5);

    logout(true);
    cy.loginAs({ role: "teacher" });
    goToGroupClasswideIntervention(TEST_GROUPS.grade8group1.name);
    checkCurrentClasswideSkill(classwideSkillNameByInterventionProgressIndex[27]);
    verifyStudentScoreInputs();
  });

  it("can clear classwide intervention scores", () => {
    cy.loginAs({ role: "universalDataAdmin" });
    cy.findByText("Client List").should("be.visible");
    clickVisibleText("Test/Demo Organization");
    goToStudentListFor(TEST_GROUPS.grade8group1.name);
    clickVisibleText("Manage Scores");
    waitForLoadingToDisappear();
    cy.findByTestId(classwideSkillNameByInterventionProgressIndex[26]).should("be.visible");
    // eslint-disable-next-line cypress/unsafe-to-chain-command
    cy.findByTestId("cwi-clear-scores-btn")
      .scrollIntoView()
      .should("be.visible")
      .click();
    clickVisibleText("Yes, clear scores");
    waitForDetachedDom();
    cy.findByTestId(classwideSkillNameByInterventionProgressIndex[25]).should("be.visible");
    cy.findByTestId(classwideSkillNameByInterventionProgressIndex[26]).should("not.exist");
  });

  it("can view the client list", () => {
    cy.loginAs({ email: validEmail });
    cy.findByText("Client List").should("be.visible");
    cy.findByTestId("topNav_client-list-dashboard").should("be.visible");
    cy.findByTestId("topNav_clients-dashboard", { timeout: 100 }).should("not.exist");
    cy.findByTestId("addSupportUserButton", { timeout: 100 }).should("not.exist");
    cy.findByTestId("addUniversalCoachButton", { timeout: 100 }).should("not.exist");
    cy.findByTestId("addUniversalDataAdminButton", { timeout: 100 }).should("not.exist");
    cy.findByTestId("deactivateOrganizationButton", { timeout: 100 }).should("not.exist");
    cy.get("[data-testid$='_row']").should("have.length.within", 4, 5);
  });

  it("can add a client", () => {
    cy.loginAs({ role: "universalDataAdmin" });
    clickVisibleTestId("addClientButton");

    // add client form
    cy.get('input[name="clientName"]').type("testClientName");
    selectRosteringOption("rostering-select", "File Upload", false);
    cy.get('input[name="city"]').type("testCity");
    cy.get('input[name="state"]').type("testState");
    cy.get('input[name="firstName"]').type("testClientFirstName");
    cy.get('input[name="lastName"]').type("testClientLastName");
    clickVisibleText("Create Client");
    cy.findByText("testClientName").should("be.visible");
    cy.findByText("No Sites Uploaded").should("be.visible");
  });

  it("can add a class with a teacher, add a student, and upload a roster", () => {
    cy.loginAs({ role: "universalDataAdmin" });
    cy.findByTestId("Test/Demo Organization").should("be.visible");
    clickVisibleText("Hide Empty Organizations");
    clickVisibleText("Blocked Uploads");

    // add grade and new teacher to new site
    cy.findByTestId(SCHOOL_ITEMS.blocked).within(() => {
      clickVisibleText("Manage");
    });
    clickVisibleText("Add Class & Teacher");
    cy.findByPlaceholderText("Class Name")
      .should("be.visible")
      .type("Test Class");
    cy.findByPlaceholderText("Class Section ID").type("222111");
    reactSelectByText("#select-teacher", "Add new teacher");
    cy.findByPlaceholderText("Last name").type("testTeacher");
    cy.findByPlaceholderText("First name").type("testUser");
    cy.findByPlaceholderText("Teacher ID").type("333222");
    cy.findByPlaceholderText("Email").type("<EMAIL>");
    cy.findByText("Upload students", { exact: false }).should("be.visible");
    clickVisibleText("Next");
    cy.findByText("Class has been added successfully").should("be.visible");
    dropFile("exampleUploadStudent3.csv");
    clickVisibleText("Finalize Upload");
    cy.findByTestId("Tomato_row").should("be.visible");

    // upload full roster
    clickVisibleTestId("navbar-school-year");
    clickVisibleText("Blocked Uploads");
    clickVisibleText("Menu");
    clickVisibleText("Data Admin Dashboard");
    clickVisibleTestId("import-records-testid");
    dropFile("fullRosterTestClient_universalDataAdmin.csv");
    clickVisibleText("Finalize Upload");
    cy.get("#menu-nav-dropdown")
      .should("be.visible")
      .click();
    clickVisibleTestId("topNav_data-admin-dashboard");
    cy.findByTestId("district-details-testid").within(() => {
      cy.findByText("Complete").should("be.visible");
    });
  });

  it("can add and remove coaches and data admins", () => {
    cy.loginAs({ role: "universalDataAdmin" });
    cy.findByTestId("Test/Demo Organization").should("be.visible");
    clickVisibleText("Hide Empty Organizations");
    clickVisibleText("Blocked Uploads");

    // add and remove coach user
    clickVisibleTestId("addCoachUserMain");
    // eslint-disable-next-line cypress/unsafe-to-chain-command
    cy.findByText("Submit")
      .scrollIntoView()
      .should("be.visible");
    cy.findByText("You must select at least one site").should("be.visible");
    cy.get("input[type='checkbox']").check();
    cy.findByText("You must select at least one site", { timeout: 200 }).should("not.exist");
    cy.findByTestId("txtCoachEmail").type("<EMAIL>");
    cy.findByTestId("txtCoachFirstName").type("testCoachFirstName");
    cy.findByTestId("txtCoachLastName").type("testCoachLastName");
    cy.findByText("Submit").click();
    cy.findByText("Back to Dashboard").click();

    clickVisibleText("Blocked Uploads");
    cy.get(".conCoachesList")
      .first()
      .within(() => {
        cy.findByText("testCoachFirstName testCoachLastName").should("be.visible");
        cy.findByTestId("removeCoaches").click();
      });
    clickVisibleTestId("confirm-modal-btn");
    cy.findByText("No Existing Coaches").should("be.visible");

    // add and remove data administrator
    cy.findByTestId("addDataAdminUserMain").click();
    cy.findByTestId("txtDataAdminEmail").type("<EMAIL>");
    cy.findByTestId("txtDataAdminFirstName").type("testDataAdminFirstName");
    cy.findByTestId("txtDataAdminLastName").type("testDataAdminLastName");
    clickVisibleText("Submit");

    cy.findByTestId("navbar-school-year").click();
    clickVisibleText("Blocked Uploads");

    cy.findByTestId("district-details-testid")
      .should("be.visible")
      .within(() => {
        cy.findByText("testDataAdminFirstName testDataAdminLastName").should("be.visible");
        cy.findAllByTestId("removeDataAdminUsers")
          .eq(1)
          .click();
      });
    clickVisibleTestId("confirm-modal-btn");
    cy.findAllByTestId("removeDataAdminUsers").should("have.length", 1);
  });

  it("can search for students and navigate back to schools", () => {
    // duplicate
    cy.loginAs({ role: "universalDataAdmin" });
    cy.findByTestId("Test/Demo Organization").should("be.visible");
    clickVisibleText("Hide Empty Organizations");
    clickVisibleText("Blocked Uploads");
    cy.findByTestId(SCHOOL_ITEMS.blocked).within(() => {
      clickVisibleText("Manage");
    });
    clickVisibleText("Student Search");

    cy.findByTestId("studentLastName")
      .should("be.visible")
      .type("test");
    cy.findByTestId("studentFirstName").type("test");
    cy.findByTestId("studentLocalId").type("444");
    cy.findByTestId("studentStateId").type("555");
    cy.findByTestId("studentSearchButton").click();
    cy.findByTestId("student-search-rows")
      .children()
      .should("have.length", 2);
    cy.findByTestId("studentStateId").type("444");
    cy.findByTestId("studentSearchButton").click();
    cy.findByTestId("student-search-rows")
      .should("be.visible")
      .children()
      .should("have.length", 1);

    cy.findByText("Back to School").click();
    cy.findByText("Add Class & Teacher").should("be.visible");
    cy.findByText("Back to All Schools").click();
    cy.findByTestId(SCHOOL_ITEMS.blocked).should("be.visible");
  });

  it("can move, archive and unarchive, add, edit and upload students, add and set teachers", () => {
    cy.loginAs({ role: "universalDataAdmin" });
    cy.findByTestId("Test/Demo Organization").should("be.visible");
    clickVisibleText("Hide Empty Organizations");
    clickVisibleText("Blocked Uploads");
    cy.findByTestId(SCHOOL_ITEMS.blocked).within(() => {
      clickVisibleText("Manage");
    });

    clickElementByTextWithinSideNav("Test Class (222111)");

    cy.findByTestId("manage-group-nav-route-items")
      .children()
      .should("have.length", 3);
    cy.findByTestId("manage-group-students").should("have.class", "active");

    // move
    cy.findByText("testStudentLast", { exact: false }).click();
    moveSelectedStudentsToGroup("Test Class #2 (222333)");
    cy.findByText("Test Class (222111)", { timeout: 1000 }).should("not.exist");

    // archive
    clickVisibleTestId("testStudentLast_checkbox");
    cy.findByText("Archive").click();
    cy.findByTestId("confirm-modal-btn")
      .should("have.text", "Yes, archive students")
      .click();
    cy.findByText("Students have been archived successfully").should("be.visible");

    // add
    clickVisibleTestId("addNewStudentButton");
    cy.findByPlaceholderText("Last name").type("testLast");
    cy.findByPlaceholderText("First name").type("testFirst");
    cy.findByPlaceholderText("YYYY-MM-DD").type("1990-01-15");
    cy.findByPlaceholderText("Local ID").type("777666");
    cy.findByPlaceholderText("State ID").type("888777");
    cy.findByText("Save").click();
    cy.contains("Student has been added successfully").should("be.visible");

    // upload
    // clickVisibleText("Upload");
    // dropFile("exampleUploadStudent2.csv");
    // cy.findAllByTestId("studentUploadRow").should("have.length", 1);
    // clickVisibleText("Finalize Upload");
    // cy.findByText("Sunflower", { exact: false }).should("be.visible");

    // edit
    clickVisibleTestId("editStudentsButton");
    cy.get("input[value='testLast'").type("Changed");
    cy.get("input[value='testFirst'").type("Changed");
    cy.get("input[value='1990-01-15'").type("1991-02-16");
    cy.get("input[value='testLastChanged'") // TODO use studentLastName_row data-testid(already present)
      .parent()
      .parent()
      .parent()
      .within(() => {
        cy.get("[data-testid^='saveStudent_']").click();
      });
    cy.findByTestId("cancelEditStudentsButton").click();
    cy.findByText("Student has been updated successfully").should("be.visible");
    cy.findByText("Student has been updated successfully").should("not.exist");

    // unarchive
    clickVisibleTestId("manage-school-unarchive");
    waitForDetachedDom();
    clickVisibleTestId("testStudentLast_checkbox");
    moveSelectedStudentsToGroup("Test Class #2 (222333)");
    cy.contains("Back to School").click();
    clickElementByTextWithinSideNav("Test Class #2 (222333)");
    cy.findByTestId("manage-group-students").click();
    cy.findByTestId("testStudentLast_row").should("be.visible");
    cy.findByTestId("manage-school-unarchive").click();
    cy.findByText("testStudentLast", { exact: true, timeout: 400 }).should("not.exist");
    cy.contains("Back to School").click();
    clickElementByTextWithinSideNav("Test Class #2 (222333)");

    // // manage class
    clickVisibleTestId("manage-group-manage-class");
    // eslint-disable-next-line cypress/unsafe-to-chain-command
    cy.get("input[value='Test Class #2']")
      .clear()
      .type("Test Class #1");
    cy.findByTestId("manage-group-details-grade").select("01");
    cy.findByText("Add new teacher").click();
    cy.findByPlaceholderText("Last name").type("testNewTeacherLast");
    cy.findByPlaceholderText("First name").type("testNewTeacherFirst");
    cy.findByPlaceholderText("Teacher ID").type("111333");
    cy.findByPlaceholderText("Email").type("<EMAIL>");
    cy.findByText("Add teacher").click();
    cy.findByText("Add new secondary teacher").click();
    reactSelectByText("[data-testid='select-secondary-teacher']", "testNewTeacherFirst testNewTeacherLast");
    cy.findAllByTestId("remove-secondary-teacher")
      .should("have.length", 1)
      .should("be.visible");
    cy.findByText("Save").click();
    cy.findAllByText("Test Class #1 (222333)")
      .should("be.visible")
      .should("have.length", 2);
  });

  it("can clear screening scores added by a teacher", () => {
    cy.loginAs({ email: "<EMAIL>" });
    clickVisibleTestId("beginScreening");
    // clickVisibleTestId("close-and-assign-screening-button");
    inputScreeningScores(true);
    cy.findByText("View Results").click();
    cy.findByText("Continue to results").click();
    cy.findByText("The results are in. Let's take a look...").should("be.visible");

    logout();
    cy.loginAs({ role: "universalDataAdmin" });
    clickVisibleText("Hide Empty Organizations");
    clickVisibleText("Blocked Uploads");
    cy.findByTestId(SCHOOL_ITEMS.blocked).within(() => {
      clickVisibleText("Manage");
    });
    // TODO(fmazur) - select correct group?
    clickVisibleTestId("manage-group-manage-scores");
    cy.findByTestId("screeningScoresEntered").should(
      "have.text",
      numberOfScreeningScoresEnteredByBenchmarkPeriod[benchmarkPeriodName]
    );
    clickVisibleText("Clear Scores");
    cy.findByText("Yes, clear screening scores").click();
    cy.findByText("Screening scores have been cleared successfully").should("be.visible");
    cy.findByTestId("screeningDate").should("have.text", "In progress");
    cy.findByTestId("screeningScoresEntered").should("have.text", "0");
    cy.findByTestId("screeningScoresSkipped").should("have.text", "0");
  });

  it("can be removed by a super admin", () => {
    cy.loginAs({ role: "superAdmin" });
    cy.findByText("Test/Demo Organization").should("be.visible");
    cy.get("#menu-nav-dropdown")
      .should("be.visible")
      .click();
    clickVisibleTestId("topNav_manage-users");
    clickVisibleTestId("manage_universalDataAdmin");
    cy.findAllByTestId("user-row").should("have.length", 2);
    cy.findByText("sourcewell-mn", { exact: false })
      .parent()
      .within(() => {
        cy.findByTestId("user-row-checkbox").click();
      });
    cy.findByText("Delete Selected Users").click();
    cy.findByTestId("confirm-modal-btn")
      .should("have.text", "Yes, delete selected users")
      .click();
    cy.findByText("Selected users removed successfully").should("be.visible");
    cy.findAllByTestId("user-row").should("have.length", 1);
  });

  it("is unable to log in after being removed", () => {
    cy.loginAs({ email: validEmail, shouldCheckLoginLoading: false });
    cy.findByText("Wrong user name or password.").should("be.visible");
  });
});

function editStudentScore(index, score) {
  // eslint-disable-next-line cypress/unsafe-to-chain-command
  cy.findAllByTestId("scoreDisplayRow")
    .eq(index)
    .scrollIntoView();
  // eslint-disable-next-line cypress/unsafe-to-chain-command
  cy.findAllByTestId("scoreDisplayRow")
    .eq(index)
    .clear()
    .type(score.toString());
  // eslint-disable-next-line cypress/unsafe-to-chain-command
  cy.get("[data-testid^='saveScore_']")
    .eq(index)
    .scrollIntoView()
    .click();
  // eslint-disable-next-line cypress/unsafe-to-chain-command
  cy.get("[data-testid^='saveScore_']")
    .eq(index)
    .should("be.visible");
  cy.findByText("Student score updated!").should("be.visible");
  cy.findByText("Student score updated!").should("not.be.visible");
}

function navigateToEditClasswideScores() {
  cy.findByText("Client List").should("be.visible");
  cy.findByText("Test/Demo Organization").click({ force: true });
  goToStudentListFor(TEST_GROUPS.grade8group1.name);
  clickVisibleText("Manage Scores");
  waitForLoadingToDisappear();
  // eslint-disable-next-line cypress/unsafe-to-chain-command
  cy.findByText("Edit Scores")
    .scrollIntoView()
    .should("be.visible")
    .click();
  clickVisibleText("Acknowledged");
}
