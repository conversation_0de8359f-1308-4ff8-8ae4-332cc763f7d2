import { TEST_GROUPS } from "../support/common/constants";
import { clickDashboardTab, clickElementByTextWithinSideNav, goToStudentListFor } from "../support/common/navigating";
import { moveSelectedStudentsToGroup } from "../support/common/utils";

const startingGroupName = TEST_GROUPS.grade1group1.name;
const nextGroupName = TEST_GROUPS.gradeKgroup2.name;
const movedStudentName = "Coleman";
const getStudentByName = studentName => cy.findByText(studentName, { exact: false });

function getAllStudentActivities() {
  return cy.get("ul.activity").find("li");
}

function getStudentActivityItem(itemText) {
  return cy.findByText(itemText, { exact: false });
}

/**
 * Groups used: gradeKgroup2, grade1group1, grade6group3, grade6group4
 * Modifies: Student roster
 * Can't be rerun without test database restart
 * Requires Student Groups with students to move
 * Requires All groups need to have screening completed
 * Requires gradeKgroup2 to have 2 students in individual intervention eligibility
 * Requires grade6group4 to have failed screening
 */
describe("Move student between groups:", () => {
  it("Data admin moves a student with assessment history to another grade in a different grade", () => {
    cy.loginAs({ role: "dataAdmin" });
    goToStudentListFor(startingGroupName);
    getStudentByName(movedStudentName).click();
    moveSelectedStudentsToGroup(nextGroupName);
    cy.findByText(nextGroupName).click();
    getStudentByName(movedStudentName).should("be.visible");
  });
  it("Teacher verifies if student's scores from previous group are still displayed in the student log", () => {
    cy.loginAs({ role: "teacher" });
    clickElementByTextWithinSideNav(nextGroupName);
    clickDashboardTab(nextGroupName, "studentsTab");
    getStudentByName(movedStudentName).click();
    cy.findByText("Student Activity Log").should("be.visible");
    getStudentActivityItem("moved from group").should("be.visible");
    getStudentActivityItem("was enrolled in class").should("be.visible");
    getAllStudentActivities().should("have.length", 2);
  });
});

describe("Intervention eligibility", () => {
  it("Data Admin moves an eligible student for individual intervention to another grade", () => {
    cy.loginAs({ role: "dataAdmin" });
    goToStudentListFor(TEST_GROUPS.gradeKgroup2.name);
    getStudentByName("Bowman").click();
    moveSelectedStudentsToGroup(TEST_GROUPS.grade1group1.name);
    clickElementByTextWithinSideNav(TEST_GROUPS.grade1group1.name);
    getStudentByName("Bowman").should("be.visible");
  });
  it("Teacher sees that moved student is not eligible for intervention anymore", () => {
    cy.loginAs({ role: "teacher" });
    clickElementByTextWithinSideNav(TEST_GROUPS.grade1group1.name);
    clickDashboardTab(TEST_GROUPS.grade1group1.name, "studentsTab");
    cy.findByTestId("interventionCard_Bowman, Maggie", { timeout: 1000 }).should("not.exist");
  });

  // NOTE(fmazur) - Intervention eligibility stays only when two groups have completed screening
  it("Data Admin moves an eligible student for individual intervention to another group in the same grade", () => {
    cy.loginAs({ role: "dataAdmin" });
    goToStudentListFor(TEST_GROUPS.grade6group4.name);
    getStudentByName("Doe").click();
    moveSelectedStudentsToGroup(TEST_GROUPS.grade6group3.name);
    clickElementByTextWithinSideNav(TEST_GROUPS.grade6group3.name);
    getStudentByName("Doe").should("be.visible");
  });
  it("Teacher sees that moved student is still eligible for intervention", () => {
    cy.loginAs({ role: "teacher" });
    clickElementByTextWithinSideNav(TEST_GROUPS.grade6group3.name);
    clickDashboardTab(TEST_GROUPS.grade6group3.name, "studentsTab");
    cy.findByTestId("interventionCard_Doe, John").should("be.visible");
  });
});
