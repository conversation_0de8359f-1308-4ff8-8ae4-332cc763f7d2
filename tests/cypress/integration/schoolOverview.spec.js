import { assertSchoolOverviewHeader, clickVisibleTestId } from "../support/common/utils";
import { CONTEXT_MENU_SITE_IDS } from "../support/common/constants";

/**
 * Groups used: -
 * Modifies: Organization rostering option
 * Can't be rerun without test database restart
 * Requires Organization with blocked file import
 */
describe("School Overview page", () => {
  describe("for Elementary School", () => {
    beforeEach(() => {
      cy.loginAs({ role: "coach" });
    });
    it("should render properly", () => {
      cy.findByTestId("classwideInterventionSection")
        .scrollIntoView()
        .should("be.visible")
        .within(() => {
          cy.findByTestId("classwideInterventionSectionHeader")
            .should("be.visible")
            .contains("Classwide Interventions");
        });
      cy.findByText("Screening Results (Percent of Students Meeting Target)")
        .scrollIntoView()
        .should("be.visible");
      cy.findByTestId("demographics-header").should("be.visible");
    });
  });

  describe("for High School", () => {
    beforeEach(() => {
      cy.loginAs({ role: "coach" });
      assertSchoolOverviewHeader();
      clickVisibleTestId("userContextMenu");
      clickVisibleTestId(CONTEXT_MENU_SITE_IDS.highSchool);
    });
    it("should render properly", () => {
      cy.findByTestId("classwideInterventionSectionHeader")
        .should("be.visible")
        .contains("Classwide Interventions");
      cy.findByTestId("demographics-header")
        .should("be.visible")
        .contains("40 Students");
    });
    after(() => {
      clickVisibleTestId("userContextMenu");
      clickVisibleTestId(CONTEXT_MENU_SITE_IDS.elementary);
    });
  });
});
