/* eslint-disable cypress/unsafe-to-chain-command */
import { goToStudentListFor } from "../support/common/navigating";
import { TEST_GROUPS } from "../support/common/constants";
import { clickVisibleTestId, clickVisibleText, moveSelectedStudentsToGroup } from "../support/common/utils";

const CURRENT_SCHOOL_YEAR = Cypress.env("CURRENT_SCHOOL_YEAR");

/**
 * Groups used: grade5group1, grade5group2, grade6group1
 * Modifies: Student roster
 * Can't be rerun without test database restart
 * Requires student groups with students
 */
describe("Data Admin", () => {
  beforeEach(() => {
    cy.loginAs({ role: "dataAdmin" });
  });
  const studentsGrade5group1 = [
    { name: "<PERSON>, <PERSON>" },
    { name: "<PERSON>, <PERSON>" },
    { name: "<PERSON>, <PERSON>" },
    { name: "<PERSON>, <PERSON>" },
    { name: "<PERSON>, <PERSON><PERSON>" }
  ];
  const studentsGrade5group1AfterMoving = [
    { name: "<PERSON>, <PERSON>" },
    { name: "<PERSON>, <PERSON>" },
    { name: "<PERSON>, <PERSON>" },
    { name: "<PERSON>, <PERSON><PERSON>" }
  ];
  const studentsGrade5group2 = [{ name: "<PERSON>, <PERSON>" }, { name: "<PERSON>, <PERSON>" }, { name: "Ward, <PERSON>" }];
  const editedStudentId = `u3je5Fcxio<PERSON>dBZe6K${CURRENT_SCHOOL_YEAR}`;

  it("Archive student", () => {
    goToStudentListFor(TEST_GROUPS.grade5group2.name);

    cy.findByText("Logan, Aiden").should("be.visible");
    clickCheckboxForStudentRowByLastName("Logan");
    archiveStudents();
    cy.contains("Students have been archived successfully").should("be.visible");

    clickVisibleTestId("manage-school-unarchive");
    cy.findByText("Logan, Aiden").should("be.visible");
  });

  it("Archive many students", () => {
    goToStudentListFor(TEST_GROUPS.grade5group2.name);

    selectAllStudents();
    clickCheckboxForStudentRowByLastName("Paul");

    archiveStudents();
    cy.contains("Students have been archived successfully").should("be.visible");
    const numberOfExpectedStudents = 1;
    // FIXME(fmazur) - does it need number as a string?
    cy.get(".stat").should("contain", numberOfExpectedStudents);
  });

  it("Move many students", () => {
    goToStudentListFor(TEST_GROUPS.grade5group1.name);

    selectAllStudents();
    clickCheckboxForStudentRowByLastName("Cunningham");

    areStudentsVisible({ students: studentsGrade5group1 });
    moveSelectedStudentsToGroup(TEST_GROUPS.grade5group2.name);
    const numberOfExpectedStudents = 1;
    cy.get(".stat").should("contain", `${numberOfExpectedStudents}`);

    cy.findByText(TEST_GROUPS.grade5group2.name).click();
    areStudentsVisible({ students: studentsGrade5group1AfterMoving });
  });

  describe("Add student:", () => {
    it("can insert a student when all required fields are provided", () => {
      goToStudentListFor(TEST_GROUPS.grade5group1.name);

      clickVisibleText("Add");

      cy.findByPlaceholderText("Last name")
        .should("be.visible")
        .type("Smith");
      cy.findByPlaceholderText("First name").type("John");
      cy.get(":nth-child(2) > .form-control").select("05");
      cy.findByTestId("newStudentDOB").type("2005-06-20");
      cy.findByPlaceholderText("Local ID").type("2341723");
      cy.findByPlaceholderText("State ID").type("2356502");

      clickVisibleText("Save");
      cy.contains("Student has been added successfully").should("be.visible");
    });

    it("sees errors when provided data does not match the required fields format", () => {
      goToStudentListFor(TEST_GROUPS.grade5group1.name);

      clickVisibleText("Add");

      cy.findByPlaceholderText("Last name").type("Wrong");
      cy.findByPlaceholderText("First name").type("John");
      cy.get(":nth-child(2) > .form-control").select("05");
      cy.findByPlaceholderText("Local ID").type("7.2-38364");
      cy.findByPlaceholderText("State ID").type("383-292[]");
      cy.findByTestId("newStudentDOB")
        .clear()
        .type("2005-12-31");

      clickVisibleText("Save");
      cy.contains("Invalid Student Local ID format - should only contain letters and digits").should("be.visible");
      cy.get(".s-alert-close").should("be.visible");
      cy.findByPlaceholderText("Local ID")
        .clear()
        .type("7238364");

      clickVisibleText("Save");
      cy.contains("Invalid Student State ID format - should only contain letters and digits").should("be.visible");
    });
  });

  it("Unarchive student", () => {
    goToStudentListFor(TEST_GROUPS.grade5group2.name);
    clickVisibleTestId("manage-school-unarchive");

    cy.findByText("Unarchive Students").should("be.visible");
    cy.findByText("Logan, Aiden").should("be.visible");
    clickCheckboxForStudentRowByLastName("Logan");

    moveSelectedStudentsToGroup(TEST_GROUPS.grade5group2.name);
    cy.contains("Back to All Schools").click();
    goToStudentListFor(TEST_GROUPS.grade5group2.name);
    cy.findByText("Logan, Aiden").should("be.visible");
  });

  it("Unarchive all selected students", () => {
    goToStudentListFor(TEST_GROUPS.grade5group2.name);
    clickVisibleText("Unarchive");

    cy.findByText("Unarchive Students").should("be.visible");
    areStudentsVisible({ students: studentsGrade5group2 });
    selectStudents(studentsGrade5group2);

    moveSelectedStudentsToGroup(TEST_GROUPS.grade5group2.name);
    cy.contains("Back to All Schools").click();
    goToStudentListFor(TEST_GROUPS.grade5group2.name);
    const numberOfExpectedStudents = 9;
    cy.get(".stat").should("contain", `${numberOfExpectedStudents}`);

    areStudentsVisible({ students: studentsGrade5group2 });
  });

  it("Edit student name and birthDate", () => {
    goToStudentListFor(TEST_GROUPS.grade6group1.name);
    cy.findByText("Herrera, Ruth").should("be.visible");

    clickVisibleText("Edit");
    cy.findByTestId(`saveStudent_${editedStudentId}`)
      .should("be.visible")
      .and("be.disabled");
    cy.findByTestId(`studentFirstName_${editedStudentId}`)
      .clear()
      .type("Tony");
    cy.findByTestId(`studentLastName_${editedStudentId}`)
      .clear()
      .type("Banana");
    cy.findByTestId(`studentBirthDate_${editedStudentId}`)
      .clear()
      .type("2001-01-01");

    cy.findByTestId(`saveStudent_${editedStudentId}`)
      .should("be.visible")
      .and("not.be.disabled")
      .click();
    cy.findByText("Student has been updated successfully").should("be.visible");
    cy.findByText("End Editing").click();

    cy.findByText("Banana, Tony").should("be.visible");
    cy.findByText("2001-01-01").should("be.visible");
  });

  it("Validates input fields when saving edited student", () => {
    goToStudentListFor(TEST_GROUPS.grade6group1.name);
    clickVisibleText("Edit");

    cy.findByTestId(`studentLastName_${editedStudentId}`)
      .should("be.visible")
      .clear();
    cy.findByTestId(`saveStudent_${editedStudentId}`).click();
    cy.findByText("Invalid last name").should("be.visible");
    cy.findByTestId(`studentLastName_${editedStudentId}`).type("Smith");

    cy.findByTestId(`studentFirstName_${editedStudentId}`).clear();
    cy.findByTestId(`saveStudent_${editedStudentId}`).click();
    cy.findByText("Invalid first name").should("be.visible");
    cy.findByTestId(`studentFirstName_${editedStudentId}`).type("Ann");

    cy.findByTestId(`studentBirthDate_${editedStudentId}`).clear();
    cy.findByTestId(`studentBirthDate_${editedStudentId}`)
      .invoke("removeAttr", "type")
      .type("abc")
      .trigger("change");
    cy.findByTestId(`saveStudent_${editedStudentId}`).click();
    cy.findByText("Invalid birth date").should("be.visible");
    cy.findByTestId(`studentBirthDate_${editedStudentId}`)
      .invoke("attr", "type", "date")
      .clear();
    cy.findByTestId(`studentBirthDate_${editedStudentId}`).type("2001-02-01");

    cy.findByTestId(`saveStudent_${editedStudentId}`).click();
    cy.findByText("End Editing").click();
    cy.findByText("Smith, Ann").should("be.visible");
    cy.findByText("2001-02-01").should("be.visible");
  });
});

function selectAllStudents() {
  clickVisibleTestId("select-all-students-checkbox");
}

function selectStudents(students) {
  students.forEach(student => {
    const [studentLastName] = student.name.split(",");
    clickCheckboxForStudentRowByLastName(studentLastName);
  });
}

function clickCheckboxForStudentRowByLastName(studentLastName) {
  clickVisibleTestId(`${studentLastName}_checkbox`);
}

function areStudentsVisible({ students }) {
  // eslint-disable-next-line no-restricted-syntax
  for (const student of students) {
    cy.findByText(student.name).should("be.visible");
  }
}

function archiveStudents() {
  cy.findByText("Archive")
    .should("not.be.disabled")
    .click();
  clickVisibleText("Yes, archive students");
}
