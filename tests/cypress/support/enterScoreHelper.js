/* eslint-disable cypress/unsafe-to-chain-command */
import {
  clickVisibleTestId,
  clickVisibleText,
  waitForDetachedDom,
  // waitForLoadingToDisappear,
  waitForNthScoreToBeSaved
} from "./common/utils";

export const inputAndSaveInterventionScore = value => {
  let actualValue = value;
  waitForDetachedDom();
  cy.findByTestId("enterSkillScore")
    .should("be.visible")
    .then(([$input]) => {
      actualValue =
        parseInt(value) > parseInt($input.dataset.assessmentScoreLimit) ? $input.dataset.assessmentScoreLimit : value;
      cy.findByTestId("enterSkillScore")
        .type(actualValue, { force: true })
        .should("have.value", actualValue);
      waitForNthScoreToBeSaved({ testId: "enterSkillScore" });
    });
  clickVisibleTestId("enterScoreBtn");
  waitForDetachedDom();
};

export const enterAndPreviewGoalSkillScore = value => {
  let actualValue = value;
  cy.findByTestId("enterGoalScore").then(([$input]) => {
    actualValue =
      parseInt(value) > parseInt($input.dataset.assessmentScoreLimit) ? $input.dataset.assessmentScoreLimit : value;
    cy.findByTestId("enterGoalScore")
      .type(`{selectall}${actualValue}`)
      .blur();
    waitForNthScoreToBeSaved({ testId: "enterGoalScore" });
    cy.findByTestId("enterGoalScore").should("have.value", actualValue);
    cy.findByTestId("saveScoreBtn")
      .scrollIntoView()
      .should("be.visible")
      .should("not.be.disabled");
  });
};

export const inputAndSaveGoalScore = value => {
  enterAndPreviewGoalSkillScore(value);
  clickVisibleTestId("saveScoreBtn");
  // waitForLoadingToDisappear(10000, false);
};

export const enterAndSaveInterventionAndGoalSkillScores = (skillValue, goalValue) => {
  cy.findByTestId("enterSkillScore")
    .scrollIntoView()
    .should("be.visible")
    .then(([$input]) => {
      cy.findByTestId("enterSkillScore")
        .type(
          parseInt(skillValue) > parseInt($input.dataset.assessmentScoreLimit)
            ? $input.dataset.assessmentScoreLimit
            : skillValue,
          { delay: 10 }
        )
        .blur();
    });
  waitForNthScoreToBeSaved({ testId: "enterSkillScore" });
  inputAndSaveGoalScore(goalValue, true);
};

export function inputScreeningScores(isPassing = true, numFailingTimesMeasures = 0, shouldFailFromStart = false) {
  cy.findAllByTestId("scoreInput").each((input, index, inputs) => {
    let shouldFail = index >= (isPassing ? inputs.length - numFailingTimesMeasures : 0);
    if (isPassing && shouldFailFromStart) {
      shouldFail = index < numFailingTimesMeasures;
    }
    cy.wrap(input)
      .then(([$input]) => {
        cy.wrap(input).type(shouldFail ? 0 : $input.dataset.assessmentScoreLimit, { delay: 10 });
      })
      .blur();
  });
  waitForNthScoreToBeSaved({ testId: "scoreInput" });
}

export function clearAllScores(inputDataTestId = "scoreInput") {
  cy.findAllByTestId(inputDataTestId).each(input => cy.wrap(input).clear());
  waitForNthScoreToBeSaved();
}

export function inputAndCalculateClasswideScores({ isPassing, score, numberOfStudentsInClass = 5 }) {
  cy.findAllByTestId("scoreInput")
    .should("have.length", numberOfStudentsInClass)
    .each(input =>
      cy
        .wrap(input)
        .then(([$input]) => {
          const scoreToType = score || (isPassing ? $input.dataset.assessmentScoreLimit : 0);
          cy.wrap(input)
            .type(scoreToType, { delay: 10 })
            .should("have.value", scoreToType);
        })
        .blur()
    );
  cy.findByTestId("placementNotification")
    .scrollIntoView()
    .should("be.visible");
  cy.findAllByTestId("scoreInput")
    .last()
    .scrollIntoView();
  waitForNthScoreToBeSaved();
}

export function saveClasswideScores() {
  cy.findByTestId("saveScoreBtn")
    .scrollIntoView()
    .should("be.visible")
    .click();
  cy.findByText("Enter All Scores to Continue").should("be.visible");
  verifyStudentScoreInputs();
}

export function saveScreeningScores() {
  clickVisibleText("View Results");
  clickVisibleText("Continue to results");
  cy.findByText("The results are in. Let's take a look...").should("be.visible");
}

function assertStudentRowStatus() {
  waitForDetachedDom();
  cy.get(".student-name")
    .first()
    .scrollIntoView()
    .should("be.visible")
    .children()
    .first()
    .should("have.class", "text-default");
  cy.get(".student-name")
    .last()
    .scrollIntoView()
    .should("be.visible")
    .children()
    .first()
    .should("have.class", "text-default");
}

export function verifyStudentScoreInputs() {
  assertStudentRowStatus();
  cy.findAllByTestId("sort-by")
    .scrollIntoView()
    .should("be.visible");
  cy.get('input[placeholder*="Enter Score"]').should("be.visible");
  cy.findAllByTestId("scoreInput").each(input => {
    cy.wrap(input).should("have.attr", "placeholder", "Enter Score");
    cy.wrap(input).should("have.value", "");
  });
}
