// ***********************************************************
// This example support/index.js is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import "./commands";

// Alternatively you can use CommonJS syntax:
// require('./commands')
require("cypress-terminal-report/src/installLogsCollector")();
require("cypress-plugin-xhr-toggle");

cy.on("uncaught:exception", (e, runnable) => {
  console.log("error", e);
  console.log("runnable", runnable);
  return false;
});
