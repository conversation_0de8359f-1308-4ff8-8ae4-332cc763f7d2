import {
  clickVisibleTestId,
  clickVisibleText,
  expandAllInterventions,
  waitForDetachedDom,
  waitForLoadingToDisappear
} from "./utils";
import { SCHOOL_ITEMS } from "./constants";

export const clickElementByTextWithinSideNav = queryText => {
  waitForDetachedDom(300);
  cy.get(".side-nav")
    .should("be.visible")
    .within(() => {
      // eslint-disable-next-line cypress/unsafe-to-chain-command
      cy.findByText(queryText)
        .scrollIntoView()
        .should("be.visible")
        .click();
    });
};

export const goToGroupIndividualIntervention = groupName => {
  clickElementByTextWithinSideNav(groupName);
  waitForLoadingToDisappear(10000, false);
  clickVisibleTestId("individualInterventionTab");
  waitForDetachedDom(500);
  expandAllInterventions();
};

export const checkElementByTextWithinSideNav = queryText => {
  waitForDetachedDom(500);
  cy.get(".side-nav")
    .first()
    .should("be.visible")
    .within(() => {
      // eslint-disable-next-line cypress/unsafe-to-chain-command
      cy.findByText(queryText)
        .scrollIntoView()
        .should("be.visible");
    });
};

export function goToGroupClasswideIntervention(groupName) {
  clickElementByTextWithinSideNav(groupName);
  clickDashboardTab(groupName, "classwideInterventionTab");
}

export function goToStudentListFor(groupName) {
  cy.findByTestId(SCHOOL_ITEMS.elementary)
    .should("be.visible")
    .within(() => {
      clickVisibleText("Manage");
    });
  clickElementByTextWithinSideNav(groupName);
}

export function changeActiveSite(siteTestId) {
  clickVisibleTestId("userContextMenu");
  clickVisibleTestId(siteTestId);
}

export function changeSchoolYear(schoolYearId) {
  clickVisibleTestId("userContextMenu");
  clickVisibleTestId(schoolYearId);
}

export function goToScreeningForGivenPeriod(groupName, periodName = "") {
  clickElementByTextWithinSideNav(groupName);
  clickDashboardTab(groupName, "screeningTab");

  cy.findByText("Results Summary", { exact: false }).should("be.visible");

  const buttonText = `View ${periodName}`;
  // eslint-disable-next-line cypress/unsafe-to-chain-command
  cy.findByText(buttonText, { exact: false })
    .scrollIntoView()
    .should("be.visible")
    .click();
}

export function goToSite(siteItemTestId) {
  cy.findByTestId(siteItemTestId)
    .should("be.visible")
    .within(() => {
      clickVisibleText("Manage");
    });
}

export function clickDashboardTab(groupHeaderText, tabTestId) {
  cy.findByTestId(`student-group_${groupHeaderText}`).should("be.visible");
  cy.get("#dashboard-nav-items .active")
    .should("be.visible")
    .then(([$el]) => {
      if ($el.dataset?.testid === "screeningTab") {
        cy.findByTestId("screeningContent").should("exist");
      } else if ($el.dataset?.testid === "studentsTab") {
        cy.findByText("Roster").should("be.visible");
      } else if ($el.dataset?.testid === "individualInterventionTab") {
        cy.get(".scrollableContainer")
          .contains(/Individual Interventions|Drill-Down Assessments|Great work|Congratulations/g)
          .should("be.visible");
      }
    });
  clickVisibleTestId(tabTestId);
  cy.findByTestId(tabTestId)
    .should("be.visible")
    .should("have.class", "active");
}

export function goToOrganizationById(orgid) {
  cy.findByTestId(`${orgid}_row`).within(() => {
    cy.findByTestId("orgLink").click();
  });
}
