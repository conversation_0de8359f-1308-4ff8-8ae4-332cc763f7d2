import { SCHOOL_OVERVIEW_TITLE } from "/imports/api/constants";

import { BENCHMARK_PERIODS } from "./constants";

// NOTE(fmazur) - Cypress@8.5.0 doesn't support automatic re-querying elements
// NOTE(fmazur) - https://github.com/cypress-io/cypress/issues/7306#issuecomment-906486106
export const waitForDetachedDom = customDelay => {
  // eslint-disable-next-line cypress/no-unnecessary-waiting
  cy.wait(customDelay || 200); // NOTE(fmazur) - 200ms is enough for rerender to settle
};

// Coach or UniversalCoach
export function startScreeningFor(groupName) {
  waitForDetachedDom();
  cy.get(".side-nav")
    .should("be.visible")
    .within(() => {
      clickVisibleText(groupName);
    });
  cy.findByTestId(`student-group_${groupName}`).should("be.visible");
  clickVisibleText("Begin Screening");
  // cy.findByTestId(`screening-instructional-video-modal`).should("be.visible");
  // clickVisibleTestId("close-and-assign-screening-button");
}

export function continueScreeningFor(groupName) {
  waitForDetachedDom();
  cy.get(".side-nav")
    .first()
    .should("be.visible")
    .within(() => {
      cy.findByText(groupName)
        .should("be.visible")
        .click();
    });
  cy.findByTestId(`student-group_${groupName}`).should("be.visible");
  cy.findByTestId("continue-button").click({ force: true });
}

export function logout(shouldWaitForAlertToDisappear = false) {
  waitForDetachedDom(300);
  cy.findByTestId("siteSelectorId").should("be.visible");
  clickVisibleTestId("userContextMenu");
  // eslint-disable-next-line cypress/no-unnecessary-waiting
  cy.wait(shouldWaitForAlertToDisappear ? 2000 : 1000);
  clickVisibleTestId("logout");
  cy.findByText("Logging out...").should("be.visible");
  cy.findByTestId("login-forgot-password-link").should("be.visible");
  cy.clearLocalStorage();
  waitForDetachedDom(500);
}

export function assertSchoolOverviewHeader() {
  // eslint-disable-next-line cypress/unsafe-to-chain-command
  cy.get(".overviewContainer")
    .first()
    .should("be.visible")
    .within(() => {
      cy.findByText(SCHOOL_OVERVIEW_TITLE).should("be.visible");
    })
    .within(() => {
      cy.findByTestId("classwideInterventionSection").should("be.visible");
    });
}

export function getBenchmarkPeriodNameByDate(date = new Date()) {
  const month = date.getMonth();
  const day = date.getDate();

  const benchmarkPeriod = BENCHMARK_PERIODS.find(bmp => {
    const bmpStartMonth = bmp.startDate.month - 1;
    const bmpStartDay = bmp.startDate.day;
    const bmpEndMonth = bmp.endDate.month - 1;
    const bmpEndDay = bmp.endDate.day;

    const offsetMonth = (((month - bmpStartMonth) % 12) + 12) % 12;
    const offsetEndMonth = (((bmpEndMonth - bmpStartMonth) % 12) + 12) % 12;

    if (offsetMonth === 0) {
      return day >= bmpStartDay;
    }
    if (offsetMonth > 0 && offsetMonth < offsetEndMonth) {
      return true;
    }
    if (offsetMonth === offsetEndMonth) {
      return day <= bmpEndDay;
    }
    return false;
  });

  return benchmarkPeriod.name;
}

export function setCustomDate(date) {
  if (!date) {
    cy.reload();
  }
  waitForDetachedDom(1000);
  cy.findByTestId("loading-text", { timeout: 10000 }).should("not.exist");
  cy.findByTestId("userContextMenu").should("be.visible");
  cy.findByTestId("userContextMenu").click();
  if (date) {
    cy.findByTestId("customUserDate")
      .should("be.visible")
      .type(date);
  } else {
    cy.findByTestId("customUserDate")
      .should("be.visible")
      .clear();
  }
  cy.findByTestId("setCustomUserDate").click();
  waitForDetachedDom(2000);
  cy.findByTestId("userContextMenu").should("be.visible");
}

export function waitForLoadingToDisappear(timeout = 10000, shouldAssertLoading = true) {
  if (shouldAssertLoading) {
    cy.get('[data-testid="loading-icon"]').should("be.visible");
  }
  cy.get('[data-testid="loading-icon"]', { timeout }).should("not.exist");
  waitForDetachedDom(500);
}

export function selectRosteringOption(selectTestId, option, shouldVerifyAlert = true) {
  cy.findByTestId(selectTestId).within(() => {
    cy.get(".react-select__control").click();
    cy.get(".react-select__menu")
      .first()
      .within(() => {
        cy.findByText(option).click();
      });
  });
  if (shouldVerifyAlert) {
    cy.findByText("Successfully updated Rostering").should("be.visible");
  }
}

export function reactSelectByText(selector, optionText) {
  cy.get(selector)
    .first()
    .within(() => {
      cy.get(".react-select__control").click();
      cy.get(".react-select__menu").within(() => {
        cy.findByText(optionText, { exact: false }).click();
      });
    });
}

export const getSchoolYearLabel = (schoolYear = Cypress.env("CURRENT_SCHOOL_YEAR")) => {
  return `${schoolYear - 1}-${schoolYear % 100}`;
};

export const expandInterventionForStudent = studentName => {
  waitForDetachedDom(500); // NOTE(fmazur) - wait for context to settle after previous score save
  cy.findByText(studentName)
    .should("be.visible")
    .parent()
    .parent()
    .parent()
    .children()
    .first()
    .should("be.visible")
    .click({ force: true });
};

export const expandAllInterventions = () => {
  cy.findAllByTestId("individual_grouped_section").each(el => {
    cy.wrap(el).click();
  });
};

export const waitForNthScoreToBeSaved = ({
  scoreNumber,
  testId = "scoreInput",
  shouldWaitForSaveToDisappear = true
} = {}) => {
  // eslint-disable-next-line cypress/no-assigning-return-values
  const yieldedElements = cy.findAllByTestId(testId);
  if (scoreNumber || scoreNumber === 0) {
    yieldedElements
      .eq(scoreNumber)
      .next()
      .should("be.visible")
      .should("have.text", "Saved");
    if (shouldWaitForSaveToDisappear) {
      yieldedElements
        .last()
        .parent()
        .parent()
        .within(() => {
          cy.findByText("Saved").should("not.be.visible");
        });
    }
  } else {
    yieldedElements
      .last()
      .next()
      .should("be.visible")
      .should("have.text", "Saved");

    if (shouldWaitForSaveToDisappear) {
      yieldedElements
        .last()
        .parent()
        .parent()
        .within(() => {
          cy.findByText("Saved").should("not.be.visible");
        });
    }
  }
};

export const addCoachToSchool = siteId => {
  cy.findByTestId(`addCoach_${siteId}`).click();
  // eslint-disable-next-line cypress/unsafe-to-chain-command
  cy.get(`input[value="${siteId}"]`)
    .scrollIntoView()
    .should("be.checked");
};

export function typeByTestId(testId, text) {
  // eslint-disable-next-line cypress/unsafe-to-chain-command
  cy.findByTestId(testId)
    .should("be.visible")
    .type(text)
    .should("have.value", text);
}

export function clickVisibleText(text) {
  // eslint-disable-next-line cypress/unsafe-to-chain-command
  cy.findByText(text)
    .should("be.visible")
    .should("not.be.disabled");
  cy.findByText(text).click();
}

export function clickVisibleTestId(testId) {
  cy.findByTestId(testId).should("be.visible");
  cy.findByTestId(testId).click();
}

export function dropFile(fileName) {
  cy.fixture(fileName, "utf-8").then(content => {
    cy.get("#dz1").should("not.be.disabled");
    cy.get("#dz1").upload(content, fileName);
  });
}

export function verifyInstructionalVideoPopup(/* videoType */) {
  // cy.findByTestId(`${videoType}-instructional-video-modal`).should("be.visible");
  // clickVisibleTestId(`close-and-assign-${videoType}-button`);
}

export function moveSelectedStudentsToGroup(groupName) {
  // eslint-disable-next-line cypress/unsafe-to-chain-command
  cy.findByTestId("moveStudentButton")
    .should("not.be.disabled")
    .click();
  cy.findByText("Select the Class/Group to move these students to.").should("be.visible");
  // eslint-disable-next-line cypress/unsafe-to-chain-command
  cy.findByTestId(groupName)
    .scrollIntoView()
    .should("be.visible")
    .click();
  cy.findByTestId("submitMoveStudents").click();
  cy.contains("Students have been moved successfully").should("be.visible");
}

export function checkCurrentClasswideSkill(skillName) {
  // eslint-disable-next-line cypress/unsafe-to-chain-command
  cy.get(".intervention-content")
    .first()
    .scrollIntoView()
    .within(() => {
      cy.findByText(skillName).should("be.visible");
    });
}

export function addSchool(schoolName, schoolNumber) {
  clickVisibleTestId("add-school-button");
  cy.findByTestId("school-name-input").type(schoolName);
  // eslint-disable-next-line cypress/unsafe-to-chain-command
  cy.findByTestId("school-number-input")
    .type(schoolNumber)
    .focus()
    .blur();
  clickVisibleTestId("submitNewSite");
  cy.findByTestId("schoolItem_FirstSchool").should("be.visible");
}

export function waitForAlertToDisappear(alertType = "success", timeout = 5000) {
  cy.get(`.s-alert-${alertType}`).should("be.visible");
  cy.get(`.s-alert-${alertType}`, { timeout }).should("not.exist");
}
