const { MAILTRAP_API_TOKEN } = require("./constants");

const mailtrapUrl = "https://mailtrap.io/api/v1/inboxes/3370078/messages";
const newUsersPassword = "A124B124C!d";

function clearMailbox() {
  it("clears mailbox", () => {
    const oneHour = 60 * 60;
    cy.request(`${mailtrapUrl}?api_token=${MAILTRAP_API_TOKEN}`).then(inboxResponse => {
      if (inboxResponse.isOkStatusCode && inboxResponse.body && inboxResponse.body.length > 0) {
        inboxResponse.body.forEach(message => {
          const messageWasSentMoreThanOneHourAgo = (message.sent_at_timestamp + oneHour) * 1000 < new Date().valueOf();
          if (messageWasSentMoreThanOneHourAgo) {
            cy.request("DELETE", `${mailtrapUrl}/${message.id}?api_token=${MAILTRAP_API_TOKEN}`);
          }
        });
      }
    });
  });
}

const generateRandomString = () =>
  Math.random()
    .toString(36)
    .substr(2, 12);

const generateNewUserEmail = prefix => `${prefix}_${generateRandomString()}@test.com`;

const randomizeCase = string =>
  string
    .split("")
    .map(letter => {
      const charCase = Math.round(Math.random()) === 0; // 0 or 1
      return charCase ? letter.toUpperCase() : letter.toLowerCase();
    })
    .join("");

function waitForInvitationEmail(newTeacherEmail, noOfRetries = 0) {
  /* eslint-disable cypress/no-unnecessary-waiting */
  const waitPeriodBeforeNextRequest = 1000;
  const maxNoOfRetries = 60;
  if (noOfRetries >= maxNoOfRetries) {
    return false;
  }
  const enrollmentMailRegExp = /.*enroll-account.*/;
  return cy.request(`${mailtrapUrl}?api_token=${MAILTRAP_API_TOKEN}`).then(inboxResponse => {
    if (inboxResponse.isOkStatusCode && inboxResponse.body && inboxResponse.body.length > 0) {
      const foundMessage = inboxResponse.body.find(message => message.to_email === newTeacherEmail);
      const foundMessageId = foundMessage && foundMessage.id;
      if (foundMessageId) {
        cy.request(`${mailtrapUrl}/${foundMessageId}/body.txt?api_token=${MAILTRAP_API_TOKEN}`).then(
          invitationMailResponse => {
            if (
              inboxResponse.isOkStatusCode &&
              inboxResponse.body &&
              enrollmentMailRegExp.exec(invitationMailResponse.body)[0]
            ) {
              return true;
            }
            cy.wait(waitPeriodBeforeNextRequest);
            return waitForInvitationEmail(newTeacherEmail, noOfRetries + 1);
          }
        );
      } else {
        cy.wait(waitPeriodBeforeNextRequest);
        return waitForInvitationEmail(newTeacherEmail, noOfRetries + 1);
      }
    } else {
      cy.wait(waitPeriodBeforeNextRequest);
      return waitForInvitationEmail(newTeacherEmail, noOfRetries + 1);
    }
    return false;
  });
}

function acceptInvitationInEmail(newTeacherEmail, expectedMessage) {
  const enrollmentMailRegExp = /.*enroll-account.*/;
  cy.request(`${mailtrapUrl}?api_token=${MAILTRAP_API_TOKEN}`).then(inboxResponse => {
    if (!(inboxResponse.body && inboxResponse.body.length > 0)) {
      return false;
    }
    const foundMessage = inboxResponse.body.find(message => message.to_email === newTeacherEmail);
    const foundMessageId = foundMessage && foundMessage.id;
    if (!foundMessageId) {
      return false;
    }
    cy.request(`${mailtrapUrl}/${foundMessageId}/body.txt?api_token=${MAILTRAP_API_TOKEN}`).then(
      invitationMailResponse => {
        const match = enrollmentMailRegExp.exec(invitationMailResponse.body);
        cy.visit(match[0]);

        cy.get("#txtPassword").type(newUsersPassword);
        cy.get("#txtConfirmPassword").type(newUsersPassword);
        cy.findByText("Set Password").click();
        cy.findByText("Continue").click();

        cy.findByText(expectedMessage, { timeout: 30000 }).should("be.visible");
      }
    );
    return true;
  });
}

module.exports = {
  clearMailbox,
  generateNewUserEmail,
  waitForInvitationEmail,
  acceptInvitationInEmail,
  newUsersPassword,
  randomizeCase
};
