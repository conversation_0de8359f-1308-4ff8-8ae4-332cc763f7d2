import "@testing-library/cypress/add-commands";
import { configure } from "@testing-library/cypress";
import { inputCredentialsAndLogIn } from "./common/loginUtils";
import { DOMAIN_URL, EMAILS } from "./common/constants";

configure({
  // Note(fmazur) - can use second parameter to get page html
  getElementError: message => {
    return new Error(message);
  }
});

Cypress.Commands.add("loginAs", ({ role, email, shouldCheckLoginLoading = true, queryString = "" } = {}) => {
  cy.visit(`${DOMAIN_URL}/${queryString}`, {
    onBeforeLoad(win) {
      // eslint-disable-next-line no-param-reassign
      win.open = cy.stub().as("windowOpenStub");
    }
  });

  inputCredentialsAndLogIn(email || EMAILS[role], shouldCheckLoginLoading);
});

function isPseudoVisible(elem) {
  return !!(elem.offsetWidth || elem.offsetHeight || elem.getClientRects().length);
}

Cypress.Commands.add(
  "isPseudoVisible",
  {
    prevSubject: true
  },
  subject => {
    return cy.wrap(subject).then(insideElem => expect(isPseudoVisible(insideElem[0])).to.be.true);
  }
);

Cypress.Commands.add(
  "upload",
  {
    prevSubject: "element"
  },
  (subject, fileContent, fileName) => {
    cy.window().then(window => {
      const blob = new Blob([fileContent], { type: "text/csv" });
      const testFile = new window.File([blob], fileName);
      const options = { dataTransfer: { files: [testFile] } };

      cy.wrap(subject)
        .trigger("dragcenter", options)
        .trigger("drop", options);
    });
  }
);
