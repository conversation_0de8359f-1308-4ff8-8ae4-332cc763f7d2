const fs = require("fs");
const path = require("path");

// ***********************************************************
// This example plugins/index.js can be used to load plugins
//
// You can change the location of this file or turn off loading
// the plugins file with the 'pluginsFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/plugins-guide
// ***********************************************************

const deleteFiles = folderPath => {
  let deletedFiles = [];
  if (fs.existsSync(folderPath)) {
    fs.readdirSync(folderPath).forEach(file => {
      const currentPath = path.join(folderPath, file);
      if (fs.lstatSync(currentPath).isDirectory()) {
        // Recursively delete folder contents if necessary
        const subDeletedFiles = deleteFiles(currentPath);
        deletedFiles = deletedFiles.concat(subDeletedFiles);
        fs.rmdirSync(currentPath);
      } else {
        fs.unlinkSync(currentPath);
        deletedFiles.push(currentPath);
      }
    });
  }
  return deletedFiles;
};

module.exports = on => {
  on("task", {
    doesFileExist({ folderPath, fileName, partialMatch = false }) {
      if (partialMatch) {
        const files = fs.readdirSync(folderPath);
        return files.some(file => file.startsWith(fileName));
      }
      const fullPath = path?.join(folderPath, fileName);
      return fs.existsSync(fullPath);
    },
    deleteFilesInFolder({ folderPath }) {
      const deletedFiles = deleteFiles(folderPath);
      return deletedFiles;
    }
  });
};
