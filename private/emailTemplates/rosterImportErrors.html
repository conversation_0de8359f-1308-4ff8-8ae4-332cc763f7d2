<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html lang="en">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="initial-scale=1.0" />
    <!-- So that mobile webkit will display zoomed in -->
    <meta name="format-detection" content="telephone=no" />
    <!-- disable auto telephone linking in iOS -->

    <title>{emailSubject}</title>
    <style type="text/css">
      /* Resets: see reset.css for details */
      body {
        -webkit-text-size-adjust: none;
        -ms-text-size-adjust: none;
      }
      body {
        margin: 0;
        padding: 0;
      }
      table {
        border-spacing: 0;
      }
      table td {
        border-collapse: collapse;
      }

      /* Constrain email width for small screens */
      @media screen and (max-width: 600px) {
        table[class="container"] {
          width: 95% !important;
        }
      }

      /* Give content more room on mobile */
      @media screen and (max-width: 480px) {
        td[class="container-padding"] {
          padding-left: 15px !important;
          padding-right: 15px !important;
        }
      }
    </style>
  </head>
  <body
    style="margin:0; padding:10px 0;"
    leftmargin="0"
    topmargin="0"
    marginwidth="0"
    marginheight="0"
    bgcolor="#F8F7F3"
  >
    <br />

    <!-- 100% wrapper (grey background) -->
    <table border="0" width="100%" height="100%" cellpadding="0" cellspacing="0">
      <tr>
        <td align="center" valign="top">
          <!-- 600px container (white background) -->
          <table border="0" width="600" cellpadding="10" cellspacing="0" class="container">
            <tr>
              <td
                style="background-color:#005bb5; padding-top: 20px; padding-bottom: 20px; padding-left: 35px; padding-right: 35px; line-height: 20px; font-family: Helvetica, sans-serif;"
              >
                <h2 style="color: #ffffff; margin-top: 0; margin-bottom: 0; text-align: center;">SpringMath</h2>
              </td>
            </tr>
            <tr>
              <td
                class="container-padding"
                bgcolor="#FFFFFF"
                style="background-color:#FFFFFF; border: 1px solid #E8E8E8; padding-bottom: 20px; padding-left: 35px; padding-right: 35px; font-size: 14px; line-height: 20px; font-family: Helvetica, sans-serif; color: #333;"
              >
                <!-- ### BEGIN CONTENT ### -->
                <div
                  style="border-bottom: thin solid #DDDDDD; margin-bottom: 5px; padding-bottom: 20px; padding-top: 15px;"
                >
                  <table border="0" width="600" cellpadding="0" cellspacing="0" class="container">
                    <tr>
                      <td>
                        <span style="color: #555; font-size: 16px; font-family: Arial; line-height: 24px;">
                          <strong>Hello,</strong>
                        </span>
                        <br /><br />

                        <span style="color: #555; font-size: 16px; font-family: Arial; line-height: 24px;">
                          There were some errors with the latest {rosterImportType} roster import for {orgName}.
                        </span>
                        <br /><br />

                        <div
                          style="color: #d00; font-size: 12px; font-family: Arial; background: rgba(239, 83, 80, 0.2); border: 1px solid rgba(239, 83, 80, 0.5); padding: 5px"
                        >
                          {importErrors}
                        </div>
                        <br /><br />

                        <span style="color: #555; font-size: 16px; font-family: Arial; line-height: 24px;">
                          Thank you for using SpringMath.
                        </span>
                        <br /><br />

                        <span style="color: #555; font-size: 16px; font-family: Arial; line-height: 24px;">
                          -Team SpringMath
                        </span>
                        <br /><br />
                      </td>
                    </tr>
                  </table>
                </div>
                <!-- end note 1 -->

                <span style="color: #aaa; float: right; font-size: 12px; font-family: Arial; line-height: 20px;">
                  <em>
                    This was sent from
                    <a href="https://springmath.org" style="color: #333333; text-decoration: underline;">SpringMath</a>.
                  </em>
                </span>
              </td>
            </tr>
          </table>

          <!--/600px container -->
        </td>
      </tr>
    </table>

    <!--/100% wrapper-->
    <br />
    <br />
  </body>
</html>
