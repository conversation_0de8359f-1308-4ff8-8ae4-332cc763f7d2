import { Meteor } from "meteor/meteor";
import * as mongodb from "mongodb"; // For connection to external db for bootstrapping (e.g. DEV Databse)
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { StudentGroupEnrollments } from "/imports/api/studentGroupEnrollments/studentGroupEnrollments";
import { Organizations } from "/imports/api/organizations/organizations";
import { Sites } from "/imports/api/sites/sites";
import { Students } from "/imports/api/students/students";
import { Ethnicities } from "/imports/api/ethnicities/ethnicities";
import { Roles } from "/imports/api/roles/roles";
import { Grades } from "/imports/api/grades/grades";
import { BenchmarkPeriods } from "/imports/api/benchmarkPeriods/benchmarkPeriods";
import { BenchmarkWindows } from "/imports/api/benchmarkWindows/benchmarkWindows";
import { ScreeningAssignments } from "/imports/api/screeningAssignments/screeningAssignments";
import { Assessments } from "/imports/api/assessments/assessments";
import { AssessmentResults } from "/imports/api/assessmentResults/assessmentResults";
import { Settings } from "/imports/api/settings/settings";
import {
  AssessmentResultsRestorePoint,
  BenchmarkWindowsRestorePoint,
  StudentsRestorePoint,
  StudentGroupsRestorePoint
} from "/imports/api/restorePointCollections/restorePointCollections";
import { Interventions } from "/imports/api/interventions/interventions";
import { Rules } from "/imports/api/rules/rules";
import { Users } from "/imports/api/users/users";
import { ninjalog } from "/imports/api/utilities/utilities";
import cronManager from "/imports/api/cron/cronManager";

Meteor.startup(async () => {
  const BOOTSTRAP_DB_MIN = process.env.BOOTSTRAP_DB_MIN || false;
  const ENVIRONMENT = Meteor.settings.public.ENVIRONMENT || "DEV";
  const connectionUrl = Meteor.settings.BOOTSTRAP_FROM_DB;
  const attemptBootstrap = ENVIRONMENT !== "PROD" && BOOTSTRAP_DB_MIN;
  if (attemptBootstrap) {
    ninjalog.trace({
      msg: "Checking if bootstrap is needed...",
      context: "bootstrap"
    });
    // Build up an array of collections that need bootstrapping (don't have any documents in them)
    let collectionUniverse = [
      Ethnicities,
      Grades,
      Roles,
      BenchmarkPeriods,
      Rules,
      Interventions,
      ScreeningAssignments,
      Assessments,
      Users,
      Organizations,
      Settings,
      Sites,
      Students,
      StudentsRestorePoint,
      StudentGroups,
      StudentGroupsRestorePoint,
      StudentGroupEnrollments,
      AssessmentResults,
      AssessmentResultsRestorePoint,
      BenchmarkWindows,
      BenchmarkWindowsRestorePoint
    ];
    const numberOfCollections = collectionUniverse.length;
    // Check if collections are empty using async findOneAsync
    const collectionChecks = await Promise.all(
      collectionUniverse.map(async coll => {
        const hasData = await coll.findOneAsync();
        return { coll, isEmpty: !hasData };
      })
    );
    collectionUniverse = collectionChecks.filter(({ isEmpty }) => isEmpty).map(({ coll }) => coll);
    // Bootstrap Data from external db (e.g. DEV), but only if all collections are empty (e.g. after meteor reset)
    if (collectionUniverse.length === numberOfCollections) {
      mongodb.MongoClient.connect(connectionUrl, (err, client) => {
        const devDb = client.db("meteor");
        if (err || !devDb) {
          ninjalog.trace({
            msg: "Could not connect to db for bootstrapping data...",
            context: "bootstrap",
            val: err
          });
        } else {
          let connectedToDb = true;
          collectionUniverse.forEach(collection => {
            if (BOOTSTRAP_DB_MIN) {
              ninjalog.trace({
                msg: "Bootstrapping collection from external DB: ",
                // eslint-disable-next-line no-underscore-dangle
                val: collection._name,
                context: "bootstrap"
              }); // eslint-disable-line no-underscore-dangle
              devDb
                // eslint-disable-next-line no-underscore-dangle
                .collection(collection._name)
                .find()
                .toArray() // eslint-disable-line no-underscore-dangle
                .then(docs => {
                  docs.forEach(doc => {
                    collection.insert(doc);
                  });
                  collectionUniverse = collectionUniverse.filter(coll => coll !== collection);
                });
            }
          });
          // When all bootstrapping is done close connection and save as a restore point
          if (connectedToDb) {
            const promisesFulfilled = setInterval(() => {
              if (collectionUniverse.length === 0) {
                ninjalog.trace({
                  msg: "Closing connection to external DB after bootstrapping...",
                  context: "bootstrap"
                });
                devDb.close();
                connectedToDb = false;
                clearInterval(promisesFulfilled);
              }
            }, 5000);
          }
        }
      });
    } else {
      ninjalog.trace({
        msg:
          "Bootstrap not performed because some of your collections have data. " +
          "Either clear your collections or type Meteor Reset in order to perform a bootstrap."
      });
    }
  }
  cronManager.initJobs();
});
