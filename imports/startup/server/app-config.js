import { Meteor } from "meteor/meteor";
import { Accounts } from "meteor/accounts-base";

import * as utils from "/imports/api/utilities/utilities";

const urlParser = require("url").parse;

function getSetupPasswordUrl(url, user, link = "/onboarding/welcome/") {
  const { protocol, host } = urlParser(url);
  const token = user.services.password.reset?.token || user.services.password.enroll?.token;
  return `${protocol}//${host}${link}${token}`;
}

export function setAppConfig() {
  if (Meteor.settings.MAIL_URL) {
    process.env.MAIL_URL = Meteor.settings.MAIL_URL;
  } else {
    throw Error("You have not set MAIL_URL in settings.json. The format is: smtp://USER:PASS@SMTP_SERVER:PORT");
  }

  Accounts.config({
    passwordEnrollTokenExpirationInDays: 3,
    passwordResetTokenExpirationInDays: 3,
    forbidClientAccountCreation: true
  });

  Accounts.emailTemplates.siteName = "SpringMath";
  Accounts.emailTemplates.from = "<EMAIL>";
  Accounts.emailTemplates.enrollAccount.subject = () => "Invitation for SpringMath!";
  Accounts.emailTemplates.enrollAccount.html = (user, url) => {
    const updatedUrl = getSetupPasswordUrl(url, user);
    const replaceMap = {
      "{userName}": `${user.profile.name.first} ${user.profile.name.last}`,
      "{url}": updatedUrl
    };
    return utils.mapReplaceAll(Assets.getText("emailTemplates/enrollmentInvitation.html"), replaceMap);
  };

  Accounts.emailTemplates.resetPassword.subject = () => "Reset Password for your SpringMath account";
  Accounts.emailTemplates.resetPassword.html = function(user, url) {
    const updatedUrl = getSetupPasswordUrl(url, user, "/resetPassword/");
    const replaceMap = {
      "{userName}": `${user.profile.name.first} ${user.profile.name.last}`,
      "{url}": updatedUrl
    };
    return utils.mapReplaceAll(Assets.getText("emailTemplates/resetPassword.html"), replaceMap);
  };
  if (Accounts.urls) {
    Accounts.urls.resetPassword = (token, extraParams) => Accounts.buildEmailUrl(`resetPassword/${token}`, extraParams);
    Accounts.urls.enrollAccount = (token, extraParams) => Accounts.buildEmailUrl(`verifyEmail/${token}`, extraParams);
    Accounts.urls.verifyEmail = (token, extraParams) => Accounts.buildEmailUrl(`enrollAccount/${token}`, extraParams);
  }
}

setAppConfig();

Meteor.users.deny({
  insert() {
    return true;
  },
  update() {
    return true;
  },
  remove() {
    return true;
  }
});

export default { setAppConfig }; // for testing for now
