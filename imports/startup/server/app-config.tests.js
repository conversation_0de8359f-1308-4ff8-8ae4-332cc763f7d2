import { Meteor } from "meteor/meteor";
import { assert } from "chai";
import sinon from "sinon";
import { Accounts } from "meteor/accounts-base";
import stubUtils from "../../test-helpers/methods/stubUtils.js";

const originalMeteorValues = { ...Meteor.settings };
Meteor.settings.MAIL_URL = "testValue";
const appConfig = require("./app-config.js");

describe("imports/startup/server/app-config.js tests", () => {
  let accountsConfigSpy;
  beforeEach(() => {
    accountsConfigSpy = sinon.spy(Accounts, "config");
  });
  afterEach(() => {
    stubUtils.safeRestore(Accounts.config);
    Meteor.settings = originalMeteorValues;
  });
  it("should attempt to set the Accounts package configuration", () => {
    Meteor.settings.MAIL_URL = "testValue";
    appConfig.setAppConfig();
    assert.isTrue(accountsConfigSpy.calledOnce);
  });
  it("should attempt to set the Accounts package configuration to expire enrollments/password resets after some number of days", () => {
    Meteor.settings.MAIL_URL = "testValue";
    appConfig.setAppConfig();
    assert.isTrue(
      accountsConfigSpy.calledWith({
        passwordResetTokenExpirationInDays: 3,
        passwordEnrollTokenExpirationInDays: 3,
        forbidClientAccountCreation: true
      })
    );
  });
});

Meteor.settings = originalMeteorValues;
