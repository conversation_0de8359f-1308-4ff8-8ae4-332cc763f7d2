import "./app-config"; // !!! must be first <--- important!

/* ============================================================================ */
/* Keeping ths around for when we build up a bootstrapper that clones           */
/*  non-sensitive production collections instead of manually bootstrapping them */
/* ---------------------------------------------------------------------------- */
// import '../../api/dbProxy/collections';
/* ============================================================================ */

import "/imports/api/assessments/assessments";

import "/imports/api/assessmentResults/methods";
import "/imports/api/assessmentResults/server/publications";
import "/imports/api/assessments/methods";
import "/imports/api/assessments/server/publications";
import "/imports/api/assessments/server/methods";
import "/imports/api/studentsBySkill/methods";
import "/imports/api/authentication/inactivity/server/methods";
import "/imports/api/authentication/zendesk/server/methods";
import "/imports/api/authorization/server/methods";
import "/imports/api/benchmarkPeriods/methods";
import "/imports/api/benchmarkPeriods/server/publications";
import "/imports/api/benchmarkPeriods/server/methods";
import "/imports/api/benchmarkWindows/methods";
import "/imports/api/benchmarkWindows/server/publications";
import "/imports/api/districtReporting/server/methods";
import "/imports/api/ethnicities/methods";
import "/imports/api/ethnicities/server/publications";
import "/imports/api/rosterImportItems/methods";
import "/imports/api/rosterImports/methods";
import "/imports/api/rosterImports/server/publications";
import "/imports/api/assessmentScoresUpload/methods";
import "/imports/api/assessmentScoresUpload/publications";
import "/imports/api/grades/methods";
import "/imports/api/grades/server/publications";
import "/imports/api/groupedAssessments/server/methods";
import "/imports/api/instructionalVideos/server/methods";
import "/imports/api/interventions/methods";
import "/imports/api/interventions/server/publications";
import "/imports/api/interventions/server/methods";
import "/imports/api/news/server/methods";
import "/imports/api/organizations/server/publications";
import "/imports/api/printMaterials/methods";
import "/imports/api/roles/methods";
import "/imports/api/roles/server/publications";
import "/imports/api/rules/server/methods";
import "/imports/api/rules/server/publications";
import "/imports/api/screeningAssignments/methods";
import "/imports/api/screeningAssignments/server/publications";
import "/imports/api/settings/settings";
import "/imports/api/settings/server/methods";
import "/imports/api/sites/methods";
import "/imports/api/sites/server/publications";
import "/imports/api/ssoAzureAdB2c/server/azureAdB2cServer";
import "/imports/api/ssoAzureAdB2c/server/accountsAzureAdB2cServer";
import "/imports/api/studentGroupEnrollments/methods";
import "/imports/api/studentGroupEnrollments/server/publications";
import "/imports/api/studentGroups/methods";
import "/imports/api/studentGroups/server/publications";
import "/imports/api/students/methods";
import "/imports/api/students/server/methods";
import "/imports/api/students/server/publications";
import "/imports/api/users/server/methods";
import "/imports/api/users/server/publications";
import "/imports/api/assessmentGrowth/server/publications";
import "/imports/api/assessmentGrowth/methods";
import "/imports/api/utilities/methods";
import "/imports/api/utilities/server/individualRuleProcessing";
import "/imports/api/utilities/server/interventionStats";
import "/imports/api/utilities/utilities";
import "/imports/api/methods";
import "/imports/api/cron/methods";
import "/imports/api/auditLogs/auditLogs";
import "/imports/api/auditLogs/server/methods";
import "/imports/api/organizations/server/methods";
import "../../scripts/schoolScrubber/server/methods";

import "./fixtures";
import "../../scripts/autorun/methods";
import "../../scripts/autorun/createIndexes";
