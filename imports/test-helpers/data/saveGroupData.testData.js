import { generateRandomId } from "./rosterImportItemsValidator.testData";
import { flatten } from "lodash";

export function getUserInContext({ role, schoolYear, ...topProfileParams }) {
  return function({
    _id = generateRandomId(),
    siteIds = [],
    schoolYearsToUse = [schoolYear],
    isActive = true,
    address,
    ...customProfileParams
  }) {
    const siteAccess = flatten(
      siteIds.map(siteId =>
        schoolYearsToUse.map(year => ({
          siteId,
          schoolYear: year,
          role,
          isActive
        }))
      )
    );
    return {
      _id,
      emails: [{ address: address || generateRandomId() }],
      profile: {
        siteAccess,
        ...topProfileParams,
        ...customProfileParams
      }
    };
  };
}

export function getTestGroupInContext({ ...params }) {
  return function({ _id = generateRandomId(), ownerIds = ["someTeacherId"], ...rest }) {
    return {
      _id,
      isActive: true,
      ownerIds,
      ...params,
      ...rest
    };
  };
}
