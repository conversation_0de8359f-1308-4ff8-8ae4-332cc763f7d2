import { getTimestampInfo } from "/imports/api/helpers/getTimestampInfo";
import { initializeEmptyRosterStats } from "/imports/api/utilities/utilities";

export function rosterImport() {
  return {
    orgid: "test_organization_id",
    status: "uploading",
    itemCount: 0,
    error: {},
    started: {
      by: "TEST",
      on: Date.now(),
      date: new Date()
    },
    source: "CSV",
    ...initializeEmptyRosterStats()
  };
}

export async function getStudent({
  orgid = "test_orgid",
  byDateOn,
  rosterImportId = "someRosterImportsId",
  schoolYear = 2018,
  firstName = "Student",
  lastName = "Fortest",
  hasIndividualIntervention = false,
  assessmentResultId = "someAssessmentResultId"
} = {}) {
  byDateOn ??= await getTimestampInfo("spec");
  return {
    orgid,
    grade: "K",
    schoolYear,
    districtNumber: "0000",
    created: byDateOn,
    lastModified: byDateOn,
    comments: ["Comment1", "Comment2"],
    demographic: {
      birthDate: "",
      birthDateTimeStamp: null
    },
    identity: {
      name: {
        firstName,
        lastName
      },
      identification: {
        localId: "7078576",
        stateId: "6495769"
      }
    },
    rosterImportId,
    status: "",
    history: null,
    currentSkill: hasIndividualIntervention
      ? {
          benchmarkAssessmentId: "WAv6GQ7DzR44ouxrg",
          benchmarkAssessmentName: "Count Objects 1-10, Circle Answer",
          benchmarkAssessmentTargets: [4, 8, 225],
          assessmentId: "hOV15Gn17XpXrl2Pp",
          assessmentName: "Count Objects Aloud 1-20",
          assessmentTargets: [8, 13, 300],
          interventions: [],
          assessmentResultId,
          whenStarted: {
            by: "",
            on: 1511973722617.0,
            date: new Date("2017-11-29T16:42:02.617+0000")
          },
          benchmarkPeriodId: "8S52Gz5o85hRkECgq",
          message: {
            messageCode: "51",
            dismissed: false
          }
        }
      : {}
  };
}

export async function getStudentGroup({
  orgid = "test_orgid",
  byDateOn,
  rosterImportId = "someRosterImportsId",
  schoolYear = 2018,
  name = "StudentGroup Name",
  hasClasswideIntervention = false,
  siteId = "test_elementary_site_id"
} = {}) {
  byDateOn ??= await getTimestampInfo("test");
  return {
    orgid,
    siteId,
    grade: "03",
    created: byDateOn,
    lastModified: byDateOn,
    type: "CLASS",
    ownerIds: ["test_teacherId"],
    isActive: true,
    sectionId: "test_sectionId",
    name,
    schoolYear,
    rosterImportId,
    history: [],
    currentAssessmentResultIds: ["oEsjkDjNaymjQvH9z"],
    currentClasswideSkill: hasClasswideIntervention
      ? {
          assessmentId: "WAv6GQ7DzR44ouxrg",
          assessmentName: "Count Objects 1-10, Circle Answer",
          interventions: [],
          targets: [4, 8, 225],
          whenStarted: {
            by: "",
            on: 1511973599815.0,
            date: new Date("2017-11-29T16:39:59.815+0000")
          },
          assessmentResultId: "oEsjkDjNaymjQvH9z",
          benchmarkPeriodId: "8S52Gz5o85hRkECgq",
          message: {
            additionalStudentsAddedToInterventionQueue: false,
            messageCode: "1",
            dismissed: false
          }
        }
      : {}
  };
}

export async function getAssessmentResult({
  _id = "someAssessmentResultId",
  orgid = "test_organization_id",
  byDateOn,
  studentGroupId = "test_studentGroupId",
  schoolYear = 2018,
  type = "benchmark",
  status = "OPEN"
} = {}) {
  byDateOn ??= await getTimestampInfo("test");
  return {
    _id,
    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
    created: byDateOn,
    schoolYear,
    status,
    studentGroupId,
    type,
    grade: "05",
    orgid,
    scores: [],
    assessmentIds: ["aqikjxjsFL3bwTfSm", "Smm39q4tPmM9TephC", "J8aGfDwkaHFN2cCAM", "3PPCM9w37uGt3Djzc"]
  };
}

export function getStudentGroupEnrollment({
  isActive = true,
  studentGroupId = "test_studentGroupId",
  studentId = "test_studentId",
  orgid = "test_organization_id",
  schoolYear = 2018,
  siteId = "test_siteId",
  grade = "05"
}) {
  return {
    isActive,
    studentGroupId,
    studentId,
    orgid,
    schoolYear,
    siteId,
    grade
  };
}

export const generateMultipleSpringMathGradesDataPackage = (springMathGradesForStudents = ["1","2"]) => ({
  data: [
    {
      DistrictID: "1",
      DistrictName: "File Upload",
      SchoolID: "1",
      SchoolName: "FirstSchool",
      TeacherID: "1",
      TeacherLastName: "Forest",
      TeacherFirstName: "Jeanette E",
      TeacherEmail: "<EMAIL>",
      ClassName: "FirstClass",
      ClassSectionID: "abc",
      StudentLocalID: "10",
      StudentStateID: "100",
      StudentLastName: "Anderson",
      StudentFirstName: "Mister",
      StudentBirthDate: "2005-01-03",
      SpringMathGrade: springMathGradesForStudents[0]
    },
    {
      DistrictID: "1",
      DistrictName: "File Upload",
      SchoolID: "1",
      SchoolName: "FirstSchool",
      TeacherID: "1",
      TeacherLastName: "Forest",
      TeacherFirstName: "Jeanette E",
      TeacherEmail: "<EMAIL>",
      ClassName: "FirstClass",
      ClassSectionID: "abc",
      StudentLocalID: "20",
      StudentStateID: "200",
      StudentLastName: "Smith",
      StudentFirstName: "Agent",
      StudentBirthDate: "2005-01-03",
      SpringMathGrade: springMathGradesForStudents[1]
    }
  ],
  source: "CSV"
});
