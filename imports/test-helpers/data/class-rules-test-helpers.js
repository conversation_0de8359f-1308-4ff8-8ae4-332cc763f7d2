export function getSingleAssessmentClassRulesData() {
  const grade = "K";
  const grades = [
    {
      _id: grade,
      display: grade
    }
  ];
  const assessmentId = "WAv6GQ7DzR44ouxrg";
  const ruleId = "wiSJXiCwZjGwqazMC";
  const rules = [
    {
      _id: ruleId,
      grade,
      skills: [
        {
          assessmentId,
          interventions: []
        }
      ]
    }
  ];
  const assessmentName = "Count Objects";
  const instructionalTarget = 4;
  const masteryTarget = 8;
  const assessments = [
    {
      _id: assessmentId,
      name: assessmentName,
      associatedGrades: [grade],
      strands: [
        {
          scores: [
            {
              targets: [
                {
                  assessmentType: "classwide",
                  grade,
                  periods: [
                    {
                      values: [instructionalTarget, masteryTarget]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ],
      monitorAssessmentMeasure: "123"
    }
  ];
  return {
    grade,
    grades,
    assessmentId,
    rules,
    assessmentName,
    instructionalTarget,
    masteryTarget,
    assessments,
    ruleId
  };
}

export function getMultipleAssessmentsClassRulesData(grade = "K", monitorAssessmentMeasure = 22) {
  const grades = [
    {
      _id: grade,
      display: grade
    }
  ];
  const topAssessmentId = "topAssessmentId";
  const middleAssessmentId = "middleAssessmentID";
  const bottomAssessmentId = "bottomAssessmentId";
  const ruleId = "ruleId1";
  const rules = [
    {
      _id: ruleId,
      grade,
      skills: [
        {
          assessmentId: topAssessmentId,
          interventions: []
        },
        {
          assessmentId: middleAssessmentId,
          interventions: []
        },
        {
          assessmentId: bottomAssessmentId,
          interventions: []
        }
      ]
    }
  ];
  const assessmentName1 = "Count";
  const assessmentName2 = "Circle";
  const assessmentName3 = "Write";
  const assessments = [
    {
      _id: topAssessmentId,
      name: assessmentName1,
      strands: [
        {
          scores: [
            {
              targets: [
                {
                  assessmentType: "classwide",
                  grade,
                  periods: [
                    {
                      values: [1, 2]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ],
      monitorAssessmentMeasure: (monitorAssessmentMeasure + 3).toString()
    },
    {
      _id: middleAssessmentId,
      name: assessmentName2,
      strands: [
        {
          scores: [
            {
              targets: [
                {
                  assessmentType: "classwide",
                  grade,
                  periods: [
                    {
                      values: [3, 4]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ],
      monitorAssessmentMeasure: (monitorAssessmentMeasure + 1).toString()
    },
    {
      _id: bottomAssessmentId,
      name: assessmentName3,
      strands: [
        {
          scores: [
            {
              targets: [
                {
                  assessmentType: "classwide",
                  grade,
                  periods: [
                    {
                      values: [5, 6]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ],
      monitorAssessmentMeasure: (monitorAssessmentMeasure + 2).toString()
    }
  ];
  return { grade, grades, middleAssessmentId, topAssessmentId, bottomAssessmentId, ruleId, rules, assessments };
}

export function getMultipleAssessmentsClassRulesDataForGrades(gradesAndMeasuresArray) {
  const combinedGrades = [];
  const combinedRules = [];
  const combinedAssessments = [];

  gradesAndMeasuresArray.forEach(({ grade, am }) => {
    const { grades, rules, assessments } = getMultipleAssessmentsClassRulesData(grade, am);
    combinedGrades.push(...grades);
    combinedRules.push(...rules);
    combinedAssessments.push(...assessments);
  });

  return {
    grades: combinedGrades,
    rules: combinedRules,
    assessments: combinedAssessments
  };
}
