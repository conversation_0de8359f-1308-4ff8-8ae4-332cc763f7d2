export function getStudent() {
  return {
    _id: "student_id",
    orgid: "test_organization_id",
    grade: "03",
    schoolYear: 2017,
    districtNumber: "1234",
    created: {
      by: "TEST",
      on: Date.now(),
      date: new Date()
    },
    lastModified: {
      by: "TEST",
      on: Date.now(),
      date: new Date()
    },
    comments: ["Comment1", "Comment2"],
    demographic: {
      gender: "F",
      ethnicity: "eFrQw2h9rZDFLT59z",
      birthDate: "2010-05-29",
      birthDateTimeStamp: 126434563200000.0
    },
    identity: {
      name: {
        firstName: "<PERSON>",
        lastName: "Doe",
        middleName: ""
      },
      identification: {
        localId: "239845",
        stateId: "23253452345"
      }
    },
    rosterImportId: "56d8c1ff36e699ed6523019e",
    status: "zeARDCgzCMoFWiJtq"
  };
}
