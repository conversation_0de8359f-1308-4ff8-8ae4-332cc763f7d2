export function studentGroup(data) {
  return {
    _id: (data._id && `test_student_group_${data._id}`) || "test_student_group_0",
    orgid: "test_organization_id",
    siteId: "test_elementary_site_id",
    grade: data.grade || "03",
    created: {
      by: "DDP",
      on: 1440961346358.0,
      date: new Date("2015-08-30T19:02:26.358+0000")
    },
    lastModified: {
      by: "DDP",
      on: 1466079224621.0,
      date: new Date("2016-06-16T12:13:44.621+0000")
    },
    type: "CLASS",
    ownerIds: data.ownerIds || [],
    history: [{}],
    currentAssessmentResultIds: ["testAssessmentResultId"],
    currentClasswideSkill: {
      assessmentId: "testAssessmentId",
      assessmentName: "testAssessmentName",
      assessmentResultId: "testAssessmentResultId",
      interventions: ["testInterventionId"],
      targets: [1, 2, 3],
      whenStarted: {
        by: "DDP",
        on: 1466079224621.0,
        date: new Date("2016-06-16T12:13:44.621+0000")
      }
    },
    isActive: false,
    sectionId: "abcde",
    name: data.name || "1234abcd",
    schoolYear: 2017
  };
}

export default studentGroup;
