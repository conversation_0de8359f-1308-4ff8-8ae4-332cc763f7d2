export function studentGroupEnrollment(data) {
  return {
    _id: `test_student_group_enrollment_${data._id}` || null,
    orgid: "test_organization_id",
    siteId: "test_elementary_site_id",
    grade: "03",
    studentGroupId: data.studentGroupId || "test_student_group_1",
    studentId: data.studentId || "Noam_Pikelny_TEST_STUDENT",
    isActive: true,
    schoolYear: 2017,
    giftedAndTalented: false,
    ELL: false,
    IEP: false,
    title1: false,
    freeReducedLunch: "no",
    created: {
      by: "DDP",
      on: 1440961346358.0,
      date: new Date("2015-08-30T19:02:26.358+0000")
    },
    lastModified: {
      by: "DDP",
      on: 1466079224621.0,
      date: new Date("2016-06-16T12:13:44.621+0000")
    }
  };
}
