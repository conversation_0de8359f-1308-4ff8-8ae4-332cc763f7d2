export const averageStatsStudentGroupEnrollments = [
  {
    studentGroupId: "groupA",
    studentId: "BSjb3CAZMBKfmBi2u2022",
    isActive: true
  },
  {
    studentGroupId: "groupB",
    studentId: "studentId1",
    isActive: true
  },
  {
    studentGroupId: "groupB",
    studentId: "studentId2",
    isActive: true
  },
  {
    studentGroupId: "groupB",
    studentId: "studentId3",
    isActive: true
  },
  {
    studentGroupId: "groupB",
    studentId: "studentId4",
    isActive: true
  },
  {
    studentGroupId: "groupB",
    studentId: "studentId5",
    isActive: true
  }
];

export const averageStatsStudentGroups = [
  {
    _id: "groupA",
    orgid: "test_organization_id",
    siteId: "test_elementary_site_id",
    name: "Test 04 (fqaFT_04)",
    schoolYear: 2022,
    history: []
  },
  {
    _id: "groupB",
    orgid: "test_organization_id",
    siteId: "test_elementary_site_id",
    isActive: true,
    name: "Test 02 (fqaFT_02)",
    schoolYear: 2022,
    history: [
      {
        assessmentId: "5JREydmmbAnRT5usy",
        assessmentName: "Add 2-Digit Numbers with Regrouping",
        whenStarted: {
          on: 1642095257880,
          date: "2022-01-13T17:34:17.880Z"
        },
        benchmarkPeriodId: "nEsbWokBWutTZFkTh",
        whenEnded: {
          on: 1642096717712,
          date: "2022-01-13T17:58:37.712Z"
        },
        assessmentResultMeasures: [
          {
            assessmentId: "5JREydmmbAnRT5usy",
            assessmentName: "Add 2-Digit Numbers with Regrouping",
            studentResults: [
              {
                studentId: "studentId1"
              },
              {
                studentId: "studentId5"
              },
              {
                studentId: "studentId2"
              },
              {
                studentId: "studentId4"
              },
              {
                studentId: "studentId3"
              }
            ],
            benchmarkPeriodId: "nEsbWokBWutTZFkTh"
          }
        ],
        type: "classwide"
      },
      {
        assessmentId: "Q69EvFPRa394q5cpT",
        assessmentName: "Create Equivalent Addition & Subtraction Problems using Place Value & Decomposition",
        whenStarted: {
          on: 1642095229265,
          date: "2022-01-13T17:33:49.265Z"
        },
        benchmarkPeriodId: "nEsbWokBWutTZFkTh",
        whenEnded: {
          on: 1642095257892,
          date: "2022-01-13T17:34:17.892Z"
        },
        assessmentResultMeasures: [
          {
            assessmentId: "Q69EvFPRa394q5cpT",
            assessmentName: "Create Equivalent Addition & Subtraction Problems using Place Value & Decomposition",
            studentResults: [
              {
                studentId: "studentId1"
              },
              {
                studentId: "studentId5"
              },
              {
                studentId: "studentId2"
              },
              {
                studentId: "studentId4"
              },
              {
                studentId: "studentId3"
              }
            ],
            benchmarkPeriodId: "nEsbWokBWutTZFkTh"
          }
        ],
        type: "classwide"
      },
      {
        assessmentId: "DHTEiT2DMwtuSMtgA",
        assessmentName:
          "Create Equivalent Addition & Subtraction Problems using Associative Property & Near Easy Problems",

        whenStarted: {
          on: 1642095225774,
          date: "2022-01-13T17:33:45.774Z"
        },
        benchmarkPeriodId: "nEsbWokBWutTZFkTh",

        whenEnded: {
          on: 1642095229275,
          date: "2022-01-13T17:33:49.275Z"
        },
        assessmentResultMeasures: [
          {
            assessmentId: "DHTEiT2DMwtuSMtgA",
            assessmentName:
              "Create Equivalent Addition & Subtraction Problems using Associative Property & Near Easy Problems",

            studentResults: [
              {
                studentId: "studentId1"
              },
              {
                studentId: "studentId5"
              },
              {
                studentId: "studentId2"
              },
              {
                studentId: "studentId4"
              },
              {
                studentId: "studentId3"
              }
            ],
            benchmarkPeriodId: "nEsbWokBWutTZFkTh"
          }
        ],
        type: "classwide"
      },
      {
        assessmentId: "TWCTEasrvMv7HFMRt",
        assessmentName: "Subtract 2-digit Numbers without Regrouping",

        whenStarted: {
          on: 1642095222281,
          date: "2022-01-13T17:33:42.281Z"
        },

        benchmarkPeriodId: "nEsbWokBWutTZFkTh",

        whenEnded: {
          on: 1642095225782,
          date: "2022-01-13T17:33:45.782Z"
        },
        assessmentResultMeasures: [
          {
            assessmentId: "TWCTEasrvMv7HFMRt",
            assessmentName: "Subtract 2-digit Numbers without Regrouping",

            studentResults: [
              {
                studentId: "studentId1"
              },
              {
                studentId: "studentId5"
              },
              {
                studentId: "studentId2"
              },
              {
                studentId: "studentId4"
              },
              {
                studentId: "studentId3"
              }
            ],
            benchmarkPeriodId: "nEsbWokBWutTZFkTh"
          }
        ],
        type: "classwide"
      }
    ],
    currentClasswideSkill: {
      assessmentId: "ijHGak9a8NLWA5pfz",
      assessmentName: "Subtract 2-Digit Numbers with Regrouping",
      whenStarted: {
        on: 1642096717702,
        date: "2022-01-13T17:58:37.702Z"
      },
      assessmentResultId: "AWgoz2p9eLEtSmgeR",
      benchmarkPeriodId: "nEsbWokBWutTZFkTh"
    }
  }
];
