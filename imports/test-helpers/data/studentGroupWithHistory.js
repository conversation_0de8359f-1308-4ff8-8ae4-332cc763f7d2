export const defaultStudentGroup = {
  _id: "someGroupId",
  orgid: "someOrgId",
  siteId: "someSiteId",
  grade: "05",
  ownerIds: ["teacher_user_id"],
  isActive: true,
  sectionId: "someSectionId",
  name: "someName",
  schoolYear: 2019,
  history: [
    {
      assessmentId: "aqikjxjsFL3bwTfSm",
      assessmentName: "Fact Families: Multiplication/Division 0-12",
      interventions: [],
      targets: [28, 56, 220],
      assessmentResultId: "SQoobQBkjodi4FFew",
      benchmarkPeriodId: "nEsbWokBWutTZFkTh",
      whenEnded : {
        by : "49b8KnCSh3u8GcCWj",
        on : 1560767775080,
        date : "2019-06-17T12:36:15.080+02:00"
      },
      assessmentResultMeasures: [
        {
          assessmentId: "aqikjxjsFL3bwTfSm",
          assessmentName: "Fact Families: Multiplication/Division 0-12",
          cutoffTarget: 56,
          targetScores: [28, 56, 220],
          medianScore: 60,
          studentScores: [0, 60, 60, 60, 60],
          percentMeetingTarget: 80,
          numberMeetingTarget: 4,
          totalStudentsAssessed: 5,
          studentResults: [
            {
              studentId: "testStudentId1",
              status: "COMPLETE",
              firstName: "Wesley",
              lastName: "Barber",
              score: "60",
              meetsTarget: true,
              individualRuleOutcome: "above"
            },
            {
              studentId: "testStudentId2",
              status: "COMPLETE",
              firstName: "Liem",
              lastName: "Griffes",
              score: "65",
              meetsTarget: true,
              individualRuleOutcome: "above"
            },
            {
              studentId: "testStudentId3",
              status: "COMPLETE",
              firstName: "Alexis",
              lastName: "Prather",
              score: "70",
              meetsTarget: true,
              individualRuleOutcome: "above"
            },
            {
              studentId: "testStudentId4",
              status: "COMPLETE",
              firstName: "Taylor",
              lastName: "Turner",
              score: "80",
              meetsTarget: true,
              individualRuleOutcome: "above"
            },
            {
              studentId: "testStudentId5",
              status: "COMPLETE",
              firstName: "Ethan",
              lastName: "Ward",
              score: "0",
              meetsTarget: false,
              individualRuleOutcome: "below"
            }
          ],
          benchmarkPeriodId: "nEsbWokBWutTZFkTh",
          grade: "05",
          assessmentResultType: "classwide"
        }
      ],
      type: "classwide",
      enrolledStudentIds: ["testStudentId1", "testStudentId2", "testStudentId3", "testStudentId4", "testStudentId5"]
    },
    {
      type: "benchmark",
      benchmarkPeriodId: "nEsbWokBWutTZFkTh",
      assessmentResultId: "JESrWLdnzbRdQqaSe",
      assessmentResultMeasures: [
        {
          assessmentId: "GWzcFb2APLvktZ96a",
          assessmentName: "Convert Improper Fractions to Mixed Numbers",
          cutoffTarget: 11,
          targetScores: [11, 23, 300],
          medianScore: 0,
          studentScores: [0, 0, 0, 0],
          percentMeetingTarget: 0,
          numberMeetingTarget: 0,
          totalStudentsAssessed: 5,
          studentResults: [
            {
              studentId: "testStudentId1",
              status: "COMPLETE",
              firstName: "Wesley",
              lastName: "Barber",
              score: "50",
              meetsTarget: false,
              individualRuleOutcome: "below"
            },
            {
              studentId: "testStudentId2",
              status: "COMPLETE",
              firstName: "Liem",
              lastName: "Griffes",
              score: "60",
              meetsTarget: false,
              individualRuleOutcome: "below"
            },
            {
              studentId: "testStudentId3",
              status: "COMPLETE",
              firstName: "Alexis",
              lastName: "Prather",
              score: "30",
              meetsTarget: false,
              individualRuleOutcome: "below"
            },
            {
              studentId: "testStudentId4",
              status: "COMPLETE",
              firstName: "Taylor",
              lastName: "Turner",
              score: "45",
              meetsTarget: false,
              individualRuleOutcome: "below"
            },
            {
              studentId: "testStudentId5",
              status: "COMPLETE",
              firstName: "Ethan",
              lastName: "Ward",
              score: "0",
              meetsTarget: false,
              individualRuleOutcome: "below"
            }
          ],
          benchmarkPeriodId: "nEsbWokBWutTZFkTh",
          grade: "05",
          assessmentResultType: "benchmark"
        },
        {
          assessmentId: "RAicL73Hwai6BSCPH",
          assessmentName: "Subtraction 0-20",
          cutoffTarget: 17,
          targetScores: [17, 33, 300],
          medianScore: 0,
          studentScores: [0],
          percentMeetingTarget: 0,
          numberMeetingTarget: 0,
          totalStudentsAssessed: 1,
          studentResults: [
            {
              studentId: "testStudentId1",
              status: "COMPLETE",
              firstName: "Wesley",
              lastName: "Barber",
              score: "60",
              meetsTarget: true,
              individualRuleOutcome: "above"
            },
            {
              studentId: "testStudentId2",
              status: "COMPLETE",
              firstName: "Liem",
              lastName: "Griffes",
              score: "65",
              meetsTarget: true,
              individualRuleOutcome: "above"
            },
            {
              studentId: "testStudentId3",
              status: "COMPLETE",
              firstName: "Alexis",
              lastName: "Prather",
              score: "70",
              meetsTarget: true,
              individualRuleOutcome: "above"
            },
            {
              studentId: "testStudentId4",
              status: "COMPLETE",
              firstName: "Taylor",
              lastName: "Turner",
              score: "80",
              meetsTarget: true,
              individualRuleOutcome: "above"
            },
            {
              studentId: "testStudentId5",
              status: "COMPLETE",
              firstName: "Ethan",
              lastName: "Ward",
              score: "0",
              meetsTarget: false,
              individualRuleOutcome: "below"
            }
          ]
        }
      ],
      enrolledStudentIds: ["testStudentId1", "testStudentId2", "testStudentId3", "testStudentId4", "testStudentId5"]
    }
  ],
  currentAssessmentResultIds: ["iEw9HzsnnH5pPemvw"],
  currentClasswideSkill: {
    assessmentId: "J8aGfDwkaHFN2cCAM",
    assessmentName: "2-Digit Multiplied by 2-Digit with and without Regrouping",
    interventions: [],
    targets: [5, 10, 300],
    assessmentResultId: "iEw9HzsnnH5pPemvw",
    benchmarkPeriodId: "nEsbWokBWutTZFkTh"
  },
  individualInterventionQueue: ["testStudentId5"]
};

export const studentGroupWithBenchmarkHistory = {
  _id: "benchmarkMeasuresGroupId",
  orgid: "someOrgId",
  schoolYear: 2019,
  isActive: true,
  siteId: "someSiteId",
  ownerIds: ["teacher_user_id"],
  name: "someName",
  grade: "01",
  sectionId: "someSectionId",
  history: [
    {
      type: "benchmark",
      benchmarkPeriodId: "cjCMnZKARBJmG8suT",
      assessmentResultId: "xHE3WdqE82FueuNn4",
      assessmentResultMeasures: [
        {
          assessmentId: "7arH8a6z3BAEdEARm",
          assessmentName: "Sums to 20",
          targetScores: [20, 40, 300],
          studentResults: [
            {
              studentId: "testStudentId1",
              firstName: "Wesley",
              lastName: "Barber",
              score: "60"
            },
            {
              studentId: "testStudentId2",
              firstName: "Liem",
              lastName: "Griffes",
              score: "65"
            },
            {
              studentId: "testStudentId3",
              firstName: "Alexis",
              lastName: "Prather",
              score: "70"
            },
            {
              studentId: "testStudentId4",
              firstName: "Taylor",
              lastName: "Turner",
              score: "80"
            },
            {
              studentId: "testStudentId5",
              firstName: "Ethan",
              lastName: "Ward",
              score: "0"
            }
          ],
          benchmarkPeriodId: "cjCMnZKARBJmG8suT",
          grade: "01",
          assessmentResultType: "benchmark"
        },
        {
          assessmentId: "RAicL73Hwai6BSCPH",
          assessmentName: "Subtraction 0-20",
          targetScores: [20, 40, 300],
          studentResults: [
            {
              studentId: "testStudentId1",
              firstName: "Wesley",
              lastName: "Barber",
              score: "60"
            },
            {
              studentId: "testStudentId2",
              firstName: "Liem",
              lastName: "Griffes",
              score: "65"
            },
            {
              studentId: "testStudentId3",
              firstName: "Alexis",
              lastName: "Prather",
              score: "70"
            },
            {
              studentId: "testStudentId4",
              firstName: "Taylor",
              lastName: "Turner",
              score: "80"
            },
            {
              studentId: "testStudentId5",
              firstName: "Ethan",
              lastName: "Ward",
              score: "0"
            }
          ]
        },
        {
          assessmentId: "FxYDJWbMdT8QiZwcD",
          assessmentName: "Fact Families: Add/Subtract 0-9",
          targetScores: [20, 40, 300],
          studentResults: [
            {
              studentId: "testStudentId1",
              firstName: "Wesley",
              lastName: "Barber",
              score: "60"
            },
            {
              studentId: "testStudentId2",
              firstName: "Liem",
              lastName: "Griffes",
              score: "65"
            },
            {
              studentId: "testStudentId3",
              firstName: "Alexis",
              lastName: "Prather",
              score: "70"
            },
            {
              studentId: "testStudentId4",
              firstName: "Taylor",
              lastName: "Turner",
              score: "80"
            },
            {
              studentId: "testStudentId5",
              firstName: "Ethan",
              lastName: "Ward",
              score: "0"
            }
          ]
        }
      ],
      enrolledStudentIds: ["testStudentId1", "testStudentId2", "testStudentId3", "testStudentId4", "testStudentId5"]
    }
  ],
  currentAssessmentResultIds: ["zD86ZDvNTHqXQqNjR"],
  individualInterventionQueue: [],
  rosterImportId: "bBGLeMwCxZZ2vvdbg"
};
