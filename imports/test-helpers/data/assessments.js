export const idOfAssessmentWithBenchmarkTarget = "yAL8Z67XYCeb9BxJT";
export const grade = "06";
const otherGrade = "07";
export const highSchoolGrade = "HS";
export const fallPeriodId = "8S52Gz5o85hRkECgq";
export const springPeriodId = "cjCMnZKARBJmG8suT";
export const winterPeriodId = "nEsbWokBWutTZFkTh";
const allPeriodsId = "allPeriods";
export const expectedUpdatedHighSchoolAssessment = {
  strands: [
    {
      name: "Overall",
      scores: [
        {
          name: "Number Correct",
          targets: [
            {
              grade: otherGrade, // ADDED TO VERIFY THIS ONE DOES NOT GET MODIFIED
              assessmentType: "individual",
              periods: [
                {
                  name: "Fall",
                  benchmarkPeriodId: fallPeriodId,
                  values: [10, 20, 300]
                },
                {
                  name: "Spring",
                  benchmarkPeriodId: springPeriodId,
                  values: [10, 20, 300]
                },
                {
                  name: "Winter",
                  benchmarkPeriodId: winterPeriodId,
                  values: [10, 20, 300]
                }
              ]
            },
            {
              grade: highSchoolGrade,
              assessmentType: "classwide",
              periods: [
                {
                  name: "All",
                  benchmarkPeriodId: allPeriodsId,
                  values: [15, 30, 300]
                }
              ]
            },
            {
              grade: highSchoolGrade,
              assessmentType: "individual",
              periods: [
                {
                  name: "All",
                  benchmarkPeriodId: allPeriodsId,
                  values: [7, 15, 300]
                }
              ]
            }
          ]
        }
      ]
    }
  ]
};
export const highSchoolAssessmentTargetsWithRemovedIndividualTarget = [
  {
    grade: highSchoolGrade,
    assessmentType: "classwide",
    periods: [
      {
        name: "All",
        benchmarkPeriodId: allPeriodsId,
        values: [10, 20, 300]
      }
    ]
  },
  {
    grade: otherGrade, // ADDED TO VERIFY THIS ONE DOES NOT GET MODIFIED
    assessmentType: "individual",
    periods: [
      {
        name: "Fall",
        benchmarkPeriodId: fallPeriodId,
        values: [10, 20, 300]
      },
      {
        name: "Spring",
        benchmarkPeriodId: springPeriodId,
        values: [10, 20, 300]
      },
      {
        name: "Winter",
        benchmarkPeriodId: winterPeriodId,
        values: [10, 20, 300]
      }
    ]
  }
];
export const idOfHighSchoolAssessment = "highSchoolAssesssmentId";
export const highSchoolAssessment = {
  _id: idOfHighSchoolAssessment,
  strands: [
    {
      name: "Overall",
      scores: [
        {
          name: "Number Correct",
          targets: [
            {
              grade: highSchoolGrade,
              assessmentType: "classwide",
              periods: [
                {
                  name: "All",
                  benchmarkPeriodId: allPeriodsId,
                  values: [10, 20, 300]
                }
              ]
            },
            {
              grade: highSchoolGrade,
              assessmentType: "individual",
              periods: [
                {
                  name: "All",
                  benchmarkPeriodId: allPeriodsId,
                  values: [10, 20, 300]
                }
              ]
            },
            {
              grade: otherGrade, // ADDED TO VERIFY THIS ONE DOES NOT GET MODIFIED
              assessmentType: "individual",
              periods: [
                {
                  name: "Fall",
                  benchmarkPeriodId: fallPeriodId,
                  values: [10, 20, 300]
                },
                {
                  name: "Spring",
                  benchmarkPeriodId: springPeriodId,
                  values: [10, 20, 300]
                },
                {
                  name: "Winter",
                  benchmarkPeriodId: winterPeriodId,
                  values: [10, 20, 300]
                }
              ]
            }
          ]
        }
      ]
    }
  ]
};
export const expectedUpdatedPrimaryGradeAssessment = {
  strands: [
    {
      name: "Overall",
      scores: [
        {
          name: "Number Correct",
          targets: [
            {
              grade: otherGrade, // ADDED TO VERIFY THIS ONE DOES NOT GET MODIFIED
              assessmentType: "individual",
              periods: [
                {
                  name: "Fall",
                  benchmarkPeriodId: fallPeriodId,
                  values: [10, 20, 300]
                },
                {
                  name: "Spring",
                  benchmarkPeriodId: springPeriodId,
                  values: [10, 20, 300]
                },
                {
                  name: "Winter",
                  benchmarkPeriodId: winterPeriodId,
                  values: [10, 20, 300]
                }
              ]
            },
            {
              grade,
              periods: [
                {
                  name: "Fall",
                  benchmarkPeriodId: fallPeriodId,
                  values: [1, 2, 300]
                },
                {
                  name: "Spring",
                  benchmarkPeriodId: springPeriodId,
                  values: [3, 4, 300]
                },
                {
                  name: "Winter",
                  benchmarkPeriodId: winterPeriodId,
                  values: [5, 6, 300]
                }
              ]
            },
            {
              grade,
              assessmentType: "individual",
              periods: [
                {
                  name: "Fall",
                  benchmarkPeriodId: fallPeriodId,
                  values: [7, 8, 300]
                },
                {
                  name: "Spring",
                  benchmarkPeriodId: springPeriodId,
                  values: [9, 10, 300]
                },
                {
                  name: "Winter",
                  benchmarkPeriodId: winterPeriodId,
                  values: [11, 12, 300]
                }
              ]
            }
          ]
        }
      ]
    }
  ]
};
export const assessmentWithDefaultAndIndividualTarget = {
  strands: [
    {
      name: "Overall",
      scores: [
        {
          name: "Number Correct",
          targets: [
            {
              grade,
              periods: [
                {
                  name: "Fall",
                  benchmarkPeriodId: fallPeriodId,
                  values: [20, 40, 300]
                },
                {
                  name: "Spring",
                  benchmarkPeriodId: springPeriodId,
                  values: [20, 40, 300]
                },
                {
                  name: "Winter",
                  benchmarkPeriodId: winterPeriodId,
                  values: [20, 40, 300]
                }
              ]
            },
            {
              grade,
              assessmentType: "individual",
              periods: [
                {
                  name: "Fall",
                  benchmarkPeriodId: fallPeriodId,
                  values: [10, 20, 300]
                },
                {
                  name: "Spring",
                  benchmarkPeriodId: springPeriodId,
                  values: [10, 20, 300]
                },
                {
                  name: "Winter",
                  benchmarkPeriodId: winterPeriodId,
                  values: [10, 20, 300]
                }
              ]
            },
            {
              grade: otherGrade, // ADDED TO VERIFY THIS ONE DOES NOT GET MODIFIED
              assessmentType: "individual",
              periods: [
                {
                  name: "Fall",
                  benchmarkPeriodId: fallPeriodId,
                  values: [10, 20, 300]
                },
                {
                  name: "Spring",
                  benchmarkPeriodId: springPeriodId,
                  values: [10, 20, 300]
                },
                {
                  name: "Winter",
                  benchmarkPeriodId: winterPeriodId,
                  values: [10, 20, 300]
                }
              ]
            }
          ]
        }
      ]
    }
  ]
};
export const assessmentWithBenchmarkTarget = {
  _id: idOfAssessmentWithBenchmarkTarget,
  strands: [
    {
      name: "Overall",
      scores: [
        {
          name: "Number Correct",
          targets: [
            {
              grade,
              assessmentType: "benchmark",
              periods: [
                {
                  name: "Fall",
                  benchmarkPeriodId: fallPeriodId,
                  values: [20, 40, 300]
                },
                {
                  name: "Spring",
                  benchmarkPeriodId: springPeriodId,
                  values: [20, 40, 300]
                },
                {
                  name: "Winter",
                  benchmarkPeriodId: winterPeriodId,
                  values: [20, 40, 300]
                }
              ]
            }
          ]
        }
      ]
    }
  ]
};
