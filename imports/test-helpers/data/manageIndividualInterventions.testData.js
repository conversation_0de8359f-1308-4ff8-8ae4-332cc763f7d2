export function getExpectedStartingAssessmentResult(targetScores = []) {
  return {
    _id: "startingAssessmentResult",
    benchmarkPeriodId: "allPeriods",
    schoolYear: 2018,
    status: "COMPLETED",
    studentGroupId: "studentGroupId",
    type: "benchmark",
    grade: "HS",
    orgid: "org1",
    studentId: "studentId",
    assessmentIds: ["123", "234", "345"],
    scores: [
      {
        studentId: "studentId",
        status: "COMPLETE",
        firstName: undefined,
        lastName: undefined,
        score: "0",
        meetsTarget: false,
        individualRuleOutcome: "below",
        value: "0",
        siteId: "site1",
        orgid: "org1",
        assessmentId: "123"
      }
    ],
    measures: [
      {
        assessmentId: "123",
        assessmentName: "Test assessment",
        cutoffTarget: targetScores[0],
        targetScores,
        medianScore: 0,
        studentScores: [0],
        percentMeetingTarget: 0,
        numberMeetingTarget: 0,
        totalStudentsAssessed: 1,
        studentResults: [
          {
            studentId: "studentId",
            status: "COMPLETE",
            firstName: undefined,
            lastName: undefined,
            score: "0",
            meetsTarget: false,
            individualRuleOutcome: "below"
          }
        ]
      }
    ]
  };
}

export function generateHighSchoolAssessment(targetsForAssessmentTypes = [], topLevelFields = {}) {
  return {
    _id: "123",
    name: "Test assessment",
    associatedGrades: ["07", "HS"],
    strands: [
      {
        scores: [
          {
            targets: [
              {
                grade: "07",
                periods: [
                  {
                    values: ["12", "13"]
                  }
                ]
              },
              ...targetsForAssessmentTypes.map(({ assessmentType, values }) => ({
                grade: "HS",
                assessmentType,
                periods: [
                  {
                    values
                  }
                ]
              }))
            ]
          }
        ]
      }
    ],
    monitorAssessmentMeasure: "123",
    ...topLevelFields
  };
}
