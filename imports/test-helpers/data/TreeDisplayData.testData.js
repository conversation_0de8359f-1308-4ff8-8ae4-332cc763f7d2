// RULES, ASSESSMENTS AND INTERVENTIONS FOR DECISION TREE FOR AM 51 FOR GRADE 1 IN SPRING PERIOD

export const assessments = [
  {
    _id: "FxYDJWbMdT8QiZwcD",
    name: "Fact Families: Add/Subtract 0-9",
    associatedGrades: ["01", "02", "03", "04", "05", "06"],
    strands: [
      {
        name: "Overall",
        scores: [
          {
            name: "Number Correct",
            externalId: "number_correct",
            targets: [
              {
                grade: "01",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [20, 40, 300]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [20, 40, 300]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [20, 40, 300]
                  }
                ]
              },
              {
                grade: "02",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [20, 40, 300]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [20, 40, 300]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [20, 40, 300]
                  }
                ]
              },
              {
                grade: "03",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [20, 40, 300]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [20, 40, 300]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [20, 40, 300]
                  }
                ]
              },
              {
                grade: "04",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [40, 80, 300]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [40, 80, 300]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [40, 80, 300]
                  }
                ]
              },
              {
                grade: "05",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [40, 80, 300]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [75, 150, 225]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [75, 150, 225]
                  }
                ]
              },
              {
                grade: "06",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [75, 150, 225]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [40, 80, 300]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [40, 80, 300]
                  }
                ]
              }
            ]
          },
          {
            name: "Number Errors",
            externalId: "number_errors",
            targets: []
          },
          {
            name: "Accuracy",
            externalId: "accuracy",
            targets: []
          },
          {
            name: "Total Items",
            externalId: "total_items",
            targets: []
          }
        ]
      }
    ],
    monitorAssessmentMeasure: "51"
  },
  {
    _id: "J7J7fw6x3CsYLHHx7",
    name: "Subtraction 0-5",
    associatedGrades: ["01", "02", "03", "04", "05", "06", "07", "08", "HS"],
    strands: [
      {
        name: "Overall",
        scores: [
          {
            name: "Number Correct",
            externalId: "number_correct",
            targets: [
              {
                grade: "01",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [20, 40, 300]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [20, 40, 300]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [20, 40, 300]
                  }
                ]
              },
              {
                grade: "02",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [20, 40, 300]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [20, 40, 300]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [20, 40, 300]
                  }
                ]
              },
              {
                grade: "03",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [20, 40, 300]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [20, 40, 300]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [20, 40, 300]
                  }
                ]
              },
              {
                grade: "04",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [40, 80, 300]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [40, 80, 300]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [40, 80, 300]
                  }
                ]
              },
              {
                grade: "05",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [40, 80, 300]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [40, 80, 300]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [40, 80, 300]
                  }
                ]
              },
              {
                grade: "06",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [40, 80, 300]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [40, 80, 300]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [40, 80, 300]
                  }
                ]
              },
              {
                grade: "07",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [50, 100, 300]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [50, 100, 300]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [50, 100, 300]
                  }
                ]
              },
              {
                grade: "08",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [50, 100, 300]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [50, 100, 300]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [50, 100, 300]
                  }
                ]
              },
              {
                assessmentType: "individual",
                grade: "HS",
                periods: [
                  {
                    name: "All",
                    benchmarkPeriodId: "allPeriods",
                    values: [50, 100, 300]
                  }
                ]
              }
            ]
          },
          {
            name: "Number Errors",
            externalId: "number_errors",
            targets: []
          },
          {
            name: "Accuracy",
            externalId: "accuracy",
            targets: []
          },
          {
            name: "Total Items",
            externalId: "total_items",
            targets: []
          }
        ]
      }
    ],
    monitorAssessmentMeasure: "22"
  },
  {
    _id: "ZtJxX6rjuoRwAqRFo",
    name: "Sums to 12",
    associatedGrades: ["01", "02", "03", "04", "05", "06", "07", "08", "HS"],
    strands: [
      {
        name: "Overall",
        scores: [
          {
            name: "Number Correct",
            externalId: "number_correct",
            targets: [
              {
                grade: "01",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [13, 26, 299]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [13, 26, 299]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [13, 26, 299]
                  }
                ]
              },
              {
                grade: "02",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [13, 26, 299]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [13, 26, 299]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [13, 26, 299]
                  }
                ]
              },
              {
                grade: "03",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [13, 26, 299]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [13, 26, 299]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [13, 26, 299]
                  }
                ]
              },
              {
                grade: "04",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [25, 52, 299]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [25, 52, 299]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [25, 52, 299]
                  }
                ]
              },
              {
                grade: "05",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [25, 52, 225]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [25, 52, 225]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [25, 52, 225]
                  }
                ]
              },
              {
                grade: "06",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [25, 52, 225]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [25, 52, 225]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [25, 52, 225]
                  }
                ]
              },
              {
                grade: "07",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [31, 65, 225]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [31, 65, 225]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [31, 65, 225]
                  }
                ]
              },
              {
                grade: "08",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [31, 65, 225]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [31, 65, 225]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [31, 65, 225]
                  }
                ]
              },
              {
                assessmentType: "individual",
                grade: "HS",
                periods: [
                  {
                    name: "All",
                    benchmarkPeriodId: "allPeriods",
                    values: [31, 65, 225]
                  }
                ]
              }
            ]
          },
          {
            name: "Number Errors",
            externalId: "number_errors",
            targets: []
          },
          {
            name: "Accuracy",
            externalId: "accuracy",
            targets: []
          },
          {
            name: "Total Items",
            externalId: "total_items",
            targets: []
          }
        ]
      }
    ],
    monitorAssessmentMeasure: "37"
  },
  {
    _id: "pexzg5K88e3XBHKYi",
    name: "Subtraction 0-9",
    associatedGrades: ["01", "02", "03", "04", "05", "06", "07", "08", "HS"],
    strands: [
      {
        name: "Overall",
        scores: [
          {
            name: "Number Correct",
            externalId: "number_correct",
            targets: [
              {
                grade: "01",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [20, 40, 225]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [20, 40, 225]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [20, 40, 225]
                  }
                ]
              },
              {
                grade: "02",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [20, 40, 225]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [20, 40, 225]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [20, 40, 225]
                  }
                ]
              },
              {
                grade: "03",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [20, 40, 225]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [20, 40, 225]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [20, 40, 225]
                  }
                ]
              },
              {
                grade: "04",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [40, 80, 225]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [40, 80, 225]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [40, 80, 225]
                  }
                ]
              },
              {
                grade: "05",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [20, 40, 225]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [20, 40, 225]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [20, 40, 225]
                  }
                ]
              },
              {
                grade: "06",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [40, 80, 300]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [40, 80, 300]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [40, 80, 300]
                  }
                ]
              },
              {
                grade: "07",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [50, 100, 300]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [50, 100, 300]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [50, 100, 300]
                  }
                ]
              },
              {
                grade: "08",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [50, 100, 300]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [50, 100, 300]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [50, 100, 300]
                  }
                ]
              },
              {
                assessmentType: "individual",
                grade: "HS",
                periods: [
                  {
                    name: "All",
                    benchmarkPeriodId: "allPeriods",
                    values: [50, 100, 300]
                  }
                ]
              }
            ]
          },
          {
            name: "Number Errors",
            externalId: "number_errors",
            targets: []
          },
          {
            name: "Accuracy",
            externalId: "accuracy",
            targets: []
          },
          {
            name: "Total Items",
            externalId: "total_items",
            targets: []
          }
        ]
      }
    ],
    monitorAssessmentMeasure: "50"
  },
  {
    _id: "yxBaWDvbNjBJLcaHQ",
    name: "Sums to 6",
    associatedGrades: ["01", "02", "03", "04", "05", "06", "07", "08", "HS"],
    strands: [
      {
        name: "Overall",
        scores: [
          {
            name: "Number Correct",
            externalId: "number_correct",
            targets: [
              {
                grade: "01",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [20, 40, 300]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [20, 40, 300]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [20, 40, 300]
                  }
                ]
              },
              {
                grade: "02",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [20, 40, 300]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [20, 40, 300]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [20, 40, 300]
                  }
                ]
              },
              {
                grade: "03",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [20, 40, 300]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [20, 40, 300]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [20, 40, 300]
                  }
                ]
              },
              {
                grade: "04",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [20, 40, 300]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [40, 80, 300]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [20, 40, 300]
                  }
                ]
              },
              {
                grade: "05",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [20, 40, 300]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [20, 40, 300]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [20, 40, 300]
                  }
                ]
              },
              {
                grade: "06",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [40, 80, 300]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [40, 80, 300]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [40, 80, 300]
                  }
                ]
              },
              {
                grade: "07",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [50, 100, 300]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [50, 100, 300]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [50, 100, 300]
                  }
                ]
              },
              {
                grade: "08",
                periods: [
                  {
                    name: "Fall",
                    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
                    values: [50, 100, 300]
                  },
                  {
                    name: "Spring",
                    benchmarkPeriodId: "cjCMnZKARBJmG8suT",
                    values: [50, 100, 300]
                  },
                  {
                    name: "Winter",
                    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
                    values: [50, 100, 300]
                  }
                ]
              },
              {
                assessmentType: "individual",
                grade: "HS",
                periods: [
                  {
                    name: "All",
                    benchmarkPeriodId: "allPeriods",
                    values: [50, 100, 300]
                  }
                ]
              }
            ]
          },
          {
            name: "Number Errors",
            externalId: "number_errors",
            targets: []
          },
          {
            name: "Accuracy",
            externalId: "accuracy",
            targets: []
          },
          {
            name: "Total Items",
            externalId: "total_items",
            targets: []
          }
        ]
      }
    ],
    monitorAssessmentMeasure: "21"
  }
];
export const interventions = [
  {
    _id: "56attvmgjtrjMsFif",
    name: "Intervention Adviser - Cover Copy and Compare"
  },
  {
    _id: "GBqyqxcE6oA6Lo2X7",
    name: "Intervention Adviser - Timed Trial"
  },
  {
    _id: "JC4A2Jx6gKfqLe9LF",
    name: "Intervention Adviser - Guided Practice"
  },
  {
    _id: "YAfRn9TocxMptjqkF",
    name: "Intervention Adviser - Response Cards"
  },
  {
    _id: "oRLcohGFunw5ZqoAN",
    name: "Intervention Adviser - Bingo"
  }
];
export const rules = [
  {
    _id: "e422f42ed22144",
    rootRuleId: "e422f42ed22143",
    attributeValues: {
      grade: "01",
      benchmarkPeriod: "spring-period",
      assessmentId: "ZtJxX6rjuoRwAqRFo"
    },
    outcomes: {
      above: {
        assessmentId: "pexzg5K88e3XBHKYi",
        interventionIds: []
      },
      at: {
        assessmentId: "ZtJxX6rjuoRwAqRFo",
        interventionIds: ["YAfRn9TocxMptjqkF", "GBqyqxcE6oA6Lo2X7"]
      },
      below: {
        assessmentId: "yxBaWDvbNjBJLcaHQ",
        interventionIds: []
      }
    }
  },
  {
    _id: "e422f42ed22143",
    attributeValues: {
      grade: "01",
      benchmarkPeriod: "spring-period",
      assessmentId: "FxYDJWbMdT8QiZwcD"
    },
    outcomes: {
      above: null,
      at: {
        assessmentId: "FxYDJWbMdT8QiZwcD",
        interventionIds: ["GBqyqxcE6oA6Lo2X7"]
      },
      below: {
        assessmentId: "ZtJxX6rjuoRwAqRFo",
        interventionIds: []
      }
    },
    rootRuleId: "e422f42ed22143"
  },
  {
    _id: "e422f42ed22147",
    rootRuleId: "e422f42ed22143",
    attributeValues: {
      grade: "01",
      benchmarkPeriod: "spring-period",
      assessmentId: "J7J7fw6x3CsYLHHx7"
    },
    outcomes: {
      above: {
        assessmentId: "pexzg5K88e3XBHKYi",
        interventionIds: ["56attvmgjtrjMsFif", "JC4A2Jx6gKfqLe9LF"]
      },
      at: {
        assessmentId: "J7J7fw6x3CsYLHHx7",
        interventionIds: ["YAfRn9TocxMptjqkF", "GBqyqxcE6oA6Lo2X7"]
      },
      below: {
        assessmentId: "J7J7fw6x3CsYLHHx7",
        interventionIds: ["oRLcohGFunw5ZqoAN", "56attvmgjtrjMsFif", "JC4A2Jx6gKfqLe9LF"]
      }
    }
  },
  {
    _id: "e422f42ed22145",
    rootRuleId: "e422f42ed22143",
    attributeValues: {
      grade: "01",
      benchmarkPeriod: "spring-period",
      assessmentId: "yxBaWDvbNjBJLcaHQ"
    },
    outcomes: {
      above: {
        assessmentId: "ZtJxX6rjuoRwAqRFo",
        interventionIds: ["56attvmgjtrjMsFif", "JC4A2Jx6gKfqLe9LF"]
      },
      at: {
        assessmentId: "yxBaWDvbNjBJLcaHQ",
        interventionIds: ["YAfRn9TocxMptjqkF", "GBqyqxcE6oA6Lo2X7"]
      },
      below: {
        assessmentId: "yxBaWDvbNjBJLcaHQ",
        interventionIds: ["oRLcohGFunw5ZqoAN", "56attvmgjtrjMsFif", "JC4A2Jx6gKfqLe9LF"]
      }
    }
  },
  {
    _id: "e422f42ed22146",
    rootRuleId: "e422f42ed22143",
    attributeValues: {
      grade: "01",
      benchmarkPeriod: "spring-period",
      assessmentId: "pexzg5K88e3XBHKYi"
    },
    outcomes: {
      above: {
        assessmentId: "FxYDJWbMdT8QiZwcD",
        interventionIds: ["56attvmgjtrjMsFif", "JC4A2Jx6gKfqLe9LF"]
      },
      at: {
        assessmentId: "pexzg5K88e3XBHKYi",
        interventionIds: ["YAfRn9TocxMptjqkF", "GBqyqxcE6oA6Lo2X7"]
      },
      below: {
        assessmentId: "J7J7fw6x3CsYLHHx7",
        interventionIds: []
      }
    }
  }
];
export const expectedTreeDisplayData = [
  {
    name: "AM 51- FF: Add/Subtr 0-9",
    fullName: "Fact Families: Add/Subtract 0-9",
    assessmentId: "FxYDJWbMdT8QiZwcD",
    ruleId: "e422f42ed22143",
    isRoot: true,
    children: [
      {
        name: "AM 37- Sums to 12",
        fullName: "Sums to 12",
        assessmentId: "ZtJxX6rjuoRwAqRFo",
        ruleId: "e422f42ed22144",
        outcome: "below",
        children: [
          {
            name: "AM 21- Sums to 6",
            fullName: "Sums to 6",
            assessmentId: "yxBaWDvbNjBJLcaHQ",
            ruleId: "e422f42ed22145",
            outcome: "below",
            children: [
              {
                name: "AM 21- Sums to 6",
                fullName: "Sums to 6",
                assessmentId: "yxBaWDvbNjBJLcaHQ",
                outcome: "below",
                ruleId: null,
                isEnding: true,
                interventionIds: [
                  "oRLcohGFunw5ZqoAN", // BINGO
                  "56attvmgjtrjMsFif", // CCC
                  "JC4A2Jx6gKfqLe9LF" // GP
                ]
              },
              {
                name: "AM 21- Sums to 6",
                fullName: "Sums to 6",
                assessmentId: "yxBaWDvbNjBJLcaHQ",
                outcome: "at",
                ruleId: null,
                isEnding: true,
                interventionIds: [
                  "YAfRn9TocxMptjqkF", // RC
                  "GBqyqxcE6oA6Lo2X7" // TT
                ]
              },
              {
                name: "AM 37- Sums to 12",
                fullName: "Sums to 12",
                assessmentId: "ZtJxX6rjuoRwAqRFo",
                outcome: "above",
                ruleId: null,
                isEnding: true,
                interventionIds: [
                  "56attvmgjtrjMsFif", // CCC
                  "JC4A2Jx6gKfqLe9LF" //GP
                ]
              }
            ]
          },
          {
            name: "AM 37- Sums to 12",
            fullName: "Sums to 12",
            assessmentId: "ZtJxX6rjuoRwAqRFo",
            ruleId: null,
            isEnding: true,
            outcome: "at",
            interventionIds: [
              "YAfRn9TocxMptjqkF",
              "GBqyqxcE6oA6Lo2X7" //TT
            ]
          },
          {
            name: "AM 50- Subtr 0-9",
            fullName: "Subtraction 0-9",
            assessmentId: "pexzg5K88e3XBHKYi",
            ruleId: "e422f42ed22146",
            outcome: "above",
            children: [
              {
                name: "AM 22- Subtr 0-5",
                fullName: "Subtraction 0-5",
                assessmentId: "J7J7fw6x3CsYLHHx7",
                outcome: "below",
                ruleId: "e422f42ed22147",
                children: [
                  {
                    name: "AM 22- Subtr 0-5",
                    fullName: "Subtraction 0-5",
                    assessmentId: "J7J7fw6x3CsYLHHx7",
                    outcome: "below",
                    ruleId: null,
                    isEnding: true,
                    interventionIds: [
                      "oRLcohGFunw5ZqoAN", // BINGO
                      "56attvmgjtrjMsFif", // CCC
                      "JC4A2Jx6gKfqLe9LF" // GP
                    ]
                  },
                  {
                    name: "AM 22- Subtr 0-5",
                    fullName: "Subtraction 0-5",
                    assessmentId: "J7J7fw6x3CsYLHHx7",
                    outcome: "at",
                    ruleId: null,
                    isEnding: true,
                    interventionIds: [
                      "YAfRn9TocxMptjqkF", // RC
                      "GBqyqxcE6oA6Lo2X7" // TT
                    ]
                  },
                  {
                    name: "AM 50- Subtr 0-9",
                    fullName: "Subtraction 0-9",
                    assessmentId: "pexzg5K88e3XBHKYi",
                    outcome: "above",
                    ruleId: null,
                    isEnding: true,
                    interventionIds: [
                      "56attvmgjtrjMsFif", // CCC
                      "JC4A2Jx6gKfqLe9LF" //GP
                    ]
                  }
                ]
              },
              {
                name: "AM 50- Subtr 0-9",
                fullName: "Subtraction 0-9",
                assessmentId: "pexzg5K88e3XBHKYi",
                outcome: "at",
                ruleId: null,
                isEnding: true,
                interventionIds: [
                  "YAfRn9TocxMptjqkF", // RC
                  "GBqyqxcE6oA6Lo2X7" // TT
                ]
              },
              {
                name: "AM 51- FF: Add/Subtr 0-9",
                fullName: "Fact Families: Add/Subtract 0-9",
                assessmentId: "FxYDJWbMdT8QiZwcD",
                outcome: "above",
                ruleId: null,
                isEnding: true,
                interventionIds: [
                  "56attvmgjtrjMsFif", // CCC
                  "JC4A2Jx6gKfqLe9LF" // GP
                ]
              }
            ]
          }
        ]
      },
      {
        name: "AM 51- FF: Add/Subtr 0-9",
        fullName: "Fact Families: Add/Subtract 0-9",
        assessmentId: "FxYDJWbMdT8QiZwcD",
        ruleId: null,
        isEnding: true,
        outcome: "at",
        interventionIds: [
          "GBqyqxcE6oA6Lo2X7" // TT
        ]
      },
      {
        name: "Passed...",
        outcome: "above",
        isEnding: true,
        isComplete: true
      }
    ]
  }
];
