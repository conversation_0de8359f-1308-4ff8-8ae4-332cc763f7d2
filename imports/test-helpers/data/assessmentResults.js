const assessmentResults = {
  _id: "AqtXLWi4acMxjxSYQ",
  studentGroupId: "test_student_group_1",
  schoolYear: 2017,
  benchmarkPeriodId: "8S52Gz5o85hRkECgq",
  classwideResults: {
    percentMeetingTarget: 78,
    percentAtRisk: 22,
    totalStudentsMeetingAllTargets: 7,
    totalStudentsAssessedOnAllMeasures: 9,
    studentIdsNotMeetingTarget: ["<PERSON><PERSON><PERSON>_TEST_STUDENT", "<PERSON>_<PERSON>lny_TEST_STUDENT"]
  },
  measures: [
    {
      assessmentId: "add_three_digit_with_and_without_regrouping",
      assessmentName: "Add 3-Digit w & w/o Regrouping",
      cutoffTarget: 11,
      medianScore: 31,
      studentScores: [18, 22, 25, 28, 31, 33, 34, 42, 49],
      percentMeetingTarget: 100,
      numberMeetingTarget: 9,
      totalStudentsAssessed: 9,
      studentResults: [
        {
          studentId: "Bela_Jenkins_TEST_STUDENT",
          status: "COMPLETE",
          firstName: "Bela",
          lastName: "<PERSON>",
          score: "34",
          meetsTarget: true
        },
        {
          studentId: "Bela_Osbourne_TEST_STUDENT",
          status: "COMPLETE",
          firstName: "Bela",
          lastName: "Osbourne",
          score: "22",
          meetsTarget: true
        },
        {
          studentId: "Bela_Pikelny_TEST_STUDENT",
          status: "COMPLETE",
          firstName: "Bela",
          lastName: "Pikelny",
          score: "49",
          meetsTarget: true
        },
        {
          studentId: "Bela_Scruggs_TEST_STUDENT",
          status: "CANCELLED",
          firstName: "Bela",
          lastName: "Scruggs",
          score: "",
          meetsTarget: null
        }
      ]
    },
    {
      assessmentId: "fact_fam_add_sub_0_20",
      assessmentName: "Fact Families: Add/Subtract 0-20",
      cutoffTarget: 28,
      medianScore: 38,
      studentScores: [14, 24, 30, 35, 38, 38, 38, 43, 45],
      percentMeetingTarget: 78,
      numberMeetingTarget: 7,
      totalStudentsAssessed: 9,
      studentResults: [
        {
          studentId: "Bela_Jenkins_TEST_STUDENT",
          status: "COMPLETE",
          firstName: "Bela",
          lastName: "Jenkins",
          score: "38",
          meetsTarget: true
        },
        {
          studentId: "Bela_Osbourne_TEST_STUDENT",
          status: "COMPLETE",
          firstName: "Bela",
          lastName: "Osbourne",
          score: "30",
          meetsTarget: true
        },
        {
          studentId: "Bela_Pikelny_TEST_STUDENT",
          status: "COMPLETE",
          firstName: "Bela",
          lastName: "Pikelny",
          score: "38",
          meetsTarget: true
        },
        {
          studentId: "Bela_Scruggs_TEST_STUDENT",
          status: "CANCELLED",
          firstName: "Bela",
          lastName: "Scruggs",
          score: "",
          meetsTarget: null
        }
      ]
    },
    {
      assessmentId: "three_digit_subtraction_with_and_without_regrouping",
      assessmentName: "3-Digit Subtraction w & w/o Regrouping",
      cutoffTarget: 15,
      medianScore: 30,
      studentScores: [15, 23, 25, 30, 30, 31, 31, 34, 44],
      percentMeetingTarget: 100,
      numberMeetingTarget: 9,
      totalStudentsAssessed: 9,
      studentResults: [
        {
          studentId: "Bela_Jenkins_TEST_STUDENT",
          status: "COMPLETE",
          firstName: "Bela",
          lastName: "Jenkins",
          score: "30",
          meetsTarget: true
        },
        {
          studentId: "Bela_Osbourne_TEST_STUDENT",
          status: "COMPLETE",
          firstName: "Bela",
          lastName: "Osbourne",
          score: "15",
          meetsTarget: true
        },
        {
          studentId: "Bela_Pikelny_TEST_STUDENT",
          status: "COMPLETE",
          firstName: "Bela",
          lastName: "Pikelny",
          score: "34",
          meetsTarget: true
        },
        {
          studentId: "Bela_Scruggs_TEST_STUDENT",
          status: "CANCELLED",
          firstName: "Bela",
          lastName: "Scruggs",
          score: "",
          meetsTarget: null
        }
      ]
    }
  ]
};

export default assessmentResults;
