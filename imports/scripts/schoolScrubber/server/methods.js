import { Meteor } from "meteor/meteor";
import { check, Match } from "meteor/check";

import { runSchoolScrubber } from "../schoolScrubber";
import * as auth from "/imports/api/authorization/server/methods";

Meteor.methods({
  async "Script:runSchoolScrubber"({ targetOrgId, schoolYear }) {
    check(targetOrgId, String);
    check(schoolYear, Match.Maybe(Number));

    if (!this.userId) {
      throw new Meteor.Error("403", "No logged in user found!");
    }
    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin"], {
        userId: this.userId
      }))
    ) {
      throw new Meteor.Error("403", "You are not authorized to run school scrubber");
    }
    let result;
    try {
      result = await runSchoolScrubber({ targetOrgId, schoolYear });
    } catch (e) {
      throw new Meteor.Error(e.message);
    }
    return result;
  }
});
