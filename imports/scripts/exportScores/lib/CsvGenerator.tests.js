import CsvGenerator from "./CsvGenerator";

describe("CsvGenerator", () => {
  describe("generateCsv data", () => {
    it("should generate correct data", () => {
      const params = { TYPE: "classwide" };
      const rows = {
        2017: {
          studentId1: {
            getOutputFields: () => ({
              "commaSeparated,row1": "commaSeparated,value1",
              row2: null
            })
          }
        }
      };
      const csvGenerator = new CsvGenerator(params);

      const csvContent = csvGenerator.generateCsvContent(rows);

      const expectedCsvContent = ['"commaSeparated,row1",row2,\r\n', '"commaSeparated,value1",N/A,\r\n'];
      expect(csvContent).toEqual(expectedCsvContent);
    });
  });
  describe("generateFilename", () => {
    it("should generate a correct filename", () => {
      const params = { TYPE: "classwide" };
      const currentDate = new Date().toISOString().slice(0, 10);
      const csvGenerator = new CsvGenerator(params);

      const filename = csvGenerator.generateFilename();

      expect(filename).toEqual(`scoresHistory-allYears-allOrgs-classwide-${currentDate}.csv`);
    });
  });
  describe("saveCsv", () => {
    it("should save csv data to file", () => {
      const params = { TYPE: "classwide" };
      const fs = require("fs");
      fs.writeFileSync = jest.fn();
      const csvData = "testData";
      const csvGenerator = new CsvGenerator(params);
      const filename = csvGenerator.generateFilename();

      csvGenerator.saveCsv(csvData);

      expect(fs.writeFileSync).toHaveBeenCalledWith(filename, csvData);
    });
  });
});
