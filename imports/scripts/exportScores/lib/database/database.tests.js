import { MongoMemoryServer } from "mongodb-memory-server";
import { MongoClient } from "mongodb";
import Database from "./database";

let mongoUrl;
let database;
let mongod;
let setupDbConnection;

describe("Database", () => {
  beforeAll(async () => {
    jest.setTimeout(60000);
    mongod = await MongoMemoryServer.create();

    mongoUrl = mongod.getUri();

    setupDbConnection = await MongoClient.connect(mongoUrl, { useUnifiedTopology: true });
    const db = setupDbConnection.db(setupDbConnection.s.options.dbName);

    const studentsCollection = db.collection("Students");
    await studentsCollection.insertMany([
      {
        _id: "1",
        identity: {},
        demographic: {},
        orgid: "12345",
        schoolYear: 2017,
        history: [
          {
            type: "classwide"
          }
        ]
      },
      {
        _id: "2",
        identity: {},
        demographic: {},
        orgid: "54321",
        schoolYear: 2017,

        history: [
          {
            type: "individual"
          }
        ]
      },
      {
        _id: "3",
        identity: {},
        demographic: {},
        orgid: "12345",
        schoolYear: 2017,
        history: [
          {
            type: "individual"
          }
        ]
      }
    ]);

    const organizationsCollection = db.collection("Organizations");
    await organizationsCollection.insertMany([
      { _id: "12345", name: "testOrg1" },
      { _id: "54321", name: "testOrg2" }
    ]);

    const sitesCollection = db.collection("Sites");
    await sitesCollection.insertMany([
      { _id: "site12345", orgid: "12345", name: "testSite1" },
      { _id: "site54321", orgid: "12345", name: "testSite2" }
    ]);

    const usersCollection = db.collection("users");
    await usersCollection.insertMany([
      {
        _id: "user12345",
        profile: {
          orgid: "12345",
          siteAccess: [{ role: "arbitraryIdteacher" }],
          name: { first: "FirstTest1Name", last: "LastTest1Name" }
        }
      },
      {
        _id: "user54321",
        profile: {
          orgid: "12345",
          siteAccess: [{ role: "arbitraryIdadmin" }],
          name: { first: "FirstTest2Name", last: "LastTest2Name" }
        }
      }
    ]);

    const studentGroupEnrollmentsCollection = db.collection("StudentGroupEnrollments");
    await studentGroupEnrollmentsCollection.insertMany([
      {
        _id: "enroll1",
        orgid: "12345",
        schoolYear: 2017,
        studentGroupId: "group12345",
        studentId: "1",
        title1: true,
        freeReducedLunch: true
      },
      {
        _id: "enroll2",
        orgid: "54321",
        schoolYear: 2017,
        studentGroupId: "group54321",
        studentId: "2",
        title1: false,
        freeReducedLunch: true
      },
      {
        _id: "enroll3",
        orgid: "12345",
        schoolYear: 2017,
        studentGroupId: "group12345",
        studentId: "3",
        title1: true,
        freeReducedLunch: false
      }
    ]);
    const benchmarkPeriodsCollection = db.collection("BenchmarkPeriods");
    await benchmarkPeriodsCollection.insertMany([
      {
        _id: "nEsbWokBWutTZFkTh",
        name: "Winter"
      },
      {
        _id: "8S52Gz5o85hRkECgq",
        name: "Fall"
      },
      {
        _id: "cjCMnZKARBJmG8suT",
        name: "Spring"
      }
    ]);

    const studentGroupsCollection = db.collection("StudentGroups");
    await studentGroupsCollection.insertMany([
      {
        _id: "1",
        schoolYear: 2017,
        orgid: "12345",
        siteId: "site12345",
        ownerIds: "user12345",
        sectionId: "group1",
        name: "TestStudentGroup1",
        grade: "02",
        history: [
          {
            type: "classwide"
          }
        ]
      },
      {
        _id: "2",
        schoolYear: 2017,
        orgid: "54321",
        siteId: "site54321",
        ownerIds: "user12345",
        sectionId: "group2",
        name: "TestStudentGroup2",
        grade: "03",
        history: [
          {
            type: "benchmark"
          }
        ]
      },
      {
        _id: "3",
        schoolYear: 2018,
        orgid: "12345",
        siteId: "site12345",
        ownerIds: "user12345",
        sectionId: "group3",
        name: "TestStudentGroup3",
        grade: "04",
        history: [
          {
            type: "benchmark"
          }
        ]
      },
      {
        _id: "4",
        schoolYear: 2018,
        orgid: "54321",
        siteId: "site54321",
        ownerIds: "user12345",
        sectionId: "group4",
        name: "TestStudentGroup4",
        grade: "04",
        history: [
          {
            type: "benchmark"
          }
        ]
      },
      {
        _id: "5",
        schoolYear: 2017,
        orgid: "12345",
        siteId: "site12345",
        ownerIds: "user54321",
        sectionId: "group2",
        name: "TestStudentGroup2",
        grade: "03",
        history: [
          {
            type: "benchmark"
          }
        ]
      }
    ]);

    const assessmentResultsCollection = db.collection("AssessmentResults");
    await assessmentResultsCollection.insertMany([
      {
        _id: "1",
        orgid: "12345",
        schoolYear: 2017,
        studentGroupId: "1",
        type: "classwide",
        created: { on: 1483452632 }
      },
      {
        _id: "2",
        orgid: "12345",
        schoolYear: 2017,
        studentGroupId: "1",
        type: "individual",
        created: { on: 1483452632 }
      },
      {
        _id: "3",
        orgid: "12345",
        schoolYear: 2017,
        studentGroupId: "1",
        type: "benchmark",
        created: { on: 1483452632 }
      }
    ]);
  });
  afterAll(async () => {
    jest.setTimeout(10000);
    try {
      if (setupDbConnection) {
        await setupDbConnection.close();
        setupDbConnection = null;
      }
    } catch (error) {
      console.warn("Error closing setup DB connection:", error.message);
    }
    try {
      if (mongod) {
        await mongod.stop({ doCleanup: true });
        mongod = null;
      }
    } catch (error) {
      console.warn("Error stopping mongod:", error.message);
    }
    // Give some time for cleanup
    await new Promise(resolve => setTimeout(resolve, 100));
  });
  beforeEach(async () => {
    database = new Database(mongoUrl);
    await database.init();
  });
  afterEach(async () => {
    try {
      if (database?.dbConnection) {
        await database.closeConnection();
      }
    } catch (error) {
      console.warn("Error closing database connection:", error.message);
    }
    database = null;
  });
  describe("constructor", () => {
    it("should set mongoUrl", () => {
      const fakeMongoUrl = "mongoUrl";

      const newDatabase = new Database(fakeMongoUrl);

      expect(newDatabase.mongoUrl).toEqual(fakeMongoUrl);
    });
    it("should set db objects to null", () => {
      const newDatabase = new Database();

      expect(newDatabase.db).toEqual(null);
      expect(newDatabase.studentGroupsCollection).toEqual(null);
      expect(newDatabase.studentsCollection).toEqual(null);
      expect(newDatabase.usersCollection).toEqual(null);
      expect(newDatabase.organizationsCollection).toEqual(null);
      expect(newDatabase.sitesCollection).toEqual(null);
      expect(newDatabase.studentGroupEnrollmentsCollection).toEqual(null);
      expect(newDatabase.benchmarkPeriodsCollection).toEqual(null);
      expect(newDatabase.assessmentResultsCollection).toEqual(null);
    });
  });
  describe("init", () => {
    it("should connect to db", async () => {
      expect(database.dbConnection).not.toEqual(null);
      expect(database.db).not.toEqual(null);
      expect(database.studentGroupsCollection).not.toEqual(null);
      expect(database.studentsCollection).not.toEqual(null);
      expect(database.usersCollection).not.toEqual(null);
      expect(database.organizationsCollection).not.toEqual(null);
      expect(database.sitesCollection).not.toEqual(null);
      expect(database.studentGroupEnrollmentsCollection).not.toEqual(null);
      expect(database.benchmarkPeriodsCollection).not.toEqual(null);
      expect(database.assessmentResultsCollection).not.toEqual(null);
    });
  });
  describe("getStudents", () => {
    it("should return correct data for TYPE benchmark and empty ORGID", async () => {
      const students = await database.getStudents({ TYPE: "benchmark", SCHOOLYEAR: 2017 });

      expect(students[0]).toMatchObject({ _id: "1", identity: {}, demographic: {} });
      expect(students[0]).not.toHaveProperty("orgid");
      expect(students[0]).not.toHaveProperty("history");
    });
    it("should return correct data for TYPE benchmark and set ORGID", async () => {
      const students = await database.getStudents({ TYPE: "benchmark", ORGID: "54321", SCHOOLYEAR: 2017 });

      expect(students.length).toEqual(1);
      expect(students[0]).toMatchObject({ _id: "2", identity: {}, demographic: {} });
      expect(students[0]).not.toHaveProperty("orgid");
      expect(students[0]).not.toHaveProperty("history");
    });
    it("should return correct data for TYPE classwide and empty ORGID", async () => {
      const students = await database.getStudents({ TYPE: "classwide", SCHOOLYEAR: 2017 });

      expect(students[0]).toMatchObject({ _id: "1", identity: {}, demographic: {} });
      expect(students[0]).not.toHaveProperty("orgid");
      expect(students[0]).not.toHaveProperty("history");
    });
    it("should return correct data for TYPE classwide and set ORGID", async () => {
      const students = await database.getStudents({ TYPE: "classwide", ORGID: "54321", SCHOOLYEAR: 2017 });

      expect(students.length).toEqual(1);
      expect(students[0]).toMatchObject({ _id: "2", identity: {}, demographic: {} });
      expect(students[0]).not.toHaveProperty("orgid");
      expect(students[0]).not.toHaveProperty("history");
    });
    it("should return correct data for TYPE individual and empty ORGID", async () => {
      const students = await database.getStudents({ TYPE: "individual", SCHOOLYEAR: 2017 });

      expect(students.length).toEqual(2);
      expect(students[0]).toMatchObject({ _id: "2", identity: {}, demographic: {}, history: [{ type: "individual" }] });
      expect(students[0]).not.toHaveProperty("orgid");
    });
    it("should return correct data for TYPE individual and set ORGID", async () => {
      const students = await database.getStudents({ TYPE: "individual", ORGID: "54321", SCHOOLYEAR: 2017 });

      expect(students.length).toEqual(1);
      expect(students[0]).toMatchObject({ _id: "2", identity: {}, demographic: {}, history: [{ type: "individual" }] });
      expect(students[0]).not.toHaveProperty("orgid");
    });
  });
  describe("getOrganization", () => {
    it("should return correct data", async () => {
      const organization = await database.getOrganization({ ORGID: "12345" });

      const expectedData = { _id: "12345", name: "testOrg1" };
      expect(organization).toEqual(expectedData);
    });
  });
  describe("getSites", () => {
    it("should return correct data", async () => {
      const sites = await database.getSites({ ORGID: "12345" });

      const expectedData = [
        { _id: "site12345", name: "testSite1" },
        { _id: "site54321", name: "testSite2" }
      ];
      expect(sites).toEqual(expectedData);
    });
  });
  describe("getUsers", () => {
    it("should return correct data", async () => {
      const users = await database.getUsers({ ORGID: "12345" });

      const expectedData = [
        {
          _id: "user12345",
          profile: {
            orgid: "12345",
            siteAccess: [{ role: "arbitraryIdteacher" }],
            name: { first: "FirstTest1Name", last: "LastTest1Name" }
          }
        },
        {
          _id: "user54321",
          profile: {
            orgid: "12345",
            siteAccess: [{ role: "arbitraryIdadmin" }],
            name: { first: "FirstTest2Name", last: "LastTest2Name" }
          }
        }
      ];
      expect(users).toEqual(expectedData);
    });
  });
  describe("getStudentGroupEnrollments", () => {
    it("should return correct data", async () => {
      const studentGroupEnrollments = await database.getStudentGroupEnrollments({ ORGID: "12345", SCHOOLYEAR: 2017 });

      const expectedData = [
        {
          _id: "enroll1",
          studentGroupId: "group12345",
          studentId: "1"
        },
        {
          _id: "enroll3",
          studentGroupId: "group12345",
          studentId: "3"
        }
      ];
      expect(studentGroupEnrollments).toEqual(expectedData);
    });
  });
  describe("getBenchmarkPeriods", () => {
    it("should return correct data", async () => {
      const benchmarkPeriods = await database.getBenchmarkPeriods();

      const expectedData = [
        { _id: "nEsbWokBWutTZFkTh", name: "Winter" },
        { _id: "8S52Gz5o85hRkECgq", name: "Fall" },
        { _id: "cjCMnZKARBJmG8suT", name: "Spring" }
      ];
      expect(benchmarkPeriods).toEqual(expectedData);
    });
  });
  describe("getStudentGroups", () => {
    it("should throw an exception when type is not passed", async () => {
      const expectedErrorMessage = "Type must be defined";

      expect.assertions(1);
      return expect(database.getStudentGroups({})).rejects.toEqual(new Error(expectedErrorMessage));
    });
    it("should return correct data for TYPE benchmark and empty ORGID and empty SCHOOLYEAR", async () => {
      const studentGroups = await database.getStudentGroups({ TYPE: "benchmark" });

      const expectedData = [
        {
          _id: "1",
          grade: "02",
          history: [{ type: "classwide" }], // student groups with classwide interventions are needed for SPRIN-2399
          name: "TestStudentGroup1",
          orgid: "12345",
          ownerIds: "user12345",
          schoolYear: 2017,
          sectionId: "group1",
          siteId: "site12345"
        },
        {
          _id: "2",
          grade: "03",
          history: [{ type: "benchmark" }],
          name: "TestStudentGroup2",
          orgid: "54321",
          ownerIds: "user12345",
          schoolYear: 2017,
          sectionId: "group2",
          siteId: "site54321"
        },
        {
          _id: "5",
          grade: "03",
          history: [{ type: "benchmark" }],
          name: "TestStudentGroup2",
          orgid: "12345",
          ownerIds: "user54321",
          schoolYear: 2017,
          sectionId: "group2",
          siteId: "site12345"
        },
        {
          _id: "3",
          grade: "04",
          history: [{ type: "benchmark" }],
          name: "TestStudentGroup3",
          orgid: "12345",
          ownerIds: "user12345",
          schoolYear: 2018,
          sectionId: "group3",
          siteId: "site12345"
        },
        {
          _id: "4",
          grade: "04",
          history: [{ type: "benchmark" }],
          name: "TestStudentGroup4",
          orgid: "54321",
          ownerIds: "user12345",
          schoolYear: 2018,
          sectionId: "group4",
          siteId: "site54321"
        }
      ];
      expect(studentGroups).toEqual(expectedData);
    });
    it("should return correct data for TYPE classwide and empty ORGID and empty SCHOOLYEAR", async () => {
      const studentGroups = await database.getStudentGroups({ TYPE: "classwide" });

      const expectedData = [
        {
          _id: "1",
          grade: "02",
          history: [{ type: "classwide" }],
          name: "TestStudentGroup1",
          orgid: "12345",
          ownerIds: "user12345",
          schoolYear: 2017,
          sectionId: "group1",
          siteId: "site12345"
        }
      ];
      expect(studentGroups).toEqual(expectedData);
    });
    it("should return correct data for TYPE individual and empty ORGID and empty SCHOOLYEAR", async () => {
      const studentGroups = await database.getStudentGroups({ TYPE: "individual" });

      const expectedData = [
        {
          _id: "1",
          grade: "02",
          name: "TestStudentGroup1",
          orgid: "12345",
          ownerIds: "user12345",
          schoolYear: 2017,
          sectionId: "group1",
          siteId: "site12345"
        },
        {
          _id: "2",
          grade: "03",
          name: "TestStudentGroup2",
          orgid: "54321",
          ownerIds: "user12345",
          schoolYear: 2017,
          sectionId: "group2",
          siteId: "site54321"
        },
        {
          _id: "5",
          grade: "03",
          name: "TestStudentGroup2",
          orgid: "12345",
          ownerIds: "user54321",
          schoolYear: 2017,
          sectionId: "group2",
          siteId: "site12345"
        },
        {
          _id: "3",
          grade: "04",
          name: "TestStudentGroup3",
          orgid: "12345",
          ownerIds: "user12345",
          schoolYear: 2018,
          sectionId: "group3",
          siteId: "site12345"
        },
        {
          _id: "4",
          grade: "04",
          name: "TestStudentGroup4",
          orgid: "54321",
          ownerIds: "user12345",
          schoolYear: 2018,
          sectionId: "group4",
          siteId: "site54321"
        }
      ];
      expect(studentGroups).toEqual(expectedData);
    });
    it("should return correct data for TYPE benchmark and set ORGID and empty SCHOOLYEAR", async () => {
      const studentGroups = await database.getStudentGroups({ TYPE: "benchmark", ORGID: "54321" });

      const expectedData = [
        {
          _id: "2",
          grade: "03",
          history: [{ type: "benchmark" }],
          name: "TestStudentGroup2",
          orgid: "54321",
          ownerIds: "user12345",
          schoolYear: 2017,
          sectionId: "group2",
          siteId: "site54321"
        },
        {
          _id: "4",
          grade: "04",
          history: [{ type: "benchmark" }],
          name: "TestStudentGroup4",
          orgid: "54321",
          ownerIds: "user12345",
          schoolYear: 2018,
          sectionId: "group4",
          siteId: "site54321"
        }
      ];
      expect(studentGroups).toEqual(expectedData);
    });
    it("should return correct data for TYPE benchmark and empty ORGID and set SCHOOLYEAR", async () => {
      const studentGroups = await database.getStudentGroups({ TYPE: "benchmark", SCHOOLYEAR: "2018" });

      const expectedData = [
        {
          _id: "3",
          grade: "04",
          history: [{ type: "benchmark" }],
          name: "TestStudentGroup3",
          orgid: "12345",
          ownerIds: "user12345",
          schoolYear: 2018,
          sectionId: "group3",
          siteId: "site12345"
        },
        {
          _id: "4",
          grade: "04",
          history: [{ type: "benchmark" }],
          name: "TestStudentGroup4",
          orgid: "54321",
          ownerIds: "user12345",
          schoolYear: 2018,
          sectionId: "group4",
          siteId: "site54321"
        }
      ];
      expect(studentGroups).toEqual(expectedData);
    });
    it("should return correct data for TYPE benchmark and set ORGID and set SCHOOLYEAR", async () => {
      const studentGroups = await database.getStudentGroups({ TYPE: "benchmark", SCHOOLYEAR: "2018", ORGID: "12345" });

      const expectedData = [
        {
          _id: "3",
          grade: "04",
          history: [{ type: "benchmark" }],
          name: "TestStudentGroup3",
          orgid: "12345",
          ownerIds: "user12345",
          schoolYear: 2018,
          sectionId: "group3",
          siteId: "site12345"
        }
      ];
      expect(studentGroups).toEqual(expectedData);
    });
  });
  describe("getAssessmentResults", () => {
    it("should return this.assessmentResults with correct data", async () => {
      const assessmentResults = await database.getAssessmentResults({ ORGID: "12345", SCHOOLYEAR: 2017 });

      expect(assessmentResults.length).toEqual(3);
    });
  });
  describe("getDataFromDB", () => {
    it("should get all data needed for classwide scores export when TYPE is set to classwide", async () => {
      await database.getDataFromDB({ TYPE: "classwide", ORGID: "12345", SCHOOLYEAR: 2017 });

      expect(Object.values(database.studentById).length).toEqual(2);
      expect(Object.values(database.teacherById).length).toEqual(2);
      expect(database.district).toBeDefined();
      expect(Object.values(database.schoolById).length).toEqual(2);
      expect(database.studentGroupEnrollments.length).toEqual(2);
      expect(Object.values(database.studentGroupById).length).toEqual(1);
    });
    it("should get all data needed for benchmark scores export when TYPE is set to benchmark", async () => {
      await database.getDataFromDB({ TYPE: "benchmark", ORGID: "12345", SCHOOLYEAR: 2017 });

      expect(Object.values(database.studentById).length).toEqual(2);
      expect(Object.values(database.teacherById).length).toEqual(2);
      expect(database.district).toBeDefined();
      expect(Object.values(database.schoolById).length).toEqual(2);
      expect(database.studentGroupEnrollments.length).toEqual(2);
      expect(Object.values(database.studentGroupById).length).toEqual(2); // student groups with classwide interventions are needed for SPRIN-2399
      expect(database.benchmarkPeriods.length).toEqual(3);
      expect(Object.values(database.benchmarkAssessmentResultsByStudentGroupId).length).toEqual(1);
      expect(Object.values(database.classwideAssessmentResultsByStudentGroupId).length).toEqual(1);
    });
    it("should get all data needed for individual scores export when TYPE is set to individual", async () => {
      await database.getDataFromDB({ TYPE: "individual", ORGID: "12345", SCHOOLYEAR: 2017 });

      expect(Object.values(database.studentById).length).toEqual(1);
      expect(Object.values(database.teacherById).length).toEqual(2);
      expect(database.district).toBeDefined();
      expect(Object.values(database.schoolById).length).toEqual(2);
      expect(database.studentGroupEnrollments.length).toEqual(2);
      expect(Object.values(database.studentGroupById).length).toEqual(2);
      expect(database.benchmarkPeriods).toEqual(undefined);
      expect(Object.values(database.individualAssessmentResultsById).length).toEqual(1);
    });
  });
});
