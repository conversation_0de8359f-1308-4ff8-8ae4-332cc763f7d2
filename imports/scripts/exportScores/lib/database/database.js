import { groupBy, keyBy } from "lodash";
import MongoRepository from "./MongoRepository";
import { normalizeGrade } from "/imports/api/utilities/sortingHelpers/normalizeSortItem";
import sortByPropertyFor from "/imports/api/utilities/sortingHelpers/sortByPropertyFor";

export default class Database extends MongoRepository {
  constructor(mongoUrl) {
    super(mongoUrl);

    this.studentGroupsCollection = null;
    this.studentsCollection = null;
    this.usersCollection = null;
    this.organizationsCollection = null;
    this.sitesCollection = null;
    this.studentGroupEnrollmentsCollection = null;
    this.benchmarkPeriodsCollection = null;
    this.assessmentResultsCollection = null;
  }

  init = async () => {
    await this.connect();
    this.selectDb();
    this.studentGroupsCollection = this.db.collection("StudentGroups");
    this.studentsCollection = this.db.collection("Students");
    this.usersCollection = this.db.collection("users");
    this.organizationsCollection = this.db.collection("Organizations");
    this.sitesCollection = this.db.collection("Sites");
    this.studentGroupEnrollmentsCollection = this.db.collection("StudentGroupEnrollments");
    this.benchmarkPeriodsCollection = this.db.collection("BenchmarkPeriods");
    this.assessmentResultsCollection = this.db.collection("AssessmentResults");
  };

  getStudents = async ({ ORGID, TYPE, SCHOOLYEAR }) => {
    const query = { schoolYear: parseInt(SCHOOLYEAR) };
    const projection = {
      identity: true,
      demographic: true,
      schoolYear: true
    };
    if (ORGID) {
      query.orgid = ORGID;
    }
    if (TYPE === "individual") {
      query.history = { $elemMatch: { type: TYPE } };
      projection.history = true;
    }
    if (TYPE === "benchmark") {
      projection.currentSkill = true;
    }
    return this.studentsCollection.find(query, { projection }).toArray();
  };

  getOrganization = async ({ ORGID }) => {
    const query = {};
    if (ORGID) {
      query._id = ORGID;
    }
    return this.organizationsCollection.findOne(query, { projection: { name: true } });
  };

  getSites = async ({ ORGID }) => {
    return this.sitesCollection.find({ orgid: ORGID }, { projection: { name: true } }).toArray();
  };

  getUsers = async ({ ORGID }) => {
    return this.usersCollection
      .find(
        {
          "profile.orgid": ORGID,
          "profile.siteAccess.role": { $in: ["arbitraryIdteacher", "arbitraryIdadmin"] }
        },
        { projection: { profile: true } }
      )
      .toArray();
  };

  getStudentGroupEnrollments = async ({ ORGID, SCHOOLYEAR, TYPE }) => {
    const query = { orgid: ORGID, schoolYear: parseInt(SCHOOLYEAR) };
    // if (TYPE === "individual") {
    //   query.isActive = true;
    // }
    const studentEnrollments = await this.studentGroupEnrollmentsCollection
      .find(query, {
        projection: {
          studentGroupId: true,
          siteId: true,
          studentId: true,
          isActive: true,
          grade: true
        }
      })
      .toArray();
    if (TYPE === "individual" && studentEnrollments.length) {
      return sortByPropertyFor({
        list: studentEnrollments,
        paths: ["grade", "siteId", "studentGroupId"],
        order: 1
      });
    }
    return studentEnrollments;
  };

  getBenchmarkPeriods = async () => {
    return this.benchmarkPeriodsCollection.find({}, { projection: { name: true } }).toArray();
  };

  getStudentGroups = async ({ ORGID, TYPE, SCHOOLYEAR }) => {
    if (!TYPE) {
      throw new Error("Type must be defined");
    }
    const query = {};
    const projection = {
      schoolYear: true,
      orgid: true,
      siteId: true,
      ownerIds: true,
      sectionId: true,
      name: true,
      grade: true
    };
    if (TYPE !== "individual") {
      if (TYPE !== "benchmark") {
        query.history = { $elemMatch: { type: TYPE } };
      }
      projection.history = true;
      projection.individualInterventionQueue = true;
      projection.currentClasswideSkill = true;
    }
    if (ORGID) {
      query.orgid = ORGID;
    }
    if (SCHOOLYEAR) {
      query.schoolYear = parseInt(SCHOOLYEAR);
    }
    const studentGroups = await this.studentGroupsCollection.find(query, { projection }).toArray();

    return studentGroups.sort((a, b) => normalizeGrade(a.grade) - normalizeGrade(b.grade) || a._id - b._id);
  };

  getAssessmentResults = async ({ ORGID, SCHOOLYEAR, TYPE }) => {
    let projection = { studentGroupId: true, "created.on": true, type: true };
    const query = { orgid: ORGID, schoolYear: parseInt(SCHOOLYEAR) };
    if (TYPE === "benchmark") {
      projection = {
        ...projection,
        studentId: true,
        status: true,
        measures: true,
        grade: true,
        scores: true,
        previousAssessmentResultId: true,
        nextAssessmentResultId: true,
        benchmarkPeriodId: true,
        classwideResults: true,
        assessmentIds: true
      };
    }

    const assessmentResults = await this.assessmentResultsCollection.find(query, { projection }).toArray();
    return assessmentResults.sort((a, b) => {
      return a.created.on - b.created.on;
    });
  };

  prepareData = ({
    users,
    sites,
    studentGroups,
    students,
    studentGroupEnrollments,
    organization,
    assessmentResults
  }) => {
    this.teacherById = keyBy(users || [], "_id");
    this.district = organization;
    this.schoolById = keyBy(sites || [], "_id");
    this.studentGroupById = keyBy(studentGroups || [], "_id");
    this.studentById = keyBy(students || [], "_id");
    this.studentGroupEnrollments = studentGroupEnrollments;
    this.sgeByStudentGroupId = groupBy(studentGroupEnrollments || [], "studentGroupId");
    this.classwideAssessmentResultsByStudentGroupId = {};
    this.individualAssessmentResultsById = {};
    this.benchmarkAssessmentResultsByStudentGroupId = {};
    assessmentResults?.forEach(ar => {
      if (ar.type === "classwide") {
        (this.classwideAssessmentResultsByStudentGroupId[ar.studentGroupId] =
          this.classwideAssessmentResultsByStudentGroupId[ar.studentGroupId] || []).push(ar);
      } else if (ar.type === "individual") {
        this.individualAssessmentResultsById[ar._id] = ar;
      } else if (ar.type === "benchmark") {
        (this.benchmarkAssessmentResultsByStudentGroupId[ar.studentGroupId] =
          this.benchmarkAssessmentResultsByStudentGroupId[ar.studentGroupId] || []).push(ar);
      }
    });
  };

  getDataFromDB = async ({ TYPE, ORGID, SCHOOLYEAR }) => {
    await this.init();

    const students = await this.getStudents({ ORGID, TYPE, SCHOOLYEAR });
    const users = await this.getUsers({ ORGID });
    const organization = await this.getOrganization({ ORGID });
    const sites = await this.getSites({ ORGID });
    const studentGroupEnrollments = await this.getStudentGroupEnrollments({ ORGID, SCHOOLYEAR, TYPE });
    const studentGroups = await this.getStudentGroups({ ORGID, TYPE, SCHOOLYEAR });
    let assessmentResults = [];
    if (TYPE === "benchmark") {
      this.benchmarkPeriods = await this.getBenchmarkPeriods();
      assessmentResults = await this.getAssessmentResults({ ORGID, SCHOOLYEAR, TYPE });
    }
    if (TYPE === "individual") {
      assessmentResults = await this.getAssessmentResults({ ORGID, SCHOOLYEAR, TYPE });
    }

    this.prepareData({
      users,
      sites,
      studentGroups,
      students,
      studentGroupEnrollments,
      organization,
      assessmentResults
    });

    await this.closeConnection();
  };
}
