import IndividualReportRow from "./IndividualReportRow";

describe("individual report row", () => {
  describe("getCurrentIndividualAssessmentIndex", () => {
    it("should generate correct indexes for scores", () => {
      const individualReportRow = new IndividualReportRow({
        studentGroup: {},
        teacher: { profile: { name: {} } },
        district: {},
        school: {},
        student: { demographic: { sped: "" }, identity: { name: {}, identification: {} } }
      });

      const firstIndividualIndex = individualReportRow.getCurrentIndividualAssessmentIndex();
      expect(firstIndividualIndex).toEqual(1);
      const secondIndividualIndex = individualReportRow.getCurrentIndividualAssessmentIndex();
      expect(secondIndividualIndex).toEqual(2);
    });
  });
  describe("addIndividualScore", () => {
    it("should add a new score and set correct output fields", () => {
      const individualReportRow = new IndividualReportRow({
        studentGroup: {},
        teacher: { profile: { name: {} } },
        district: {},
        school: {},
        student: { demographic: { sped: "" }, identity: { name: {}, identification: {} } }
      });
      const individualScoreParams1 = {
        whenEndedDate: new Date(1527159216722),
        assessmentName: "testSkill1",
        score: "1"
      };
      const individualScoreParams2 = {
        whenEndedDate: new Date(1527159216722),
        assessmentName: "testSkill1",
        score: "3"
      };

      individualReportRow.addIndividualScore(individualScoreParams1);
      individualReportRow.addIndividualScore(individualScoreParams2);

      expect(individualReportRow.outputFields).toHaveProperty("individual intervention progress monitoring skill-1");
      expect(individualReportRow.outputFields).toHaveProperty(
        "individual intervention progress monitoring progress monitoring score-1"
      );
      expect(individualReportRow.outputFields).toHaveProperty(
        "individual intervention progress monitoring score entry date-1"
      );
      expect(individualReportRow.outputFields).toHaveProperty("individual intervention progress monitoring skill-2");
      expect(individualReportRow.outputFields).toHaveProperty(
        "individual intervention progress monitoring progress monitoring score-2"
      );
      expect(individualReportRow.outputFields).toHaveProperty(
        "individual intervention progress monitoring score entry date-2"
      );
    });
  });
});
