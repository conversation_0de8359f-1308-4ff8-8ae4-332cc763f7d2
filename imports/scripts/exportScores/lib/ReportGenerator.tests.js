import ReportGenerator from "./ReportGenerator";

const assessment1Date = new Date(1528190617478);
const assessment1Id = "testAssessmentId1";
const assessment1Name = "testAssessmentName1";
const assessment1Score = "27";
const assessment2Id = "testAssessmentId2";
const assessment2Name = "testAssessmentName2";
const assessment2Score = "12";

const assessment3Date = new Date(1529190617478);
const assessment3Id = "testAssessmentId3";
const assessment3Name = "testAssessmentName3";
const assessment3Score = "1";
const assessment4Id = "testAssessmentId4";
const assessment4Name = "testAssessmentName4";
const assessment4Score = "16";

const assessment5Date = new Date(1538190617478);
const assessment5Name = "testAssessmentName5";
const assessment5Score = "5";
const assessment6Name = "testAssessmentName6";
const assessment6Score = "42";
const assessment7Name = "testAssessmentName7";
const assessment7Score = "5";
const assessment8Name = "testAssessmentName8";
const assessment8Score = "13";

const benchmarkPeriods = [
  { _id: "fall", name: "Fall" },
  { _id: "winter", name: "Winter" },
  { _id: "spring", name: "Spring" }
];
const studentGroup1 = {
  _id: "testGroup1",
  schoolYear: "2017",
  history: [
    {
      type: "benchmark",
      benchmarkPeriodId: "spring",
      whenEnded: { date: assessment5Date },
      assessmentResultMeasures: [
        {
          assessmentName: assessment5Name,
          studentResults: [{ studentId: "testStudent1", score: assessment5Score }]
        },
        {
          assessmentName: assessment6Name,
          studentResults: [{ studentId: "testStudent1", score: assessment6Score }]
        },
        {
          assessmentName: assessment7Name,
          studentResults: [{ studentId: "testStudent1", score: assessment7Score }]
        },
        {
          assessmentName: assessment8Name,
          studentResults: [{ studentId: "testStudent1", score: assessment8Score }]
        }
      ]
    },
    {
      type: "benchmark",
      benchmarkPeriodId: "winter",
      whenEnded: { date: assessment3Date },
      assessmentResultMeasures: [
        {
          assessmentName: assessment3Name,
          studentResults: [{ studentId: "testStudent1", score: assessment3Score }]
        },
        {
          assessmentName: assessment4Name,
          studentResults: [{ studentId: "testStudent1", score: assessment4Score }]
        }
      ]
    },
    {
      assessmentId: assessment1Id,
      type: "classwide",
      benchmarkPeriodId: "fall",
      whenEnded: { date: assessment1Date },
      assessmentResultMeasures: [
        {
          assessmentName: assessment1Name,
          studentResults: [{ studentId: "testStudent1", score: assessment1Score }]
        }
      ]
    },
    {
      type: "benchmark",
      benchmarkPeriodId: "fall",
      whenEnded: { date: assessment1Date },
      assessmentResultMeasures: [
        {
          assessmentName: assessment1Name,
          studentResults: [{ studentId: "testStudent1", score: assessment1Score }]
        },
        {
          assessmentName: assessment2Name,
          studentResults: [{ studentId: "testStudent1", score: assessment2Score }]
        }
      ]
    }
  ]
};
const studentGroup2 = {
  _id: "testGroup1",
  schoolYear: "2017",
  currentClasswideSkill: {
    assessmentId: assessment4Id
  },
  history: [
    {
      assessmentId: assessment1Id,
      type: "classwide",
      whenEnded: { date: assessment1Date },
      assessmentResultMeasures: [
        {
          assessmentName: assessment1Name,
          studentResults: [{ studentId: "testStudent1", score: assessment1Score }]
        }
      ]
    },
    {
      assessmentId: assessment2Id,
      type: "classwide",
      whenEnded: { date: assessment1Date },
      assessmentResultMeasures: [
        {
          assessmentName: assessment2Name,
          studentResults: [{ studentId: "testStudent1", score: assessment2Score }]
        }
      ]
    },
    {
      assessmentId: assessment3Id,
      type: "classwide",
      whenEnded: { date: assessment3Date },
      assessmentResultMeasures: [
        {
          assessmentName: assessment3Name,
          studentResults: [{ studentId: "testStudent1", score: assessment3Score }]
        }
      ]
    },
    {
      assessmentId: assessment4Id,
      type: "classwide",
      whenEnded: { date: assessment3Date },
      assessmentResultMeasures: [
        {
          assessmentName: assessment4Name,
          studentResults: [{ studentId: "testStudent1", score: assessment4Score }]
        }
      ]
    }
  ]
};
const studentGroup3 = {
  _id: "testGroup1",
  schoolYear: "2017",
  currentClasswideSkill: {
    assessmentId: assessment2Id
  },
  history: [
    {
      assessmentId: assessment1Id,
      type: "classwide",
      whenEnded: { date: assessment1Date },
      assessmentResultMeasures: [
        {
          assessmentName: assessment1Name,
          studentResults: [
            { studentId: "testStudent1", score: assessment1Score },
            { studentId: "testStudent2", score: assessment3Score }
          ]
        }
      ]
    },
    {
      assessmentId: assessment2Id,
      type: "classwide",
      whenEnded: { date: assessment1Date },
      assessmentResultMeasures: [
        {
          assessmentName: assessment2Name,
          studentResults: [
            { studentId: "testStudent1", score: assessment2Score },
            { studentId: "testStudent2", score: assessment4Score }
          ]
        }
      ]
    }
  ]
};
const assessmentResultId = "testAssessmentResultId1";
const student1ForIndividualTest = {
  _id: "testStudent1",
  schoolYear: "2017",
  history: [
    {
      type: "individual",
      whenEnded: { date: assessment1Date },
      assessmentResultId,
      assessmentResultMeasures: [
        {
          assessmentName: assessment1Name,
          studentScores: [assessment1Score]
        }
      ]
    }
  ]
};
const student2ForIndividualTest = {
  _id: "testStudent2",
  schoolYear: "2017",
  history: [
    {
      type: "individual",
      whenEnded: { date: assessment3Date },
      assessmentResultId,
      assessmentResultMeasures: [
        {
          assessmentName: assessment2Name,
          studentScores: [assessment2Score]
        }
      ]
    }
  ]
};

const stubDbState = ({
  reportGenerator,
  studentGroupId = "someGroupId",
  studentIds = ["1234"],
  ownerIds = [1],
  customDb
}) => {
  const dbData = {
    users: [
      {
        _id: "234",
        profile: { name: { first: "g", last: "h" } }
      }
    ],
    sites: [{ _id: "23313", name: "testSchool" }],
    assessmentResults: [{ _id: "4321", studentGroupId }],
    studentGroups: [{ _id: studentGroupId, ownerIds }],
    studentGroupEnrollments: studentIds.map(sId => ({ studentGroupId, studentId: sId })),
    organization: { _id: "1233", name: "testDistrict" },
    ...customDb
  };

  reportGenerator.db.prepareData(dbData);
};
const prepareDataForCreateReportRow = (exportType, customDb) => {
  const schoolYear = 2017;
  const student = {
    _id: "1234",
    schoolYear,
    identity: { name: { firstName: "k", lastName: "l" }, identification: { localId: "m", stateId: "n" } },
    demographic: { birthDate: "o", ethnicity: "p", gender: "q", sped: "r" }
  };
  const studentGroup = {
    _id: "2345",
    ownerIds: [1],
    schoolYear,
    name: "testGroupName"
  };
  const reportGenerator = new ReportGenerator({ TYPE: exportType });
  reportGenerator.rows = { 2017: {} };
  stubDbState({ reportGenerator, studentGroupId: "2345", studentIds: ["1234"], customDb });
  const rowId = `${studentGroup._id}${student._id}`;
  reportGenerator.createReportRow(student, studentGroup);
  return { reportGenerator, schoolYear, rowId, studentId: student._id };
};

let oldMongoUrl = "";

const fs = require("fs");

fs.writeFileSync = jest.fn();

describe("reportGenerator", () => {
  beforeAll(() => {
    oldMongoUrl = process.env.MONGO_URL;
    process.env.MONGO_URL = "fake mongo url";
  });
  afterAll(() => {
    process.env.MONGO_URL = oldMongoUrl;
  });
  describe("constructor", () => {
    it("should set the TYPE of scores export", () => {
      const params = { TYPE: "classwide" };

      const reportGenerator = new ReportGenerator(params);

      expect(reportGenerator.TYPE).toBe("classwide");
    });
    it("should set ORGID if given", () => {
      const params = { TYPE: "classwide", ORGID: "SKSvNXnjnJzGz2utA" };

      const reportGenerator = new ReportGenerator(params);

      expect(reportGenerator.ORGID).toBe("SKSvNXnjnJzGz2utA");
    });
    it("should set SCHOOLYEAR if given", () => {
      const params = { TYPE: "classwide", SCHOOLYEAR: "2017" };

      const reportGenerator = new ReportGenerator(params);

      expect(reportGenerator.SCHOOLYEAR).toBe("2017");
    });
    it("should throw exception if TYPE is not specified", () => {
      const params = {};

      const expectedErrorMessage =
        "Parameter TYPE is mandatory. It should have one of values: benchmark, classwide or individual";
      expect(() => new ReportGenerator(params)).toThrow(expectedErrorMessage);
    });
    it("should throw exception if TYPE is not correct", () => {
      const params = { TYPE: "someScores" };

      const expectedErrorMessage =
        "Parameter TYPE is mandatory. It should have one of values: benchmark, classwide or individual";
      expect(() => new ReportGenerator(params)).toThrow(expectedErrorMessage);
    });
    it("should throw exception if MONGO_URL is not specified", () => {
      const params = {};
      const OLD_MONGO_URL = process.env.MONGO_URL;
      process.env.MONGO_URL = "";

      const expectedErrorMessage =
        "You need to export system environment variable MONGO_URL with connection string to SpringMath DB";
      expect(() => new ReportGenerator(params)).toThrow(expectedErrorMessage);

      process.env.MONGO_URL = OLD_MONGO_URL;
    });
  });
  describe("parseIndividualAssessmentResult", () => {
    const studentGroupId = "someGroupId";
    it("should create report row for a new student", () => {
      const student = {
        _id: "1234",
        schoolYear: 2017,
        identity: { name: { firstName: "k", lastName: "l" }, identification: { localId: "m", stateId: "n" } },
        demographic: { birthDate: "o", ethnicity: "p", gender: "q", sped: "r" }
      };
      const assResMeasure = {
        assessmentName: "testName",
        studentScores: [10]
      };
      const whenEndedDate = new Date();
      const newAssessmentResultId = "4321";
      const reportGenerator = new ReportGenerator({ TYPE: "individual" });
      reportGenerator.rows = { 2017: {} };
      const customDb = {
        assessmentResults: [{ _id: "4321", studentGroupId, type: "individual" }]
      };
      stubDbState({ reportGenerator, studentGroupId, customDb });
      reportGenerator.parseIndividualAssessmentResult(student, assResMeasure, whenEndedDate, newAssessmentResultId);

      expect(reportGenerator.rows[2017]).toHaveProperty(student._id);
    });
    it("should add individual score for existing student", () => {
      const schoolYear = 2017;
      const student = {
        _id: "1234",
        schoolYear,
        identity: { name: { firstName: "k", lastName: "l" }, identification: { localId: "m", stateId: "n" } },
        demographic: { birthDate: "o", ethnicity: "p", gender: "q", sped: "r" }
      };
      const assessmentName = "testName";
      const studentScores = 10;
      const assResMeasure = {
        assessmentName,
        studentScores: [studentScores]
      };
      const whenEndedDate = new Date();
      const newAssessmentResultId = "4321";
      const reportGenerator = new ReportGenerator({ TYPE: "individual" });
      const customDb = {
        assessmentResults: [{ _id: "4321", studentGroupId, type: "individual" }]
      };
      stubDbState({ reportGenerator, studentGroupId, customDb });
      reportGenerator.rows = { 2017: {} };

      reportGenerator.parseIndividualAssessmentResult(student, assResMeasure, whenEndedDate, newAssessmentResultId);

      const addedStudent = reportGenerator.rows[schoolYear][student._id];
      const addedStudentRow = addedStudent.getOutputFields();

      const expectedWhenEndedDate = whenEndedDate.toISOString();
      expect(addedStudentRow["individual intervention progress monitoring skill-1"]).toEqual(
        assResMeasure.assessmentName
      );
      expect(addedStudentRow["individual intervention progress monitoring progress monitoring score-1"]).toEqual(
        studentScores
      );
      expect(addedStudentRow["individual intervention progress monitoring score entry date-1"]).toEqual(
        expectedWhenEndedDate
      );
    });
  });
  describe("createReportRow", () => {
    it("should get existing teacher data ", () => {
      const schoolYear = 2017;
      const student = {
        _id: "1234",
        schoolYear,
        identity: { name: { firstName: "k", lastName: "l" }, identification: { localId: "m", stateId: "n" } },
        demographic: { birthDate: "o", ethnicity: "p", gender: "q", sped: "r" }
      };
      const studentGroup = {
        _id: "someGroupId",
        ownerIds: ["234"]
      };
      const reportGenerator = new ReportGenerator({ TYPE: "individual" });
      reportGenerator.rows = { 2017: {} };
      stubDbState({ reportGenerator, studentGroupId: "someGroupId", studentIds: ["1234"] });

      reportGenerator.createReportRow(student, studentGroup);

      const createdRow = reportGenerator.rows[schoolYear][student._id].getOutputFields();
      expect(createdRow.TeacherID).toEqual("234");
      expect(createdRow["Teacher First Name"]).toEqual("g");
      expect(createdRow["Teacher Last Name"]).toEqual("h");
    });
    it("should set teacher id from student group when teacher data is not available", () => {
      const { reportGenerator, schoolYear, studentId } = prepareDataForCreateReportRow("individual");

      const createdRow = reportGenerator.rows[schoolYear][studentId].getOutputFields();

      expect(createdRow.TeacherID).toEqual(1);
      expect(createdRow["Teacher First Name"]).toEqual("N/A");
      expect(createdRow["Teacher Last Name"]).toEqual("N/A");
    });
    it("should get existing district data", () => {
      const schoolYear = 2017;
      const student = {
        _id: "1234",
        schoolYear,
        identity: { name: { firstName: "k", lastName: "l" }, identification: { localId: "m", stateId: "n" } },
        demographic: { birthDate: "o", ethnicity: "p", gender: "q", sped: "r" }
      };
      const studentGroup = {
        _id: "someGroupId",
        orgid: "1233",
        ownerIds: [1]
      };
      const reportGenerator = new ReportGenerator({ TYPE: "individual" });
      reportGenerator.rows = { 2017: {} };
      stubDbState({ reportGenerator, studentGroupId: "someGroupId", studentIds: ["1234"] });

      reportGenerator.createReportRow(student, studentGroup);

      const createdRow = reportGenerator.rows[schoolYear][student._id].getOutputFields();
      expect(createdRow.DistrictID).toEqual("1233");
      expect(createdRow["District Name"]).toEqual("testDistrict");
    });
    it("should set empty object if district is not available", () => {
      const customDb = {
        organization: {}
      };
      const { reportGenerator, schoolYear, studentId } = prepareDataForCreateReportRow("individual", customDb);

      const createdRow = reportGenerator.rows[schoolYear][studentId].getOutputFields();

      expect(createdRow.DistrictID).toEqual(undefined);
      expect(createdRow["District Name"]).toEqual("N/A");
    });
    it("should get existing school data", () => {
      const schoolYear = 2017;
      const student = {
        _id: "1234",
        schoolYear,
        identity: { name: { firstName: "k", lastName: "l" }, identification: { localId: "m", stateId: "n" } },
        demographic: { birthDate: "o", ethnicity: "p", gender: "q", sped: "r" }
      };
      const studentGroup = {
        _id: "someGroupId",
        orgid: "1233",
        siteId: "23313",
        ownerIds: [1]
      };
      const reportGenerator = new ReportGenerator({ TYPE: "individual" });
      reportGenerator.rows = { 2017: {} };
      stubDbState({ reportGenerator, studentGroupId: "someGroupId", studentIds: ["1234"] });

      reportGenerator.createReportRow(student, studentGroup);

      const createdRow = reportGenerator.rows[schoolYear][student._id].getOutputFields();
      expect(createdRow.SchoolID).toEqual("23313");
      expect(createdRow["School Name"]).toEqual("testSchool");
    });
    it("should set empty object if school is not available", () => {
      const { reportGenerator, schoolYear, studentId } = prepareDataForCreateReportRow("individual");

      const createdRow = reportGenerator.rows[schoolYear][studentId].getOutputFields();

      expect(createdRow.SchoolID).toEqual(undefined);
      expect(createdRow["School Name"]).toEqual("N/A");
    });
    it("should set row school year for individual type", () => {
      const { reportGenerator, schoolYear, studentId } = prepareDataForCreateReportRow("individual");

      const createdRow = reportGenerator.rows[schoolYear][studentId].getOutputFields();

      expect(createdRow["School Year"]).toEqual(schoolYear);
    });
    it("should set row school year for classwide type", () => {
      const { reportGenerator, schoolYear, rowId } = prepareDataForCreateReportRow("classwide");

      const createdRow = reportGenerator.rows[schoolYear][rowId].getOutputFields();

      expect(createdRow["School Year"]).toEqual(schoolYear);
    });
    it("should set row school year for benchmark type", () => {
      const { reportGenerator, schoolYear, rowId } = prepareDataForCreateReportRow("benchmark");

      const createdRow = reportGenerator.rows[schoolYear][rowId].getOutputFields();

      expect(createdRow["School Year"]).toEqual(schoolYear);
    });
    it("should set row student local id for individual type", () => {
      const { reportGenerator, schoolYear, studentId } = prepareDataForCreateReportRow("individual");

      const createdRow = reportGenerator.rows[schoolYear][studentId].getOutputFields();

      expect(createdRow["Student LocalID"]).toEqual("m");
    });
    it("should set row student id for classwide type", () => {
      const { reportGenerator, schoolYear, rowId } = prepareDataForCreateReportRow("classwide");

      const createdRow = reportGenerator.rows[schoolYear][rowId].getOutputFields();

      expect(createdRow["Student LocalID"]).toEqual("m");
    });
    it("should set row student id for benchmark type", () => {
      const { reportGenerator, schoolYear, rowId } = prepareDataForCreateReportRow("benchmark");

      const createdRow = reportGenerator.rows[schoolYear][rowId].getOutputFields();

      expect(createdRow["Student LocalID"]).toEqual("m");
    });
    it("should set class name for individual type", () => {
      const { reportGenerator, schoolYear, studentId } = prepareDataForCreateReportRow("individual");

      const createdRow = reportGenerator.rows[schoolYear][studentId].getOutputFields();

      expect(createdRow["Class Name"]).toEqual("testGroupName");
    });
    it("should set class name for classwide type", () => {
      const { reportGenerator, schoolYear, rowId } = prepareDataForCreateReportRow("classwide");

      const createdRow = reportGenerator.rows[schoolYear][rowId].getOutputFields();

      expect(createdRow["Class Name"]).toEqual("testGroupName");
    });
    it("should set class name for benchmark type", () => {
      const { reportGenerator, schoolYear, rowId } = prepareDataForCreateReportRow("benchmark");

      const createdRow = reportGenerator.rows[schoolYear][rowId].getOutputFields();

      expect(createdRow["Class Name"]).toEqual("testGroupName");
    });
  });
  describe("getReport for benchmark", () => {
    const studentGroupId = "someGroupId";
    it("should not fail when there is no history in studentGroup in DB", () => {
      const schoolYear = 2017;
      const student = {
        _id: "1234",
        schoolYear,
        identity: { name: { firstName: "k", lastName: "l" }, identification: { localId: "m", stateId: "n" } },
        demographic: { birthDate: "o", ethnicity: "p", gender: "q", sped: "r" }
      };
      const studentGroup = {
        _id: "2345",
        ownerIds: [1],
        schoolYear,
        name: "testGroupName"
      };
      const reportGenerator = new ReportGenerator({ TYPE: "benchmark" });
      reportGenerator.rows = { 2017: {} };
      stubDbState({ reportGenerator, studentGroupId });
      reportGenerator.getReport(student, studentGroup);
    });
    it("should not fail when there is no assessmentResultMeasures in studentGroup in DB", () => {
      const schoolYear = 2017;
      const student = {
        _id: "1234",
        schoolYear,
        identity: { name: { firstName: "k", lastName: "l" }, identification: { localId: "m", stateId: "n" } },
        demographic: { birthDate: "o", ethnicity: "p", gender: "q", sped: "r" }
      };
      const studentGroup = {
        _id: "2345",
        ownerIds: [1],
        schoolYear,
        name: "testGroupName"
      };
      const reportGenerator = new ReportGenerator({ TYPE: "benchmark" });
      reportGenerator.rows = { 2017: {} };
      const customDb = {
        studentGroups: [{ _id: "testGroup1", history: [{ type: "benchmark" }] }]
      };
      stubDbState({ reportGenerator, studentGroupId, customDb });
      reportGenerator.getReport(student, studentGroup);
    });
    it("should not fail when there is no studentResults in studentGroup in DB", () => {
      const schoolYear = 2017;
      const student = {
        _id: "1234",
        schoolYear,
        identity: { name: { firstName: "k", lastName: "l" }, identification: { localId: "m", stateId: "n" } },
        demographic: { birthDate: "o", ethnicity: "p", gender: "q", sped: "r" }
      };
      const studentGroup = {
        _id: "2345",
        ownerIds: [1],
        schoolYear,
        name: "testGroupName"
      };
      const reportGenerator = new ReportGenerator({ TYPE: "benchmark" });
      reportGenerator.rows = { 2017: {} };
      const customDb = {
        studentGroups: [
          {
            _id: "testGroup1",
            history: [
              {
                type: "benchmark",
                assessmentResultMeasures: [
                  {
                    assessmentName: "testAssessmentName1"
                  }
                ]
              }
            ]
          }
        ]
      };
      stubDbState({ reportGenerator, studentGroupId, customDb });
      reportGenerator.getReport(student, studentGroup);
    });
    it("should contain all scores in history for a group", () => {
      const schoolYear = 2017;
      const student1 = {
        _id: "testStudent1"
      };

      const reportGenerator = new ReportGenerator({ TYPE: "benchmark" });
      reportGenerator.rows = { 2017: {} };
      const customDb = {
        students: [student1],
        studentGroups: [studentGroup1],
        studentGroupEnrollments: [student1._id].map(sId => ({ studentGroupId: studentGroup1._id, studentId: sId }))
      };
      stubDbState({ reportGenerator, studentGroupId, customDb });
      reportGenerator.db.benchmarkPeriods = benchmarkPeriods;
      const rowId = `${studentGroup1._id}${student1._id}`;

      reportGenerator.getReport();

      const generatedReportRow = reportGenerator.rows[schoolYear][rowId];
      const outputFields = generatedReportRow.getOutputFields();
      const expectedDate = date =>
        date
          .toISOString()
          .slice(0, 19)
          .replace("T", " ");
      expect(outputFields["Fall screening assessment score entry date"]).toEqual(expectedDate(assessment1Date));
      expect(outputFields["Fall screening assessment #1 name"]).toEqual(assessment1Name);
      expect(outputFields["Fall screening assessment #1 score"]).toEqual(assessment1Score);
      expect(outputFields["Fall screening assessment #2 name"]).toEqual(assessment2Name);
      expect(outputFields["Fall screening assessment #2 score"]).toEqual(assessment2Score);
      expect(outputFields["Fall screening assessment #3 name"]).toEqual("N/A");
      expect(outputFields["Fall screening assessment #3 score"]).toEqual("N/A");
      expect(outputFields["Fall screening assessment #4 name"]).toEqual("N/A");
      expect(outputFields["Fall screening assessment #4 score"]).toEqual("N/A");
      expect(outputFields["Winter screening assessment score entry date"]).toEqual(expectedDate(assessment3Date));
      expect(outputFields["Winter screening assessment #1 name"]).toEqual(assessment3Name);
      expect(outputFields["Winter screening assessment #1 score"]).toEqual(assessment3Score);
      expect(outputFields["Winter screening assessment #2 name"]).toEqual(assessment4Name);
      expect(outputFields["Winter screening assessment #2 score"]).toEqual(assessment4Score);
      expect(outputFields["Winter screening assessment #3 name"]).toEqual("N/A");
      expect(outputFields["Winter screening assessment #3 score"]).toEqual("N/A");
      expect(outputFields["Winter screening assessment #4 name"]).toEqual("N/A");
      expect(outputFields["Winter screening assessment #4 score"]).toEqual("N/A");
      expect(outputFields["Spring screening assessment score entry date"]).toEqual(expectedDate(assessment5Date));
      expect(outputFields["Spring screening assessment #1 name"]).toEqual(assessment5Name);
      expect(outputFields["Spring screening assessment #1 score"]).toEqual(assessment5Score);
      expect(outputFields["Spring screening assessment #2 name"]).toEqual(assessment6Name);
      expect(outputFields["Spring screening assessment #2 score"]).toEqual(assessment6Score);
      expect(outputFields["Spring screening assessment #3 name"]).toEqual(assessment7Name);
      expect(outputFields["Spring screening assessment #3 score"]).toEqual(assessment7Score);
      expect(outputFields["Spring screening assessment #4 name"]).toEqual(assessment8Name);
      expect(outputFields["Spring screening assessment #4 score"]).toEqual(assessment8Score);
    });
  });
  describe("classwide scores export", () => {
    it("should contain valid scores for all classwide interventions", () => {
      const schoolYear = 2017;
      const student1 = {
        _id: "testStudent1"
      };

      const reportGenerator = new ReportGenerator({ TYPE: "classwide" });
      reportGenerator.rows = { 2017: {} };
      const customDb = {
        students: [student1],
        studentGroups: [studentGroup2],
        studentGroupEnrollments: [student1._id].map(sId => ({ studentGroupId: studentGroup2._id, studentId: sId })),
        benchmarkPeriods,
        getDataFromDB: () => true,
        closeConnection: () => true
      };
      stubDbState({ reportGenerator, studentGroupId: studentGroup2._id, customDb });

      const rowId = `${studentGroup1._id}${student1._id}`;

      reportGenerator.getReport();

      const generatedReportRow = reportGenerator.rows[schoolYear][rowId];
      const outputFields = generatedReportRow.getOutputFields();

      expect(outputFields["classwide intervention count of progress monitoring scores"]).toEqual(4);
      expect(outputFields["classwide intervention number of skills mastered"]).toEqual(3);
      expect(outputFields["classwide intervention average weeks per skill"]).toEqual("0.6");
      expect(outputFields["classwide intervention date started"]).toEqual("2018-06-05T09:23:37.478Z");
      expect(outputFields["classwide intervention last score entry date"]).toEqual("2018-06-16T23:10:17.478Z");

      expect(outputFields["classwide intervention progress monitoring skill-1"]).toEqual(assessment1Name);
      expect(outputFields["classwide intervention progress monitoring progress monitoring score-1"]).toEqual(
        assessment1Score
      );
      expect(outputFields["classwide intervention progress monitoring score entry date-1"]).toEqual(
        assessment1Date.toISOString()
      );

      expect(outputFields["classwide intervention progress monitoring skill-2"]).toEqual(assessment2Name);
      expect(outputFields["classwide intervention progress monitoring progress monitoring score-2"]).toEqual(
        assessment2Score
      );
      expect(outputFields["classwide intervention progress monitoring score entry date-2"]).toEqual(
        assessment1Date.toISOString()
      );

      expect(outputFields["classwide intervention progress monitoring skill-3"]).toEqual(assessment3Name);
      expect(outputFields["classwide intervention progress monitoring progress monitoring score-3"]).toEqual(
        assessment3Score
      );
      expect(outputFields["classwide intervention progress monitoring score entry date-3"]).toEqual(
        assessment3Date.toISOString()
      );

      expect(outputFields["classwide intervention progress monitoring skill-4"]).toEqual(assessment4Name);
      expect(outputFields["classwide intervention progress monitoring progress monitoring score-4"]).toEqual(
        assessment4Score
      );
      expect(outputFields["classwide intervention progress monitoring score entry date-4"]).toEqual(
        assessment3Date.toISOString()
      );
    });
    it("should contain all students", () => {
      const schoolYear = 2017;
      const student1 = {
        _id: "testStudent1"
      };
      const student2 = {
        _id: "testStudent2"
      };

      const reportGenerator = new ReportGenerator({ TYPE: "classwide" });
      reportGenerator.rows = { 2017: {} };
      const customDb = {
        students: [student1, student2],
        studentGroups: [studentGroup3],
        studentGroupEnrollments: [student1._id, student2._id].map(sId => ({
          studentGroupId: studentGroup3._id,
          studentId: sId
        })),
        benchmarkPeriods
      };
      stubDbState({ reportGenerator, studentGroupId: studentGroup3._id, customDb });

      const rowId1 = `${studentGroup1._id}${student1._id}`;
      const rowId2 = `${studentGroup1._id}${student2._id}`;

      reportGenerator.getReport();

      const generatedReportRow1 = reportGenerator.rows[schoolYear][rowId1];
      const outputFields1 = generatedReportRow1.getOutputFields();
      expect(outputFields1["classwide intervention count of progress monitoring scores"]).toEqual(2);
      expect(outputFields1["classwide intervention number of skills mastered"]).toEqual(1);
      expect(outputFields1["classwide intervention average weeks per skill"]).toEqual("1.0");
      expect(outputFields1["classwide intervention date started"]).toEqual("2018-06-05T09:23:37.478Z");
      expect(outputFields1["classwide intervention last score entry date"]).toEqual("2018-06-05T09:23:37.478Z");
      expect(outputFields1["classwide intervention progress monitoring skill-1"]).toEqual(assessment1Name);
      expect(outputFields1["classwide intervention progress monitoring progress monitoring score-1"]).toEqual(
        assessment1Score
      );
      expect(outputFields1["classwide intervention progress monitoring score entry date-1"]).toEqual(
        assessment1Date.toISOString()
      );
      expect(outputFields1["classwide intervention progress monitoring skill-2"]).toEqual(assessment2Name);
      expect(outputFields1["classwide intervention progress monitoring progress monitoring score-2"]).toEqual(
        assessment2Score
      );
      expect(outputFields1["classwide intervention progress monitoring score entry date-2"]).toEqual(
        assessment1Date.toISOString()
      );

      const generatedReportRow2 = reportGenerator.rows[schoolYear][rowId2];
      const outputFields2 = generatedReportRow2.getOutputFields();
      expect(outputFields2["classwide intervention count of progress monitoring scores"]).toEqual(2);
      expect(outputFields2["classwide intervention number of skills mastered"]).toEqual(1);
      expect(outputFields2["classwide intervention average weeks per skill"]).toEqual("1.0");
      expect(outputFields2["classwide intervention date started"]).toEqual("2018-06-05T09:23:37.478Z");
      expect(outputFields2["classwide intervention last score entry date"]).toEqual("2018-06-05T09:23:37.478Z");
      expect(outputFields2["classwide intervention progress monitoring skill-1"]).toEqual(assessment1Name);
      expect(outputFields2["classwide intervention progress monitoring progress monitoring score-1"]).toEqual(
        assessment3Score
      );
      expect(outputFields2["classwide intervention progress monitoring score entry date-1"]).toEqual(
        assessment1Date.toISOString()
      );
      expect(outputFields2["classwide intervention progress monitoring skill-2"]).toEqual(assessment2Name);
      expect(outputFields2["classwide intervention progress monitoring progress monitoring score-2"]).toEqual(
        assessment4Score
      );
      expect(outputFields2["classwide intervention progress monitoring score entry date-2"]).toEqual(
        assessment1Date.toISOString()
      );
    });
  });
  describe("individual scores export", () => {
    it("should contain valid scores for all individual interventions", () => {
      const schoolYear = 2017;
      const newAssessmentResultId = "testAssessmentResultId1";
      const student1 = {
        _id: "testStudent1",
        schoolYear,
        history: [
          {
            type: "individual",
            whenEnded: { date: assessment1Date },
            assessmentResultId: newAssessmentResultId,
            assessmentResultMeasures: [
              {
                assessmentName: assessment1Name,
                studentScores: [assessment1Score]
              }
            ]
          }
        ]
      };

      const studentGroupEnrollment = {
        studentId: student1._id,
        studentGroupId: studentGroup2._id,
        isActive: true,
        schoolYear
      };

      const reportGenerator = new ReportGenerator({ TYPE: "individual" });
      reportGenerator.rows = { [schoolYear]: {} };
      const customDb = {
        students: [student1],
        studentGroups: [studentGroup2],
        studentGroupEnrollments: [studentGroupEnrollment],
        benchmarkPeriods,
        assessmentResults: [
          {
            _id: assessmentResultId,
            studentGroupId: "testGroup1",
            type: "individual"
          }
        ]
      };
      stubDbState({ reportGenerator, studentGroupId: studentGroup2._id, customDb });

      const rowId = student1._id;

      reportGenerator.getReport();

      const generatedReportRow = reportGenerator.rows[schoolYear][rowId];
      const outputFields = generatedReportRow.getOutputFields();

      expect(outputFields["individual intervention progress monitoring skill-1"]).toEqual(assessment1Name);
      expect(outputFields["individual intervention progress monitoring progress monitoring score-1"]).toEqual(
        assessment1Score
      );
      expect(outputFields["individual intervention progress monitoring score entry date-1"]).toEqual(
        assessment1Date.toISOString()
      );
    });
    it("should contain all students", () => {
      const schoolYear = 2017;
      const reportGenerator = new ReportGenerator({ TYPE: "individual" });
      const studentGroupEnrollments = [
        {
          studentId: student1ForIndividualTest._id,
          studentGroupId: studentGroup2._id,
          isActive: true,
          schoolYear
        },
        {
          studentId: student2ForIndividualTest._id,
          studentGroupId: studentGroup2._id,
          isActive: true,
          schoolYear
        }
      ];
      reportGenerator.rows = { [schoolYear]: {} };
      const customDb = {
        students: [student1ForIndividualTest, student2ForIndividualTest],
        studentGroups: [studentGroup2],
        studentGroupEnrollments,
        benchmarkPeriods,
        assessmentResults: [
          {
            _id: assessmentResultId,
            studentGroupId: "testGroup1",
            type: "individual"
          }
        ],
        getDataFromDB: () => true,
        closeConnection: () => true
      };
      stubDbState({ reportGenerator, studentGroupId: studentGroup2._id, customDb });

      const rowId1 = student1ForIndividualTest._id;
      const rowId2 = student2ForIndividualTest._id;

      reportGenerator.getReport();

      const generatedReportRow1 = reportGenerator.rows[schoolYear][rowId1];
      const outputFields1 = generatedReportRow1.getOutputFields();

      expect(outputFields1["individual intervention progress monitoring skill-1"]).toEqual(assessment1Name);
      expect(outputFields1["individual intervention progress monitoring progress monitoring score-1"]).toEqual(
        assessment1Score
      );
      expect(outputFields1["individual intervention progress monitoring score entry date-1"]).toEqual(
        assessment1Date.toISOString()
      );

      const generatedReportRow2 = reportGenerator.rows[schoolYear][rowId2];
      const outputFields2 = generatedReportRow2.getOutputFields();

      expect(outputFields2["individual intervention progress monitoring skill-1"]).toEqual(assessment2Name);
      expect(outputFields2["individual intervention progress monitoring progress monitoring score-1"]).toEqual(
        assessment2Score
      );
      expect(outputFields2["individual intervention progress monitoring score entry date-1"]).toEqual(
        assessment3Date.toISOString()
      );
    });
  });
});
