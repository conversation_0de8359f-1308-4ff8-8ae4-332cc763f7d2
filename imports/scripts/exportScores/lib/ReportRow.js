export default class ReportRow {
  constructor(params) {
    const {
      studentGroup,
      teacher,
      district,
      school,
      student,
      shouldGenerateInterventionNotes,
      interventionNotesToSet
    } = params;
    const instructionalNotes = shouldGenerateInterventionNotes
      ? {
          "Number of students in class": interventionNotesToSet.getNumberOfStudentsInClass,
          "Had Recommended Individual Intervention from Fall/Winter Screening":
            interventionNotesToSet.getHadRecommendedIndividualIntervention,
          "Eligible for Individual Intervention recommendation from Four Week Rule - # of weeks": interventionNotesToSet.getNumberOfEligibleIndividualInterventionWeeksFromFourWeekRuleStudentRow.toString(),
          "Had Classwide Intervention Recommended":
            interventionNotesToSet.hadClasswideInterventionRecommendedInFallOrWinterRow,
          "Was Individual Intervention Scheduled from Four Week Rule":
            interventionNotesToSet.getWasIndividualInterventionScheduledFromFourWeekRuleRow,
          "Individual Intervention Scheduled + Recommended from Four Week Rule - # of weeks":
            interventionNotesToSet.getNumberOfWeeksWhenIndividualInterventionWasRecommendedFromFourWeekRuleRow
        }
      : {};

    this.outputFields = {
      "School Year": studentGroup.schoolYear,
      DistrictID: district._id,
      SchoolID: school._id,
      TeacherID: teacher._id,
      "Class SectionID": studentGroup.sectionId,
      "Class Name": studentGroup.name,
      Grade: studentGroup.grade,
      "Teacher First Name": teacher?.profile?.name?.first || "N/A",
      "Teacher Last Name": teacher?.profile?.name?.last || "N/A",
      "District Name": district?.name || "N/A",
      "School Name": school?.name || "N/A",
      "Student First Name": student?.identity?.name?.firstName || "N/A",
      "Student Last Name": student?.identity?.name?.lastName || "N/A",
      "Student LocalID": student?.identity?.identification?.localId || "N/A",
      "Student StateID": student?.identity?.identification?.stateId || "N/A",
      "Student Birth Date": student?.demographic?.birthDate || "N/A",
      ...instructionalNotes
    };

    this.statsAreCounted = true;
  }

  getOutputFields = () => {
    if (!this.statsAreCounted) {
      this.calculateClasswideRowStatistics();
    }
    return this.outputFields;
  };
}
