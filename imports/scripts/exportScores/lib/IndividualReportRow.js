import ReportRow from "./ReportRow";

export default class IndividualReportRow extends ReportRow {
  constructor({ studentGroup, teacher, district, school, student, studentGroupEnrollment }) {
    super({ studentGroup, teacher, district, school, student, studentGroupEnrollment });
    this.currentIndividualAssessmentIndex = 0;
  }

  getCurrentIndividualAssessmentIndex = () => {
    this.currentIndividualAssessmentIndex += 1;
    return this.currentIndividualAssessmentIndex;
  };

  addIndividualScore = ({ assessmentName, score, whenEndedDate }) => {
    const currentIndividualIndex = this.getCurrentIndividualAssessmentIndex();

    this.outputFields[`individual intervention progress monitoring skill-${currentIndividualIndex}`] = assessmentName;
    this.outputFields[
      `individual intervention progress monitoring progress monitoring score-${currentIndividualIndex}`
    ] = score;
    this.outputFields[
      `individual intervention progress monitoring score entry date-${currentIndividualIndex}`
    ] = whenEndedDate ? whenEndedDate.toISOString() : whenEndedDate;
  };
}
