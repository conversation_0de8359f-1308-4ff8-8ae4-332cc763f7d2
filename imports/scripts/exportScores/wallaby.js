process.env.BABEL_ENV = "test";
process.env.MONGO_URL = "test";

module.exports = wallaby => ({
  debug: true,
  testFramework: "jest",
  files: [
    "**/*.js",
    { pattern: "**/*.tests.js", ignore: true },
    { pattern: "node_modules/**/*", ignore: true },
    "testing/common/**/*.js"
  ],
  tests: ["**/*.tests.js"],
  compilers: { "**/*.js": wallaby.compilers.babel() },
  env: { type: "node" }
});
