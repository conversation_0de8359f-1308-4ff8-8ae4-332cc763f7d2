import { Mongo } from "meteor/mongo";
import SimpleSchema from "simpl-schema";

export const Roles = new Mongo.Collection("Roles");

Roles.schemaRole = new SimpleSchema({
  _id: { type: String, optional: true },
  internalWeight: { type: Number, optional: true },
  label: { type: String },
  name: { type: String },
  sortOrder: { type: Number, optional: true }
});

Roles.validate = role => {
  Roles.schemaRole.validate(role);
};
Roles.isValid = role => {
  return Roles.schemaRole.namedContext("testContext").validate(role);
};
