import { assert } from "chai";

import { Roles } from "./roles.js";
import { role } from "../../test-helpers/data/roles.js";

describe("Roles", () => {
  describe("role", () => {
    describe("Should pass schema validation method", () => {
      it("validate", () => {
        assert.isUndefined(Roles.validate(role("arbitraryRole")));
      });
      it("isValid", () => {
        assert.isTrue(Roles.isValid(role("arbitraryRole")));
      });
    });
    describe("Should fail schema validation method", () => {
      it("isValid", () => {
        const roleDoc = role("arbitraryRole");
        delete roleDoc.name;
        assert.isFalse(Roles.isValid(roleDoc));
      });
    });
  });
});
