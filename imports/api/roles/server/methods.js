import { Meteor } from "meteor/meteor";
import { get, keyBy } from "lodash";
import { check, Match } from "meteor/check";
import { Roles } from "../roles";
import { Sites } from "../../sites/sites";
import { getMeteorUserId } from "../../utilities/utilities";

export async function insert(roleDoc) {
  Roles.validate(roleDoc);
  await Roles.insertAsync(roleDoc);
}

export async function getCurrentLoggedInUserRole() {
  const user = await Meteor.users.findOneAsync({ _id: getMeteorUserId() }, { "profile.siteAccess": 1 });
  if (user && user.profile.siteAccess[0].role) {
    const curRoleId = user.profile.siteAccess[0].role;
    const role = await Roles.findOneAsync({ _id: curRoleId });
    if (role) {
      return role.name;
    }
  }
  return "";
}

export async function getCurrentLoggedInUserSiteAccess(siteId) {
  const site = await Sites.findOneAsync({ _id: siteId }, { _id: 1 });
  if (siteId && !site) {
    throw new Meteor.Error(404, "Site not found");
  }
  const user = await Meteor.users.findOneAsync({ _id: getMeteorUserId() }, { fields: { "profile.siteAccess": 1 } });
  if (!user) {
    throw new Meteor.Error(404, "User not found");
  }
  const siteAccess = get(user, "profile.siteAccess", []);
  if (!siteAccess.length) {
    throw new Meteor.Error(403, "User does not have required site access");
  }
  const userRoleIds = user.profile.siteAccess.map(sa => sa.role);
  const roles = await Roles.find({ _id: { $in: userRoleIds } }).fetchAsync();
  const keyByRoles = keyBy(roles, "_id");
  const parsedSiteAccess = user.profile.siteAccess.map(sa => ({ ...sa, role: keyByRoles[sa.role].name }));
  if (roles.length) {
    return parsedSiteAccess;
  }
  return [];
}

Meteor.methods({
  async "Roles:getCurrentLoggedInUserSiteAccess"(siteId) {
    check(siteId, Match.Maybe(String));
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    return getCurrentLoggedInUserSiteAccess(siteId);
  },
  async "Roles:getAllRoles"() {
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    return Roles.find({}, { fields: { _id: 1, name: 1 } }).fetchAsync();
  }
});
