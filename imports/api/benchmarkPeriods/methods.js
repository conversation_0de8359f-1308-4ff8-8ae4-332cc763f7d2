import { check } from "meteor/check";
import { get } from "lodash";

import { BenchmarkPeriods } from "./benchmarkPeriods";
import { Organizations } from "../organizations/organizations";
import { getCurrentDate, getCurrentDateSync } from "../helpers/getCurrentDate";
import { getMeteorUser } from "../utilities/utilities";

const getFinalBmPeriod = (bmps, bmpGroupId, month, day) => {
  return (bmps || []).find(bmp => {
    const startDate = get(bmp.startDate, bmpGroupId, bmp.startDate.default);
    const endDate = get(bmp.endDate, bmpGroupId, bmp.endDate.default);
    const bmpStartMonth = startDate.month - 1;
    const bmpStartDay = startDate.day;
    const bmpEndMonth = endDate.month - 1;
    const bmpEndDay = endDate.day;
    const offSetStartMonth = bmpStartMonth;
    const offSetEndMonth = (((bmpEndMonth - offSetStartMonth) % 12) + 12) % 12;
    const offSetMonth = (((month - offSetStartMonth) % 12) + 12) % 12;

    if (offSetMonth > 0 && offSetMonth < offSetEndMonth) {
      return true;
    }
    if (offSetMonth === 0) {
      return day >= bmpStartDay;
    }
    if (offSetMonth === offSetEndMonth) {
      return day <= bmpEndDay;
    }
    return false;
  });
};

export const getBenchmarkPeriodByDate = params => {
  const { customDate, benchmarkPeriodsGroupId, isTestOrg, curDate, benchmarkPeriods } = params;
  const currentDate = curDate || getCurrentDateSync(customDate, isTestOrg);
  const month = currentDate.getMonth();
  const day = currentDate.getDate();
  const bmpGroupId = benchmarkPeriodsGroupId || "default";
  // NOTE(fmazur) - benchmarkPeriods are expected to be sorted via mongodb query sortOrder 1
  return getFinalBmPeriod(benchmarkPeriods, bmpGroupId, month, day);
};

export const getNextBMPeriod = params => {
  // NOTE(fmazur) - benchmarkPeriods are expected to be sorted via mongodb query sortOrder 1
  const { bmPeriodId, benchmarkPeriods } = params;
  const currentBMP = bmPeriodId
    ? benchmarkPeriods.find(bmp => bmp._id === bmPeriodId)
    : getBenchmarkPeriodByDate(params); // { customDate, benchmarkPeriodsGroupId, isTestOrg, curDate, benchmarkPeriods } = params
  const nextBMPSortOrder = (currentBMP.sortOrder % 3) + 1;
  return benchmarkPeriods.find(bmp => bmp.sortOrder === nextBMPSortOrder);
};

export default class BenchmarkPeriodHelpers {
  // TODO get rid of the years on benchmark periods...  should just be a day of a general year
  // then use the combination of period and actual year to create the windows.
  static async getBenchmarkPeriodByDate({ curDate, orgid } = {}) {
    const bmps = await BenchmarkPeriods.find({}, { sort: { sortOrder: 1 } }).fetchAsync();
    const user = await getMeteorUser();
    const customDate = get(user, "profile.customDate");
    const currentDate = curDate || (await getCurrentDate(customDate, orgid));
    const month = currentDate.getMonth();
    const day = currentDate.getDate();
    let bmpGroupId = "default";
    if (orgid) {
      const organization = (await Organizations.findOneAsync(orgid, { fields: { benchmarkPeriodsGroupId: 1 } })) || {};
      if (organization.benchmarkPeriodsGroupId) {
        bmpGroupId = organization.benchmarkPeriodsGroupId;
      }
    }

    return getFinalBmPeriod(bmps, bmpGroupId, month, day);
  }

  static async getNextBMPeriod({ bmPeriodId, orgid }) {
    const bmps = await BenchmarkPeriods.find({}, { sort: { sortOrder: 1 } }).fetchAsync();

    const currentBMP = bmPeriodId
      ? bmps.find(bmp => bmp._id === bmPeriodId)
      : await this.getBenchmarkPeriodByDate({ orgid });

    const nextBMPSortOrder = (currentBMP.sortOrder % 3) + 1;
    return bmps.find(bmp => bmp.sortOrder === nextBMPSortOrder);
  }
}

export async function insert(benchmarkPeriodDoc) {
  check(benchmarkPeriodDoc, Object);
  BenchmarkPeriods.validate(benchmarkPeriodDoc);
  await BenchmarkPeriods.insertAsync(benchmarkPeriodDoc);
}
