import { Meteor } from "meteor/meteor";
import { assert } from "chai";

import { BenchmarkPeriods } from "./benchmarkPeriods";
import { getBenchmarkPeriods } from "../../test-helpers/data/benchmarkPeriods";

if (Meteor.isServer) {
  describe("BenchmarkPeriods", () => {
    describe("Winter", () => {
      describe("Should pass schema validation method", () => {
        it("validate", () => {
          assert.isUndefined(BenchmarkPeriods.validate(getBenchmarkPeriods()[0]));
        });
        it("isValid", () => {
          assert.isTrue(BenchmarkPeriods.isValid(getBenchmarkPeriods()[0]));
        });
      });
      describe("Should fail schema validation method", () => {
        it("isValid", () => {
          const benchmarkPeriodDoc = getBenchmarkPeriods()[0];
          benchmarkPeriodDoc._id = 1234;
          assert.isFalse(BenchmarkPeriods.isValid(benchmarkPeriodDoc));
        });
      });
    });

    describe("Spring", () => {
      describe("Should pass schema validation method", () => {
        it("validate", () => {
          assert.isUndefined(BenchmarkPeriods.validate(getBenchmarkPeriods()[1]));
        });
        it("isValid", () => {
          assert.isTrue(BenchmarkPeriods.isValid(getBenchmarkPeriods()[1]));
        });
      });
      describe("Should fail schema validation method", () => {
        it("isValid", () => {
          const benchmarkPeriodDoc = getBenchmarkPeriods()[1];
          benchmarkPeriodDoc._id = 1234;
          assert.isFalse(BenchmarkPeriods.isValid(benchmarkPeriodDoc));
        });
      });
    });

    describe("Fall", () => {
      describe("Should pass schema validation method", () => {
        it("validate", () => {
          assert.isUndefined(BenchmarkPeriods.validate(getBenchmarkPeriods()[2]));
        });
        it("isValid", () => {
          assert.isTrue(BenchmarkPeriods.isValid(getBenchmarkPeriods()[2]));
        });
      });
      describe("Should fail schema validation method", () => {
        it("isValid", () => {
          const benchmarkPeriodDoc = getBenchmarkPeriods()[2];
          benchmarkPeriodDoc._id = 1234;
          assert.isFalse(BenchmarkPeriods.isValid(benchmarkPeriodDoc));
        });
      });
    });
  });
}
