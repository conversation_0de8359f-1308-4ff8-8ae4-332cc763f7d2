import { Mongo } from "meteor/mongo";
import SimpleSchema from "simpl-schema";
import * as yup from "yup";

import { ByDateOn } from "../helpers/schemas/byDateOn/byDateOn";

export const BenchmarkPeriods = new Mongo.Collection("BenchmarkPeriods");

BenchmarkPeriods.schema = new SimpleSchema({
  _id: { type: String, optional: true },
  name: { type: String },
  label: { type: String },
  sortOrder: { type: Number },
  startDate: { type: Object },
  "startDate.default": { type: Object },
  "startDate.default.month": { type: Number },
  "startDate.default.day": { type: Number },
  endDate: { type: Object },
  "endDate.default": { type: Object },
  "endDate.default.month": { type: Number },
  "endDate.default.day": { type: Number },
  created: { type: ByDateOn },
  lastModified: { type: ByDateOn }
});

BenchmarkPeriods.validate = benchmarkPeriod => {
  BenchmarkPeriods.schema.validate(benchmarkPeriod);
};
BenchmarkPeriods.isValid = benchmarkPeriod =>
  BenchmarkPeriods.schema.namedContext("benchmarkPeriodContext").validate(benchmarkPeriod);

export function createCustomBenchmarkPeriodsSchema(restrictedNames = []) {
  return yup.object().shape({
    name: yup
      .string()
      .label("Benchmark Periods Group Name")
      .notOneOf(restrictedNames)
      .required(),
    fallStartMonth: yup
      .number()
      .integer()
      .required()
      .min(1)
      .max(12)
      .label("Fall Start Month"),
    winterStartMonth: yup
      .number()
      .integer()
      .required()
      .min(1)
      .max(12)
      .label("Winter Start Month"),
    springStartMonth: yup
      .number()
      .integer()
      .required()
      .min(1)
      .max(12)
      .label("Spring Start Month"),
    fallStartDay: yup
      .number()
      .integer()
      .required()
      .min(1)
      .max(31)
      .label("Fall Start Day"),
    winterStartDay: yup
      .number()
      .integer()
      .required()
      .min(1)
      .max(31)
      .label("Winter Start Day"),
    springStartDay: yup
      .number()
      .integer()
      .required()
      .min(1)
      .max(31)
      .label("Spring Start Day")
  });
}
