import { assert } from "chai";
import sinon from "sinon";
import { BenchmarkPeriods } from "./benchmarkPeriods.js";
import stubUtils from "../../test-helpers/methods/stubUtils.js";
import BenchmarkPeriodHelpers from "./methods.js";

describe("imports/api/benchmarkPeriods/methods.js tests", () => {
  describe("getBenchmarkPeriodByDate", () => {
    const benchmarkPeriods = () => [
      {
        _id: "8S52Gz5o85hRkECgq",
        name: "Fall",
        label: "fall-period",
        sortOrder: 1,
        startDate: {
          default: {
            month: 8,
            day: 1
          }
        },
        endDate: {
          default: {
            month: 12,
            day: 31
          }
        }
      },
      {
        _id: "nEsbWokBWutTZFkTh",
        name: "Winter",
        label: "winter-period",
        sortOrder: 2,
        startDate: {
          default: {
            month: 1,
            day: 1
          }
        },
        endDate: {
          default: {
            month: 3,
            day: 31
          }
        }
      },
      {
        _id: "cjCMnZKARBJmG8suT",
        name: "Spring",
        label: "spring-period",
        sortOrder: 3,
        startDate: {
          default: {
            month: 4,
            day: 1
          }
        },
        endDate: {
          default: {
            month: 7,
            day: 31
          }
        }
      }
    ];
    beforeEach(() => {
      sinon.stub(BenchmarkPeriods, "find").callsFake(() => ({
        fetchAsync: () => benchmarkPeriods()
      }));
    });
    afterEach(() => {
      stubUtils.safeRestore(BenchmarkPeriods.find);
    });
    describe("given the periods cover the entire year without overlapping", () => {
      it("should return fall period if the curDate is in the fall window", async () => {
        const testResult = await BenchmarkPeriodHelpers.getBenchmarkPeriodByDate({ curDate: new Date("08-21-2001") });
        assert.equal(testResult.name, "Fall");
      });
      it("should return the winter period if the curDate is in the winter window", async () => {
        const testResult = await BenchmarkPeriodHelpers.getBenchmarkPeriodByDate({ curDate: new Date("02-21-2001") });
        assert.equal(testResult.name, "Winter");
      });
    });
    describe("given the periods cover the entire year and some overlap into two year numbers", () => {
      beforeEach(() => {
        stubUtils.safeRestore(BenchmarkPeriods.find);
        sinon.stub(BenchmarkPeriods, "find").callsFake(() => ({
          fetchAsync: () => {
            const testBenchmarkPeriods = benchmarkPeriods();
            testBenchmarkPeriods[1].startDate.default.month = 12;
            testBenchmarkPeriods[1].startDate.default.day = 15;
            testBenchmarkPeriods[0].endDate.default.day = 14;
            return testBenchmarkPeriods;
          }
        }));
      });
      it("should return the winter period if the curDate is in the winter window and before December 31", async () => {
        const testResult = await BenchmarkPeriodHelpers.getBenchmarkPeriodByDate({ curDate: new Date("12-21-2001") });
        assert.equal(testResult.name, "Winter");
      });
      it("should return the winter period if the curDate is in the winter window and after January 1", async () => {
        const testResult = await BenchmarkPeriodHelpers.getBenchmarkPeriodByDate({ curDate: new Date("01-21-2001") });
        assert.equal(testResult.name, "Winter");
      });
    });
  });
});
