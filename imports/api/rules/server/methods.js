import extend from "lodash/extend";
import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";
import get from "lodash/get";
import {
  getMeteorUserId,
  hasGroupPassedClasswideIntervention,
  MAX_SKILLS_FROM_NEXT_GRADE,
  ninjalog,
  translateBenchmarkPeriod
} from "../../utilities/utilities";
import { Rules } from "../rules";
import { Assessments } from "../../assessments/assessments";
import { getScoreTargets } from "../../assessments/methods";
import { getTimestampInfo } from "../../helpers/getTimestampInfo";
import * as auth from "../../authorization/server/methods";

export function getNextSkill({ rule, passed, assessmentId, isBenchmark, defaultSkillsLength }) {
  check(rule, Object);
  check(passed, Boolean);
  check(assessmentId, String);
  check(isBenchmark, Boolean);
  let skill = {};
  let position;
  const { skills } = rule;
  if (isBenchmark) {
    position = 0;
  } else {
    skills.forEach((s, i) => {
      if (s.assessmentId === assessmentId) {
        position = i;
      }
    });
  }
  let nextSkillPosition = position;
  if (!passed) {
    skill = skills[position];
  } else {
    nextSkillPosition = position + 1;
    skill = skills[nextSkillPosition];
  }
  if (skill && defaultSkillsLength && nextSkillPosition >= defaultSkillsLength) {
    skill.isAdditional = true;
  }
  return skill || undefined;
}

export async function resolveClassWideInterventionRoute(
  {
    grade,
    assessmentId,
    assessmentResultType,
    cutoffTarget,
    medianScore,
    studentScores,
    targetScores,
    totalStudentsAssessed
  },
  forceClasswideIntervention = false,
  totalStudentsEnrolled
) {
  check(grade, String);
  check(assessmentId, String);
  check(cutoffTarget, Number);
  check(medianScore, Number);

  // Get the rule
  const rule = await Rules.findOneAsync({ grade });
  const additionalRule = grade === "K" ? await Rules.findOneAsync({ grade: "01" }) : null;
  const defaultSkillsLength = rule?.skills.length || 0;
  if (additionalRule) {
    rule.skills.push(...additionalRule.skills.slice(0, MAX_SKILLS_FROM_NEXT_GRADE));
  }
  // caluclating passing score results
  const passed =
    !forceClasswideIntervention &&
    ((assessmentResultType === "benchmark" && medianScore >= cutoffTarget) ||
      hasGroupPassedClasswideIntervention({
        medianScore,
        totalStudentsAssessed,
        targetScores,
        studentScores,
        numberOfEnrolledStudents: totalStudentsEnrolled
      }));

  // determine if new PM or nothing in this tree
  let nextSkill;
  let isBenchmark = false;
  if (assessmentResultType === "benchmark") {
    isBenchmark = true;
    if (passed) {
      nextSkill = undefined;
    } else {
      nextSkill = getNextSkill({
        rule,
        passed,
        assessmentId,
        isBenchmark
      });
    }
  } else {
    // now determine what the next skill should be
    nextSkill = getNextSkill({
      rule,
      passed,
      assessmentId,
      isBenchmark,
      defaultSkillsLength
    });
  }
  return {
    passed,
    nextSkill
  };
}

export default resolveClassWideInterventionRoute;

function moveIndex(arr, oldIndex, newIndex) {
  if (newIndex >= arr.length) {
    let k = newIndex - arr.length;
    while (k-- + 1) {
      arr.push(undefined);
    }
  }
  arr.splice(newIndex, 0, arr.splice(oldIndex, 1)[0]);
  return arr; // for testing purposes
}

export async function insert(ruleDoc) {
  Rules.validate(ruleDoc);
  return Rules.insertAsync(ruleDoc);
}

export async function addSkill(ruleId, { assessmentId }) {
  check(ruleId, String);
  check(assessmentId, String);
  const skill = {
    assessmentId,
    interventions: []
  };
  const lastModified = await getTimestampInfo(getMeteorUserId(), null, "addSkill");
  await Rules.updateAsync(ruleId, { $push: { skills: skill }, $set: { lastModified } });
}

export async function removeSkill(ruleId, assessmentId) {
  check(ruleId, String);
  check(assessmentId, String);
  const rule = await Rules.findOneAsync(ruleId);
  const { skills } = rule;
  const index = skills.map(s => s.assessmentId).indexOf(assessmentId);
  const removed = skills.splice(index, 1);
  if (removed[0].assessmentId === assessmentId) {
    return Rules.updateAsync(ruleId, { $set: { skills, lastModified: await getTimestampInfo(getMeteorUserId()) } });
  }
  return false;
}

export async function moveSkillUp(ruleId, assessmentId) {
  check(ruleId, String);
  check(assessmentId, String);
  const rule = await Rules.findOneAsync(ruleId);
  let { skills } = rule;
  const index = skills.map(s => s.assessmentId).indexOf(assessmentId);
  skills = moveIndex(skills, index, index - 1);
  const lastModified = await getTimestampInfo(getMeteorUserId(), null, "moveSkillUp");
  return Rules.updateAsync(ruleId, { $set: { skills, lastModified } });
}

export async function moveSkillDown(ruleId, assessmentId) {
  check(ruleId, String);
  check(assessmentId, String);
  const rule = await Rules.findOneAsync(ruleId);
  let { skills } = rule;
  const index = skills.map(s => s.assessmentId).indexOf(assessmentId);
  skills = moveIndex(skills, index, index + 1);
  const lastModified = await getTimestampInfo(getMeteorUserId(), null, "moveSkillDown");
  return Rules.updateAsync(ruleId, { $set: { skills, lastModified } });
}

export async function processClasswideRules(assessmentResult, shouldForceClasswide = false) {
  ninjalog.trace({ msg: "Processing Classwide Rule!", context: "classwide" });
  check(assessmentResult, Object);
  const { benchmarkPeriodId } = assessmentResult;
  const { grade } = assessmentResult;
  let result = {};
  // If screening, only look at first two measures when determining classwide results
  const numberToProcess = assessmentResult.measures.length < 2 ? assessmentResult.measures.length : 2;
  for (let i = 0; i < numberToProcess; i += 1) {
    const m = assessmentResult.measures[i];
    extend(m, {
      benchmarkPeriodId,
      grade,
      assessmentResultType: assessmentResult.type
    });
    // shortcut for now. should revisit.
    let forceClasswideIntervention = shouldForceClasswide;
    if (assessmentResult.type === "benchmark" && assessmentResult.classwideResults.percentAtRisk > 50) {
      forceClasswideIntervention = true;
    }
    // eslint-disable-next-line no-await-in-loop
    result = await resolveClassWideInterventionRoute(m, forceClasswideIntervention, assessmentResult.scores.length);
    if (result.nextSkill) {
      // eslint-disable-next-line no-await-in-loop
      const assessment = await Assessments.findOneAsync(result.nextSkill.assessmentId);
      if (assessment) {
        result.nextSkill.targets = getScoreTargets({
          assessment,
          grade,
          benchmarkPeriodId,
          assessmentType: "classwide"
        });
      } else {
        throw new Meteor.Error(
          "processClasswideRules",
          `Cannot find assessment with id: ${result.nextSkill.assessmentId} Check rules for grade ${grade}.`
        );
      }
      break;
    }
  }
  // Class has finished all classwide interventions or passed screening
  if (!result.nextSkill) {
    ninjalog.log({
      msg:
        "processClasswideRules: result.nextSkill was not found.  Class is probably finishing screening and " +
        "NOT being assigned CLASSWIDE interventions or they just finished the entire classwide trees.",
      context: "classwide"
    });
  }
  return result;
}

const availableOutcomes = ["below", "at", "above"];

function validateOutcomeKeys(outcomesObject) {
  const keys = Object.keys(outcomesObject);
  return keys.length === 3 && availableOutcomes.every(outcome => keys.includes(outcome));
}

export async function getValidatedRule(ruleId, outcomes) {
  const rule = await Rules.findOneAsync(ruleId);
  if (!rule) {
    throw new Meteor.Error("getValidatedRule", `Rule with _id: ${ruleId} does not exist!`);
  }
  const outcomesValidationResult = validateOutcomeKeys(outcomes);
  if (!outcomesValidationResult) {
    throw new Meteor.Error("getValidatedRule", "Incomplete outcomes provided!");
  }
  return rule;
}

async function hasExistingRule({ rootRuleId, benchmarkPeriod, grade, assessmentId }) {
  return !!(await Rules.findOneAsync({
    rootRuleId,
    "attributeValues.benchmarkPeriod": benchmarkPeriod,
    "attributeValues.grade": grade,
    "attributeValues.assessmentId": assessmentId
  }));
}

async function addNewSubRule({ rootRuleId, enabled, benchmarkPeriod, grade, assessmentId, timestampData }) {
  if (await hasExistingRule({ rootRuleId, benchmarkPeriod, grade, assessmentId })) {
    throw new Meteor.Error("addNewSubRule", "The sub-rule already exists!");
  }
  const newSubRule = {
    rootRuleId,
    attributeValues: {
      benchmarkPeriod,
      grade,
      assessmentId
    },
    outcomes: {
      below: null,
      at: null,
      above: null
    },
    created: timestampData,
    lastModified: timestampData,
    enabled
  };
  await Rules.insertAsync(newSubRule);
}

export async function updateRuleOutcomes(ruleId, outcomeAssessments) {
  const rule = await getValidatedRule(ruleId, outcomeAssessments);
  const { outcomes } = rule;
  const rulesToAdd = new Set();
  Object.entries(outcomes).forEach(([property, currentValue]) => {
    const updatedAssessmentId = outcomeAssessments[property] && outcomeAssessments[property].assessmentId;
    if (!updatedAssessmentId) {
      outcomes[property] = null;
    } else {
      const wasAssessmentChanged = !currentValue || currentValue.assessmentId !== updatedAssessmentId;
      if (wasAssessmentChanged) {
        rulesToAdd.add(updatedAssessmentId);
      }
      outcomes[property] = {
        interventionIds: wasAssessmentChanged ? [] : currentValue.interventionIds,
        assessmentId: updatedAssessmentId
      };
    }
  });
  const timestampData = await getTimestampInfo(getMeteorUserId(), null, "updateRuleOutcomes");
  if (rulesToAdd.size) {
    const { rootRuleId, enabled, attributeValues } = rule;
    const { benchmarkPeriod, grade } = attributeValues;
    // eslint-disable-next-line no-restricted-syntax
    for await (const assessmentId of rulesToAdd) {
      if (!(await hasExistingRule({ rootRuleId, benchmarkPeriod, grade, assessmentId }))) {
        await addNewSubRule({ rootRuleId, enabled, benchmarkPeriod, grade, assessmentId, timestampData });
      }
    }
  }
  return Rules.updateAsync(ruleId, {
    $set: {
      outcomes,
      lastModified: timestampData
    }
  });
}

export async function updateOutcomeInterventions(ruleId, outcomeInterventions) {
  const rule = await getValidatedRule(ruleId, outcomeInterventions);
  const { outcomes } = rule;
  Object.entries(outcomes).forEach(([property, value]) => {
    const updatedPropertyValue = outcomeInterventions[property];
    if (value && Array.isArray(updatedPropertyValue)) {
      outcomes[property] = {
        ...value,
        interventionIds: updatedPropertyValue.map(int => int.interventionId)
      };
    }
  });
  const lastModified = await getTimestampInfo(getMeteorUserId(), null, "updateOutcomeInterventions");

  return Rules.updateAsync(ruleId, {
    $set: {
      outcomes,
      lastModified
    }
  });
}

async function getValidatedRuleAndOutcomeName(ruleId, outcomeName, checkValue = true) {
  const rule = await Rules.findOneAsync(ruleId);
  if (!rule) {
    throw new Meteor.Error("validateRuleAndOutcomeValue", `Rule with _id: ${ruleId} does not exist!`);
  }
  if (!availableOutcomes.includes(outcomeName)) {
    throw new Meteor.Error("validateRuleAndOutcomeValue", `The provided outcome: "${outcomeName}" is not supported!`);
  }
  if (checkValue) {
    const currentValue = get(rule.outcomes, `${outcomeName}.interventionIds.length`);
    if (currentValue) {
      throw new Meteor.Error("validateRuleAndOutcomeValue", `The provided outcome: "${outcomeName}" is not empty!`);
    }
  }
  return rule;
}

async function updateRuleOutcome(ruleId, outcomeName, assessmentId) {
  const lastModified = await getTimestampInfo(getMeteorUserId(), null, "updateRuleOutcome");
  return Rules.updateAsync(ruleId, {
    $set: {
      [`outcomes.${outcomeName}`]: { interventionIds: [], assessmentId },
      lastModified
    }
  });
}

export async function addInterventionsToOutcome({ outcomeName, ruleId, assessmentId }) {
  await getValidatedRuleAndOutcomeName(ruleId, outcomeName);
  const assessment = await Assessments.findOneAsync(assessmentId);
  if (!assessment) {
    throw new Meteor.Error("addInterventionsToOutcome", `Assessment with _id: ${assessmentId} does not exist!`);
  }
  return updateRuleOutcome(ruleId, outcomeName, assessmentId);
}

export async function addOutcomeToRule(ruleId, outcomeName) {
  const rule = await getValidatedRuleAndOutcomeName(ruleId, outcomeName);
  const { assessmentId } = rule.attributeValues;
  return updateRuleOutcome(ruleId, outcomeName, assessmentId);
}

export async function removeRuleOutcome(ruleId, outcomeName) {
  await getValidatedRuleAndOutcomeName(ruleId, outcomeName, false);
  const lastModified = await getTimestampInfo(getMeteorUserId(), null, "removeRuleOutcome");
  return Rules.updateAsync(ruleId, {
    $set: {
      [`outcomes.${outcomeName}`]: null,
      lastModified
    }
  });
}

export async function addRootRuleFor({ assessmentId, grade, benchmarkPeriodId }) {
  if (!assessmentId || !grade || !benchmarkPeriodId) {
    throw new Meteor.Error("addRootRuleFor", "Insufficent data provided");
  }
  const timestamp = await getTimestampInfo(getMeteorUserId(), null, "addRootRuleFor");
  const ruleId = await Rules.insertAsync({
    attributeValues: {
      grade,
      benchmarkPeriod: translateBenchmarkPeriod(benchmarkPeriodId).label,
      assessmentId
    },
    outcomes: {
      above: null,
      at: null,
      below: null
    },
    enabled: true,
    created: timestamp,
    lastModified: timestamp
  });
  await Rules.updateAsync(ruleId, { $set: { rootRuleId: ruleId } });
  return ruleId;
}

export async function checkProgressMonitoringManagementPermissions(methodName) {
  const userId = getMeteorUserId();
  if (!userId) {
    throw new Meteor.Error(methodName, "No logged in user found!");
  }
  if (!(await auth.hasAccess(["superAdmin"], { userId }))) {
    throw new Meteor.Error(methodName, "You are not authorized to manage Progress Monitoring");
  }
}

Meteor.methods({
  async "Rules:insert"(ruleDoc) {
    check(ruleDoc, Object);
    if (!this.userId) {
      Meteor.Error(403, "No logged in user found!");
    }
    return insert(ruleDoc);
  },
  async "Rules:addSkill"(ruleId, { assessmentId }) {
    check(ruleId, String);
    check(assessmentId, String);
    if (!this.userId) {
      Meteor.Error(403, "No logged in user found!");
    }
    return addSkill(ruleId, {
      assessmentId
    });
  },
  async "Rules:removeSkill"(ruleId, assessmentId) {
    check(ruleId, String);
    check(assessmentId, String);
    if (!this.userId) {
      Meteor.Error(403, "No logged in user found!");
    }
    return removeSkill(ruleId, assessmentId);
  },
  async "Rules:moveSkillUp"(ruleId, assessmentId) {
    check(ruleId, String);
    check(assessmentId, String);
    if (!this.userId) {
      Meteor.Error(403, "No logged in user found!");
    }
    return moveSkillUp(ruleId, assessmentId);
  },
  async "Rules:moveSkillDown"(ruleId, assessmentId) {
    check(ruleId, String);
    check(assessmentId, String);
    if (!this.userId) {
      Meteor.Error(403, "No logged in user found!");
    }
    return moveSkillDown(ruleId, assessmentId);
  },
  async "Rules:updateRuleOutcomes"(ruleId, outcomeAssessments) {
    check(ruleId, String);
    check(outcomeAssessments, Object);
    await checkProgressMonitoringManagementPermissions("Rules:updateRuleOutcomes");
    return updateRuleOutcomes(ruleId, outcomeAssessments);
  },
  async "Rules:updateOutcomeInterventions"(ruleId, outcomeAssessments) {
    check(ruleId, String);
    check(outcomeAssessments, Object);
    await checkProgressMonitoringManagementPermissions("Rules:updateOutcomeInterventions");
    return updateOutcomeInterventions(ruleId, outcomeAssessments);
  },
  async "Rules:addInterventionsToOutcome"({ outcomeName, ruleId, assessmentId }) {
    check(ruleId, String);
    check(outcomeName, String);
    check(assessmentId, String);
    await checkProgressMonitoringManagementPermissions("Rules:addInterventionsToOutcome");
    return addInterventionsToOutcome({ outcomeName, ruleId, assessmentId });
  },
  async "Rules:addOutcomeToRule"(ruleId, outcomeName) {
    check(ruleId, String);
    check(outcomeName, String);
    await checkProgressMonitoringManagementPermissions("Rules:addOutcomeToRule");
    return addOutcomeToRule(ruleId, outcomeName);
  },
  async "Rules:removeRuleOutcome"(ruleId, outcomeName) {
    check(ruleId, String);
    check(outcomeName, String);
    await checkProgressMonitoringManagementPermissions("Rules:removeRuleOutcome");
    return removeRuleOutcome(ruleId, outcomeName);
  },
  async "Rules:addRootRuleFor"({ assessmentId, grade, benchmarkPeriodId }) {
    check(assessmentId, String);
    check(grade, String);
    check(benchmarkPeriodId, String);
    await checkProgressMonitoringManagementPermissions("Rules:addRootRuleFor");
    return addRootRuleFor({ assessmentId, grade, benchmarkPeriodId });
  }
});
