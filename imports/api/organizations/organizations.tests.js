import { Meteor } from "meteor/meteor";
import { assert } from "chai";

import { Organizations } from "./organizations.js";
import { organization } from "../../test-helpers/data/organizations.js";

if (Meteor.isServer) {
  describe("Organizations", () => {
    describe("Should pass schema validation method", () => {
      it("validate", () => {
        assert.isUndefined(Organizations.validate(organization()));
      });
      it("isValid", () => {
        assert.isTrue(Organizations.isValid(organization()));
      });
    });
  });
}
