import { Meteor } from "meteor/meteor";
import { check, Match } from "meteor/check";
import { Organizations } from "../organizations";
import * as auth from "../../authorization/server/methods";
import { getMeteorUser, isUserLoggedOut, ninjalog } from "../../utilities/utilities";
import { hasDashboard } from "../../users/server/methods";

function getOrganizationsCursor(orgid, query) {
  const organizationsQuery = orgid ? { _id: orgid } : query;
  return Organizations.find(organizationsQuery, {
    fields: {
      benchmarkPeriodsGroupId: 1,
      classwideEnabled: 1,
      created: 1,
      details: 1,
      isActive: 1,
      isDistrict: 1,
      isSelfEnrollee: 1,
      isTestOrg: 1,
      name: 1,
      rostering: 1,
      rosteringSettings: 1,
      schoolYearBoundary: 1,
      schoolBreaks: 1,
      allowMultipleGradeLevels: 1,
      ssoIssuerOrgId: 1,
      useSSOOnly: 1,
      isMFARequired: 1
    }
  });
}

Meteor.publish("Organizations", async function organizationsPublication(orgid) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(orgid, Match.Maybe(String));

  const user = await getMeteorUser();

  if (!user) {
    return [];
  }

  if (
    await auth.hasAccess(["universalCoach", "superAdmin", "universalDataAdmin", "downloader"], {
      user
    })
  ) {
    const query = {};
    return getOrganizationsCursor(orgid, query);
  }

  if (auth.hasRoleInSiteAccess(auth.getSiteAccess(user), "support")) {
    const query = { _id: { $in: auth.getSupportUserAccess(user) } };
    return getOrganizationsCursor(orgid, query);
  }

  return getOrganizationsCursor(user.profile.orgid);
});

Meteor.publish("Organizations:NameById", async function nameById() {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  if (
    (await auth.hasAccess(["superAdmin", "universalDataAdmin"], {
      userId: this.userId
    })) ||
    (await hasDashboard())
  ) {
    return Organizations.find({}, { fields: { name: 1, isActive: 1 } });
  }
  ninjalog.log({
    msg: "Publication Access Denied: Organizations:NameById"
  });
  return this.ready();
});

Meteor.publish("Organizations:NamesAndStates", async function nameById() {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  if (
    (await auth.hasAccess(["superAdmin", "universalDataAdmin"], {
      userId: this.userId
    })) ||
    (await hasDashboard())
  ) {
    return Organizations.find({}, { fields: { name: 1, "details.state": 1, isActive: 1 } });
  }
  ninjalog.log({
    msg: "Publication Access Denied: Organizations:NamesAndStates"
  });
  return this.ready();
});
