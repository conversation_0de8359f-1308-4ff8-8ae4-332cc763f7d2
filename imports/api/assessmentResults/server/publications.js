import { Meteor } from "meteor/meteor";
import { check, Match } from "meteor/check";
import { AssessmentResults } from "../assessmentResults";
import { StudentGroups } from "../../studentGroups/studentGroups";
import * as utils from "../../utilities/utilities";
import { Users } from "../../users/users";
import { isUserLoggedOut } from "../../utilities/utilities";

Meteor.publish("getAssessmentResultsForStudentGroupOrByIds", async function getAssessmentResultsPub({
  studentGroupId,
  orgid,
  ids
}) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(studentGroupId, String);
  check(orgid, String);
  check(ids, Match.Maybe([String]));

  const user = await Users.findOneAsync(this.userId);
  const schoolYear = await utils.getCurrentSchoolYear(user, orgid);
  return AssessmentResults.find(
    {
      $or: [{ _id: { $in: ids } }, { studentGroupId, schoolYear }]
    },
    { sort: { "created.on": 1 } }
  );
});

Meteor.publish("AssessmentResultsForStudentGroup", async function(studentGroupId, orgid) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(studentGroupId, String);
  check(orgid, String);

  const user = await Users.findOneAsync(this.userId);
  const schoolYear = await utils.getCurrentSchoolYear(user, orgid);
  return AssessmentResults.find({ studentGroupId, schoolYear }, { sort: { "created.on": -1 } });
});

Meteor.publish("AssessmentResults:FindByIds", function f(assResIds) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(assResIds, [String]);

  return AssessmentResults.find({ _id: { $in: assResIds } });
});

Meteor.publish("AssessmentResults:screeningResultsBySite", async function(siteId, schoolYear) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  if (!siteId || !schoolYear) {
    utils.ninjalog.log({
      msg: "Publication Access Denied: AssessmentResults:screeningResultsBySite - missing parameters"
    });
    return this.ready();
  }
  check(siteId, String);
  check(schoolYear, Number);

  const studentGroupIds = (
    await StudentGroups.find({ siteId, schoolYear, isActive: true }, { fields: { _id: 1 } }).fetchAsync()
  ).map(sg => sg._id);
  return AssessmentResults.find(
    { studentGroupId: { $in: studentGroupIds }, schoolYear, type: "benchmark" },
    { fields: { studentGroupId: 1, type: 1, status: 1, benchmarkPeriodId: 1, grade: 1, classwideResults: 1 } }
  );
});

Meteor.publish("AssessmentResults:ForSite", async function(siteId, schoolYear, projection = {}) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  if (!siteId || !schoolYear) {
    utils.ninjalog.log({
      msg: "Publication Access Denied: AssessmentResults:ForSite - missing parameters"
    });
    return this.ready();
  }
  check(siteId, String);
  check(schoolYear, Number);
  check(projection, Object);

  const studentGroupIds = (
    await StudentGroups.find({ siteId, schoolYear, isActive: true }, { fields: { _id: 1 } }).fetchAsync()
  ).map(sg => sg._id);
  return AssessmentResults.find(
    { studentGroupId: { $in: studentGroupIds }, schoolYear },
    {
      fields: {
        assessmentIds: 1,
        benchmarkPeriodId: 1,
        classwideResults: 1,
        grade: 1,
        measures: 1,
        nextAssessmentResultId: 1,
        orgid: 1,
        previousAssessmentResultId: 1,
        ruleResults: 1,
        schoolYear: 1,
        status: 1,
        studentGroupId: 1,
        studentId: 1,
        type: 1,
        ...projection
      }
    }
  );
});

Meteor.publish("AssessmentResults:IndividualByStudentGroupIds", function(studentGroupIds, schoolYear, grade) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(studentGroupIds, [String]);
  check(schoolYear, Number);
  check(grade, Match.OneOf(String, Object));

  return AssessmentResults.find({
    studentGroupId: { $in: studentGroupIds },
    schoolYear,
    grade,
    type: "individual"
  });
});

Meteor.publish("AssessmentResults:FindStudentResultsFromOtherGroups", function({
  studentGroupId,
  studentIds,
  schoolYear
}) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(studentGroupId, String);
  check(studentIds, [String]);
  check(schoolYear, Number);

  if (!studentIds.length) {
    return this.ready();
  }
  const type = { $in: ["classwide", "benchmark"] };
  return AssessmentResults.find(
    {
      studentGroupId: { $ne: studentGroupId },
      schoolYear,
      status: "COMPLETED",
      type,
      "measures.studentResults.studentId": { $in: studentIds }
    },
    {
      fields: {
        lastModified: 1,
        type: 1,
        "measures.assessmentId": 1,
        "measures.assessmentName": 1,
        "measures.studentResults": 1,
        benchmarkPeriodId: 1,
        studentGroupId: 1,
        status: 1
      }
    }
  );
});
