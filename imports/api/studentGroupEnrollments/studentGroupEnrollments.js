import { Mongo } from "meteor/mongo";
import SimpleSchema from "simpl-schema";
import { ByDateOn } from "../helpers/schemas/byDateOn/byDateOn.js";

export const StudentGroupEnrollments = new Mongo.Collection("StudentGroupEnrollments");

StudentGroupEnrollments.schema = new SimpleSchema({
  _id: { type: String, optional: true },
  orgid: { type: String },
  siteId: { type: String },
  grade: { type: String },
  studentGroupId: { type: String },
  studentId: { type: String },
  isActive: { type: Boolean },
  schoolYear: { type: Number },
  isInIndividualIntervention: { type: Boolean, optional: true },
  wasIndividualInterventionClosed: { type: Boolean, optional: true },
  giftedAndTalented: { type: Boolean },
  ELL: { type: Boolean },
  IEP: { type: Boolean },
  title1: { type: Boolean },
  freeReducedLunch: { type: String },
  created: { type: ByDateOn },
  lastModified: { type: ByDateOn }
});

StudentGroupEnrollments.validate = studentGroupEnrollment => {
  StudentGroupEnrollments.schema.validate(studentGroupEnrollment);
};

StudentGroupEnrollments.isValid = studentGroupEnrollment =>
  StudentGroupEnrollments.schema.namedContext("checkStudentGroupEnrollment").validate(studentGroupEnrollment);
