import { Meteor } from "meteor/meteor";
import { assert } from "chai";

import { StudentGroupEnrollments } from "./studentGroupEnrollments.js";
import { studentGroupEnrollment } from "../../test-helpers/data/studentGroupEnrollments.js";

if (Meteor.isServer) {
  describe("StudentGroupEnrollments", () => {
    describe("Should pass schema validation ", () => {
      it("this method is for inline testing", () => {
        assert.isUndefined(StudentGroupEnrollments.validate(studentGroupEnrollment({})));
      });
      it("this method returns a value", () => {
        assert.isTrue(StudentGroupEnrollments.isValid(studentGroupEnrollment({})));
      });
    });
    describe("Should fail schema validation ", () => {
      it("this method returns a value", () => {
        const studentGroupEnrollmentDoc = studentGroupEnrollment({});
        studentGroupEnrollmentDoc._id = 1234;
        assert.isFalse(StudentGroupEnrollments.isValid(studentGroupEnrollmentDoc));
      });
    });
  });
}
