import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";
import { StudentGroupEnrollments } from "./studentGroupEnrollments.js";
import { hasSiteAccess } from "../authorization/server/methods";
import { getTimestampInfo } from "../helpers/getTimestampInfo";
import { getMeteorUserId } from "../utilities/utilities";

Meteor.methods({
  // TODO(fmazur) - rename
  async "StudentGroupEnrollments:isInIndividualIntervention"({ studentGroupId, studentId }) {
    check(studentGroupId, String);
    check(studentId, String);
    const sge = await StudentGroupEnrollments.findOneAsync({
      studentGroupId,
      studentId
    });
    if (await hasSiteAccess(this.userId, sge.siteId)) {
      const lastModified = await getTimestampInfo(
        this?.userId || getMeteorUserId(),
        sge?.orgid,
        "StudentGroupEnrollments:isInIndividualIntervention"
      );
      await StudentGroupEnrollments.updateAsync(sge._id, {
        $set: { isInIndividualIntervention: true, lastModified }
      });
    }
  }
});
