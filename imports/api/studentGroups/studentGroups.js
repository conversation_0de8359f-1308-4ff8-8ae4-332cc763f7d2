import { Mongo } from "meteor/mongo";
import SimpleSchema from "simpl-schema";
import { ByDateOn } from "../helpers/schemas/byDateOn/byDateOn";

export const StudentGroups = new Mongo.Collection("StudentGroups");

StudentGroups.schema = new SimpleSchema({
  _id: { type: String, optional: true },
  created: ByDateOn,
  currentAssessmentResultIds: [String],
  currentClasswideSkill: { type: Object, blackbox: true, optional: true },
  grade: { type: String },
  history: { type: Array, blackbox: true, optional: true },
  "history.$": { type: Object, optional: true },
  isActive: { type: Boolean },
  rosterImportId: { type: String, optional: true },
  lastModified: { type: ByDateOn },
  name: { type: String },
  orgid: { type: String },
  ownerIds: [String],
  schoolYear: { type: Number },
  sectionId: { type: String },
  siteId: { type: String },
  type: { type: String }
});

StudentGroups.validate = studentGroup => {
  StudentGroups.schema.validate(studentGroup);
};

StudentGroups.isValid = studentGroup => StudentGroups.schema.namedContext("checkStudentGroup").validate(studentGroup);
