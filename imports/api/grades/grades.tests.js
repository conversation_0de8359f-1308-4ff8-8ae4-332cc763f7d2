import { Meteor } from "meteor/meteor";
import { assert } from "chai";

import { Grades } from "./grades.js";
import { grades } from "../../test-helpers/data/grades.js";

if (Meteor.isServer) {
  describe("Grades", () => {
    describe("Grade K", () => {
      describe("Should pass schema validation method", () => {
        it("validate", () => {
          assert.isUndefined(Grades.validate(grades()[0]));
        });
        it("isValid", () => {
          assert.isTrue(Grades.isValid(grades()[0]));
        });
      });
    });
  });
}
