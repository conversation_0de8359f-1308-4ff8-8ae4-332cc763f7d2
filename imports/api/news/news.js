import { Mongo } from "meteor/mongo";
import SimpleSchema from "simpl-schema";
import { ByDateOn } from "../helpers/schemas/byDateOn/byDateOn";

// eslint-disable-next-line import/prefer-default-export
export const News = new Mongo.Collection("News");

News.schema = new SimpleSchema({
  _id: { type: String, optional: true },
  learnMoreActive: { type: Boolean },
  learnMoreUrl: { type: String },
  messageActive: { type: Boolean },
  messageContent: { type: String },
  messageColor: { type: String },
  messageTextColor: { type: String },
  created: { type: ByDateOn },
  lastModified: { type: ByDateOn },
  type: { type: String, optional: true }
});

News.validate = news => {
  News.schema.validate(news);
};
News.isValid = news => News.schema.namedContext("checkNews").validate(news);
