import { News } from "../news";
import { addNewMessage, updateMessage } from "./methods";

describe("News", () => {
  const activeNewsMessageId = "activeNews";
  const inactiveNewsMessageId = "inactiveNews";
  const messageContent = "Test content";
  const updatedMessageContent = "Updated content";

  describe("addNewMessage", () => {
    const testNews = [];
    const validNewMessage = {
      learnMoreActive: true,
      learnMoreUrl: "",
      messageActive: true,
      messageContent,
      messageColor: "#fa5",
      messageTextColor: "#fff",
      created: expect.any(Object),
      lastModified: expect.any(Object)
    };
    beforeEach(async () => {
      await News.insertAsync(testNews);
    });
    afterEach(async () => {
      await News.removeAsync({});
    });
    it("should add new news message", async () => {
      await addNewMessage(validNewMessage);
      const allNews = await News.find().fetchAsync();
      expect(allNews).toEqual([{ _id: expect.any(String), ...validNewMessage }]);
    });
    it("should deactivate other messages if added message is set to be active", async () => {
      await addNewMessage(validNewMessage);
      validNewMessage.messageContent = "NewMessage";
      await addNewMessage(validNewMessage);
      const activeMessages = await News.find({ messageActive: true }).fetchAsync();
      expect(activeMessages).toHaveLength(1);
    });
  });

  describe("updateMessage", () => {
    const messageColor = "#fa5";
    const messageTextColor = "#fff";
    const expectedMessageColor = "#fff";
    const expectedMessageTextColor = "#fa5";
    const testNews = [
      {
        _id: inactiveNewsMessageId,
        learnMoreActive: true,
        learnMoreUrl: "",
        messageActive: false,
        messageContent: "Test",
        messageColor,
        messageTextColor
      },
      {
        _id: activeNewsMessageId,
        learnMoreActive: false,
        learnMoreUrl: "",
        messageActive: true,
        messageContent,
        messageColor,
        messageTextColor
      }
    ];
    beforeEach(async () => {
      await News.insertAsync(testNews);
    });
    afterEach(async () => {
      await News.removeAsync({});
    });
    it("should update news message with provided data", async () => {
      const updatedInactiveMessage = {
        _id: inactiveNewsMessageId,
        learnMoreActive: false,
        learnMoreUrl: "",
        messageActive: false,
        messageContent: updatedMessageContent,
        messageColor: expectedMessageColor,
        messageTextColor: expectedMessageTextColor
      };

      await updateMessage(updatedInactiveMessage);
      const expectedUpdatedMessage = await News.findOneAsync({ _id: inactiveNewsMessageId });
      expect(expectedUpdatedMessage).toMatchObject(updatedInactiveMessage);
    });
    it("should activate message and deactivate others", async () => {
      const messageParamsToUpdate = {
        _id: inactiveNewsMessageId,
        messageActive: true,
        messageContent: updatedMessageContent
      };
      await updateMessage(messageParamsToUpdate);
      const allMessages = await News.find({ messageActive: true }).fetchAsync();
      expect(allMessages).toHaveLength(1);
    });
  });
});
