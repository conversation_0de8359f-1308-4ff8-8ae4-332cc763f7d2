import moment from "moment";

import { updateBenchmarkWindowsForOrganization } from "./methods";
import { BenchmarkWindows } from "./benchmarkWindows";
import { BenchmarkPeriods } from "../benchmarkPeriods/benchmarkPeriods";

function getDateWithDayTime(dateString, dayTime) {
  const date = moment.utc(dateString);
  if (["start", "end"].includes(dayTime)) {
    date[`${dayTime}Of`]("day");
  }
  return new Date(date.toISOString());
}

describe("updateBenchmarkWindowsForOrganization", () => {
  const userId = "userId";
  const orgid = "orgid";
  const siteId = "siteId";
  const siteId2 = "siteId2";
  const schoolYear = 2023;
  const schoolYearBoundary = {
    month: 8,
    day: 8
  };
  const benchmarkPeriodsGroupId = "custom";
  const fallBenchmarkPeriodId = "fallBenchmarkPeriodId";
  const winterBenchmarkPeriodId = "winterBenchmarkPeriodId";
  const springBenchmarkPeriodId = "springBenchmarkPeriodId";
  const fallStartDate = getDateWithDayTime("2022-08-01", "start");
  const fallEndDate = getDateWithDayTime("2022-12-31", "end");
  const winterStartDate = getDateWithDayTime("2023-01-01", "start");
  const winterEndDate = getDateWithDayTime("2023-03-31", "end");
  const springStartDate = getDateWithDayTime("2023-04-01", "start");
  const springEndDate = getDateWithDayTime("2023-07-31", "end");
  const benchmarkPeriods = [
    {
      _id: fallBenchmarkPeriodId,
      startDate: {
        default: {
          month: 8,
          day: 1
        },
        [benchmarkPeriodsGroupId]: {
          month: 9,
          day: 9
        }
      },
      endDate: {
        default: {
          month: 12,
          day: 31
        },
        [benchmarkPeriodsGroupId]: {
          month: 2,
          day: 8
        }
      },
      created: {
        by: userId
      },
      lastModified: {
        by: userId
      }
    },
    {
      _id: winterBenchmarkPeriodId,
      startDate: {
        default: {
          month: 1,
          day: 1
        },
        [benchmarkPeriodsGroupId]: {
          month: 2,
          day: 9
        }
      },
      endDate: {
        default: {
          month: 3,
          day: 31
        },
        [benchmarkPeriodsGroupId]: {
          month: 5,
          day: 8
        }
      },
      created: {
        by: userId
      },
      lastModified: {
        by: userId
      }
    },
    {
      _id: springBenchmarkPeriodId,
      startDate: {
        default: {
          month: 4,
          day: 1
        },
        [benchmarkPeriodsGroupId]: {
          month: 5,
          day: 9
        }
      },
      endDate: {
        default: {
          month: 7,
          day: 31
        },
        [benchmarkPeriodsGroupId]: {
          month: 9,
          day: 8
        }
      },
      created: {
        by: userId
      },
      lastModified: {
        by: userId
      }
    }
  ];
  const benchmarkWindows = [
    {
      schoolYear,
      orgid,
      siteId,
      benchmarkPeriodId: fallBenchmarkPeriodId,
      startDate: fallStartDate,
      endDate: fallEndDate
    },
    {
      schoolYear,
      orgid,
      siteId,
      benchmarkPeriodId: winterBenchmarkPeriodId,
      startDate: winterStartDate,
      endDate: winterEndDate
    },
    {
      schoolYear,
      orgid,
      siteId,
      benchmarkPeriodId: springBenchmarkPeriodId,
      startDate: springStartDate,
      endDate: springEndDate
    },
    {
      schoolYear,
      orgid,
      siteId: siteId2,
      benchmarkPeriodId: fallBenchmarkPeriodId,
      startDate: fallStartDate,
      endDate: fallEndDate
    },
    {
      schoolYear,
      orgid,
      siteId: siteId2,
      benchmarkPeriodId: winterBenchmarkPeriodId,
      startDate: winterStartDate,
      endDate: winterEndDate
    },
    {
      schoolYear,
      orgid,
      siteId: siteId2,
      benchmarkPeriodId: springBenchmarkPeriodId,
      startDate: springStartDate,
      endDate: springEndDate
    }
  ];

  beforeAll(async () => {
    await BenchmarkPeriods.insertAsync(benchmarkPeriods);
    await BenchmarkWindows.insertAsync(benchmarkWindows);
  });
  afterAll(async () => {
    await BenchmarkPeriods.removeAsync({});
    await BenchmarkWindows.removeAsync({});
  });

  it("should return false and don't do anything when there aren't any Benchmark Windows for an organization", async () => {
    const result = await updateBenchmarkWindowsForOrganization({
      orgid: "orgid2",
      benchmarkPeriodsGroupId,
      schoolYear,
      schoolYearBoundary
    });

    expect(result).toBe(false);
  });

  it("should correctly update existing Benchmark Windows based on a benchmarkPeriodsGroupId value", async () => {
    const result = await updateBenchmarkWindowsForOrganization({
      orgid,
      benchmarkPeriodsGroupId,
      schoolYear,
      schoolYearBoundary
    });

    expect(result).toBe(true);

    const fallBenchmarkWindows = await BenchmarkWindows.find({
      orgid,
      benchmarkPeriodId: fallBenchmarkPeriodId
    }).fetchAsync();
    fallBenchmarkWindows.forEach(fallBenchmarkWindow => {
      expect(fallBenchmarkWindow.startDate).toEqual(getDateWithDayTime("2022-09-09", "start"));
      expect(fallBenchmarkWindow.endDate).toEqual(getDateWithDayTime("2023-02-08", "end"));
    });
    const winterBenchmarkWindows = await BenchmarkWindows.find({
      orgid,
      benchmarkPeriodId: winterBenchmarkPeriodId
    }).fetchAsync();
    winterBenchmarkWindows.forEach(winterBenchmarkWindow => {
      expect(winterBenchmarkWindow.startDate).toEqual(getDateWithDayTime("2023-02-09", "start"));
      expect(winterBenchmarkWindow.endDate).toEqual(getDateWithDayTime("2023-05-08", "end"));
    });
    const springBenchmarkWindows = await BenchmarkWindows.find({
      orgid,
      benchmarkPeriodId: springBenchmarkPeriodId
    }).fetchAsync();
    springBenchmarkWindows.forEach(springBenchmarkWindow => {
      expect(springBenchmarkWindow.startDate).toEqual(getDateWithDayTime("2023-05-09", "start"));
      expect(springBenchmarkWindow.endDate).toEqual(getDateWithDayTime("2023-09-08", "end"));
    });
  });
});
