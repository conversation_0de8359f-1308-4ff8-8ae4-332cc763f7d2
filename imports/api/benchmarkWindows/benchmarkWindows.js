import { Mongo } from "meteor/mongo";
import SimpleSchema from "simpl-schema";

import { ByDateOn } from "../helpers/schemas/byDateOn/byDateOn.js";

export const BenchmarkWindows = new Mongo.Collection("BenchmarkWindows");

BenchmarkWindows.schema = new SimpleSchema({
  _id: { type: String, optional: true },
  schoolYear: { type: Number },
  benchmarkPeriodId: { type: String },
  siteId: { type: String },
  startDate: { type: Date },
  endDate: { type: Date },
  created: { type: ByDateOn },
  lastModified: { type: ByDateOn },
  orgid: { type: String }
});

BenchmarkWindows.validate = benchmarkWindow => {
  BenchmarkWindows.schema.validate(benchmarkWindow);
};

BenchmarkWindows.isValid = benchmarkWindow =>
  BenchmarkWindows.schema.namedContext("benchmarkWindowContext").validate(benchmarkWindow);
