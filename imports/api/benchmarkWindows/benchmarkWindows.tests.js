import { Meteor } from "meteor/meteor";
import { assert } from "chai";

import { BenchmarkWindows } from "./benchmarkWindows.js";
import { benchmarkWindows } from "../../test-helpers/data/benchmarkWindows.js";

if (Meteor.isServer) {
  describe("BenchmarkWindows", () => {
    describe("Should pass schema validation method", () => {
      it("validate", () => {
        assert.isUndefined(BenchmarkWindows.validate(benchmarkWindows({})));
      });
      it("isValid", () => {
        assert.isTrue(BenchmarkWindows.isValid(benchmarkWindows({})));
      });
    });
    describe("Should fail schema validation method", () => {
      it("isValid", () => {
        const benchmarkWindowDoc = benchmarkWindows({});
        benchmarkWindowDoc._id = 1234;
        assert.isFalse(BenchmarkWindows.isValid(benchmarkWindowDoc));
      });
    });
  });
}
