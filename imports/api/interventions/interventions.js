import { Mongo } from "meteor/mongo";
import SimpleSchema from "simpl-schema";

export const Interventions = new Mongo.Collection("Interventions");

Interventions.schema = new SimpleSchema({
  _id: { type: String, optional: true },
  abbreviation: { type: String },
  name: { type: String }
});

Interventions.validate = intervention => Interventions.schema.validate(intervention);

Interventions.isValid = intervention => Interventions.schema.namedContext("interventionContext").validate(intervention);
