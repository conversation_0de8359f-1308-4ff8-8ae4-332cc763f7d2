import { assert } from "chai";

import { Interventions } from "./interventions.js";

describe("Interventions", () => {
  const interventions = [
    {
      _id: "intervention_id_for_bingo",
      name: "Intervention Adviser - Bingo",
      abbreviation: "B"
    }
  ];
  describe("Should pass schema validation method", () => {
    it("validate", () => {
      assert.isUndefined(Interventions.validate(interventions[0]));
    });
    it("isValid", () => {
      assert.isTrue(Interventions.isValid(interventions[0]));
    });
  });
  describe("Should fail schema validation method", () => {
    it("isValid", () => {
      const interventionDoc = interventions[0];
      interventionDoc._id = 1234;
      assert.isFalse(Interventions.isValid(interventionDoc));
    });
  });
});
