import { Meteor } from "meteor/meteor";
import { check, Match } from "meteor/check";
import { Users } from "/imports/api/users/users";
import { get, keyBy } from "lodash";
import { AuditLogs } from "../auditLogs";
import * as auth from "../../authorization/server/methods";
import { replacePropertiesInObject } from "./utilities";
import { rosteringSettingsReplacer } from "../../utilities/utilities";

Meteor.methods({
  async "AuditLogs:getLogs"(orgid, type, page, limit) {
    check(orgid, Match.Maybe(String));
    check(type, Match.Maybe(String));
    check(page, Number);
    check(limit, Number);

    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "Not authorized to use AuditLogs:getLogs");
    }

    const positiveLimit = Math.max(limit, 0);
    const positiveSkip = Math.max(page - 1, 0) * positiveLimit;
    const query = {
      ...(orgid ? { orgid } : {}),
      ...(type ? { type } : {})
    };
    const totalCount = await AuditLogs.find(query).countAsync();
    const data = await AuditLogs.find(query, {
      sort: { "created.on": -1 },
      skip: positiveSkip,
      limit: positiveLimit
    }).fetchAsync();

    // resolve user ids to names
    const userIds = data.map(log => log.created.by);
    const uniqUserIds = [...new Set(userIds)];
    const users = await Users.find({ _id: { $in: uniqUserIds } }, { fields: { "profile.name": 1 } }).fetchAsync();
    const usersByUserId = keyBy(users, "_id");
    data.forEach(datum => {
      const previousClientSecretValue = datum.outdated?.find(obj => get(obj, "rosteringSettings.clientSecret"))
        ?.rosteringSettings.clientSecret;
      replacePropertiesInObject({
        object: datum,
        propertyNamesToReplace: ["rosteringSettings"],
        replacerFunc: rosteringSettingsReplacer,
        replacerArgs: [["<Old Password>", "<New Password>"], previousClientSecretValue]
      });
    });
    const dataWithNames = data.map(item => ({
      ...item,
      userProfileName: usersByUserId[item.created.by]?.profile?.name || {
        first: "Unknown",
        last: "",
        middle: ""
      }
    }));
    return {
      totalCount,
      data: dataWithNames
    };
  }
});
