// Replace properties, designated by name from an array of strings, mutating object on any level in the structure using a replacer function.
export function replacePropertiesInObject({ object, propertyNamesToReplace, replacerFunc, replacerArgs = [] }) {
  if (object !== null) {
    if (Array.isArray(object)) {
      object.forEach(elem =>
        replacePropertiesInObject({
          object: elem,
          propertyNamesToReplace,
          replacerFunc,
          replacerArgs
        })
      );
    } else if (typeof object === "object") {
      Object.entries(object).forEach(([k, v]) => {
        if (propertyNamesToReplace.some(key => k === key)) {
          // eslint-disable-next-line no-param-reassign
          object[k] = replacerFunc(v, ...replacerArgs);
        }
        replacePropertiesInObject({
          object: v,
          propertyNamesToReplace,
          replacerFunc,
          replacerArgs
        });
      });
    }
  }
}

export const simpleReplacer = () => {
  return "***";
};
