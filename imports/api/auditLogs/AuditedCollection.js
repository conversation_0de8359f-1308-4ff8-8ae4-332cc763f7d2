import { Mon<PERSON> } from "meteor/mongo";
import { check, Match } from "meteor/check";
import { AuditLogs } from "./auditLogs";

export class AuditedCollection extends Mongo.Collection {
  // first arg is the log config for audit page filtering
  // the rest of arguments are passed through to standard update method
  // logConfig: { type: 'name of the log', orgid, timestampInfo: result of getTimestampInfo }
  updateAndLog = async (logArgs, selector, modifier, options, callback) => {
    check(logArgs.orgid, String);
    check(logArgs.type, String);
    check(logArgs.findOptions, Match.Maybe(Object));
    check(logArgs.timestampInfo, Object);
    check(logArgs.timestampInfo.by, String);
    check(logArgs.timestampInfo.on, Number);
    check(logArgs.timestampInfo.date, Date);
    const singleDoc = !options?.multi;
    const outdated = singleDoc
      ? [await this.findOneAsync(selector, logArgs.findOptions)]
      : await this.find(selector, logArgs.findOptions).fetchAsync();
    const result = await this.updateAsync(selector, modifier, options, callback);
    const updated = singleDoc
      ? [await this.findOneAsync(selector, logArgs.findOptions)]
      : await this.find(selector, logArgs.findOptions).fetchAsync();
    await AuditLogs.insertAsync({
      orgid: logArgs.orgid,
      type: logArgs.type,
      created: logArgs.timestampInfo,
      outdated,
      updated
    });
    return result;
  };
}
