import { expect } from "@jest/globals";

import { Sites } from "../sites";
import * as auth from "../../authorization/server/methods";
import { getMeteorUser, isUserLoggedOut } from "../../utilities/utilities";
import { hasDashboard } from "../../users/server/methods";

// Mock dependencies
jest.mock("../sites");
jest.mock("../../authorization/server/methods");
jest.mock("../../utilities/utilities");
jest.mock("../../users/server/methods");

describe("Sites Publications", () => {
  let mockPublicationContext;
  let mockSitesCursor;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock publication context
    mockPublicationContext = {
      userId: "user1",
      ready: jest.fn().mockReturnValue("ready"),
      stop: jest.fn()
    };

    // Mock Sites cursor
    mockSitesCursor = {
      fetch: jest.fn().mockResolvedValue([]),
      observe: jest.fn(),
      observeChanges: jest.fn()
    };

    Sites.find.mockReturnValue(mockSitesCursor);
    isUserLoggedOut.mockReturnValue(false);
    getMeteorUser.mockResolvedValue({
      profile: {
        siteAccess: [
          { siteId: "site1", role: "arbitraryIdteacher" },
          { siteId: "site2", role: "arbitraryIdadmin" }
        ]
      }
    });
  });

  describe("Sites publication", () => {
    it("should return ready when user is logged out", async () => {
      isUserLoggedOut.mockReturnValue(true);

      // eslint-disable-next-line no-unused-vars
      const mockPublication = async function(orgid, siteId) {
        if (isUserLoggedOut(this)) {
          return this.ready();
        }
        return this.ready();
      };

      await mockPublication.call(mockPublicationContext, "org1", "site1");

      expect(mockPublicationContext.ready).toHaveBeenCalled();
    });

    it("should return ready when orgid is missing", async () => {
      // eslint-disable-next-line no-unused-vars
      const mockPublication = async function(orgid, siteId) {
        if (isUserLoggedOut(this)) {
          return this.ready();
        }
        if (!orgid) {
          return this.ready();
        }
        return this.ready();
      };

      await mockPublication.call(mockPublicationContext, null, "site1");

      expect(mockPublicationContext.ready).toHaveBeenCalled();
    });

    it("should return sites for teacher with siteId", async () => {
      auth.hasAccess.mockResolvedValue(true);

      const mockPublication = async function(orgid, siteId) {
        if (isUserLoggedOut(this)) {
          return this.ready();
        }
        if (!orgid) {
          return this.ready();
        }

        if (siteId && (await auth.hasAccess(["teacher"], { userId: this.userId, siteId }))) {
          const teacherSiteIds = (await getMeteorUser()).profile.siteAccess.map(sa => sa.siteId);
          const siteIds = teacherSiteIds.length ? teacherSiteIds : [siteId];
          return Sites.find({ _id: { $in: siteIds } }, { fields: { name: 1, orgid: 1 } });
        }

        return this.ready();
      };

      const result = await mockPublication.call(mockPublicationContext, "org1", "site1");

      expect(auth.hasAccess).toHaveBeenCalledWith(["teacher"], {
        userId: "user1",
        siteId: "site1"
      });
      expect(Sites.find).toHaveBeenCalledWith({ _id: { $in: ["site1", "site2"] } }, { fields: { name: 1, orgid: 1 } });
      expect(result).toBe(mockSitesCursor);
    });

    it("should return sites for admin/support roles", async () => {
      auth.hasAccess
        .mockResolvedValueOnce(false) // teacher check
        .mockResolvedValueOnce(true); // admin/support check

      getMeteorUser.mockResolvedValue({
        profile: {
          siteAccess: [{ siteId: "site1", role: "arbitraryIdadmin" }]
        }
      });

      const mockPublication = async function(orgid, siteId) {
        if (isUserLoggedOut(this)) {
          return this.ready();
        }
        if (!orgid) {
          return this.ready();
        }

        if (siteId && (await auth.hasAccess(["teacher"], { userId: this.userId, siteId }))) {
          const teacherSiteIds = (await getMeteorUser()).profile.siteAccess.map(sa => sa.siteId);
          const siteIds = teacherSiteIds.length ? teacherSiteIds : [siteId];
          return Sites.find({ _id: { $in: siteIds } }, { fields: { name: 1, orgid: 1 } });
        }

        if (
          await auth.hasAccess(
            ["admin", "support", "universalCoach", "superAdmin", "universalDataAdmin", "dataAdmin"],
            {
              userId: this.userId,
              siteId,
              orgid
            }
          )
        ) {
          return Sites.find(
            { orgid },
            {
              fields: {
                orgid: 1,
                schoolYear: 1,
                stateInformation: 1,
                "lastModified.on": 1,
                name: 1,
                grades: 1,
                isVisible: 1,
                rosterImportId: 1,
                isHighSchool: 1
              }
            }
          );
        }

        return this.ready();
      };

      const result = await mockPublication.call(mockPublicationContext, "org1", "site1");

      expect(auth.hasAccess).toHaveBeenCalledWith(
        ["admin", "support", "universalCoach", "superAdmin", "universalDataAdmin", "dataAdmin"],
        {
          userId: "user1",
          siteId: "site1",
          orgid: "org1"
        }
      );
      expect(Sites.find).toHaveBeenCalledWith(
        { orgid: "org1" },
        expect.objectContaining({
          fields: expect.objectContaining({
            orgid: 1,
            name: 1,
            schoolYear: 1
          })
        })
      );
      expect(result).toBe(mockSitesCursor);
    });

    it("should return ready when user has no access", async () => {
      auth.hasAccess.mockResolvedValue(false);

      const mockPublication = async function(orgid, siteId) {
        if (isUserLoggedOut(this)) {
          return this.ready();
        }
        if (!orgid) {
          return this.ready();
        }

        if (siteId && (await auth.hasAccess(["teacher"], { userId: this.userId, siteId }))) {
          const teacherSiteIds = (await getMeteorUser()).profile.siteAccess.map(sa => sa.siteId);
          const siteIds = teacherSiteIds.length ? teacherSiteIds : [siteId];
          return Sites.find({ _id: { $in: siteIds } }, { fields: { name: 1, orgid: 1 } });
        }

        if (
          await auth.hasAccess(
            ["admin", "support", "universalCoach", "superAdmin", "universalDataAdmin", "dataAdmin"],
            {
              userId: this.userId,
              siteId,
              orgid
            }
          )
        ) {
          return Sites.find(
            { orgid },
            {
              fields: {
                orgid: 1,
                schoolYear: 1,
                stateInformation: 1,
                "lastModified.on": 1,
                name: 1,
                grades: 1,
                isVisible: 1,
                rosterImportId: 1,
                isHighSchool: 1
              }
            }
          );
        }

        return this.ready();
      };

      await mockPublication.call(mockPublicationContext, "org1", "site1");

      expect(mockPublicationContext.ready).toHaveBeenCalled();
    });
  });

  describe("Sites:List publication", () => {
    it("should return sites for superAdmin", async () => {
      auth.hasAccess.mockResolvedValue(true);
      hasDashboard.mockResolvedValue(false);

      const mockPublication = async function() {
        if (isUserLoggedOut(this)) {
          return this.ready();
        }

        if ((await auth.hasAccess(["superAdmin"], { userId: this.userId })) || (await hasDashboard())) {
          return Sites.find({}, { fields: { name: 1, orgid: 1 } });
        }

        return this.ready();
      };

      const result = await mockPublication.call(mockPublicationContext);

      expect(auth.hasAccess).toHaveBeenCalledWith(["superAdmin"], { userId: "user1" });
      expect(Sites.find).toHaveBeenCalledWith({}, { fields: { name: 1, orgid: 1 } });
      expect(result).toBe(mockSitesCursor);
    });

    it("should return sites for user with dashboard", async () => {
      auth.hasAccess.mockResolvedValue(false);
      hasDashboard.mockResolvedValue(true);

      const mockPublication = async function() {
        if (isUserLoggedOut(this)) {
          return this.ready();
        }

        if ((await auth.hasAccess(["superAdmin"], { userId: this.userId })) || (await hasDashboard())) {
          return Sites.find({}, { fields: { name: 1, orgid: 1 } });
        }

        return this.ready();
      };

      const result = await mockPublication.call(mockPublicationContext);

      expect(hasDashboard).toHaveBeenCalled();
      expect(Sites.find).toHaveBeenCalledWith({}, { fields: { name: 1, orgid: 1 } });
      expect(result).toBe(mockSitesCursor);
    });

    it("should return ready when user has no access", async () => {
      auth.hasAccess.mockResolvedValue(false);
      hasDashboard.mockResolvedValue(false);

      const mockPublication = async function() {
        if (isUserLoggedOut(this)) {
          return this.ready();
        }

        if ((await auth.hasAccess(["superAdmin"], { userId: this.userId })) || (await hasDashboard())) {
          return Sites.find({}, { fields: { name: 1, orgid: 1 } });
        }

        return this.ready();
      };

      await mockPublication.call(mockPublicationContext);

      expect(mockPublicationContext.ready).toHaveBeenCalled();
    });
  });
});
