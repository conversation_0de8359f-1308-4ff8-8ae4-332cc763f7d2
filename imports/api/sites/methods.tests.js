import { omit } from "lodash";
import { Sites } from "./sites.js";
import { Users } from "../users/users";
import { convertSchoolIdsToNCES, getSchoolItemData } from "./methods";
import { StudentGroups } from "../studentGroups/studentGroups";
import { getNormalizedId } from "../rosterImportItems/normalizeRosterImportItems";
import { AssessmentResults } from "../assessmentResults/assessmentResults";

describe("getSchoolItemData", () => {
  const orgid = "orgid";
  const siteId1 = "siteId1";
  const siteId2 = "siteId2";
  const siteId3 = "siteId3";
  let twoSitesAdmin;
  let secondSiteAdmin;
  beforeAll(async () => {
    await Sites.insertAsync([
      {
        _id: siteId1,
        name: "First Site",
        orgid
      },
      {
        _id: siteId2,
        name: "Second Site",
        orgid
      },
      {
        _id: siteId3,
        name: "Third Site",
        orgid
      }
    ]);
    twoSitesAdmin = {
      profile: {
        orgid,
        siteAccess: [
          {
            role: "arbitraryIdadmin",
            siteId: siteId1
          },
          {
            role: "arbitraryIdadmin",
            siteId: siteId2
          }
        ],
        name: {
          first: "TwoSites",
          last: "Admin"
        }
      }
    };
    secondSiteAdmin = {
      profile: {
        orgid,
        siteAccess: [
          {
            role: "arbitraryIdadmin",
            siteId: siteId2
          }
        ],
        name: {
          first: "SecondSite",
          last: "Admin"
        }
      }
    };
    await Users.insertAsync([
      twoSitesAdmin,
      secondSiteAdmin,
      {
        profile: {
          onboarded: false,
          orgid,
          siteAccess: [
            {
              role: "arbitraryIdteacher",
              siteId: siteId1
            }
          ],
          name: {
            first: "NotYetOnBoarded",
            last: "Teacher"
          }
        }
      },
      {
        profile: {
          onboarded: true,
          orgid,
          siteAccess: [
            {
              role: "arbitraryIdteacher",
              siteId: siteId1
            }
          ],
          name: {
            first: "OnBoarded",
            last: "Teacher"
          }
        }
      }
    ]);
  });
  afterAll(async () => {
    await Sites.removeAsync({});
    await Users.removeAsync({});
  });
  it("should return aggregated site data", async () => {
    const result = await getSchoolItemData(orgid);

    const expectedResult = [
      {
        _id: siteId1,
        name: "First Site",
        teachersOnBoardedCount: 1,
        teachersYetToOnBoardCount: 1,
        siteAdmins: [twoSitesAdmin]
      },
      {
        _id: siteId2,
        name: "Second Site",
        teachersOnBoardedCount: 0,
        teachersYetToOnBoardCount: 0,
        siteAdmins: expect.arrayContaining([
          expect.objectContaining(secondSiteAdmin),
          expect.objectContaining(twoSitesAdmin)
        ])
      },
      {
        _id: siteId3,
        name: "Third Site",
        teachersOnBoardedCount: 0,
        teachersYetToOnBoardCount: 0,
        siteAdmins: []
      }
    ];
    expect(result).toMatchObject(expectedResult);
  });
});

describe("convertSchoolIdsToNCES", () => {
  const orgid = "ozGubwWjM56AHeAqZ";
  const districtNumber = getNormalizedId(orgid);

  function generateSite(schoolNumber, siteId, name) {
    return {
      _id: siteId,
      orgid,
      name: name || `Site ${schoolNumber}`,
      stateInformation: {
        districtNumber,
        schoolNumber,
        localSchoolNumber: schoolNumber
      }
    };
  }

  function generateStudentGroup(siteId, studentGroupId) {
    return {
      _id: studentGroupId,
      siteId,
      orgid,
      isActive: true
    };
  }

  function generateAssessmentResult(studentGroupId, orgid_, siteId) {
    return {
      status: "COMPLETED",
      orgid: orgid_,
      studentGroupId,
      scores: [{ siteId }]
    };
  }

  beforeEach(async () => {
    await AssessmentResults.insertAsync([
      generateAssessmentResult("sgId1", orgid, "siteId1"),
      generateAssessmentResult("sgId2", orgid, "siteId2")
    ]);
    await StudentGroups.insertAsync([
      generateStudentGroup("siteId1", "sgId1"),
      generateStudentGroup("siteId2", "sgId2")
    ]);
  });
  afterEach(async () => {
    await Sites.removeAsync({});
    await StudentGroups.removeAsync({});
    await AssessmentResults.removeAsync({});
  });

  describe("Sites with NCES ID available", () => {
    const ncesNumberBySchoolNumber = {
      1: { ncesId: "10", name: "Site 1" },
      2: { ncesId: "20", name: "Site 2" }
    };
    it("where sites have schoolNumber as sourcedID", async () => {
      await Sites.insertAsync([generateSite("1", "siteId1", "Site 1"), generateSite("2", "siteId2", "Site 2")]);
      await convertSchoolIdsToNCES(ncesNumberBySchoolNumber, orgid);
      const sites = (await Sites.find({ orgid }, { sort: { _id: 1 } }).fetchAsync()).map(s =>
        omit(s, ["lastModified"])
      );
      const expectedSites = [
        {
          _id: "siteId1",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "10",
            localSchoolNumber: "10"
          }
        },
        {
          _id: "siteId2",
          orgid,
          name: "Site 2",
          stateInformation: {
            districtNumber,
            schoolNumber: "20",
            localSchoolNumber: "20"
          }
        }
      ];
      expect(sites).toEqual(expectedSites);
    });
    it("where sites have schoolNumber as NCES ID (student group data)", async () => {
      await Sites.insertAsync([generateSite("10", "siteId1", "Site 1"), generateSite("20", "siteId2", "Site 2")]);
      await convertSchoolIdsToNCES(ncesNumberBySchoolNumber, orgid);
      const sites = await Sites.find({ orgid }, { sort: { _id: 1 } }).fetchAsync();
      const expectedSites = [
        {
          _id: "siteId1",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "10",
            localSchoolNumber: "10"
          }
        },
        {
          _id: "siteId2",
          orgid,
          name: "Site 2",
          stateInformation: {
            districtNumber,
            schoolNumber: "20",
            localSchoolNumber: "20"
          }
        }
      ];
      expect(sites).toEqual(expectedSites);
    });
    it("where sites have schoolNumber as NCES ID (assessment result data)", async () => {
      await Sites.insertAsync([generateSite("10", "siteId1", "Site 1"), generateSite("20", "siteId2", "Site 2")]);
      await StudentGroups.removeAsync({});
      await convertSchoolIdsToNCES(ncesNumberBySchoolNumber, orgid);
      const sites = await Sites.find({ orgid }, { sort: { _id: 1 } }).fetchAsync();
      const expectedSites = [
        {
          _id: "siteId1",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "10",
            localSchoolNumber: "10"
          }
        },
        {
          _id: "siteId2",
          orgid,
          name: "Site 2",
          stateInformation: {
            districtNumber,
            schoolNumber: "20",
            localSchoolNumber: "20"
          }
        }
      ];
      expect(sites).toEqual(expectedSites);
    });
    it("where there are sites with both schoolNumber as sourcedID and NCES ID - all have data", async () => {
      await Sites.insertAsync([
        generateSite("1", "siteId1", "Site 1"),
        generateSite("10", "siteId3", "Site 1"),
        generateSite("2", "siteId2", "Site 2"),
        generateSite("20", "siteId4", "Site 2")
      ]);
      await StudentGroups.removeAsync({});
      await StudentGroups.insertAsync([
        generateStudentGroup("siteId1", "sgId1"),
        generateStudentGroup("siteId3", "sgId3"),
        generateStudentGroup("siteId2", "sgId2"),
        generateStudentGroup("siteId4", "sgId4")
      ]);
      await AssessmentResults.insertAsync([
        generateAssessmentResult("sgId3", orgid, "siteId3"),
        generateAssessmentResult("sgId4", orgid, "siteId4")
      ]);
      await convertSchoolIdsToNCES(ncesNumberBySchoolNumber, orgid);
      const sites = (await Sites.find({ orgid }, { sort: { _id: 1 } }).fetchAsync()).map(s =>
        omit(s, ["lastModified"])
      );
      const expectedSites = [
        {
          _id: "siteId1",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "10_old",
            localSchoolNumber: "10_old"
          }
        },
        {
          _id: "siteId2",
          orgid,
          name: "Site 2",
          stateInformation: {
            districtNumber,
            schoolNumber: "20_old",
            localSchoolNumber: "20_old"
          }
        },
        {
          _id: "siteId3",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "10",
            localSchoolNumber: "10"
          }
        },
        {
          _id: "siteId4",
          orgid,
          name: "Site 2",
          stateInformation: {
            districtNumber,
            schoolNumber: "20",
            localSchoolNumber: "20"
          }
        }
      ];
      expect(sites).toEqual(expectedSites);
    });
    it("where there are sites with both schoolNumber as sourcedID and NCES ID but only sites with sourcedId have data", async () => {
      await Sites.insertAsync([
        generateSite("1", "siteId1", "Site 1"),
        generateSite("10", "siteId3", "Site 1"),
        generateSite("2", "siteId2", "Site 2"),
        generateSite("20", "siteId4", "Site 2")
      ]);
      await StudentGroups.removeAsync({});
      await StudentGroups.insertAsync([
        generateStudentGroup("siteId1", "sgId1"),
        generateStudentGroup("siteId2", "sgId2")
      ]);
      await convertSchoolIdsToNCES(ncesNumberBySchoolNumber, orgid);
      const sites = (await Sites.find({ orgid }, { sort: { _id: 1 } }).fetchAsync()).map(s =>
        omit(s, ["lastModified"])
      );
      const expectedSites = [
        {
          _id: "siteId1",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "10",
            localSchoolNumber: "10"
          }
        },
        {
          _id: "siteId2",
          orgid,
          name: "Site 2",
          stateInformation: {
            districtNumber,
            schoolNumber: "20",
            localSchoolNumber: "20"
          }
        }
      ];
      expect(sites).toEqual(expectedSites);
    });
    it("where there are sites with both schoolNumber as sourcedID and NCES ID but only sites with NCES ID have data (student groups)", async () => {
      await Sites.insertAsync([
        generateSite("1", "siteId1", "Site 1"),
        generateSite("10", "siteId3", "Site 1"),
        generateSite("2", "siteId2", "Site 2"),
        generateSite("20", "siteId4", "Site 2")
      ]);
      await StudentGroups.removeAsync({});
      await StudentGroups.insertAsync([
        generateStudentGroup("siteId3", "sgId3"),
        generateStudentGroup("siteId4", "sgId4")
      ]);
      await AssessmentResults.removeAsync({});
      await convertSchoolIdsToNCES(ncesNumberBySchoolNumber, orgid);
      const sites = await Sites.find({ orgid }, { sort: { _id: 1 } }).fetchAsync();
      const expectedSites = [
        {
          _id: "siteId3",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "10",
            localSchoolNumber: "10"
          }
        },
        {
          _id: "siteId4",
          orgid,
          name: "Site 2",
          stateInformation: {
            districtNumber,
            schoolNumber: "20",
            localSchoolNumber: "20"
          }
        }
      ];
      expect(sites).toEqual(expectedSites);
    });
    it("where there are sites with both schoolNumber as sourcedID and NCES ID but only sites with NCES ID have data (assessment results)", async () => {
      await Sites.insertAsync([
        generateSite("1", "siteId1", "Site 1"),
        generateSite("10", "siteId3", "Site 1"),
        generateSite("2", "siteId2", "Site 2"),
        generateSite("20", "siteId4", "Site 2")
      ]);
      await StudentGroups.removeAsync({});
      await AssessmentResults.removeAsync({});
      await AssessmentResults.insertAsync([
        generateAssessmentResult("sgId1", orgid, "siteId3"),
        generateAssessmentResult("sgId2", orgid, "siteId4")
      ]);
      await convertSchoolIdsToNCES(ncesNumberBySchoolNumber, orgid);
      const sites = await Sites.find({ orgid }, { sort: { _id: 1 } }).fetchAsync();
      const expectedSites = [
        {
          _id: "siteId3",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "10",
            localSchoolNumber: "10"
          }
        },
        {
          _id: "siteId4",
          orgid,
          name: "Site 2",
          stateInformation: {
            districtNumber,
            schoolNumber: "20",
            localSchoolNumber: "20"
          }
        }
      ];
      expect(sites).toEqual(expectedSites);
    });
    it("where sites have schoolNumber as sourcedID but one site is missing data", async () => {
      await Sites.insertAsync([generateSite("1", "siteId1", "Site 1"), generateSite("2", "siteId2", "Site 2")]);
      await StudentGroups.removeAsync({ _id: "sgId1" });
      await AssessmentResults.removeAsync({ "scores.siteId": "siteId1" });
      await convertSchoolIdsToNCES(ncesNumberBySchoolNumber, orgid);
      const sites = (await Sites.find({ orgid }, { sort: { _id: 1 } }).fetchAsync()).map(s =>
        omit(s, ["lastModified"])
      );
      const expectedSites = [
        {
          _id: "siteId2",
          orgid,
          name: "Site 2",
          stateInformation: {
            districtNumber,
            schoolNumber: "20",
            localSchoolNumber: "20"
          }
        }
      ];
      expect(sites).toEqual(expectedSites);
    });
    it("where there are sites with both schoolNumber as sourcedID and NCES ID but conversion gets run twice - _old suffix", async () => {
      await Sites.insertAsync([
        generateSite("1", "siteId1", "Site 1"),
        generateSite("10", "siteId3", "Site 1"),
        generateSite("2", "siteId2", "Site 2"),
        generateSite("20", "siteId4", "Site 2")
      ]);
      await StudentGroups.removeAsync({});
      await StudentGroups.insertAsync([
        generateStudentGroup("siteId1", "sgId1"),
        generateStudentGroup("siteId3", "sgId3"),
        generateStudentGroup("siteId2", "sgId2"),
        generateStudentGroup("siteId4", "sgId4")
      ]);
      await convertSchoolIdsToNCES(ncesNumberBySchoolNumber, orgid);
      await convertSchoolIdsToNCES(ncesNumberBySchoolNumber, orgid);
      const sites = (await Sites.find({ orgid }, { sort: { _id: 1 } }).fetchAsync()).map(s =>
        omit(s, ["lastModified"])
      );
      const expectedSites = [
        {
          _id: "siteId1",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "10_old",
            localSchoolNumber: "10_old"
          }
        },
        {
          _id: "siteId2",
          orgid,
          name: "Site 2",
          stateInformation: {
            districtNumber,
            schoolNumber: "20_old",
            localSchoolNumber: "20_old"
          }
        },
        {
          _id: "siteId3",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "10",
            localSchoolNumber: "10"
          }
        },
        {
          _id: "siteId4",
          orgid,
          name: "Site 2",
          stateInformation: {
            districtNumber,
            schoolNumber: "20",
            localSchoolNumber: "20"
          }
        }
      ];
      expect(sites).toEqual(expectedSites);
    });
    it("should not remove site without data that is not included in import data", async () => {
      await Sites.insertAsync([
        generateSite("1", "siteId1", "Site 1"),
        generateSite("2", "siteId2", "Site 2"),
        generateSite("3", "siteId3", "Site 3")
      ]);
      await StudentGroups.removeAsync({ _id: "sgId2" });
      await AssessmentResults.removeAsync({ "scores.siteId": "siteId2" });
      await convertSchoolIdsToNCES(ncesNumberBySchoolNumber, orgid);
      const sites = (await Sites.find({ orgid }, { sort: { _id: 1 } }).fetchAsync()).map(s =>
        omit(s, ["lastModified"])
      );
      const expectedSites = [
        {
          _id: "siteId1",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "10",
            localSchoolNumber: "10"
          }
        },
        {
          _id: "siteId3",
          orgid,
          name: "Site 3",
          stateInformation: {
            districtNumber,
            schoolNumber: "3",
            localSchoolNumber: "3"
          }
        }
      ];
      expect(sites).toEqual(expectedSites);
    });
    it("should not migrate to NCES id if non migrated school number matches NCES id", async () => {
      await Sites.insertAsync([
        generateSite("1", "siteId1", "Site 1"),
        generateSite("2", "siteId2", "Site 2"),
        generateSite("3", "siteId3", "Site 3")
      ]);
      await StudentGroups.removeAsync({ _id: "sgId2" });
      await AssessmentResults.removeAsync({ "scores.siteId": "siteId2" });
      await StudentGroups.insertAsync([generateStudentGroup("siteId3", "sgId3")]);
      await convertSchoolIdsToNCES({ 1: { ncesId: "3", name: "Site 1" }, 2: { ncesId: "4", name: "Site 2" } }, orgid);
      const sites = await Sites.find({ orgid }, { sort: { _id: 1 } }).fetchAsync();
      const expectedSites = [
        {
          _id: "siteId1",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "1",
            localSchoolNumber: "1"
          }
        },
        {
          _id: "siteId3",
          orgid,
          name: "Site 3",
          stateInformation: {
            districtNumber,
            schoolNumber: "3",
            localSchoolNumber: "3"
          }
        }
      ];
      expect(sites).toEqual(expectedSites);
    });
    it("should match correct schools when existing school number matches fetched NCES id", async () => {
      await Sites.insertAsync([generateSite("1", "siteId1", "Site 1"), generateSite("2", "siteId2", "Site 2")]);
      await convertSchoolIdsToNCES({ 1: { ncesId: "2", name: "Site 1" }, 2: { ncesId: "3", name: "Site 2" } }, orgid);
      const sites = (await Sites.find({ orgid }, { sort: { _id: 1 } }).fetchAsync()).map(s =>
        omit(s, ["lastModified"])
      );
      const expectedSites = [
        {
          _id: "siteId1",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "2",
            localSchoolNumber: "2"
          }
        },
        {
          _id: "siteId2",
          orgid,
          name: "Site 2",
          stateInformation: {
            districtNumber,
            schoolNumber: "3",
            localSchoolNumber: "3"
          }
        }
      ];
      expect(sites).toEqual(expectedSites);
    });
  });

  describe("Sites without NCES ID available", () => {
    const ncesNumberBySchoolNumber = {};
    it("where sites have schoolNumber as sourcedID", async () => {
      await Sites.insertAsync([generateSite("1", "siteId1", "Site 1"), generateSite("2", "siteId2", "Site 2")]);
      await convertSchoolIdsToNCES(ncesNumberBySchoolNumber, orgid);
      const sites = await Sites.find({ orgid }, { sort: { _id: 1 } }).fetchAsync();
      const expectedSites = [
        {
          _id: "siteId1",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "1",
            localSchoolNumber: "1"
          }
        },
        {
          _id: "siteId2",
          orgid,
          name: "Site 2",
          stateInformation: {
            districtNumber,
            schoolNumber: "2",
            localSchoolNumber: "2"
          }
        }
      ];
      expect(sites).toEqual(expectedSites);
    });
    it("where sites have schoolNumber as NCES ID", async () => {
      await Sites.insertAsync([generateSite("10", "siteId1", "Site 1"), generateSite("20", "siteId2", "Site 2")]);

      await convertSchoolIdsToNCES(ncesNumberBySchoolNumber, orgid);
      const sites = await Sites.find({ orgid }, { sort: { _id: 1 } }).fetchAsync();
      const expectedSites = [
        {
          _id: "siteId1",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "10",
            localSchoolNumber: "10"
          }
        },
        {
          _id: "siteId2",
          orgid,
          name: "Site 2",
          stateInformation: {
            districtNumber,
            schoolNumber: "20",
            localSchoolNumber: "20"
          }
        }
      ];
      expect(sites).toEqual(expectedSites);
    });
    it("where there are sites with both schoolNumber as sourcedID and NCES ID - all have data", async () => {
      await Sites.insertAsync([
        generateSite("1", "siteId1", "Site 1"),
        generateSite("10", "siteId3", "Site 1"),
        generateSite("2", "siteId2", "Site 2"),
        generateSite("20", "siteId4", "Site 2")
      ]);
      await StudentGroups.removeAsync({});
      await StudentGroups.insertAsync([
        generateStudentGroup("siteId1", "sgId1"),
        generateStudentGroup("siteId3", "sgId3"),
        generateStudentGroup("siteId2", "sgId2"),
        generateStudentGroup("siteId4", "sgId4")
      ]);
      await AssessmentResults.insertAsync([
        generateAssessmentResult("sgId1", orgid, "siteId3"),
        generateAssessmentResult("sgId2", orgid, "siteId4")
      ]);
      await convertSchoolIdsToNCES(ncesNumberBySchoolNumber, orgid);
      const sites = await Sites.find({ orgid }, { sort: { _id: 1 } }).fetchAsync();
      const expectedSites = [
        {
          _id: "siteId1",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "1",
            localSchoolNumber: "1"
          }
        },
        {
          _id: "siteId2",
          orgid,
          name: "Site 2",
          stateInformation: {
            districtNumber,
            schoolNumber: "2",
            localSchoolNumber: "2"
          }
        },
        {
          _id: "siteId3",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "10",
            localSchoolNumber: "10"
          }
        },
        {
          _id: "siteId4",
          orgid,
          name: "Site 2",
          stateInformation: {
            districtNumber,
            schoolNumber: "20",
            localSchoolNumber: "20"
          }
        }
      ];
      expect(sites).toEqual(expectedSites);
    });
    it("where there are sites with both schoolNumber as sourcedID and NCES ID but only sites with sourcedId have data", async () => {
      await Sites.insertAsync([
        generateSite("1", "siteId1", "Site 1"),
        generateSite("10", "siteId3", "Site 1"),
        generateSite("2", "siteId2", "Site 2"),
        generateSite("20", "siteId4", "Site 2")
      ]);
      await StudentGroups.removeAsync({});
      await StudentGroups.insertAsync([
        generateStudentGroup("siteId1", "sgId1"),
        generateStudentGroup("siteId2", "sgId2")
      ]);
      await convertSchoolIdsToNCES(ncesNumberBySchoolNumber, orgid);
      const sites = await Sites.find({ orgid }, { sort: { _id: 1 } }).fetchAsync();
      const expectedSites = [
        {
          _id: "siteId1",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "1",
            localSchoolNumber: "1"
          }
        },
        {
          _id: "siteId2",
          orgid,
          name: "Site 2",
          stateInformation: {
            districtNumber,
            schoolNumber: "2",
            localSchoolNumber: "2"
          }
        },
        {
          _id: "siteId3",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "10",
            localSchoolNumber: "10"
          }
        },
        {
          _id: "siteId4",
          orgid,
          name: "Site 2",
          stateInformation: {
            districtNumber,
            schoolNumber: "20",
            localSchoolNumber: "20"
          }
        }
      ];
      expect(sites).toEqual(expectedSites);
    });
    it("where there are sites with both schoolNumber as sourcedID and NCES ID but only sites with NCES ID have data", async () => {
      await Sites.insertAsync([
        generateSite("1", "siteId1", "Site 1"),
        generateSite("10", "siteId3", "Site 1"),
        generateSite("2", "siteId2", "Site 2"),
        generateSite("20", "siteId4", "Site 2")
      ]);
      await StudentGroups.removeAsync({});
      await StudentGroups.insertAsync([
        generateStudentGroup("siteId3", "sgId3"),
        generateStudentGroup("siteId4", "sgId4")
      ]);
      await convertSchoolIdsToNCES(ncesNumberBySchoolNumber, orgid);
      const sites = await Sites.find({ orgid }, { sort: { _id: 1 } }).fetchAsync();
      const expectedSites = [
        {
          _id: "siteId1",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "1",
            localSchoolNumber: "1"
          }
        },
        {
          _id: "siteId2",
          orgid,
          name: "Site 2",
          stateInformation: {
            districtNumber,
            schoolNumber: "2",
            localSchoolNumber: "2"
          }
        },
        {
          _id: "siteId3",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "10",
            localSchoolNumber: "10"
          }
        },
        {
          _id: "siteId4",
          orgid,
          name: "Site 2",
          stateInformation: {
            districtNumber,
            schoolNumber: "20",
            localSchoolNumber: "20"
          }
        }
      ];
      expect(sites).toEqual(expectedSites);
    });
    it("where sites have schoolNumber as sourcedID but one site is missing data", async () => {
      await Sites.insertAsync([generateSite("1", "siteId1", "Site 1"), generateSite("2", "siteId2", "Site 2")]);
      await StudentGroups.removeAsync({ _id: "sgId1" });
      await convertSchoolIdsToNCES(ncesNumberBySchoolNumber, orgid);
      const sites = await Sites.find({ orgid }, { sort: { _id: 1 } }).fetchAsync();
      const expectedSites = [
        {
          _id: "siteId1",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "1",
            localSchoolNumber: "1"
          }
        },
        {
          _id: "siteId2",
          orgid,
          name: "Site 2",
          stateInformation: {
            districtNumber,
            schoolNumber: "2",
            localSchoolNumber: "2"
          }
        }
      ];
      expect(sites).toEqual(expectedSites);
    });
    it("where there are sites with both schoolNumber as sourcedID and NCES ID but conversion gets run twice - _old suffix", async () => {
      await Sites.insertAsync([
        generateSite("1", "siteId1", "Site 1"),
        generateSite("10", "siteId3", "Site 1"),
        generateSite("2", "siteId2", "Site 2"),
        generateSite("20", "siteId4", "Site 2")
      ]);
      await StudentGroups.removeAsync({});
      await StudentGroups.insertAsync([
        generateStudentGroup("siteId1", "sgId1"),
        generateStudentGroup("siteId3", "sgId3"),
        generateStudentGroup("siteId2", "sgId2"),
        generateStudentGroup("siteId4", "sgId4")
      ]);
      await convertSchoolIdsToNCES(ncesNumberBySchoolNumber, orgid);
      await convertSchoolIdsToNCES(ncesNumberBySchoolNumber, orgid);
      const sites = await Sites.find({ orgid }, { sort: { _id: 1 } }).fetchAsync();
      const expectedSites = [
        {
          _id: "siteId1",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "1",
            localSchoolNumber: "1"
          }
        },
        {
          _id: "siteId2",
          orgid,
          name: "Site 2",
          stateInformation: {
            districtNumber,
            schoolNumber: "2",
            localSchoolNumber: "2"
          }
        },
        {
          _id: "siteId3",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "10",
            localSchoolNumber: "10"
          }
        },
        {
          _id: "siteId4",
          orgid,
          name: "Site 2",
          stateInformation: {
            districtNumber,
            schoolNumber: "20",
            localSchoolNumber: "20"
          }
        }
      ];
      expect(sites).toEqual(expectedSites);
    });
    it("should not remove site without data that is not included in import data", async () => {
      await Sites.insertAsync([generateSite("1", "siteId1", "Site 1"), generateSite("3", "siteId3", "Site 3")]);
      await convertSchoolIdsToNCES(ncesNumberBySchoolNumber, orgid);
      const sites = await Sites.find({ orgid }, { sort: { _id: 1 } }).fetchAsync();
      const expectedSites = [
        {
          _id: "siteId1",
          orgid,
          name: "Site 1",
          stateInformation: {
            districtNumber,
            schoolNumber: "1",
            localSchoolNumber: "1"
          }
        },
        {
          _id: "siteId3",
          orgid,
          name: "Site 3",
          stateInformation: {
            districtNumber,
            schoolNumber: "3",
            localSchoolNumber: "3"
          }
        }
      ];
      expect(sites).toEqual(expectedSites);
    });
  });
});
