import { Meteor } from "meteor/meteor";
import SimpleSchema from "simpl-schema";

import { ByDateOn } from "../helpers/schemas/byDateOn/byDateOn";

export const Users = Meteor.users;

Users.schemaUserProfile = new SimpleSchema({
  customDate: { type: String, optional: true },
  onboarded: { type: Boolean },
  orgid: { type: String },
  localId: { type: String, optional: true },
  siteAccess: Array,
  "siteAccess.$": Object,
  "siteAccess.$.role": { type: String },
  "siteAccess.$.siteId": { type: String },
  "siteAccess.$.schoolYear": { type: Number },
  "siteAccess.$.isActive": { type: <PERSON>olean },
  "siteAccess.$.isDefault": { type: Boolean },
  name: { type: Object },
  "name.first": { type: String },
  "name.last": { type: String },
  "name.middle": { type: String, optional: true },
  rosterImportId: { type: String, optional: true },
  created: { type: ByDateOn },
  lastModified: { type: ByDateOn },
  hasDashboard: { type: Boolean, optional: true },
  isSelfEnrollee: { type: Bo<PERSON>an, optional: true },
  ssoIssuerLogoutRedirect: { type: String, optional: true },
  selectedSchoolYear: { type: Number, optional: true },
  loginData: { type: Object, optional: true },
  "loginData.loginCount": { type: Number, optional: true },
  "loginData.lastLogin": { type: Number, optional: true },
  "loginData.lastLoginFailed": { type: Number, optional: true },
  lastPasswordChange: { type: Number, optional: true },
  usedPasswords: { type: Array, optional: true },
  "usedPasswords.$": String
});

Users.validate = user => {
  try {
    Users.schemaUserProfile.validate(user.profile);
  } catch (e) {
    console.log("err: ", e);
  }
};
Users.isValid = user => Users.schemaUserProfile.namedContext("testContext").validate(user.profile);
