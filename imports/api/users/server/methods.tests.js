import sinon from "sinon";
import { assert } from "chai";
import { omit, orderBy, sortBy } from "lodash";
import { Accounts } from "meteor/accounts-base";
import { Meteor } from "meteor/meteor";
import {
  createCoachUserAndSendEnrollmentLink,
  updateCoachesForSiteWithNewSchoolYears,
  updateCoachUserSiteAccess,
  getUserInviteEmailStatus,
  removeGroupOwnershipAndDeactivateSiteAccess,
  getManageTeacherData,
  removeCoachUser,
  getAllSchoolYearsFor,
  removeDataAdminUsers,
  getArchivedTeacherOrAdminIds,
  updateLoginData,
  getUserAuthInfoByEmail,
  saveLastPasswordChangeDate,
  getSortedUserSiteAccess
} from "./methods";
import { Roles } from "../../roles/roles";
import { StudentGroups } from "../../studentGroups/studentGroups";
import { Sites } from "../../sites/sites";
import { Users } from "../users";
import { Organizations } from "../../organizations/organizations";
import { getTestGroupInContext, getUserInContext } from "../../../test-helpers/data/saveGroupData.testData";

jest.mock("../../utilities/utilities", () => ({
  ...jest.requireActual("../../utilities/utilities"),
  getCurrentSchoolYear: jest.fn(() => 2018),
  getLatestAvailableSchoolYear: jest.fn(() => 2018)
}));

describe("imports/api/users/server/methods.js tests", () => {
  const currentSchoolYear = 2018;
  const teacherRoleId = "arbitraryIdteacher";
  const adminRoleId = "arbitraryIdadmin";
  const dataAdminRoleId = "arbitraryIddataAdmin";
  afterAll(() => {
    jest.restoreAllMocks();
  });

  describe("getSortedUserSiteAccess", () => {
    const orgid = "test_organization_id";
    const siteId = "someSiteId";
    const userId = "teacherId";
    const getTeacher = getUserInContext({ role: teacherRoleId, schoolYear: currentSchoolYear, orgid });
    const teacherAccount = getTeacher({
      _id: userId,
      siteIds: [siteId],
      schoolYearsToUse: [2022, 2021, 2023, 2020],
      isActive: true,
      address: "singleSiteTeacherEmail"
    });

    beforeAll(async () => {
      await Users.insertAsync(teacherAccount);
      await Sites.insertAsync({ _id: siteId });
    });
    afterAll(async () => {
      await Users.removeAsync({});
      await Sites.removeAsync({});
    });

    it("should return sorted user site access", async () => {
      let user = await Users.findOneAsync({ _id: userId });
      const expectedSortedUserSiteAccess = [
        {
          isActive: true,
          role: teacherRoleId,
          schoolYear: 2023,
          siteId
        },
        {
          isActive: true,
          role: teacherRoleId,
          schoolYear: 2022,
          siteId
        },
        {
          isActive: true,
          role: teacherRoleId,
          schoolYear: 2021,
          siteId
        },
        {
          isActive: true,
          role: teacherRoleId,
          schoolYear: 2020,
          siteId
        }
      ];

      expect(user.profile.siteAccess).not.toEqual(expectedSortedUserSiteAccess);
      const sortedUserSiteAccess = await getSortedUserSiteAccess(userId, user.profile.siteAccess, [siteId]);

      expect(sortedUserSiteAccess).toEqual(expectedSortedUserSiteAccess);

      user = await Users.findOneAsync({ _id: userId });
      expect(user.profile.siteAccess).toEqual(expectedSortedUserSiteAccess);
    });

    it("should filter out sites that do not exist", async () => {
      await Users.updateAsync({ _id: userId }, { $set: { "profile.siteAccess.0.siteId": "nonExistingSiteId" } });
      let user = await Users.findOneAsync({ _id: userId });
      const expectedSortedUserSiteAccess = [
        {
          isActive: true,
          role: teacherRoleId,
          schoolYear: 2022,
          siteId
        },
        {
          isActive: true,
          role: teacherRoleId,
          schoolYear: 2021,
          siteId
        },
        {
          isActive: true,
          role: teacherRoleId,
          schoolYear: 2020,
          siteId
        }
      ];

      expect(user.profile.siteAccess).not.toEqual(expectedSortedUserSiteAccess);
      const sortedUserSiteAccess = await getSortedUserSiteAccess(userId, user.profile.siteAccess, [siteId]);

      expect(sortedUserSiteAccess).toEqual(expectedSortedUserSiteAccess);

      user = await Users.findOneAsync({ _id: userId });
      expect(user.profile.siteAccess).toEqual(expectedSortedUserSiteAccess);
    });
  });
  describe("createCoachUserAndSendEnrollmentLink tests", () => {
    it("should call Accounts.createUser with all schoolYears found in the student groups at the site", async () => {
      const schoolYearsExpected = [2015, 2016, 2017];
      const accountsCreateUserSpy = sinon.spy(Accounts, "createUserAsync");
      const rolesStub = sinon.stub(Roles, "findOneAsync");
      const studentGroupsStub = sinon.stub(StudentGroups, "find");
      const sitesStub = sinon.stub(Sites, "find");

      rolesStub.callsFake(() => ({
        _id: "testAdminRoleId",
        name: "Admin"
      }));
      studentGroupsStub.callsFake(() => ({
        fetchAsync: () =>
          schoolYearsExpected.map(schoolYear => ({
            schoolYear
          }))
      }));
      sitesStub.callsFake(() => ({
        fetchAsync: () => [{ _id: "testSiteId", name: "Test Elementary Site" }]
      }));
      await createCoachUserAndSendEnrollmentLink({
        orgid: "testOrgid",
        siteIds: ["testSiteId"],
        email: "testEmail",
        firstName: "testFirstName",
        lastName: "testLastName"
      });
      assert.isTrue(accountsCreateUserSpy.calledOnce);
      const siteAccessSchoolYears = accountsCreateUserSpy.firstCall.args[0].profile.siteAccess.map(
        siteAccess => siteAccess.schoolYear
      );
      assert.isTrue(
        schoolYearsExpected.every(schoolYear => siteAccessSchoolYears.includes(schoolYear)),
        `expected [${siteAccessSchoolYears}] elements to include [${schoolYearsExpected}]`
      );
      // assert something about the user profile
      // siteAccess array containing all of the schoolyears
      rolesStub.restore();
      studentGroupsStub.restore();
      sitesStub.restore();
    });
  });

  describe("updateCoachesForSiteWithNewSchoolYears", () => {
    const siteId = "testSiteId";
    let usersFindStub;
    let usersUpdateSpy;
    let rolesStub;
    const callMethodUnderTestWithSiteId = async schoolYearArray =>
      updateCoachesForSiteWithNewSchoolYears(siteId, schoolYearArray);
    beforeEach(() => {
      usersFindStub = sinon.stub(Users, "find");
      usersUpdateSpy = sinon.spy(Users, "updateAsync");
      rolesStub = sinon.stub(Roles, "findOneAsync");
      rolesStub.callsFake(() => ({
        _id: "testAdminRoleId",
        name: "Admin"
      }));
    });
    afterEach(() => {
      usersFindStub.restore();
      usersUpdateSpy.restore();
      rolesStub.restore();
    });
    const assertUsersFindCalledAndUpdateNotCalled = () => {
      assert.isTrue(usersFindStub.calledOnce);
      assert.isFalse(usersUpdateSpy.calledOnce);
    };
    describe("when there are no admin users at the site", () => {
      it("should not call users.update if there are no admin users in the site", async () => {
        usersFindStub.callsFake(() => ({
          fetchAsync: () => []
        }));
        await callMethodUnderTestWithSiteId();
        assertUsersFindCalledAndUpdateNotCalled();
      });
    });
    describe("when there is one admin user at the site", () => {
      beforeEach(() => {
        usersFindStub.callsFake(() => ({
          fetchAsync: () => [
            {
              _id: "testUserId",
              profile: {
                siteAccess: []
              }
            }
          ]
        }));
      });
      describe("and there is no schoolYears in the new schoolyears array", () => {
        it("should not call users.update", async () => {
          await callMethodUnderTestWithSiteId([]);
          assertUsersFindCalledAndUpdateNotCalled();
        });
      });
      describe("and there is an element in the new schoolyears array", () => {
        it("should call users.update for the admin user at the site", async () => {
          await callMethodUnderTestWithSiteId([2015]);
          assert.isTrue(usersUpdateSpy.calledOnce);
        });
        it("should call users.update with a profile.siteAccess array element for each school year present in the new school years array", async () => {
          const schoolYears = [2015, 2016];
          await callMethodUnderTestWithSiteId(schoolYears);
          assert.isTrue(usersUpdateSpy.calledOnce);
          const siteAccessArray = usersUpdateSpy.firstCall.args[1].$set["profile.siteAccess"];
          assert.isTrue(
            schoolYears.every(schoolYear => siteAccessArray.some(siteAccess => siteAccess.schoolYear === schoolYear))
          );
        });
        it("should maintain siteAccess elements even when they are not present in the new schoolYears array", async () => {
          const existingSchoolYear = 1920;
          usersFindStub.callsFake(() => ({
            fetchAsync: () => [
              {
                _id: "testUserId",
                profile: {
                  siteAccess: [
                    {
                      siteId,
                      schoolYear: existingSchoolYear
                    }
                  ]
                }
              }
            ]
          }));
          const schoolYears = [2010];
          await callMethodUnderTestWithSiteId(schoolYears);
          assert.isTrue(usersUpdateSpy.calledOnce);
          assert.isTrue(
            usersUpdateSpy.firstCall.args[1].$set["profile.siteAccess"].some(
              siteAccess => siteAccess.schoolYear === existingSchoolYear
            )
          );
        });
      });
    });
    describe("when there are multiple admin users at the site", () => {
      const testSchoolYear = 2010;
      const multipleUsersArray = [
        {
          test: "test",
          _id: "testUserId",
          profile: {
            siteAccess: [
              {
                siteId,
                schoolYear: testSchoolYear
              }
            ]
          }
        },
        {
          test: "test2",
          _id: "testUserId2",
          profile: {
            siteAccess: [
              {
                siteId,
                schoolYear: testSchoolYear
              }
            ]
          }
        }
      ];
      beforeEach(() => {
        usersFindStub.callsFake(() => ({
          fetchAsync: () => multipleUsersArray
        }));
      });
      it("should call users.update for each admin user if the admin users have site access at the site", async () => {
        await callMethodUnderTestWithSiteId([testSchoolYear]);
        assert.equal(usersUpdateSpy.callCount, multipleUsersArray.length);
      });
    });
  });

  describe("updateCoachUserSiteAccess", () => {
    const siteId = "someSiteId";
    const previousSchoolYear = 2017;
    beforeAll(async () => {
      await Roles.insertAsync([
        {
          _id: adminRoleId,
          name: "admin"
        },
        {
          _id: teacherRoleId,
          name: "teacher"
        }
      ]);
    });
    afterAll(async () => {
      await Roles.removeAsync({});
    });
    afterEach(async () => {
      await Users.removeAsync({});
    });
    it("should update teacher account to coach account when provided teacher account", async () => {
      const teacherAccountId = "teacherId";
      const teacherAccount = {
        _id: teacherAccountId,
        profile: {
          siteAccess: [
            {
              role: teacherRoleId,
              siteId,
              schoolYear: previousSchoolYear
            },
            {
              role: teacherRoleId,
              siteId,
              schoolYear: currentSchoolYear
            }
          ]
        }
      };
      await Users.insertAsync(teacherAccount);

      await updateCoachUserSiteAccess({ user: teacherAccount, siteIds: [siteId] });

      const updatedTeacher = await Users.findOneAsync(teacherAccountId);
      expect(updatedTeacher.profile.siteAccess.every(sa => sa.role === adminRoleId)).toBeTruthy();
    });
    it("should provide new siteAccess for the User", async () => {
      const newSiteId = "newSiteId";
      const anotherSiteId = "anotherSiteId";
      const adminAccountId = "teacherId";
      const adminAccount = {
        _id: adminAccountId,
        profile: {
          siteAccess: [
            {
              role: adminRoleId,
              siteId,
              schoolYear: previousSchoolYear
            },
            {
              role: adminRoleId,
              siteId,
              schoolYear: currentSchoolYear
            }
          ]
        }
      };
      await Users.insertAsync(adminAccount);

      await updateCoachUserSiteAccess({
        user: adminAccount,
        siteIds: [anotherSiteId, newSiteId]
      });

      const updatedAdminDocument = await Users.findOneAsync(adminAccountId);
      const expectedAddedSiteAccess = [
        {
          role: adminRoleId,
          siteId,
          schoolYear: previousSchoolYear
        },
        {
          role: adminRoleId,
          siteId,
          schoolYear: currentSchoolYear
        }
      ];
      expect(updatedAdminDocument.profile.siteAccess).toMatchObject(expectedAddedSiteAccess);
    });
  });

  describe("getUsersInviteEmailStatus", () => {
    const userWithInviteSent = "userWithInviteSent";
    const userWithoutInviteSent = "userWithoutInviteSent";
    const existingUsers = [
      {
        _id: userWithInviteSent,
        services: {
          password: {
            bcrypt: "testedBCrypt",
            reset: {
              token: "testedToken"
            }
          }
        }
      },
      {
        _id: userWithoutInviteSent
      }
    ];

    beforeAll(async () => {
      await Users.insertAsync(existingUsers);
    });
    afterAll(async () => {
      await Users.removeAsync({});
    });
    it("should return a boolean with inviteEmailSent status for every provided user id", async () => {
      const userIds = [userWithInviteSent, userWithoutInviteSent];
      const expectedReturnValue = {
        userWithInviteSent: true,
        userWithoutInviteSent: false
      };
      expect(await getUserInviteEmailStatus(userIds)).toMatchObject(expectedReturnValue);
    });
  });
  describe("removeGroupOwnershipAndDeactivateSiteAccess", () => {
    const siteId = "testSiteId";
    const orgid = "test_organization_id";
    const previousSchoolYear = 2017;
    const getTestStudentGroup = getTestGroupInContext({ schoolYear: currentSchoolYear, siteId });
    const getTeacher = getUserInContext({ role: teacherRoleId, schoolYear: currentSchoolYear, orgid });
    const getAdmin = getUserInContext({ role: adminRoleId, schoolYear: currentSchoolYear, orgid });

    afterEach(async () => {
      await StudentGroups.removeAsync({});
      await Users.removeAsync({});
    });
    it("should remove provided teacher or coach ids from groups' owners and secondary teachers in the provided site in the current year", async () => {
      const removedOwner = "testOwnerId";
      const removedSecondaryTeacher = "testSecondaryTeacherId";
      const removedSecondaryTeacher2 = "testSecondaryTeacherId2";
      const removedUserIds = [removedOwner, removedSecondaryTeacher, removedSecondaryTeacher2];

      const keptSecondaryTeacher = "otherSecondaryTeacherId";
      const keptOwner = "otherOwnerId";

      const sgWithAllOwnersRemoved = "sgWithAllOwnersRemoved";
      const sgWithSecondaryTeacherRemoved = "sgWithSecondaryTeacherRemoved";
      const sgFromPreviousYear = "sgFromPreviousYear";
      const sgFromOtherSite = "sgFromOtherSite";

      const otherSiteId = "otherSiteId";
      const studentGroups = [
        getTestStudentGroup({
          _id: sgWithAllOwnersRemoved,
          ownerIds: [removedOwner],
          secondaryTeachers: [removedSecondaryTeacher, removedSecondaryTeacher2]
        }),
        getTestStudentGroup({
          _id: sgWithSecondaryTeacherRemoved,
          ownerIds: [keptOwner],
          secondaryTeachers: [removedSecondaryTeacher, keptSecondaryTeacher]
        }),
        getTestStudentGroup({
          _id: sgFromPreviousYear,
          ownerIds: [keptOwner],
          schoolYear: previousSchoolYear,
          secondaryTeachers: [removedSecondaryTeacher, keptSecondaryTeacher]
        }),
        getTestStudentGroup({
          _id: sgFromOtherSite,
          ownerIds: [removedOwner],
          secondaryTeachers: [],
          siteId: otherSiteId
        })
      ];
      await StudentGroups.insertAsync(studentGroups);

      await removeGroupOwnershipAndDeactivateSiteAccess(removedUserIds, siteId);

      const expectedGroups = [
        getTestStudentGroup({
          _id: sgWithAllOwnersRemoved,
          ownerIds: [],
          secondaryTeachers: []
        }),
        getTestStudentGroup({
          _id: sgWithSecondaryTeacherRemoved,
          ownerIds: [keptOwner],
          secondaryTeachers: [keptSecondaryTeacher]
        }),
        getTestStudentGroup({
          _id: sgFromPreviousYear,
          ownerIds: [keptOwner],
          schoolYear: previousSchoolYear,
          secondaryTeachers: [removedSecondaryTeacher, keptSecondaryTeacher]
        }),
        getTestStudentGroup({
          _id: sgFromOtherSite,
          ownerIds: [removedOwner],
          secondaryTeachers: [],
          siteId: otherSiteId
        })
      ];
      const filteredGroups = await StudentGroups.find({}).mapAsync(sg => omit(sg, ["lastModified"]));
      expect(orderBy(filteredGroups, "_id")).toEqual(orderBy(expectedGroups, "_id"));
    });
    it("should deactivate teachers, coaches, data admins siteAccess when users get archived", async () => {
      const getDataAdmin = getUserInContext({ role: "arbitraryIddataAdmin", schoolYear: currentSchoolYear, orgid });
      const otherSiteId = "otherSiteId";
      const singleSiteTeacherId = "singleSiteTeacherId";
      const multiSiteTeacherId = "multiSiteTeacherId";
      const teacherWithoutSiteAccessInCurrentYear = "teacherWithoutSiteAccessInCurrentYear";
      const singleSiteAdminId = "singleSiteAdminId";
      const multiSiteAdminId = "multiSiteAdminId";
      const adminWithoutSiteAccessInCurrentYear = "adminWithoutSiteAccessInCurrentYear";
      const adminWithMulitpleSitesAndDataAdminRole = "adminWithMulitpleSitesAndDataAdminRole";
      const activeDataAdminId = "activeDataAdminId";
      const teacherRole = "arbitraryIdteacher";
      const adminRole = "arbitraryIdadmin";
      const dataAdminRole = "arbitraryIddataAdmin";
      const userIdsToArchive = [
        multiSiteTeacherId,
        singleSiteAdminId,
        teacherWithoutSiteAccessInCurrentYear,
        adminWithoutSiteAccessInCurrentYear,
        adminWithMulitpleSitesAndDataAdminRole,
        activeDataAdminId
      ];
      const users = [
        getTeacher({
          _id: singleSiteTeacherId,
          siteIds: [siteId],
          schoolYearsToUse: [currentSchoolYear],
          isActive: true,
          address: "singleSiteTeacherEmail"
        }),
        getTeacher({
          _id: multiSiteTeacherId,
          siteIds: [siteId, otherSiteId],
          schoolYearsToUse: [currentSchoolYear],
          isActive: true,
          address: "multiSiteTeacherEmail"
        }),
        getTeacher({
          _id: teacherWithoutSiteAccessInCurrentYear,
          siteIds: [siteId],
          schoolYearsToUse: [previousSchoolYear],
          isActive: true,
          address: "teacherWithoutSiteAccessInCurrentYeEmail"
        }),
        getAdmin({
          _id: singleSiteAdminId,
          siteIds: [siteId],
          schoolYearsToUse: [currentSchoolYear],
          isActive: true,
          address: "singleSiteAdminEmail"
        }),
        getAdmin({
          _id: multiSiteAdminId,
          siteIds: [siteId, otherSiteId],
          schoolYearsToUse: [currentSchoolYear],
          isActive: true,
          address: "multiSiteAdminEmail"
        }),
        getAdmin({
          _id: adminWithoutSiteAccessInCurrentYear,
          siteIds: [siteId],
          schoolYearsToUse: [previousSchoolYear],
          isActive: true,
          address: "adminWithoutSiteAccessInCurrentYeEmail"
        }),
        getAdmin({
          _id: adminWithMulitpleSitesAndDataAdminRole,
          address: "adminWithMulitpleSitesAndDataAdminRoleEmail",
          siteAccess: [
            {
              siteId,
              schoolYear: previousSchoolYear,
              role: adminRole,
              isActive: true
            },
            {
              siteId: "allSites",
              schoolYear: 0,
              role: dataAdminRole,
              isActive: true
            },
            {
              siteId: otherSiteId,
              schoolYear: previousSchoolYear,
              role: adminRole,
              isActive: true
            }
          ]
        }),
        getDataAdmin({
          _id: activeDataAdminId,
          siteIds: [siteId],
          schoolYearsToUse: [currentSchoolYear],
          isActive: true,
          address: "activeDataAdminEmail"
        })
      ];
      const studentGroups = [
        getTestStudentGroup({
          _id: "1",
          ownerIds: [singleSiteTeacherId],
          secondaryTeachers: [multiSiteAdminId, teacherWithoutSiteAccessInCurrentYear]
        }),
        getTestStudentGroup({
          _id: "2",
          ownerIds: [singleSiteAdminId],
          secondaryTeachers: [multiSiteTeacherId, adminWithoutSiteAccessInCurrentYear]
        })
      ];
      await Users.insertAsync(users);
      await StudentGroups.insertAsync(studentGroups);

      await removeGroupOwnershipAndDeactivateSiteAccess(userIdsToArchive, siteId);

      const expectedGroups = [
        getTestStudentGroup({
          _id: "1",
          ownerIds: [singleSiteTeacherId],
          secondaryTeachers: [multiSiteAdminId]
        }),
        getTestStudentGroup({
          _id: "2",
          ownerIds: [],
          secondaryTeachers: []
        })
      ];
      const filteredGroups = await StudentGroups.find({}).mapAsync(sg => omit(sg, ["lastModified"]));
      expect(orderBy(filteredGroups, "_id")).toEqual(orderBy(expectedGroups, "_id"));
      const filteredUsers = (await Users.find({}).fetchAsync())
        .filter(u => userIdsToArchive.includes(u._id))
        .map(u => omit(u, ["profile.lastModified"]));
      const expectedUsers = [
        {
          _id: multiSiteTeacherId,
          emails: [
            {
              address: "multiSiteTeacherEmail"
            }
          ],
          profile: {
            siteAccess: [
              {
                siteId,
                schoolYear: currentSchoolYear,
                role: teacherRole,
                isActive: false
              },
              {
                siteId: otherSiteId,
                schoolYear: currentSchoolYear,
                role: teacherRole,
                isActive: true
              }
            ],
            orgid
          }
        },
        {
          _id: singleSiteAdminId,
          emails: [
            {
              address: "singleSiteAdminEmail"
            }
          ],
          profile: {
            siteAccess: [
              {
                siteId,
                schoolYear: currentSchoolYear,
                role: adminRole,
                isActive: false
              }
            ],
            orgid
          }
        },
        {
          _id: adminWithoutSiteAccessInCurrentYear,
          emails: [
            {
              address: "adminWithoutSiteAccessInCurrentYeEmail"
            }
          ],
          profile: {
            siteAccess: [
              {
                siteId,
                schoolYear: previousSchoolYear,
                role: adminRole,
                isActive: false
              }
            ],
            orgid
          }
        },
        {
          _id: adminWithMulitpleSitesAndDataAdminRole,
          emails: [
            {
              address: "adminWithMulitpleSitesAndDataAdminRoleEmail"
            }
          ],
          profile: {
            siteAccess: [
              {
                siteId,
                schoolYear: previousSchoolYear,
                role: adminRole,
                isActive: false
              },
              {
                siteId: "allSites",
                schoolYear: 0,
                role: dataAdminRole,
                isActive: true
              },
              {
                siteId: otherSiteId,
                schoolYear: previousSchoolYear,
                role: adminRole,
                isActive: true
              }
            ],
            orgid
          }
        },
        {
          _id: teacherWithoutSiteAccessInCurrentYear,
          emails: [
            {
              address: "teacherWithoutSiteAccessInCurrentYeEmail"
            }
          ],
          profile: {
            siteAccess: [
              {
                siteId,
                schoolYear: previousSchoolYear,
                role: teacherRole,
                isActive: true
              }
            ],
            orgid
          }
        },
        {
          _id: activeDataAdminId,
          emails: [
            {
              address: "activeDataAdminEmail"
            }
          ],
          profile: {
            siteAccess: [
              {
                siteId,
                schoolYear: currentSchoolYear,
                role: dataAdminRole,
                isActive: false
              }
            ],
            orgid
          }
        }
      ];
      expect(orderBy(filteredUsers, "_id")).toEqual(orderBy(expectedUsers, "_id"));
    });
  });
  describe("getManageTeacherData", () => {
    const orgid = "test_organization_id";
    const getTeacher = getUserInContext({ role: teacherRoleId, schoolYear: currentSchoolYear, orgid });
    const getAdmin = getUserInContext({ role: adminRoleId, schoolYear: currentSchoolYear, orgid });
    const previousSchoolYear = 2017;
    const siteId = "test_elementary_site_id";
    const siteIds = [siteId];

    afterEach(async () => {
      await Users.removeAsync({});
      await StudentGroups.removeAsync({});
    });
    afterAll(async () => {
      jest.restoreAllMocks();
    });

    it("should return teachers, admins and data admins associated with organization", async () => {
      const getDataAdmin = getUserInContext({ role: "arbitraryIddataAdmin", schoolYear: currentSchoolYear, orgid });
      const getSuperAdmin = getUserInContext({ role: "arbitraryIdsuperAdmin", schoolYear: currentSchoolYear, orgid });

      const superAdmin = getSuperAdmin({ siteIds: [siteId] });
      const outlanderTeacher = getTeacher({
        _id: "teacherId3",
        siteIds,
        schoolYearsToUse: [previousSchoolYear, currentSchoolYear]
      });
      outlanderTeacher.profile.orgid = "otherOrgId";
      const users = [
        getTeacher({
          _id: "teacherId",
          siteIds,
          schoolYearsToUse: [previousSchoolYear, currentSchoolYear]
        }),
        getTeacher({
          _id: "teacherId2",
          siteIds,
          schoolYearsToUse: [previousSchoolYear, currentSchoolYear],
          isActive: false
        }),
        outlanderTeacher,
        getAdmin({ siteIds, schoolYearsToUse: [previousSchoolYear, currentSchoolYear] }),
        getAdmin({ siteIds, schoolYearsToUse: [previousSchoolYear] }),
        getDataAdmin({ siteIds, schoolYearsToUse: [previousSchoolYear, currentSchoolYear] })
      ];
      await Users.insertAsync([...users, superAdmin]);

      const { teachers, admins, dataAdmins } = await getManageTeacherData({ orgid });

      expect(teachers).toHaveLength(2);
      expect(admins).toHaveLength(2);
      expect(dataAdmins).toHaveLength(1);
    });

    it("should not return teachers, admins and data admins that do not have access to organization", async () => {
      const teacher = getTeacher({ _id: "teacher", siteIds });
      const otherTeacher = getTeacher({
        _id: "otherTeacher",
        siteIds,
        schoolYearsToUse: [previousSchoolYear],
        isActive: false
      });
      const admin = getAdmin({ siteIds });
      const otherAdmin = getAdmin({ siteIds, schoolYearsToUse: [previousSchoolYear] });
      const noSiteAccessAdmin = getAdmin({ siteIds: [], schoolYearsToUse: [] });
      const dataAdmin = getUserInContext({ role: "arbitraryIddataAdmin", schoolYear: currentSchoolYear, orgid })({
        siteIds,
        schoolYearsToUse: [previousSchoolYear]
      });
      await Users.insertAsync([teacher, otherTeacher, admin, otherAdmin, dataAdmin, noSiteAccessAdmin]);

      const { teachers, admins, dataAdmins } = await getManageTeacherData({ orgid });

      expect(sortBy(teachers, "_id")).toEqual(sortBy([teacher, otherTeacher], "_id"));
      expect(sortBy(admins, "_id")).toEqual(sortBy([admin, otherAdmin, noSiteAccessAdmin], "_id"));
      expect(dataAdmins).toEqual([dataAdmin]);
    });

    it("should return ownership or secondary ownership assignments for users in organization in current school year", async () => {
      const getDataAdmin = getUserInContext({ role: "arbitraryIddataAdmin", schoolYear: currentSchoolYear, orgid });
      const users = [
        getTeacher({
          _id: "ownerTeacherId",
          siteIds,
          schoolYearsToUse: [previousSchoolYear, currentSchoolYear],
          isActive: true
        }),
        getTeacher({
          _id: "secondaryOwnerTeacherId",
          siteIds,
          schoolYearsToUse: [previousSchoolYear, currentSchoolYear],
          isActive: true
        }),
        getTeacher({
          _id: "secondaryOwnerTeacherIdFromPreviousYear",
          siteIds,
          schoolYearsToUse: [previousSchoolYear],
          isActive: true
        }),
        getTeacher({
          _id: "inactiveTeacherId",
          siteIds,
          schoolYearsToUse: [previousSchoolYear, currentSchoolYear],
          isActive: false
        }),
        getTeacher({
          _id: "notAssignedTeacherId",
          siteIds,
          schoolYearsToUse: [previousSchoolYear, currentSchoolYear],
          isActive: true
        }),
        getAdmin({
          _id: "ownerAdminId",
          siteIds,
          schoolYearsToUse: [previousSchoolYear, currentSchoolYear],
          isActive: true
        }),
        getAdmin({
          _id: "secondaryOwnerAdminId",
          siteIds,
          schoolYearsToUse: [previousSchoolYear, currentSchoolYear],
          isActive: true
        }),
        getAdmin({
          _id: "inactiveAdminId",
          siteIds,
          schoolYearsToUse: [previousSchoolYear, currentSchoolYear],
          isActive: false
        }),
        getAdmin({
          _id: "previousYearAssignedId",
          siteIds,
          schoolYearsToUse: [previousSchoolYear],
          isActive: true
        }),
        getAdmin({
          _id: "previousYearNotAssignedId",
          siteIds,
          schoolYearsToUse: [previousSchoolYear],
          isActive: true
        }),
        getAdmin({
          _id: "noSiteAccessId",
          siteIds: [],
          schoolYearsToUse: [],
          isActive: true
        }),
        getDataAdmin({
          _id: "activeDataAdminId",
          siteIds,
          schoolYearsToUse: [previousSchoolYear],
          isActive: true
        })
      ];
      const studentGroups = [
        {
          orgid,
          siteId,
          isActive: true,
          schoolYear: currentSchoolYear,
          ownerIds: ["ownerTeacherId"],
          secondaryTeachers: ["secondaryOwnerTeacherId"]
        },
        {
          orgid,
          siteId,
          isActive: true,
          schoolYear: previousSchoolYear,
          ownerIds: ["ownerTeacherId"],
          secondaryTeachers: ["secondaryOwnerTeacherIdFromPreviousYear", "previousYearAssignedId"]
        },
        {
          orgid,
          siteId,
          isActive: true,
          schoolYear: currentSchoolYear,
          ownerIds: ["ownerAdminId"],
          secondaryTeachers: ["secondaryOwnerAdminId"]
        }
      ];

      await Users.insertAsync([...users]);
      await StudentGroups.insertAsync([...studentGroups]);

      const result = await getManageTeacherData({ orgid });

      expect(result.archivedUserIdsByRoleBySiteId).toEqual({
        arbitraryIdteacher: {
          test_elementary_site_id: [
            "notAssignedTeacherId",
            "secondaryOwnerTeacherIdFromPreviousYear",
            "inactiveTeacherId"
          ]
        },
        arbitraryIdadmin: {
          test_elementary_site_id: ["inactiveAdminId", "noSiteAccessId"]
        },
        arbitraryIddataAdmin: {}
      });
      expect(result.activeUserIdsByRoleBySiteId).toEqual({
        arbitraryIdteacher: {
          test_elementary_site_id: ["ownerTeacherId", "secondaryOwnerTeacherId"]
        },
        arbitraryIdadmin: {
          test_elementary_site_id: [
            "ownerAdminId",
            "secondaryOwnerAdminId",
            "previousYearAssignedId",
            "previousYearNotAssignedId"
          ].sort()
        },
        arbitraryIddataAdmin: {
          allSites: ["activeDataAdminId"]
        }
      });
    });
    it("should return admin owner or secondary owner assignments for admins in organization", async () => {
      const users = [
        getAdmin({
          _id: "activePreviousYearOwnerId",
          siteIds,
          schoolYearsToUse: [previousSchoolYear],
          isActive: true
        }),
        getAdmin({
          _id: "currentYearActiveId",
          siteIds,
          schoolYearsToUse: [currentSchoolYear],
          isActive: true
        }),
        getAdmin({
          _id: "previousYearNotAssignedActiveId",
          siteIds,
          schoolYearsToUse: [currentSchoolYear],
          isActive: true
        }),
        getAdmin({
          _id: "noSiteAccess",
          siteIds: [],
          schoolYearsToUse: [],
          isActive: true
        }),
        getAdmin({
          _id: "notActiveId",
          siteIds,
          schoolYearsToUse: [previousSchoolYear],
          isActive: false
        })
      ];

      const studentGroups = [
        {
          orgid,
          siteId,
          isActive: true,
          schoolYear: previousSchoolYear,
          ownerIds: ["activePreviousYearOwnerId"],
          secondaryTeachers: []
        }
      ];

      await Users.insertAsync(users);
      await StudentGroups.insertAsync(studentGroups);
      await Sites.insertAsync({ _id: siteId, orgid });

      const { archivedUserIdsByRoleBySiteId, activeUserIdsByRoleBySiteId } = await getManageTeacherData({ orgid });
      expect(archivedUserIdsByRoleBySiteId.arbitraryIdadmin.test_elementary_site_id.sort()).toEqual(
        ["noSiteAccess", "notActiveId"].sort()
      );
      expect(activeUserIdsByRoleBySiteId.arbitraryIdadmin.test_elementary_site_id).toEqual([
        "activePreviousYearOwnerId",
        "currentYearActiveId",
        "previousYearNotAssignedActiveId"
      ]);
    });
  });

  describe("removeCoachUser", () => {
    const coachAccountId = "adminId";
    const firstSiteId = "siteId_1";
    const secondSiteId = "siteId_2";
    const getTestStudentGroup = getTestGroupInContext({ schoolYear: currentSchoolYear, siteId: firstSiteId });
    beforeEach(async () => {
      const coachAccount = {
        _id: coachAccountId,
        profile: {
          siteAccess: [
            {
              role: adminRoleId,
              siteId: firstSiteId,
              schoolYear: currentSchoolYear
            },
            {
              role: adminRoleId,
              siteId: firstSiteId,
              schoolYear: currentSchoolYear - 1
            },
            {
              role: adminRoleId,
              siteId: secondSiteId,
              schoolYear: currentSchoolYear
            }
          ]
        }
      };
      await Users.insertAsync(coachAccount);
    });
    afterEach(async () => {
      await Users.removeAsync({});
      await StudentGroups.removeAsync({});
    });
    it("should remove coach siteAccess for site if user is not assigned to any group", async () => {
      const studentGroups = [
        getTestStudentGroup({
          secondaryTeachers: []
        })
      ];
      await StudentGroups.insertAsync(studentGroups);

      await removeCoachUser(coachAccountId, firstSiteId);

      const expectedFirstSiteAccess = [];

      const updatedUser = await Users.findOneAsync({ _id: coachAccountId });
      const firstSiteAccess = updatedUser.profile.siteAccess.filter(sa => sa.siteId === firstSiteId);
      expect(firstSiteAccess).toEqual(expectedFirstSiteAccess);
    });
    it("should remove coach account if user is not assigned to any group and only has access to a single site", async () => {
      const studentGroups = [
        getTestStudentGroup({
          secondaryTeachers: []
        })
      ];
      const singleSiteAccessCoachId = "singleSiteAccessCoachId";
      await StudentGroups.insertAsync(studentGroups);
      await Users.insertAsync({
        _id: singleSiteAccessCoachId,
        profile: {
          siteAccess: [
            {
              role: adminRoleId,
              siteId: firstSiteId,
              schoolYear: currentSchoolYear
            },
            {
              role: adminRoleId,
              siteId: firstSiteId,
              schoolYear: currentSchoolYear - 1
            }
          ]
        }
      });

      await removeCoachUser(singleSiteAccessCoachId, firstSiteId);

      const user = await Users.findOneAsync({ _id: singleSiteAccessCoachId });
      expect(user).toEqual(null);
    });
    it("should downgrade coach to teacher if they are assigned to any group as the owner", async () => {
      const studentGroups = [
        getTestStudentGroup({
          secondaryTeachers: [],
          ownerIds: [coachAccountId]
        })
      ];
      await StudentGroups.insertAsync(studentGroups);

      await removeCoachUser(coachAccountId, firstSiteId);

      const expectedFirstSiteAccess = [
        {
          role: teacherRoleId,
          schoolYear: currentSchoolYear,
          siteId: firstSiteId,
          isActive: true,
          isDefault: true
        }
      ];

      const updatedUser = await Users.findOneAsync({ _id: coachAccountId });
      const firstSiteAccess = updatedUser.profile.siteAccess.filter(sa => sa.siteId === firstSiteId);
      expect(firstSiteAccess).toEqual(expectedFirstSiteAccess);
    });
    it("should downgrade coach to teacher if they are assigned to any group as a secondary teacher", async () => {
      const studentGroups = [
        getTestStudentGroup({
          secondaryTeachers: [coachAccountId]
        })
      ];
      await StudentGroups.insertAsync(studentGroups);

      await removeCoachUser(coachAccountId, firstSiteId);

      const expectedFirstSiteAccess = [
        {
          role: teacherRoleId,
          schoolYear: currentSchoolYear,
          siteId: firstSiteId,
          isActive: true,
          isDefault: true
        }
      ];

      const updatedUser = await Users.findOneAsync({ _id: coachAccountId });
      const firstSiteAccess = updatedUser.profile.siteAccess.filter(sa => sa.siteId === firstSiteId);
      expect(firstSiteAccess).toEqual(expectedFirstSiteAccess);
    });
  });

  describe("removeDataAdminUsers", () => {
    const dataAdminAccountId = "dataAdminId";
    const firstSiteId = "siteId_1";
    afterEach(async () => {
      await Users.removeAsync({});
    });
    it("should throw an error when data admin is not found", async () => {
      await expect(removeDataAdminUsers(["nonExistingDataAdminId"])).rejects.toThrow(
        "Selected Data Administrator(s) do not exist."
      );
    });
    it("should only remove dataAdmin siteAccess from user and leave siteAccess for other roles", async () => {
      await Users.insertAsync({
        _id: dataAdminAccountId,
        profile: {
          siteAccess: [
            {
              role: dataAdminRoleId,
              siteId: "allSites",
              schoolYear: 0
            },
            {
              role: adminRoleId,
              siteId: firstSiteId,
              schoolYear: currentSchoolYear
            }
          ]
        }
      });

      await removeDataAdminUsers([dataAdminAccountId]);

      const expectedSiteAccess = [
        {
          role: adminRoleId,
          siteId: firstSiteId,
          schoolYear: currentSchoolYear
        }
      ];

      const updatedUser = await Users.findOneAsync({ _id: dataAdminAccountId });
      expect(updatedUser.profile.siteAccess).toEqual(expectedSiteAccess);
    });
    it("should remove dataAdmin account if user does not have any other role", async () => {
      await Users.insertAsync({
        _id: dataAdminAccountId,
        profile: {
          siteAccess: [
            {
              role: dataAdminRoleId,
              siteId: "allSites",
              schoolYear: 0
            }
          ]
        }
      });

      await removeDataAdminUsers([dataAdminAccountId]);

      const user = await Users.findOneAsync({ _id: dataAdminAccountId });
      expect(user).toEqual(null);
    });
  });

  describe("getAllSchoolYearsFor", () => {
    it("should return a list of all years since the beginning of SpringMath", async () => {
      const expectedYears = [2013, 2014, 2015, 2016, 2017, 2018];
      expect(await getAllSchoolYearsFor({})).toEqual(expectedYears);
    });
  });

  describe("getArchivedTeachersOrAdminsId", () => {
    const orgid = "test_organization_id";
    const getTeacher = getUserInContext({ role: teacherRoleId, schoolYear: currentSchoolYear, orgid });
    const getAdmin = getUserInContext({ role: adminRoleId, schoolYear: currentSchoolYear, orgid });
    const previousSchoolYear = 2017;
    const siteId = "test_elementary_site_id";
    const siteIds = [siteId];
    const activeUserId = "activeUserId";
    const archivedUserIsNoActiveId = "archivedUserIsNoActiveId";
    const archivedUserNoOwnerId = "archivedUserNoOwnerId";
    const archivedUserNoAccessId = "archivedUserNoAccessId";

    const users = [
      getTeacher({
        _id: activeUserId,
        siteIds,
        schoolYearsToUse: [previousSchoolYear, currentSchoolYear],
        isActive: true
      }),
      getTeacher({
        _id: archivedUserIsNoActiveId,
        siteIds,
        schoolYearsToUse: [previousSchoolYear, currentSchoolYear],
        isActive: false
      }),
      getAdmin({
        _id: archivedUserNoOwnerId,
        siteIds: [],
        schoolYearsToUse: [currentSchoolYear],
        isActive: true
      }),
      getAdmin({
        _id: archivedUserNoAccessId,
        siteIds: [],
        schoolYearsToUse: [previousSchoolYear],
        isActive: true
      })
    ];
    const studentGroups = [
      {
        orgid,
        siteId,
        isActive: true,
        schoolYear: currentSchoolYear,
        ownerIds: [activeUserId],
        secondaryTeachers: [archivedUserNoAccessId]
      },
      {
        orgid,
        siteId,
        isActive: true,
        schoolYear: previousSchoolYear,
        ownerIds: [archivedUserIsNoActiveId],
        secondaryTeachers: []
      }
    ];

    beforeEach(async () => {
      await Users.insertAsync([...users]);
      await StudentGroups.insertAsync([...studentGroups]);
    });
    afterEach(async () => {
      await Users.removeAsync({});
      await StudentGroups.removeAsync({});
    });
    afterAll(() => {
      jest.restoreAllMocks();
    });
    it("should not return group ownerId for a user with correct site access", async () => {
      const archivedUserIds = await getArchivedTeacherOrAdminIds([activeUserId], orgid);
      expect(archivedUserIds).toEqual([]);
    });
    it("should return archived userId for a user that doesn't have site access in current school year", async () => {
      const archivedUserIds = await getArchivedTeacherOrAdminIds([archivedUserNoAccessId], orgid);
      expect(archivedUserIds).toEqual([archivedUserNoAccessId]);
    });
    it("should return archived userId for a user that has correct site access and is not an owner or secondary teacher of group", async () => {
      const archivedUserIds = await getArchivedTeacherOrAdminIds([archivedUserNoOwnerId], orgid);
      expect(archivedUserIds).toEqual([archivedUserNoOwnerId]);
    });
    it("should return archived userId for a user that doesn't have active site access in current school year", async () => {
      const archivedUserIds = await getArchivedTeacherOrAdminIds([archivedUserIsNoActiveId], orgid);
      expect(archivedUserIds).toEqual([archivedUserIsNoActiveId]);
    });
    it("should return only archived userIds when provided with multiple userIds", async () => {
      const archivedUserIds = await getArchivedTeacherOrAdminIds(
        [archivedUserIsNoActiveId, activeUserId, archivedUserNoOwnerId, archivedUserNoAccessId],
        orgid
      );
      expect(archivedUserIds).toEqual([archivedUserIsNoActiveId, archivedUserNoOwnerId, archivedUserNoAccessId]);
    });
  });

  describe("getUserAuthInfoByEmail", () => {
    const orgid = "normal_org_id";
    const ssoOrgid = "sso_org_id";
    const ssoOnlyOrgid = "sso_only_org_id";
    const siteId = "test_elementary_site_id";
    const siteIds = [siteId];
    const getTeacher = getUserInContext({ role: teacherRoleId, schoolYear: currentSchoolYear, orgid });
    const getSSOTeacher = getUserInContext({ role: teacherRoleId, schoolYear: currentSchoolYear, orgid: ssoOrgid });
    const getSSOOnlyTeacher = getUserInContext({
      role: teacherRoleId,
      schoolYear: currentSchoolYear,
      orgid: ssoOnlyOrgid
    });
    const getDataAdmin = getUserInContext({ role: dataAdminRoleId, schoolYear: currentSchoolYear, orgid });
    const getSSODataAdmin = getUserInContext({ role: dataAdminRoleId, schoolYear: currentSchoolYear, orgid: ssoOrgid });
    const getSSOOnlyDataAdmin = getUserInContext({
      role: dataAdminRoleId,
      schoolYear: currentSchoolYear,
      orgid: ssoOnlyOrgid
    });
    const defaultAuthInfo = {
      isSSOOnlyOrg: false,
      isDataAdmin: false,
      ssoPortalUrl: "",
      isMFAEnabled: false,
      isMFARequired: false
    };

    const orgs = [
      { _id: orgid },
      { _id: ssoOrgid, ssoIssuerOrgId: "1234", useSSOOnly: false },
      { _id: ssoOnlyOrgid, ssoIssuerOrgId: "12345", useSSOOnly: true }
    ];

    const users = [
      getTeacher({
        siteIds,
        schoolYearsToUse: [currentSchoolYear],
        isActive: true,
        address: "<EMAIL>"
      }),
      getSSOTeacher({
        siteIds,
        schoolYearsToUse: [currentSchoolYear],
        isActive: true,
        address: "<EMAIL>"
      }),
      getSSOOnlyTeacher({
        siteIds,
        schoolYearsToUse: [currentSchoolYear],
        isActive: true,
        address: "<EMAIL>"
      }),
      getDataAdmin({
        siteIds,
        schoolYearsToUse: [currentSchoolYear],
        isActive: true,
        address: "<EMAIL>"
      }),
      getSSODataAdmin({
        siteIds,
        schoolYearsToUse: [currentSchoolYear],
        isActive: true,
        address: "<EMAIL>"
      }),
      getSSOOnlyDataAdmin({
        siteIds,
        schoolYearsToUse: [currentSchoolYear],
        isActive: true,
        address: "<EMAIL>"
      })
    ];
    beforeAll(async () => {
      Organizations.insertAsync(orgs);
      Users.insertAsync([...users]);
    });

    afterAll(async () => {
      await Organizations.removeAsync({});
      await Users.removeAsync({});
      jest.restoreAllMocks();
    });
    it("should return the default object when no email is passed", async () => {
      expect(await getUserAuthInfoByEmail()).toEqual(defaultAuthInfo);
    });
    it("should return the default object when there is no matching user", async () => {
      expect(await getUserAuthInfoByEmail("<EMAIL>")).toEqual(defaultAuthInfo);
    });
    it("should return the object with isSSOOnlyOrg as false when there is a matching user in a no SSO only organization", async () => {
      expect(await getUserAuthInfoByEmail("<EMAIL>")).toEqual(defaultAuthInfo);
    });
    it("should return the object with isSSOOnlyOrg as false when there is a matching user in a SSO organization", async () => {
      expect(await getUserAuthInfoByEmail("<EMAIL>")).toEqual(defaultAuthInfo);
    });
    it("should return the object with isSSOOnlyOrg as true when there is a matching user in a SSO only organization", async () => {
      expect(await getUserAuthInfoByEmail("<EMAIL>")).toEqual({
        ...defaultAuthInfo,
        isSSOOnlyOrg: true
      });
    });
    it("should return the object with isSSOOnlyOrg as false and isDataAdmin as true when there is a matching data admin in a no SSO only organization", async () => {
      expect(await getUserAuthInfoByEmail("<EMAIL>")).toEqual({
        ...defaultAuthInfo,
        isDataAdmin: true
      });
    });
    it("should return the object with isSSOOnlyOrg as false and isDataAdmin as true when there is a matching data admin in a SSO organization", async () => {
      expect(await getUserAuthInfoByEmail("<EMAIL>")).toEqual({
        ...defaultAuthInfo,
        isDataAdmin: true
      });
    });
    it("should return the object with isSSOOnlyOrg as true and isDataAdmin as true when there is a matching data admin in a SSO only organization", async () => {
      expect(await getUserAuthInfoByEmail("<EMAIL>")).toEqual({
        ...defaultAuthInfo,
        isSSOOnlyOrg: true,
        isDataAdmin: true
      });
    });
  });
  describe("saveLastPasswordChangeDate", () => {
    const getTeacher = getUserInContext({ role: teacherRoleId, schoolYear: currentSchoolYear, orgid: "testOrgId" });
    const userId = "teacherId";
    const teacher = getTeacher({
      _id: userId,
      siteIds: ["testSiteId"],
      isActive: true
    });

    beforeAll(async () => {
      Users.insertAsync([teacher]);
    });

    afterAll(async () => {
      Users.removeAsync({});
    });

    it("should correctly update lastPasswordChange value after changing a password", async () => {
      let user = await Users.findOneAsync({ _id: userId });

      expect(user.profile.lastPasswordChange).toBeUndefined();
      await saveLastPasswordChangeDate(userId);
      user = await Users.findOneAsync({ _id: userId });
      expect(user.profile.lastPasswordChange).toBeDefined();
      const previousLastPasswordChange = user.profile.lastPasswordChange;
      await saveLastPasswordChangeDate(userId);
      user = await Users.findOneAsync({ _id: userId });
      expect(user.profile.lastPasswordChange).toBeGreaterThanOrEqual(previousLastPasswordChange);
    });
  });
  describe("updateLoginData", () => {
    const getTeacher = getUserInContext({ role: teacherRoleId, schoolYear: currentSchoolYear, orgid: "testOrgId" });
    const teacher = getTeacher({
      _id: "teacherId",
      siteIds: ["testSiteId"],
      isActive: true
    });
    const meteorUserIdStub = sinon.stub(Meteor, "userId");
    afterEach(() => {
      meteorUserIdStub.resetBehavior();
    });
    afterAll(() => {
      jest.restoreAllMocks();
    });
    it("should update user lastLogin and loginCount on successful login", async () => {
      await Users.insertAsync([teacher]);
      meteorUserIdStub.callsFake(() => "teacherId");
      await updateLoginData();
      const updatedUser = await Users.findOneAsync();
      const initialLastLogin = updatedUser.loginData.lastLogin;
      expect(initialLastLogin).not.toEqual(null);
      expect(updatedUser.loginData.loginCount).toEqual(1);
      expect(updatedUser.loginData.lastFailedLogin).toEqual(null);
    });
    it("should update user lastLogin and increment loginCount on successful login", async () => {
      meteorUserIdStub.callsFake(() => "teacherId");
      const initilaUser = await Users.findOneAsync();
      await updateLoginData();
      const updatedUser = await Users.findOneAsync();
      expect(updatedUser.loginData.loginCount).toEqual(2);
      expect(updatedUser.loginData.lastLogin).toBeGreaterThanOrEqual(initilaUser.loginData.lastLogin);
      await Users.removeAsync({});
    });
    it("should update user lastFailedLogin on failed login", async () => {
      await Users.insertAsync([teacher]);
      meteorUserIdStub.callsFake(() => "teacherId");
      const userEmail = (await Users.findOneAsync()).emails[0].address;
      await updateLoginData(userEmail);
      const updatedUser = await Users.findOneAsync();
      expect(updatedUser.loginData.lastLogin).toEqual(null);
      expect(updatedUser.loginData.loginCount).toEqual(0);
      expect(updatedUser.loginData.lastFailedLogin).not.toEqual(null);
      await Users.removeAsync({});
    });
  });
});
