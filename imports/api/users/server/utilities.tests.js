import { getUpdatedCoachProfile } from "./utilities";

describe("getUpdatedCoachProfile", () => {
  it("should add missing site access and localId to coach profile", () => {
    const currentProfile = {
      orgid: "orgid",
      siteAccess: [
        {
          role: "arbitraryIdadmin",
          siteId: "siteId",
          schoolYear: 2018,
          isActive: true
        }
      ]
    };
    const updatedUser = {
      siteIds: ["siteId", "otherSite"],
      localId: "newLocalId",
      role: "arbitraryIdadmin",
      isActive: true
    };
    const currentSchoolYear = 2019;

    const result = getUpdatedCoachProfile(updatedUser, currentProfile, currentSchoolYear, {});

    expect(result).toMatchObject({
      orgid: "orgid",
      localId: "newLocalId",
      siteAccess: [
        {
          role: "arbitraryIdadmin",
          siteId: "siteId",
          schoolYear: 2019,
          isActive: true
        },
        {
          role: "arbitraryIdadmin",
          siteId: "otherSite",
          schoolYear: 2019,
          isActive: true
        },
        {
          role: "arbitraryIdadmin",
          siteId: "siteId",
          schoolYear: 2018,
          isActive: true
        }
      ],
      lastModified: {}
    });
  });
});
