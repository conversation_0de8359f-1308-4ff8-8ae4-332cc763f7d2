import { Meteor } from "meteor/meteor";
import { assert } from "chai";

import { Users } from "./users.js";
import { user, superAdminUser, adminUser, teacherUser, dataAdminUser } from "../../test-helpers/data/users.js";

if (Meteor.isServer) {
  describe("Users", () => {
    describe("user", () => {
      describe("Should pass schema validation method", () => {
        it("validate", () => {
          assert.isUndefined(Users.validate(user(null, null, "SomeRole")));
        });
        it("isValid", () => {
          assert.isTrue(Users.isValid(user(null, null, "SomeRole")));
        });
      });
      describe("Should fail schema validation method", () => {
        it("isValid", () => {
          const userDoc = user("SomeRole");
          userDoc.profile.name = 1234;
          assert.isFalse(Users.isValid(userDoc));
        });
      });
    });
    describe("superAdminUser", () => {
      describe("Should pass schema validation method", () => {
        it("validate", () => {
          assert.isUndefined(Users.validate(superAdminUser()));
        });
        it("isValid", () => {
          assert.isTrue(Users.isValid(superAdminUser()));
        });
      });
      describe("Should fail schema validation method", () => {
        it("isValid", () => {
          const userDoc = superAdminUser();
          userDoc.profile.name = 1234;
          assert.isFalse(Users.isValid(userDoc));
        });
      });
    });
    describe("adminUser", () => {
      describe("Should pass schema validation method", () => {
        it("validate", () => {
          assert.isUndefined(Users.validate(adminUser()));
        });
        it("isValid", () => {
          assert.isTrue(Users.isValid(adminUser()));
        });
      });
      describe("Should fail schema validation method", () => {
        it("isValid", () => {
          const userDoc = adminUser();
          userDoc.profile.name = 1234;
          assert.isFalse(Users.isValid(userDoc));
        });
      });
    });
    describe("teacherUser", () => {
      describe("Should pass schema validation method", () => {
        it("validate", () => {
          assert.isUndefined(Users.validate(teacherUser()));
        });
        it("isValid", () => {
          assert.isTrue(Users.isValid(teacherUser()));
        });
      });
      describe("Should fail schema validation method", () => {
        it("isValid", () => {
          const userDoc = teacherUser();
          userDoc.profile.name = 1234;
          assert.isFalse(Users.isValid(userDoc));
        });
      });
    });
    describe("dataAdminUser", () => {
      describe("Should pass schema validation method", () => {
        it("validate", () => {
          assert.isUndefined(Users.validate(dataAdminUser()));
        });
        it("isValid", () => {
          assert.isTrue(Users.isValid(dataAdminUser()));
        });
      });
      describe("Should fail schema validation method", () => {
        it("isValid", () => {
          const userDoc = dataAdminUser();
          userDoc.profile.name = 1234;
          assert.isFalse(Users.isValid(userDoc));
        });
      });
    });
  });
}
