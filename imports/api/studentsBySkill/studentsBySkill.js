import { Mongo } from "meteor/mongo";
import SimpleSchema from "simpl-schema";

export const StudentsBySkill = new Mongo.Collection("StudentsBySkill");

StudentsBySkill.schema = new SimpleSchema({
  _id: { type: String, optional: true },
  skillName: { type: String },
  assessmentGroupId: { type: String },
  siteId: { type: String },
  studentsBelowInstructionalTarget: [String],
  studentsBelowMasteryTarget: [String],
  studentsWithoutSkillHistory: [String]
});

StudentsBySkill.validate = groupedStudentsObject => {
  StudentsBySkill.schema.validate(groupedStudentsObject);
};
StudentsBySkill.isValid = groupedStudentsObject => {
  return StudentsBySkill.schema.namedContext("checkStudentsBySkill").validate(groupedStudentsObject);
};
