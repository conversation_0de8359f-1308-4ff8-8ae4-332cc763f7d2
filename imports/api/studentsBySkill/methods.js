import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";
import { get, groupBy, keyBy, sortBy, uniq } from "lodash";
import { Students } from "../students/students";
import { Assessments } from "../assessments/assessments";
import { GroupedAssessments } from "../groupedAssessments/groupedAssessments";
import { StudentGroups } from "../studentGroups/studentGroups";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";
import { StudentsBySkill } from "./studentsBySkill";
import * as auth from "../authorization/server/methods";
import { getValuesByKey } from "../utilities/utilities";
import { normalizeGrade } from "../utilities/sortingHelpers/normalizeSortItem";

export const assignStudentToSkillGroup = async ({ siteId, studentId }) => {
  const student = await Students.findOneAsync({ _id: studentId });
  if (!(student && student.currentSkill)) {
    return null;
  }
  if (student.history) {
    if (!student.currentSkill.assessmentId || !get(student, "currentSkill.interventions.length") > 0) {
      const assessment = await Assessments.findOneAsync({ _id: student.history[0].assessmentId });
      const assessmentGroup = await GroupedAssessments.findOneAsync({
        assessmentMeasures: assessment.monitorAssessmentMeasure
      });
      if (!assessmentGroup || !assessmentGroup._id) {
        return null;
      }
      await StudentsBySkill.updateAsync(
        { assessmentGroupId: assessmentGroup._id, siteId },
        {
          $pull: {
            studentsWithoutSkillHistory: student._id,
            studentsBelowInstructionalTarget: student._id,
            studentsBelowMasteryTarget: student._id
          }
        }
      );
      return null;
    }
    const assessment = await Assessments.findOneAsync({ _id: student.currentSkill.assessmentId });
    const assessmentGroup = await GroupedAssessments.findOneAsync({
      assessmentMeasures: assessment.monitorAssessmentMeasure
    });
    if (!assessmentGroup || !assessmentGroup._id) {
      return null;
    }
    const latestHistoryEntry = student.history[0];
    if (latestHistoryEntry.assessmentId === student.currentSkill.assessmentId) {
      const resultMeasure = latestHistoryEntry.assessmentResultMeasures.find(
        arm => arm.assessmentId === student.currentSkill.assessmentId
      );
      const score = parseInt(resultMeasure.studentResults[0].score);
      if (score < resultMeasure.targetScores[0]) {
        await StudentsBySkill.updateAsync(
          { assessmentGroupId: assessmentGroup._id, siteId },
          {
            $addToSet: { studentsBelowInstructionalTarget: student._id },
            $pull: {
              studentsWithoutSkillHistory: student._id,
              studentsBelowMasteryTarget: student._id
            }
          }
        );
      } else if (score < resultMeasure.targetScores[1]) {
        await StudentsBySkill.updateAsync(
          { assessmentGroupId: assessmentGroup._id, siteId },
          {
            $addToSet: { studentsBelowMasteryTarget: student._id },
            $pull: {
              studentsWithoutSkillHistory: student._id,
              studentsBelowInstructionalTarget: student._id
            }
          }
        );
      }
    } else {
      const previousAssessment = await Assessments.findOneAsync({ _id: latestHistoryEntry.assessmentId });
      const previousAssessmentGroup = await GroupedAssessments?.findOneAsync({
        assessmentMeasures: previousAssessment.monitorAssessmentMeasure
      });
      await StudentsBySkill.updateAsync(
        { assessmentGroupId: previousAssessmentGroup?._id, siteId },
        {
          $pull: {
            studentsWithoutSkillHistory: student._id,
            studentsBelowInstructionalTarget: student._id,
            studentsBelowMasteryTarget: student._id
          }
        }
      );
      await StudentsBySkill.updateAsync(
        { assessmentGroupId: assessmentGroup._id, siteId },
        { $addToSet: { studentsWithoutSkillHistory: student._id } }
      );
    }
  } else {
    const assessment = await Assessments.findOneAsync({ _id: student.currentSkill.assessmentId });
    const assessmentGroup = await GroupedAssessments.findOneAsync({
      assessmentMeasures: assessment.monitorAssessmentMeasure
    });
    await StudentsBySkill.updateAsync(
      { assessmentGroupId: assessmentGroup._id, siteId },
      { $addToSet: { studentsWithoutSkillHistory: student._id } }
    );
  }

  return null;
};

const getRelevantStudentsFrom = ({ skillGroup, students, studentGroupEnrollments, studentGroups }) => {
  const relevantStudents = [
    ...students
      .filter(s => skillGroup.studentsWithoutSkillHistory.includes(s._id))
      .map(s => ({ ...s, skillProgress: "No Progress" })),
    ...students
      .filter(s => skillGroup.studentsBelowInstructionalTarget.includes(s._id))
      .map(s => ({ ...s, skillProgress: "Acquisition" })),
    ...students
      .filter(s => skillGroup.studentsBelowMasteryTarget.includes(s._id))
      .map(s => ({ ...s, skillProgress: "Fluency" }))
  ];

  return relevantStudents.map(student => {
    const currentStudentGroupEnrollment = studentGroupEnrollments.find(sge => sge.studentId === student._id);

    const currentStudentGroup = studentGroups.find(sg => sg._id === currentStudentGroupEnrollment.studentGroupId);
    return {
      _id: student._id,
      firstName: student.identity.name.firstName,
      lastName: student.identity.name.lastName,
      grade: student.grade,
      skillProgress: student.skillProgress,
      interventionName: student.currentSkill.assessmentName,
      studentGroupName: currentStudentGroup?.name || "N/A"
    };
  });
};

export const getSkillGroupAssignments = async ({ siteId, schoolYear, orgid, userId }) => {
  const studentGroupsQuery = { siteId, schoolYear, orgid };
  if (await auth.hasAccess(["teacher"], { userId, siteId })) {
    studentGroupsQuery.ownerIds = userId;
  }

  const studentGroups = await StudentGroups.find(studentGroupsQuery, {
    projection: { ownerIds: 1, grade: 1, name: 1 }
  }).fetchAsync();
  const studentGroupIds = studentGroups.map(sg => sg._id);
  const studentGroupEnrollments = await StudentGroupEnrollments.find(
    { studentGroupId: { $in: studentGroupIds } },
    { projection: { studentId: 1, studentGroupId: 1 } }
  ).fetchAsync();
  const studentIds = studentGroupEnrollments.map(sge => sge.studentId);
  const students = await Students.find(
    { _id: { $in: studentIds }, currentSkill: { $exists: true } },
    { projection: { identity: 1, "currentSkill.assessmentName": 1, grade: 1 } }
  ).fetchAsync();

  const studentsByGrade = groupBy(
    sortBy(students, a => normalizeGrade(a.grade)),
    "grade"
  );

  // NOTE(fmazur) - All small groups with students within site
  const studentsBySkillQuery = {
    siteId,
    $or: [
      { "studentsWithoutSkillHistory.0": { $exists: true } },
      { "studentsBelowInstructionalTarget.0": { $exists: true } },
      { "studentsBelowMasteryTarget.0": { $exists: true } }
    ]
  };

  const studentsBySkill = await StudentsBySkill.find(studentsBySkillQuery, {
    projection: { _id: 0, assessmentGroupId: 0, siteId: 0 }
  }).fetchAsync();

  return sortBy(
    Object.values(studentsByGrade)
      .map(studentsInGrade => {
        return (
          studentsBySkill
            .map(sbs => {
              const relevantStudents = getRelevantStudentsFrom({
                skillGroup: sbs,
                students: studentsInGrade,
                studentGroupEnrollments,
                studentGroups
              });

              return {
                skillName: sbs.skillName,
                students: sortBy(relevantStudents, [
                  "interventionName",
                  "skillProgress",
                  "grade",
                  "studentGroupName",
                  "lastName",
                  "firstName"
                ])
              };
            })
            // NOTE(fmazur) - Ensure that there are at least two students in a small group
            .filter(skillRow => skillRow.students.length > 1)
        );
      })
      .flat(1),
    "skillName"
  );
};

export const createEmptySkillGroupsForSite = async ({ siteId }) => {
  const groupedAssessments = await GroupedAssessments.find().fetchAsync();
  await Promise.all(
    groupedAssessments.map(async ga => {
      const studentBySite = {
        studentsBelowInstructionalTarget: [],
        studentsBelowMasteryTarget: [],
        studentsWithoutSkillHistory: [],
        skillName: ga.skillName,
        assessmentGroupId: ga._id,
        siteId
      };
      await StudentsBySkill.insertAsync(studentBySite);
    })
  );
};

export const removeStudentFromAllSkillGroupsForSite = async ({ siteId, studentId }) => {
  return StudentsBySkill.updateAsync(
    {
      siteId,
      $or: [
        { studentsBelowInstructionalTarget: studentId },
        { studentsBelowMasteryTarget: studentId },
        { studentsWithoutSkillHistory: studentId }
      ]
    },
    {
      $pull: {
        studentsWithoutSkillHistory: studentId,
        studentsBelowInstructionalTarget: studentId,
        studentsBelowMasteryTarget: studentId
      }
    },
    { multi: true }
  );
};

Meteor.methods({
  async assignStudentToSkillGroup({ siteId, studentId }) {
    check(siteId, String);
    check(studentId, String);
    if (!this.userId) {
      throw new Meteor.Error("assignStudentToSkillGroup", "No logged in user found!");
    }
    if (
      await auth.hasAccess(["teacher", "admin", "universalCoach"], {
        userId: this.userId,
        siteId
      })
    ) {
      return assignStudentToSkillGroup({ siteId, studentId });
    }
    throw new Meteor.Error("assignStudentToSkillGroup", "You are not authorized to assign student to an skill group");
  },
  async removeStudentFromAllSkillGroups({ siteId, studentId }) {
    check(studentId, String);
    if (!this.studentId) {
      throw new Meteor.Error("removeStudentFromAllSkillGroupsForSite", "No logged in user found");
    }
    if (
      await auth.hasAccess(["teacher", "admin", "universalCoach"], {
        userId: this.userId,
        siteId
      })
    ) {
      return removeStudentFromAllSkillGroupsForSite({ studentId });
    }
    throw new Meteor.Error("assignStudentToSkillGroup", "You are not authorized to assign student to an skill group");
  },
  async getSkillGroupAssignments({ siteId, schoolYear, orgid }) {
    check(siteId, String);
    check(orgid, String);
    check(schoolYear, Number);
    if (!this.userId) {
      throw new Meteor.Error("getSkillGroupAssignments", "No logged in user found!");
    }
    if (
      !(await auth.hasAccess(["teacher", "admin", "support", "universalCoach"], {
        userId: this.userId,
        orgid,
        siteId
      }))
    ) {
      throw new Meteor.Error("getSkillGroupAssignments", "You are not authorized to assign student to an skill group");
    }

    return getSkillGroupAssignments({ siteId, schoolYear, orgid, userId: this.userId });
  },
  async getGroupedAssessments({ assessmentIds, siteId }) {
    check(assessmentIds, Array);
    check(siteId, String);

    if (!this.userId) {
      throw new Meteor.Error("getGroupedAssessments", "No logged in user found!");
    }
    if (
      !(await auth.hasAccess(["teacher", "admin", "support", "universalCoach"], {
        userId: this.userId,
        siteId
      }))
    ) {
      throw new Meteor.Error("getGroupedAssessments", "You are not authorized to fetch grouped assessments");
    }

    const assessments = await Assessments.find(
      { _id: { $in: assessmentIds } },
      { fields: { monitorAssessmentMeasure: 1 } }
    ).mapAsync(({ _id, monitorAssessmentMeasure }) => ({ _id, monitorAssessmentMeasure }));
    const assessmentIdByMeasureNumber = getValuesByKey(assessments, "monitorAssessmentMeasure", "_id");
    const groupedAssessments =
      (await GroupedAssessments.find({
        assessmentMeasures: { $in: Object.keys(assessmentIdByMeasureNumber) }
      }).fetchAsync()) || [];
    const assessmentsForGroupedAssessmentMeasures = await Assessments.find(
      {
        monitorAssessmentMeasure: {
          $in: uniq(groupedAssessments.map(ga => ga.assessmentMeasures).flat(1))
        }
      },
      { fields: { monitorAssessmentMeasure: 1, monitorProtocols: 1 } }
    ).fetchAsync();
    const assessmentByMeasureNumber = keyBy(assessmentsForGroupedAssessmentMeasures, "monitorAssessmentMeasure");

    const parsedGroupedAssessments = groupedAssessments.map(({ assessmentMeasures, skillName }) => {
      const monitorInterventionProtocolsByMeasure = {};
      const subsetAssessmentIds = assessmentMeasures
        .map(measureNumber => {
          monitorInterventionProtocolsByMeasure[measureNumber] =
            assessmentByMeasureNumber[measureNumber].monitorProtocols;
          return assessmentIdByMeasureNumber[measureNumber];
        })
        .filter(f => f);

      return {
        assessmentMeasures,
        skillName,
        assessmentIds: subsetAssessmentIds,
        monitorInterventionProtocolsByMeasure
      };
    });

    const measureNumberByAssessmentId = getValuesByKey(assessments, "_id", "monitorAssessmentMeasure");

    return {
      groupedAssessments: parsedGroupedAssessments,
      measureNumberByAssessmentId
    };
  }
});
