import { get } from "lodash";
import { getCurrentDate } from "./getCurrentDate";
import { getMeteorUser, getMeteorUserId } from "../utilities/utilities";

export async function getTimestampInfo(customUserId, orgid, context) {
  const meteorUser = await getMeteorUser();
  const userId = customUserId || (await getMeteorUserId());
  const customDate = get(meteorUser, "profile.customDate");
  const currentDate = await getCurrentDate(customDate, orgid);
  return {
    by: userId,
    on: currentDate.getTime(),
    date: currentDate,
    ...(context ? { context } : {})
  };
}
