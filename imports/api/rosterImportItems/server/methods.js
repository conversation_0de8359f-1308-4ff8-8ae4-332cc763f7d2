import { Meteor } from "meteor/meteor";
import { RosterImportItems } from "../rosterImportItems";
import rosterImportItemsHelpers from "../methods";
import { getLatestAvailableSchoolYear, getMeteorUser, getMeteorUserId } from "../../utilities/utilities";
import { getTimestampInfo } from "../../helpers/getTimestampInfo";
import { normalizeRosterImportItems } from "../normalizeRosterImportItems";

async function convert(rosterImportItem, rosterImportId, orgid, userId) {
  const rosterItem = rosterImportItemsHelpers.createFromCSVDataRow(rosterImportItem);
  const timestampInfo = await getTimestampInfo(userId, orgid);
  rosterItem.created = timestampInfo;
  rosterItem.rosterImportId = rosterImportId;
  rosterItem.orgid = orgid;
  RosterImportItems.validate(rosterItem);
  return rosterItem;
}

export async function bulkInsert(rosterImportData, rosterImportId, orgid, customUserId) {
  const convertedItems = [];
  const bulkItems = RosterImportItems.rawCollection().initializeUnorderedBulkOp();
  bulkItems.executeAsync = Meteor.wrapAsync(bulkItems.execute);
  const user = await getMeteorUser();
  // TODO(fmazur) - maybe await?
  const schoolYear = await getLatestAvailableSchoolYear(user, orgid);
  const userId = getMeteorUserId(customUserId);

  // Process items in parallel for better performance
  const rosterItems = await Promise.all(rosterImportData.map(d => convert(d, rosterImportId, orgid, userId)));

  rosterItems.forEach(rosterItem => {
    rosterItem.data = normalizeRosterImportItems(rosterItem.data);
    rosterItem.data.schoolYear = schoolYear;

    convertedItems.push(rosterItem);
    bulkItems.insert(rosterItem);
  });

  // Only execute if there are items to insert
  if (convertedItems.length > 0) {
    await bulkItems.executeAsync();
  }

  return convertedItems;
}

export default bulkInsert;
