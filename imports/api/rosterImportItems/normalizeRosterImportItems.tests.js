import {
  normalizeRosterImportItems,
  getNormalizedGrade,
  getCourseSection,
  getNormalizedId,
  stripLeading<PERSON><PERSON>s
} from "./normalizeRosterImportItems";

describe("normalizeRosterImportItem", () => {
  it("should return an extended object when data does not need to be normalized", () => {
    const fuli = {
      springMathGrade: "K",
      classSectionID: "section",
      className: "className",
      schoolID: "1234",
      districtID: "0",
      teacherID: "98",
      studentLocalID: "123",
      studentStateID: "456",
      teacherEmail: "<EMAIL>"
    };

    const expectedResult = {
      springMathGrade: "K",
      classSectionID: "section",
      className: "className",
      schoolID: "1234",
      districtID: "0",
      teacherID: "98",
      courseName: "className (section)",
      studentLocalID: "123",
      studentStateID: "456",
      teacherEmail: "<EMAIL>"
    };

    const normalizedFuli = normalizeRosterImportItems(fuli);

    expect(normalizedFuli).toMatchObject(expectedResult);
  });

  it("should return an object with normalized values", () => {
    const fuli = {
      springMathGrade: "0",
      classSectionID: "class-section",
      className: "className",
      schoolID: "001111axd",
      districtID: "0-+;",
      teacherID: "098",
      studentLocalID: "123",
      studentStateID: "456",
      teacherEmail: "     <EMAIL>          "
    };

    const expectedResult = {
      springMathGrade: "K",
      classSectionID: "class-section",
      className: "className",
      schoolID: "001111axd",
      districtID: "0",
      teacherID: "098",
      courseName: "className (class-section)",
      studentLocalID: "123",
      studentStateID: "456",
      teacherEmail: "<EMAIL>"
    };

    const normalizedFuli = normalizeRosterImportItems(fuli);

    expect(normalizedFuli).toMatchObject(expectedResult);
  });
});

describe("getNormalizedGrade", () => {
  it("should return K if grade is K", () => {
    const grade = "K";

    const normalizedGrade = getNormalizedGrade(grade);

    expect(normalizedGrade).toBe("K");
  });

  it("should return K if grade is k", () => {
    const grade = "k";

    const normalizedGrade = getNormalizedGrade(grade);

    expect(normalizedGrade).toBe("K");
  });

  it("should return K if grade is 0", () => {
    const grade = "0";

    const normalizedGrade = getNormalizedGrade(grade);

    expect(normalizedGrade).toBe("K");
  });

  it("should return 02 if grade is 2", () => {
    const grade = "2";

    const normalizedGrade = getNormalizedGrade(grade);

    expect(normalizedGrade).toBe("02");
  });
});

describe("getCourseSection", () => {
  it("should return the correct course section", () => {
    const normalizedSection = getCourseSection({
      classSectionID: "sectionID"
    });

    expect(normalizedSection).toBe("sectionID");
  });
});

describe("getNormalizedId", () => {
  it("should strip leading zeros from provided argument", () => {
    const id1 = "0001234";
    const id2 = "014";

    const normalizedId = getNormalizedId(id1);
    const normalizedId2 = getNormalizedId(id2);

    expect(normalizedId).toBe("1234");
    expect(normalizedId2).toBe("14");
  });

  it("should accept 0 as value", () => {
    const id1 = "0";
    const id2 = "000";

    const normalizedId = getNormalizedId(id1);
    const normalizedId2 = getNormalizedId(id2);

    expect(normalizedId).toBe("0");
    expect(normalizedId2).toBe("0");
  });

  it("should remove unnecessary non-numbers from the end of the provided argument", () => {
    const id1 = "0001234school";
    const id2 = "0+-;<.";

    const normalizedId = getNormalizedId(id1);
    const normalizedId2 = getNormalizedId(id2);

    expect(normalizedId).toBe("1234");
    expect(normalizedId2).toBe("0");
  });
});

describe("stripLeadingZeros", () => {
  it("should remove leading zeros from the provided argument", () => {
    const id1 = "ABC-123";
    const id2 = "abc";
    const id3 = "000xyz";

    const normalizedId = stripLeadingZeros(id1);
    const normalizedId2 = stripLeadingZeros(id2);
    const normalizedId3 = stripLeadingZeros(id3);

    expect(normalizedId).toBe("ABC-123");
    expect(normalizedId2).toBe("abc");
    expect(normalizedId3).toBe("xyz");
  });
});
