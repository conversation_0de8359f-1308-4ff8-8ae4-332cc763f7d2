import { find, get, isEmpty, flatten, startCase, trim, keyBy, uniq } from "lodash";

import { Users } from "../users/users";
import { Sites } from "../sites/sites";
import { StudentGroups } from "../studentGroups/studentGroups";
import { Organizations } from "../organizations/organizations";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";
import { capitalizeFirstLetter, displayStudentData, getCurrentSchoolYear, getMeteorUser } from "../utilities/utilities";
import { ROLE_IDS } from "../../../tests/cypress/support/common/constants";

function containsSingleValue(array) {
  return array.length === 1;
}

export function getStrippedClassName(name) {
  const regExp = new RegExp(/ \(.*\)$/);
  return name.replace(regExp, "");
}

function template(format, object = {}) {
  return format.replace(/{([^}]+)}/g, (match, fieldName) => {
    let o = object;
    const params = fieldName.split("|")[0].split(".");
    for (let i = 0; i < params.length; i++) {
      o = typeof o[params[i]] === "function" ? o[params[i]]() : o[params[i]];
      if (typeof o === "undefined" || o === null)
        return fieldName.indexOf("|") !== -1 ? fieldName.split("|")[1] : match;
    }
    return o;
  });
}

function checkGroupPropertiesInUploadItems(existingGroupsWithSiteData, rosterImportItems) {
  const errors = {};
  existingGroupsWithSiteData.forEach(sg => {
    const studentGroupItem = rosterImportItems.find(
      item =>
        item.data.districtID === sg.siteDistrictId &&
        item.data.schoolID === sg.siteLocalNumber &&
        item.data.classSectionID === sg.sectionId
    );
    if (!studentGroupItem) {
      if (!errors.missingStudentGroups) {
        errors.missingStudentGroups = [];
      }
      errors.missingStudentGroups.push({
        classSectionID: sg.sectionId,
        schoolID: sg.siteLocalNumber,
        districtID: sg.siteDistrictId,
        springMathGrade: sg.grade,
        className: sg.name
      });
    }
  });
  return errors;
}

export async function validateStudentGroupsInUploadItems(rosterImportItems, orgid) {
  const existingSites = (await Sites.find({ orgid }).fetchAsync()).map(site => ({
    districtNumber: site.stateInformation.districtNumber,
    localId: site.stateInformation.schoolNumber,
    _id: site._id,
    name: site.name
  }));
  const user = await getMeteorUser();
  const schoolYear = await getCurrentSchoolYear(user, orgid);

  const existingGroupsWithSiteData = (
    await StudentGroups.find({
      orgid,
      schoolYear,
      isActive: true
    }).fetchAsync()
  ).map(sg => {
    const groupSite = existingSites.find(s => s._id === sg.siteId);
    return {
      ...sg,
      grade: sg.grade,
      siteLocalNumber: groupSite.localId,
      siteDistrictId: groupSite.districtNumber
    };
  });

  const groupsWithActiveEnrollment = existingGroupsWithSiteData.filter(
    async sg =>
      !!(await StudentGroupEnrollments.findOneAsync(
        { studentGroupId: sg._id, isActive: true, schoolYear },
        { fields: { _id: 1 } }
      ))
  );

  const errors = checkGroupPropertiesInUploadItems(groupsWithActiveEnrollment, rosterImportItems);
  return {
    success: isEmpty(errors),
    errors
  };
}

export async function validateDistrictName(rosterImportItems, orgid) {
  const errors = [];
  const failedRows = [];
  const organizationName = (await Organizations.findOneAsync({ _id: orgid })).name;
  rosterImportItems.forEach((item, index) => {
    const { districtName } = item.data;

    if (trim(districtName) !== organizationName) {
      errors.push(
        `District name: Provided district name (${districtName}) differs from what is in the database for this district (${organizationName}). This district can't be uploaded.`
      );
      failedRows.push(index);
    }
  });
  return {
    success: isEmpty(errors),
    errors,
    failedRows
  };
}

export async function validateUniqueTeacherEmailsInUploadItems(rosterImportItems, orgid) {
  const errors = [];
  const failedRows = [];
  const teacherEmails = rosterImportItems.map(item => item.data.teacherEmail);
  const indexesByTeacherEmail = teacherEmails.reduce((acc, value, index) => {
    (acc[value] = acc[value] || []).push(index);
    return acc;
  }, {});
  // eslint-disable-next-line no-restricted-syntax
  for await (const [email, indexes] of Object.entries(indexesByTeacherEmail)) {
    const currentTeacher = await Users.findOneAsync({
      "emails.address": email
    });
    if (!currentTeacher) {
      // eslint-disable-next-line no-continue
      continue;
    }
    if (currentTeacher.profile.orgid !== orgid) {
      indexes.forEach(index => {
        failedRows.push(index);
      });
      const { name, localId } = currentTeacher.profile;
      const localIdText = localId ? ` (TeacherID: ${localId})` : "";
      errors.push(
        `Teacher e-mail: ${email} is already used by a different teacher: ${name.first} ${name.last}${localIdText} in a different organization. This teacher won't be uploaded.`
      );
    }
    const currentUserRoles = uniq(currentTeacher.profile.siteAccess.map(sa => sa.role));
    if (currentUserRoles.length === 1 && currentUserRoles[0] === ROLE_IDS.dataAdmin) {
      errors.push(
        `The same email ${email} cannot be used to create both Teacher and Data Amin roles. Only Coach and Data Admin roles assigned to the same email address is allowed.`
      );
    }
  }
  return {
    success: isEmpty(errors),
    errors,
    failedRows
  };
}

function collectDataWithIndexes({ objectArray, value, index }) {
  const foundObject = find(objectArray, value);
  if (!foundObject) {
    objectArray.push({ ...value, indexes: [index] });
  } else {
    foundObject.indexes.push(index);
  }
}

export default class RosterImportItemsValidator {
  constructor(items, isCSV = true) {
    this.items = items;
    this.isCSV = isCSV;

    this.result = {
      success: false,
      errors: {},
      failedRows: new Set()
    };
  }

  elementaryGrades = [
    "K",
    "1",
    "2",
    "3",
    "4",
    "5",
    "6",
    "7",
    "8",
    "00",
    "01",
    "02",
    "03",
    "04",
    "05",
    "06",
    "07",
    "08"
  ];

  highSchoolGrades = ["09", "9", "10", "11", "12", "HS"];

  async validate() {
    const districtsByID = {};
    const districtsByName = {};
    const schoolsByID = {};
    const studentLocalIDs = {};
    const studentStateIDs = {};
    const teachersByID = {};
    const studentGroupsBySchoolAndClassSection = {};
    const teachersLocalIDByEmail = {};
    const elementarySchools = [];
    const highSchools = [];
    const classSectionIDsByStudentLocalID = {};
    const uniqueClassSectionIDTeacherIDStudentLocalID = {};
    const uniqueClassSectionIDTeacherIDStudentStateID = {};

    this.items.forEach((item, index) => {
      const { schoolID, schoolName, classSectionID, teacherID, studentLocalID, studentStateID } = item.data;

      if (this.elementaryGrades.includes(item.data.springMathGrade)) {
        if (!elementarySchools[schoolName]) {
          elementarySchools[schoolName] = [];
        }
        elementarySchools[schoolName].push(index);
      }

      if (this.highSchoolGrades.includes(item.data.springMathGrade.toUpperCase())) {
        if (!highSchools[schoolName]) {
          highSchools[schoolName] = [];
        }
        /* eslint-disable-next-line no-param-reassign */
        item.data.springMathGrade = "HS";
        highSchools[schoolName].push(index);
      }

      if (!districtsByID[item.data.districtID]) {
        districtsByID[item.data.districtID] = [];
      }
      districtsByID[item.data.districtID].push(index);

      if (!districtsByName[item.data.districtName]) {
        districtsByName[item.data.districtName] = [];
      }
      districtsByName[item.data.districtName].push(index);

      if (!schoolsByID[schoolID]) {
        schoolsByID[schoolID] = [];
      }

      collectDataWithIndexes({
        objectArray: schoolsByID[schoolID],
        value: { schoolName },
        index
      });

      if (!studentLocalIDs[item.data.studentLocalID]) {
        studentLocalIDs[item.data.studentLocalID] = [];
      }
      studentLocalIDs[item.data.studentLocalID].push(index);

      if (!studentStateIDs[item.data.studentStateID]) {
        studentStateIDs[item.data.studentStateID] = [];
      }
      studentStateIDs[item.data.studentStateID].push(index);

      if (!teachersByID[item.data.teacherID]) {
        teachersByID[item.data.teacherID] = [];
      }

      const teacherCredentials = {
        teacherFirstName: item.data.teacherFirstName,
        teacherLastName: item.data.teacherLastName,
        teacherEmail: item.data.teacherEmail
      };

      collectDataWithIndexes({
        objectArray: teachersByID[item.data.teacherID],
        value: teacherCredentials,
        index
      });

      if (!teachersLocalIDByEmail[item.data.teacherEmail]) {
        teachersLocalIDByEmail[item.data.teacherEmail] = [];
      }

      collectDataWithIndexes({
        objectArray: teachersLocalIDByEmail[item.data.teacherEmail],
        value: {
          teacherID: item.data.teacherID
        },
        index
      });

      if (!studentGroupsBySchoolAndClassSection[schoolName]) {
        studentGroupsBySchoolAndClassSection[schoolName] = {};
      }
      if (!studentGroupsBySchoolAndClassSection[schoolName][classSectionID]) {
        studentGroupsBySchoolAndClassSection[schoolName][classSectionID] = {
          className: [],
          springMathGrade: []
        };
      }

      collectDataWithIndexes({
        objectArray: studentGroupsBySchoolAndClassSection[schoolName][classSectionID].className,
        value: {
          className: item.data.className
        },
        index
      });

      studentGroupsBySchoolAndClassSection[schoolName][classSectionID].springMathGrade.push({
        springMathGrade: item.data.springMathGrade,
        index
      });

      if (!classSectionIDsByStudentLocalID[studentLocalID]) {
        classSectionIDsByStudentLocalID[studentLocalID] = [];
      }

      collectDataWithIndexes({
        objectArray: classSectionIDsByStudentLocalID[studentLocalID],
        value: {
          classSectionID
        },
        index
      });

      const uniqueStudentRowKeyWithLocalID = `${classSectionID}|${teacherID}|${studentLocalID}`;

      if (!uniqueClassSectionIDTeacherIDStudentLocalID[uniqueStudentRowKeyWithLocalID]) {
        uniqueClassSectionIDTeacherIDStudentLocalID[uniqueStudentRowKeyWithLocalID] = [];
      }

      uniqueClassSectionIDTeacherIDStudentLocalID[uniqueStudentRowKeyWithLocalID].push(index);

      const uniqueStudentRowKeyWithStateID = `${classSectionID}|${teacherID}|${studentStateID}`;

      if (!uniqueClassSectionIDTeacherIDStudentStateID[uniqueStudentRowKeyWithStateID]) {
        uniqueClassSectionIDTeacherIDStudentStateID[uniqueStudentRowKeyWithStateID] = [];
      }

      uniqueClassSectionIDTeacherIDStudentStateID[uniqueStudentRowKeyWithStateID].push(index);
    });

    if (!this.items.length) {
      this.result.success = true;
      return this.result;
    }

    this.validateSingleValueFor(districtsByID, "districtID");
    this.validateSingleValueFor(districtsByName, "districtName");
    this.validateUniqueSetOfFields(
      uniqueClassSectionIDTeacherIDStudentLocalID,
      ["classSectionID", "teacherID", "studentLocalID"],
      "studentLocalID"
    );
    this.validateUniqueSetOfFields(
      uniqueClassSectionIDTeacherIDStudentStateID,
      ["classSectionID", "teacherID", "studentStateID"],
      "studentStateID"
    );
    this.validateKeyValueUniqueness(schoolsByID, "schoolID", ["schoolName"]);
    this.validateUniqueSchoolNames(schoolsByID);
    this.validateKeyValueUniqueness(teachersByID, "teacherID", ["teacherFirstName", "teacherLastName", "teacherEmail"]);
    this.validateKeyValueUniqueness(teachersLocalIDByEmail, "TeacherEmail", ["teacherID"]);
    this.validateUniqueValueAcrossSchool(studentGroupsBySchoolAndClassSection);
    await this.validateTeacherEmailsUniqueness(teachersLocalIDByEmail);
    this.validateKeyValueUniqueness(classSectionIDsByStudentLocalID, "studentLocalID", ["classSectionID"], false, {
      classSectionID: "Class: {teacherFirstName} {teacherLastName} - {className}"
    });

    this.result.success = this.isUploadValid();
    return this.result;
  }

  isUploadValid() {
    return isEmpty(this.result.errors);
  }

  async validateTeacherEmailsUniqueness(teachersLocalIDByEmail) {
    const teacherEmails = Object.keys(teachersLocalIDByEmail);
    const users = await Users.find({ "emails.address": { $in: teacherEmails } }).fetchAsync();
    const userByEmail = keyBy(users, "emails.0.address");
    // eslint-disable-next-line no-restricted-syntax
    for await (const teacherEmail of teacherEmails) {
      const currentTeacher = userByEmail[teacherEmail];
      if (!currentTeacher) {
        break;
      }
      const siteAccessArray = get(currentTeacher, "profile.siteAccess", []);
      if (!siteAccessArray.length) {
        break;
      }
      const isCoach = siteAccessArray[0].role === "arbitraryIdadmin";
      const teacherLocalId = currentTeacher.profile.localId;
      const [teacherData] = teachersLocalIDByEmail[teacherEmail];
      const hasDifferentLocalId = currentTeacher && (!teacherLocalId || teacherLocalId !== teacherData.teacherID);
      if (hasDifferentLocalId && !isCoach) {
        const currentTeacherWithLocalId = await Users.findOneAsync({
          "profile.orgid": currentTeacher.profile.orgid,
          "profile.localId": teacherData.teacherID
        });
        if (!currentTeacherWithLocalId) {
          break;
        }
        const { name, localId } = currentTeacherWithLocalId.profile;
        const localIdText = localId
          ? ` (TeacherID: ${localId}, Email: ${currentTeacherWithLocalId.emails[0]?.address})`
          : "";
        teacherData.indexes.forEach(index => {
          this.result.failedRows.add(index);
        });
        if (!this.result.errors.teacherID) {
          this.result.errors.teacherID = [];
        }
        this.result.errors.teacherID.push(
          `Teacher e-mail: ${teacherEmail} or teacher id: ${teacherData.teacherID} is already used by a different teacher: ${name.first} ${name.last}${localIdText}.\nYou can manage teachers' data under Manage Accounts tab.`
        );
      }
      break;
    }
  }

  validateKeyValueUniqueness(object, keyName, uniqueFieldNames = [], forceSingleLine = false, prettyNameConfig = {}) {
    const collectionKeys = Object.keys(object);
    const valueSeparator = forceSingleLine ? ", " : "\n";
    const errorSeparator = forceSingleLine ? " " : "\n";

    collectionKeys.forEach(key => {
      if (object[key].length > 1) {
        if (!this.isCSV) {
          for (let i = 1; i < object[key].length; i++) {
            object[key][i].indexes.forEach(index => {
              this.result.failedRows.add(index);
            });
          }
        }

        if (!this.result.errors[keyName]) {
          this.result.errors[keyName] = [];
        }
        const dataStrings = object[key].map(data => {
          return uniqueFieldNames
            .map(uniqueFieldName => {
              if (prettyNameConfig[uniqueFieldName]) {
                const format = prettyNameConfig[uniqueFieldName];
                const item = this.items[data.indexes[0]].data;
                const prettyName = template(format, item);
                const fieldValue = `(${data[uniqueFieldName]})`;
                if (prettyName.endsWith(fieldValue)) {
                  return prettyName;
                }
                return `${prettyName} ${fieldValue}`;
              }
              return `${startCase(uniqueFieldName)}: ${data[uniqueFieldName]}`;
            })
            .join(",\t");
        });
        this.result.errors[keyName].push(
          `More than one ${
            uniqueFieldNames.length ? uniqueFieldNames.join(", ") : "set of data"
          } is present for ${keyName}: ${key}, found:${errorSeparator}${dataStrings.join(valueSeparator)}`
        );
      }
    });
  }

  validateUniqueSetOfFields(collection, uniqueFieldsSet, fieldName) {
    const keys = Object.keys(collection);
    keys.forEach(key => {
      if (collection[key].length > 1) {
        if (!this.result.errors[fieldName]) {
          this.result.errors[fieldName] = [];
        }

        const [firstIndex, ...remainingIndexes] = collection[key];

        if (!this.isCSV) {
          this.result.failedRows.add(remainingIndexes);
        }
        const rowDataString = uniqueFieldsSet
          .map(field => `${startCase(field)}: ${this.items[firstIndex].data[field]}`)
          .join(",\t");

        const studentErrorMessages = collection[key].map(index => `\n${displayStudentData(this.items[index].data)}`);
        const fieldError = `The same ${uniqueFieldsSet
          .map(field => startCase(field))
          .join(", ")} was used in more than one row:\n${rowDataString}${studentErrorMessages}`;
        this.result.errors[fieldName].push(fieldError);
      }
    });
  }

  validateSingleValueFor(collection, field) {
    const keys = Object.keys(collection);
    if (containsSingleValue(keys)) {
      return;
    }
    if (!this.isCSV) {
      const firstValidValue = this.items[0].data[field];
      Object.entries(collection).forEach(([key, indexes]) => {
        if (firstValidValue !== key) {
          indexes.forEach(index => {
            this.result.failedRows.add(index);
          });
        }
      });
    }
    this.result.errors[field] = [
      `Expected a single ${field} in all records, instead found: ${keys
        .map(value => (value === "" ? "[empty]" : value))
        .join(", ")}`
    ];
  }

  validateUniqueValueAcrossSchoolForClassSectionId({ data, schoolName, classSectionID, paramName, fieldName }) {
    const paramValues = data[schoolName][classSectionID][paramName];
    if (paramValues.length > 1) {
      for (let i = 1; i < paramValues.length; i++) {
        paramValues[i].indexes.forEach(index => {
          this.result.failedRows.add(index);
        });
      }
      if (!this.result.errors[fieldName]) {
        this.result.errors[fieldName] = [];
      }
      this.result.errors[fieldName].push(
        `The same ${fieldName}: ${classSectionID} is present for more than one ${paramName} across a school: ${schoolName}`
      );
    }
  }

  validateUniqueValueAcrossSchoolForSpringMathGrade({ data, schoolName, classSectionID, paramName, fieldName }) {
    const groupData = data[schoolName][classSectionID];
    const paramValues = groupData[paramName];
    const missingGradesLength = paramValues.filter(g => g.springMathGrade === "").length;
    if (paramValues.length === missingGradesLength) {
      const { className } = groupData.className[0];
      paramValues.forEach(({ index }) => this.result.failedRows.add(index));
      if (!this.result.errors[fieldName]) {
        this.result.errors[fieldName] = [];
      }
      this.result.errors[fieldName].push(
        `All students for group:\n\tClass: ${className} (${classSectionID})\n\tSchool: ${schoolName}\nare missing ${capitalizeFirstLetter(
          paramName
        )}.`
      );
    } else if (paramValues.length > 0) {
      paramValues.forEach(({ springMathGrade, index }) => {
        // NOTE(fmazur) - Find missing grades
        if (springMathGrade === "") {
          this.result.failedRows.add(index);
          this.pushErrorMessageByType({ type: "missingGrade", fieldName, index });
          return;
        }
        const isGradeValid = /^(K|[0-9]|0[1-9]|1[0-2]|HS)$/i.test(springMathGrade);
        // NOTE(fmazur) - Find invalid grades
        if (!isGradeValid) {
          this.result.failedRows.add(index);
          this.pushErrorMessageByType({ type: "invalidGrade", fieldName, index });
        }
      });
    }
  }

  pushErrorMessageByType({ type, fieldName, index }) {
    const baseMessage = displayStudentData(this.items[index].data, "Student with:");
    const messageByType = {
      missingGrade: "is missing SpringMathGrade.",
      invalidGrade: "has invalid SpringMathGrade - should be K, HS or 0 - 12."
    };
    if (!this.result.errors[fieldName]) {
      this.result.errors[fieldName] = [];
    }
    this.result.errors[fieldName].push(`${baseMessage}\n${messageByType[type]}`);
  }

  validateUniqueValueAcrossSchool(studentGroupsBySchoolAndClassSectionObject) {
    const schoolNames = Object.keys(studentGroupsBySchoolAndClassSectionObject);
    /* eslint-disable-next-line no-restricted-syntax */
    for (const schoolName of schoolNames) {
      const classSectionIDs = Object.keys(studentGroupsBySchoolAndClassSectionObject[schoolName]);
      /* eslint-disable-next-line no-restricted-syntax */
      for (const classSectionID of classSectionIDs) {
        this.validateUniqueValueAcrossSchoolForClassSectionId({
          data: studentGroupsBySchoolAndClassSectionObject,
          schoolName,
          classSectionID,
          paramName: "className",
          fieldName: "classSectionID"
        });
        this.validateUniqueValueAcrossSchoolForSpringMathGrade({
          data: studentGroupsBySchoolAndClassSectionObject,
          schoolName,
          classSectionID,
          paramName: "springMathGrade",
          fieldName: "springMathGrade"
        });
      }
    }
  }

  validateUniqueSchoolNames(schoolsById) {
    const schoolNameObjects = flatten(Object.values(schoolsById));
    const schoolNames = schoolNameObjects.map(({ schoolName }) => schoolName);
    const duplicatedNameObjects = schoolNameObjects.filter(({ schoolName }, index) =>
      schoolNames.includes(schoolName, index + 1)
    );
    if (duplicatedNameObjects.length) {
      const schoolNameError = {};
      duplicatedNameObjects.forEach(({ schoolName, indexes }) => {
        schoolNameError[schoolName] = [];
        Object.entries(schoolsById).forEach(([key, values]) => {
          if (find(values, { schoolName })) {
            indexes.forEach(index => {
              this.result.failedRows.add(index);
            });
            schoolNameError[schoolName].push(key);
          }
        });
      });
      this.result.errors.schoolName = Object.entries(schoolNameError).map(([schoolName, schoolIDs]) => {
        return `The same schoolName: ${schoolName} is used for schoolIDs: ${schoolIDs.join(", ")}`;
      });
    }
  }
}
