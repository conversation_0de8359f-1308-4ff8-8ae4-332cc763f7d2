/* eslint-disable no-undef */
/* eslint-disable no-underscore-dangle */
import _ from "lodash";

const whitelistedFields = [
  "given_name",
  "family_name",
  "name",
  "sub",
  "email",
  "issuerUserId",
  "issuerOrgId",
  "issuerEmailVerified",
  "issuerLogoutRedirect"
];

export function retrieveCredential(credentialToken, credentialSecret) {
  return OAuth.retrieveCredential(credentialToken, credentialSecret);
}

function adCall(method, url, options) {
  let response;

  try {
    response = HTTP.call(method, url, options);
  } catch (err) {
    const details = JSON.stringify({
      url,
      options,
      method
    });
    throw new Meteor.Error("azure-active-directory:failed HTTP request", err.message, details);
  }

  if (response.data.error) {
    const reason = response.data.error;
    const details = JSON.stringify({
      statusCode: response.statusCode,
      url,
      options,
      method
    });
    throw new Meteor.Error("azure-active-directory:invalid HTTP response", `Url=${reason}`, details);
  } else {
    return response.data;
  }
}

async function getAccessTokensBase(additionalRequestParams, tenantId, rootUrlFromRequest) {
  const config = await ServiceConfiguration.configurations.findOneAsync({
    service: "azureAdB2c",
    tenantId
  });

  const url = config.tokenUrl;
  const baseParams = {
    client_id: config.clientId,
    client_secret: OAuth.openSecret(config.secret),
    redirect_uri: OAuth._redirectUri("azureAdB2C", config, null, null, rootUrlFromRequest)
  };
  const requestBody = { ...baseParams, ...additionalRequestParams };

  const response = adCall("POST", url, { params: requestBody });

  return {
    accessToken: response.access_token,
    idToken: response.id_token,
    refreshToken: response.refresh_token,
    expiresIn: response.expires_in,
    expiresOn: response.expires_on,
    scope: response.scope,
    resource: response.resource
  };
}

async function getTokensFromCode({ code, tenantId, rootUrlFromRequest }) {
  return getAccessTokensBase(
    {
      grant_type: "authorization_code",
      code
    },
    tenantId,
    rootUrlFromRequest
  );
}

// eslint-disable-next-line no-undef
OAuth.registerService("azureAdB2c", 2, null, async requestData => {
  const { tenantId } = requestData;

  const tokens = await getTokensFromCode(requestData);

  const { idToken } = tokens;
  const idTokenSplit = idToken.split(".");

  const idPart2 = idTokenSplit[1];

  const identity = JSON.parse(Buffer.from(idPart2, "base64").toString("binary"));

  const serviceData = {
    accessToken: tokens.accessToken,
    idToken,
    expiresAt: new Date() + 1000 * identity.exp,
    identity
  };

  let emailAddress = identity && identity.email;
  if (!emailAddress) {
    emailAddress = identity && identity.emails && identity.emails[0];
  }

  const fields = _.pick(identity, whitelistedFields);

  _.extend(serviceData, fields);

  // only set the token in serviceData if it's there. this ensures
  // that we don't lose old ones (since we only get this on the first
  // log in attempt)
  if (tokens.refreshToken) serviceData.refreshToken = tokens.refreshToken;

  const options = {
    tenantId,
    profile: {
      name: identity.name
    }
  };

  if (emailAddress) {
    options.emails = [
      {
        address: emailAddress,
        verified: true
      }
    ];
  }

  return { serviceData, options };
});
