import { Mongo } from "meteor/mongo";
import SimpleSchema from "simpl-schema";

// eslint-disable-next-line import/prefer-default-export
export const InstructionalVideos = new Mongo.Collection("InstructionalVideos");

InstructionalVideos.schema = new SimpleSchema({
  _id: { type: String, optional: true },
  type: { type: String },
  youTubeUrl: { type: String }
});

InstructionalVideos.validate = instructionalVideo => {
  InstructionalVideos.schema.validate(instructionalVideo);
};

InstructionalVideos.isValid = instructionalVideo =>
  InstructionalVideos.schema.namedContext("checkInstructionalVideo").validate(instructionalVideo);
