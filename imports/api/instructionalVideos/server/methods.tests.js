import { assert } from "chai";
import { InstructionalVideos } from "../instructionalVideos";
import getIdFromVideoUrl from "./methods";

describe("InstructionalVideos", () => {
  const individualType = "individual";
  const otherType = "otherType";
  const targetVideoId = "Wch3gJG2GJ4";

  const testIndividualInterventionInstructionalVideo = {
    _id: "testInstructionalVideo",
    type: individualType,
    youTubeUrl: "https://www.youtube.com/watch?v=Wch3gJG2GJ4&t=130s"
  };

  describe("Should pass schema validation method", () => {
    it("validate", () => {
      assert.isUndefined(InstructionalVideos.validate(testIndividualInterventionInstructionalVideo));
    });
    it("isValid", () => {
      assert.isTrue(InstructionalVideos.isValid(testIndividualInterventionInstructionalVideo));
    });
  });
  describe("getIdfromVideoUrl", () => {
    beforeEach(async () => {
      await InstructionalVideos.insertAsync(testIndividualInterventionInstructionalVideo);
    });
    afterEach(async () => {
      await InstructionalVideos.removeAsync({});
    });

    it("should return empty string if no video url was found", async () => {
      const filteredVideoUrl = await getIdFromVideoUrl(otherType);
      expect(filteredVideoUrl).toEqual("");
    });
    it("should return YouTube video id based on provided type and timestamp if exists", async () => {
      const { videoId, videoTimestamp } = await getIdFromVideoUrl(individualType);
      expect(videoId).toEqual(targetVideoId);
      expect(videoTimestamp).toEqual(130);
    });
  });
});
