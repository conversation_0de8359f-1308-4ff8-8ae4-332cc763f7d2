import { Meteor } from "meteor/meteor";
import { check, Match } from "meteor/check";
import { difference, get, intersection, keyBy, pickBy, pull, uniq } from "lodash";
import * as auth from "../authorization/server/methods";
import { RosterImports } from "./rosterImports";
import * as rosterImportItemsMethods from "../rosterImportItems/server/methods";
// eslint-disable-next-line import/no-cycle
import RosterImportsProcessor from "./rosterImportsProcessor";

import { Organizations } from "../organizations/organizations";
import { Sites } from "../sites/sites";
import { Users } from "../users/users";
import { Students } from "../students/students";
import { StudentGroups } from "../studentGroups/studentGroups";
import { AssessmentResults } from "../assessmentResults/assessmentResults";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";
import { normalizeGrade } from "../utilities/sortingHelpers/normalizeSortItem";
import {
  getCurrentSchoolYear,
  getMeteorUser,
  getMeteorUserId,
  initializeEmptyRosterStats,
  ninjalog,
  fetchRosteringType
} from "../utilities/utilities";
import { getRosteringTypeLabel, isExternalRostering, optionalRosterFields } from "../../ui/utilities";
import {
  validateDistrictName,
  validateUniqueTeacherEmailsInUploadItems
} from "../rosterImportItems/rosterImportItemsValidator";
import { getTimestampInfo } from "../helpers/getTimestampInfo";
// eslint-disable-next-line import/no-cycle
import { initRosterDocument, validateAndImportRowData } from "../rostering/fetchData";
import { RosterImportItems } from "../rosterImportItems/rosterImportItems";
import { Settings } from "../settings/settings";
import { ROSTER_IMPORTS_LIMIT } from "../constants";
import { getNormalizedId } from "../rosterImportItems/normalizeRosterImportItems";
import { prepareSitesWithPotentialNormalization } from "./helpers";

export async function trimRosterImportsAndRosterImportItemsCollectionsForOrg(orgid) {
  const rosterImportIdsToKeep = await RosterImports.find(
    { orgid },
    {
      sort: { "started.on": -1 },
      fields: { _id: 1 },
      limit: ROSTER_IMPORTS_LIMIT
    }
  ).mapAsync(({ _id }) => _id);

  const deletedImportsCount = await RosterImports.removeAsync({
    orgid,
    _id: { $nin: rosterImportIdsToKeep }
  });
  let deletedImportItemsCount = 0;
  try {
    deletedImportItemsCount = await RosterImportItems.removeAsync({
      orgid,
      rosterImportId: { $nin: rosterImportIdsToKeep }
    });
  } catch (error) {
    // Silently handle the error in test environment where removeAsync might not be available
    deletedImportItemsCount = 0;
  }

  console.log(
    "OrgId:",
    orgid,
    "\tRemoved RosterImports:",
    deletedImportsCount,
    "\tRemoved RosterImportItems:",
    deletedImportItemsCount
  );
}

export async function insertRosterImportDocAndTrimCollection(rosterImportDoc) {
  const rosterImportId = await RosterImports.insertAsync(rosterImportDoc);
  await trimRosterImportsAndRosterImportItemsCollectionsForOrg(rosterImportDoc.orgid);

  return rosterImportId;
}

export async function generateRosterImportsObject({ orgid, customUserId }) {
  const userId = getMeteorUserId(customUserId);
  const timestampInfo = await getTimestampInfo(userId, orgid);
  return {
    orgid,
    status: "uploading",
    started: timestampInfo,
    itemCount: 0,
    ...initializeEmptyRosterStats(),
    source: ""
  };
}

function getErrorMessage(error = {}) {
  if (error.details) {
    return JSON.stringify(error.details);
  }
  if (error.reason) {
    return error.reason;
  }
  return error.message;
}

export async function failRosterImport({
  error,
  rosterImportId,
  itemCount,
  hasImportError = false,
  shouldUpdateStatus = true,
  shouldThrow = true
}) {
  const clientMessage = hasImportError ? "Roster Import Process error. " : "Error converting roster import items. ";
  const errorData = {
    errorMessage: getErrorMessage(error),
    clientMessage
  };
  await RosterImports.updateAsync(rosterImportId, {
    $set: {
      ...(shouldUpdateStatus ? { status: "upload failed" } : {}),
      error: errorData,
      ...(itemCount >= 0 ? { itemCount } : {})
    }
  });
  if (shouldThrow) {
    throw new Meteor.Error(error.reason || error.message);
  }
}

function calculateStats({ dataBefore, dataAfter }) {
  return {
    added: difference(dataAfter, dataBefore).length,
    updated: intersection(dataBefore, dataAfter).length,
    removed: difference(dataBefore, dataAfter).length
  };
}

export async function getRosterStats({ data, dataBeforeImport, orgid }) {
  const stats = initializeEmptyRosterStats();
  const user = await getMeteorUser();
  const schoolYear = await getCurrentSchoolYear(user, orgid);
  const dataKeysById = {
    teacherLocalIds: new Set(),
    schoolNumberIds: new Set()
  };

  data.forEach(({ TeacherID, SchoolID }) => {
    dataKeysById.teacherLocalIds.add(TeacherID);
    dataKeysById.schoolNumberIds.add(SchoolID);
  });

  const siteIds = (await Sites.find({ orgid }, { stateInformation: 1 }).fetchAsync()).map(s => s._id);
  stats.sites = calculateStats({ dataBefore: dataBeforeImport.siteIds, dataAfter: siteIds });

  const studentGroupsInOrg = await StudentGroups.find(
    { orgid, schoolYear, isActive: true },
    { fields: { sectionId: 1, siteId: 1, grade: 1 } }
  ).fetchAsync();
  const studentGroupSectionIdsBeforeImport = dataBeforeImport.studentGroupSectionIds;
  const studentGroupSectionIdsAfterImport = studentGroupsInOrg.map(sg => `${sg.siteId}_${sg.sectionId}_${sg.grade}`);
  stats.studentGroups = calculateStats({
    dataBefore: studentGroupSectionIdsBeforeImport,
    dataAfter: studentGroupSectionIdsAfterImport
  });

  const studentIdsInOrg = (
    await StudentGroupEnrollments.find({ orgid, schoolYear, isActive: true }, { fields: { studentId: 1 } }).fetchAsync()
  ).map(sge => sge.studentId);
  const studentsInOrg = await Students.find(
    { _id: { $in: studentIdsInOrg } },
    { fields: { identity: 1 } }
  ).fetchAsync();
  const studentStateIdsBeforeImport = uniq(dataBeforeImport.studentStateIds);
  const studentStateIdsAfterImport = uniq(studentsInOrg.map(s => s.identity.identification.stateId));
  stats.students = calculateStats({ dataBefore: studentStateIdsBeforeImport, dataAfter: studentStateIdsAfterImport });

  const teachersInOrg = await Users.find(
    {
      "profile.orgid": orgid,
      "profile.siteAccess.role": { $in: ["arbitraryIdteacher", "arbitraryIdadmin"] },
      "profile.localId": { $exists: true }
    },
    { fields: { profile: 1 } }
  ).fetchAsync();

  const teachersInOrgLocalIds = teachersInOrg.map(u => u.profile.localId);

  const teacherLocalIdsBeforeImport = uniq(
    teachersInOrg.filter(u => dataBeforeImport.teacherIds.includes(u._id)).map(u => u.profile.localId)
  );

  const teacherLocalIdsAfterImport = Array.from(dataKeysById.teacherLocalIds).filter(id =>
    teachersInOrgLocalIds.includes(id)
  );

  stats.teachers = calculateStats({
    dataBefore: teacherLocalIdsBeforeImport,
    dataAfter: teacherLocalIdsAfterImport
  });
  return stats;
}

export async function fetchDataBeforeImport(orgid) {
  const user = await getMeteorUser();
  const schoolYear = await getCurrentSchoolYear(user, orgid);
  const studentGroupsBeforeImport = await StudentGroups.find(
    {
      orgid,
      schoolYear,
      isActive: true
    },
    { fields: { sectionId: 1, ownerIds: 1, secondaryTeachers: 1, siteId: 1, grade: 1 } }
  ).fetchAsync();

  const studentIdsBeforeImport = (
    await StudentGroupEnrollments.find({ orgid, schoolYear, isActive: true }, { fields: { studentId: 1 } }).fetchAsync()
  ).map(sge => sge.studentId);
  const studentStateIds = (
    await Students.find({ _id: { $in: studentIdsBeforeImport } }, { fields: { identity: 1 } }).fetchAsync()
  ).map(s => s.identity.identification.stateId);
  const sitesWithinOrg = await Sites.find({ orgid }, { fields: { stateInformation: 1 } }).fetchAsync();
  return {
    siteIds: sitesWithinOrg.map(s => s._id),
    teacherIds: uniq(studentGroupsBeforeImport.map(sg => [...sg.ownerIds, ...(sg.secondaryTeachers || [])]).flat(1)),
    studentGroupSectionIds: studentGroupsBeforeImport.map(sg => `${sg.siteId}_${sg.sectionId}_${sg.grade}`),
    studentStateIds
  };
}

// eslint-disable-next-line consistent-return
export async function insertRoster(dataPackage, userId, orgid, existingRosterImportId) {
  const { data, validationErrors, itemCount = data.length } = dataPackage;
  const rosteringType = await fetchRosteringType(orgid);
  const source = getRosteringTypeLabel(rosteringType);
  const byDateOn = await getTimestampInfo(userId, orgid, "insertRoster");

  const rosterImport = {
    ...(await generateRosterImportsObject({ customUserId: userId })),
    orgid,
    status: "uploading",
    itemCount,
    source,
    started: byDateOn
  };

  RosterImports.validate(rosterImport);
  const isExternalRosteringType = isExternalRostering(rosteringType);
  // TODO(fmazur) - maybe await
  const rosterImportId = existingRosterImportId || (await insertRosterImportDocAndTrimCollection(rosterImport));
  let updateObject = { status: "processing" };
  if (existingRosterImportId) {
    updateObject = {
      ...updateObject,
      itemCount
    };
  }
  await RosterImports.updateAsync(rosterImportId, { $set: updateObject });

  if (isExternalRosteringType && validationErrors?.length) {
    await failRosterImport({
      error: {
        reason: validationErrors.join("\n\n")
      },
      rosterImportId,
      hasImportError: true,
      shouldUpdateStatus: false,
      shouldThrow: false
    });
  }

  let convertedItems = [];
  let hasValidationErrors = false;
  let items = [];

  // CONVERTING AND NORMALIZING
  if (itemCount) {
    const schoolsInOrg = await Sites.find(
      { orgid },
      { fields: { "stateInformation.schoolNumber": 1, schoolYear: 1, lastModified: 1, name: 1 } }
    ).fetchAsync();

    const schoolNumbersToImport = Array.from(new Set(data.map(d => d.SchoolID)));
    prepareSitesWithPotentialNormalization(schoolsInOrg, schoolNumbersToImport, true);

    try {
      convertedItems = await rosterImportItemsMethods.bulkInsert(data, rosterImportId, orgid, userId);
    } catch (error) {
      await failRosterImport({ error, rosterImportId });
    }

    // TEACHER EMAILS VALIDATION
    const teachersValidation = await validateUniqueTeacherEmailsInUploadItems(convertedItems, orgid);
    items = convertedItems;
    if (!teachersValidation.success) {
      hasValidationErrors = true;
      if (isExternalRosteringType) {
        const failedRows = teachersValidation.failedRows || [];
        items = items.filter((item, index) => !failedRows.includes(index));
      }
      const shouldImportFail = !isExternalRosteringType || !items.length;
      const clientErrorMessage = "Error validating teacher email addresses";
      await RosterImports.updateAsync(rosterImportId, {
        $set: {
          ...(shouldImportFail ? { status: "upload failed" } : {}),
          uniqueEmailErrors: teachersValidation.errors,
          "error.clientMessage": clientErrorMessage
        }
      });
      if (shouldImportFail) {
        throw new Meteor.Error(clientErrorMessage);
      }
    }

    // DISTRICT NAME VALIDATION
    const districtNameValidation = await validateDistrictName(convertedItems, orgid);
    if (!districtNameValidation.success) {
      hasValidationErrors = true;
      if (isExternalRosteringType) {
        const failedRows = districtNameValidation.failedRows || [];
        items = items.filter((item, index) => !failedRows.includes(index));
      }
      const shouldImportFail = !isExternalRosteringType || !items.length;
      const clientErrorMessage = `Error validating district name.`;
      await RosterImports.updateAsync(rosterImportId, {
        $set: {
          ...(shouldImportFail ? { status: "upload failed" } : {}),
          districtErrors: uniq(districtNameValidation.errors),
          "error.clientMessage": clientErrorMessage
        }
      });
      if (shouldImportFail) {
        throw new Meteor.Error(clientErrorMessage);
      }
    }
  }

  // UPLOAD PROCESS
  try {
    const dataBeforeImport = await fetchDataBeforeImport(orgid);
    const rosterImportsProcessor = await RosterImportsProcessor.init({
      items,
      orgid,
      byDateOn,
      rosterImportId,
      userId
    });
    await rosterImportsProcessor.process();
    const { students, teachers, sites, studentGroups } = await getRosterStats({
      data,
      dataBeforeImport,
      orgid
    });

    await RosterImports.updateAsync(rosterImportId, {
      $set: {
        status: "completed",
        finished: await getTimestampInfo(userId, orgid, "insertRoster"),
        students,
        teachers,
        sites,
        studentGroups
      }
    });
    return {
      hasValidationErrors
    };
  } catch (error) {
    console.trace("Roster Import");
    await failRosterImport({ error, rosterImportId, hasImportError: true });
  }
}

export const checkIfLocalAndStateIdsAreUnique = async (data, orgid) => {
  const studentsLocalIds = data.map(studentData => studentData.localId);
  const studentsStateIds = data.map(studentData => studentData.stateId);

  const errors = [];
  const schoolYear = await getCurrentSchoolYear(await getMeteorUser(), orgid);
  const duplicatedLocalIds = (
    await Students.find({
      "identity.identification.localId": { $in: studentsLocalIds },
      orgid,
      schoolYear
    }).fetchAsync()
  ).map(student => student.identity.identification.localId);
  const duplicatedStateIds = (
    await Students.find({
      "identity.identification.stateId": { $in: studentsStateIds },
      orgid,
      schoolYear
    }).fetchAsync()
  ).map(student => student.identity.identification.stateId);

  duplicatedLocalIds.forEach(localId => {
    errors.push(`Student with ${localId} aready exists, please ensure to insert unique LocalID`);
  });

  duplicatedStateIds.forEach(stateId => {
    errors.push(`Student with ${stateId} aready exists, please ensure to insert unique StateID`);
  });

  return errors;
};

const insertUploadedStudents = async (dataPackage, orgid, siteId, studentGroupId) => {
  const userId = getMeteorUserId();
  const byDateOn = await getTimestampInfo(userId, orgid);

  const { data, source } = dataPackage;

  const rosterImportDoc = {
    ...(await generateRosterImportsObject({ orgid, customUserId: userId })),
    orgid,
    status: "uploading",
    itemCount: dataPackage.data.length,
    started: byDateOn,
    source,
    ...initializeEmptyRosterStats()
  };

  try {
    RosterImports.validate(rosterImportDoc);
  } catch (err) {
    throw new Meteor.Error(403, "Error while validating Roster Imports");
  }

  let convertedItems = [];
  let rosterImportId;
  try {
    rosterImportId = await insertRosterImportDocAndTrimCollection(rosterImportDoc);

    await RosterImports.updateAsync(rosterImportId, { $set: { status: "processing" } });
    convertedItems = await rosterImportItemsMethods.bulkInsert(data, rosterImportId, orgid);

    const dataBeforeImport = await fetchDataBeforeImport(orgid);
    const rosterImportsProcessor = await RosterImportsProcessor.init({
      items: convertedItems,
      orgid,
      byDateOn,
      rosterImportId
    });
    await rosterImportsProcessor.processUploadedStudents(siteId, studentGroupId);

    const { students } = await getRosterStats({ data, dataBeforeImport, orgid });
    await RosterImports.updateAsync(rosterImportId, {
      $set: {
        status: "completed",
        finished: await getTimestampInfo(userId, orgid, "insertUploadedStudents"),
        "students.added": students.added
      }
    });
  } catch (error) {
    const errorData = {
      errorMessage: error.message,
      clientMessage: error.message
    };

    await RosterImports.updateAsync(rosterImportId, {
      $set: { status: "upload failed", error: errorData }
    });

    throw new Meteor.Error(403, "Error while inserting Roster Imports");
  }
};

export async function addRunByInfo(rosters = []) {
  const userIds = Object.keys(keyBy(rosters, "started.by"));
  const users = await Users.find({ _id: { $in: userIds } }, { fields: { "profile.name": 1 } }).fetchAsync();
  const userNamesByUserId = users.reduce((acc, user) => {
    acc[user._id] = user.profile?.name ? `${user.profile.name.first} ${user.profile.name.last}` : "Unknown";
    return acc;
  }, {});
  return rosters.map(roster => {
    const runBy = roster.started.by.startsWith("CRON")
      ? "Sync Schedule"
      : userNamesByUserId[roster.started.by] || "Unknown";
    return { ...roster, runBy };
  });
}

if (Meteor.isServer) {
  Meteor.methods({
    async "RosterImports:insertRoster"(dataPackage, orgid) {
      check(dataPackage, Object);
      check(orgid, String);

      const { userId } = this;
      if (!userId) {
        throw new Meteor.Error(403, "No logged in user found!");
      }
      if (
        !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId,
          orgid
        }))
      ) {
        throw new Meteor.Error(403, "User is not authorized to use RosterImports:insertRoster for this organization");
      }
      try {
        return insertRoster(dataPackage, userId, orgid);
      } catch (e) {
        throw new Meteor.Error(e.reason || e.message || e.error || "Unknown roster import error");
      }
    },
    async "RosterImports:exportRoster"(orgid) {
      check(orgid, String);
      if (!this.userId) {
        throw new Meteor.Error(403, "No logged in user found!");
      }
      if (
        !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid
        }))
      ) {
        throw new Meteor.Error(403, "User is not authorized to use RosterImports:exportRoster for this organization");
      }
      const currentSchoolYear = await getCurrentSchoolYear(await getMeteorUser(), orgid);
      return getOrganizationRosterFor(orgid, currentSchoolYear);
    },
    async "RosterImports:exportPreviousRoster"(orgid) {
      check(orgid, String);
      if (!this.userId) {
        throw new Meteor.Error(403, "No logged in user found!");
      }
      if (
        !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid
        }))
      ) {
        throw new Meteor.Error(
          403,
          "User is not authorized to use RosterImports:exportPreviousRoster for this organization"
        );
      }
      const previousSchoolYear = (await getCurrentSchoolYear(await getMeteorUser(), orgid)) - 1;
      return getOrganizationRosterFor(orgid, previousSchoolYear);
    },
    async toggleRosterImportsError({ rosterImportId, orgid, isHidden }) {
      check(rosterImportId, String);
      check(orgid, String);
      if (!this.userId) {
        throw new Meteor.Error(403, "No logged in user found!");
      }
      if (
        !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid
        }))
      ) {
        throw new Meteor.Error(403, "User is not authorized to use toggleRosterImportsError for this organization");
      }

      await RosterImports.updateAsync(rosterImportId, {
        $set: { "error.isHidden": !isHidden }
      });

      return true;
    },
    async "RosterImports:CheckIfLocalAndStateIdsAreUnique"(studentDetails, orgid) {
      check(studentDetails, Array);
      check(orgid, String);
      if (!this.userId) {
        throw new Meteor.Error(403, "No logged in user found!");
      }
      if (
        !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid
        }))
      ) {
        throw new Meteor.Error(
          403,
          "User is not authorized to use RosterImports:CheckIfLocalAndStateIdsAreUnique for this organization"
        );
      }

      return checkIfLocalAndStateIdsAreUnique(studentDetails, orgid);
    },
    async "RosterImports:insertStudentRoster"({ data, orgid, siteId, studentGroupId }) {
      check(data, Object);
      check(orgid, String);
      check(siteId, String);
      check(studentGroupId, String);
      if (!this.userId) {
        throw new Meteor.Error(403, "No logged in user found!");
      }
      if (
        !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid
        }))
      ) {
        throw new Meteor.Error(403, "User is not authorized to use RosterImports:insertRoster for this organization");
      }
      await insertUploadedStudents(data, orgid, siteId, studentGroupId);
    },
    async "RosterImports:getLastImportSummaryData"({ orgid, rosterImportId }) {
      check(orgid, String);
      check(rosterImportId, String);
      const query = { orgid, rosterImportId };
      const siteCount = await Sites.find(query).countAsync();
      const studentGroupCount = await StudentGroups.find({
        ...query,
        schoolYear: await getCurrentSchoolYear(await getMeteorUser(), orgid),
        isActive: true
      }).countAsync();
      const futureUserCount = await Users.find({
        "profile.orgid": orgid,
        "profile.rosterImportId": rosterImportId
      }).countAsync();
      const studentCount = await Students.find(query).countAsync();
      return {
        siteCount,
        studentGroupCount,
        futureUserCount,
        studentCount
      };
    },
    async "RosterImports:getLastUploadErrors"(orgid) {
      check(orgid, String);
      if (
        await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid
        })
      ) {
        return RosterImports.findOneAsync(
          { orgid },
          {
            sort: { "started.date": -1 },
            fields: { status: 1, error: 1, studentGroupErrors: 1, uniqueEmailErrors: 1, districtErrors: 1 }
          }
        );
      }
      throw new Meteor.Error(
        403,
        "User is not authorized to use RosterImports:getLastUploadError for this organization"
      );
    },
    async "RosterImports:getLatestRosterImportForOrgsWithSyncSchedule"() {
      if (
        await auth.hasAccess(["superAdmin", "universalDataAdmin"], {
          userId: this.userId
        })
      ) {
        const idsOfOrgsWithSyncSchedule = (
          await Organizations.find(
            { "rosteringSettings.syncSchedule.startDate": { $exists: true } },
            { fields: { _id: 1 } }
          ).fetchAsync()
        ).map(o => o._id);
        // NOTE(fmazur) - Get first document for each unique orgid
        return RosterImports.aggregate([
          { $match: { orgid: { $in: idsOfOrgsWithSyncSchedule } } },
          { $project: { orgid: 1, error: 1, status: 1, "started.on": 1 } },
          { $sort: { "started.on": -1 } },
          { $group: { _id: "$orgid", doc: { $first: "$$ROOT" } } }
        ]).map(ri => ri.doc);
      }
      throw new Meteor.Error(
        403,
        "User is not authorized to use RosterImports:getLatestRosterImportForOrgsWithSyncSchedule for this organization"
      );
    },
    async "RosterImports:getRosterHistory"(orgid, startAt = 0, length = 50) {
      check(orgid, String);
      check(startAt, Number);
      check(length, Number);
      if (
        await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid
        })
      ) {
        const totalCount = await RosterImports.find({ orgid }).countAsync();
        const rosters = await RosterImports.find(
          { orgid },
          {
            sort: { "started.date": -1 },
            fields: {
              districtErrors: 1,
              error: 1,
              finished: 1,
              itemCount: 1,
              sites: 1,
              source: 1,
              started: 1,
              status: 1,
              studentGroupErrors: 1,
              studentGroups: 1,
              students: 1,
              teachers: 1,
              uniqueEmailErrors: 1
            },
            skip: startAt,
            limit: length
          }
        ).fetchAsync();
        return { rosters: await addRunByInfo(rosters), documentsAvailable: totalCount };
      }
      throw new Meteor.Error(403, "User is not authorized to use RosterImports:getRostersInOrg for this organization");
    },
    async "RosterImports:getNumberOfRosterImports"(orgid) {
      check(orgid, String);
      if (
        await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid
        })
      ) {
        return RosterImports.find({ orgid }).countAsync();
      }
      throw new Meteor.Error(
        403,
        "User is not authorized to use RosterImports:getNumberOfRosterImports for this organization"
      );
    },
    async "RosterImports:getCurrentRosterImportDataChangePercentageAndThreshold"(orgid, itemCount = 0) {
      ninjalog.warning({
        msg: "itemCount",
        context: "RosterImports:getCurrentRosterImportDataChangePercentageAndThreshold",
        val: itemCount
      });
      check(orgid, String);
      check(itemCount, Number);
      if (
        await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid
        })
      ) {
        const rosteringThreshold =
          (await Settings.findOneAsync({}, { fields: { "defaults.rosteringThreshold": 1 } })?.defaults
            ?.rosteringThreshold) || 0.5;
        const lastCompletedRosterImportItemCount =
          (await RosterImports.findOneAsync(
            { orgid, status: "completed" },
            {
              sort: { "started.on": -1 },
              fields: { itemCount: 1 }
            }
          )?.itemCount) || 0;
        const dataChangePercentage =
          lastCompletedRosterImportItemCount > 0 ? (1 - itemCount / lastCompletedRosterImportItemCount) * 100 : 0;
        return { dataChangePercentage, rosteringThreshold };
      }
      throw new Meteor.Error(
        403,
        "User is not authorized to use RosterImports:getCurrentRosterImportDataChangePercentageAndThreshold for this organization"
      );
    },
    async "RosterImports:prepareDataForMerging"(
      orgid,
      siteIdAssignmentBySchoolId = {},
      sectionIdAssignmentByClassSectionId = {},
      districtId
    ) {
      check(orgid, String);
      check(siteIdAssignmentBySchoolId, Object);
      check(sectionIdAssignmentByClassSectionId, Object);
      check(districtId, Match.Maybe(String));

      const shortDate = new Date().toISOString().slice(0, 10);
      const lastModified = await getTimestampInfo(
        this?.userId || getMeteorUserId() || "",
        orgid,
        "RosterImports:prepareDataForMerging"
      );

      // eslint-disable-next-line no-restricted-syntax
      for await (const [rosteringSchoolNumber, siteId] of Object.entries(siteIdAssignmentBySchoolId)) {
        await Sites.updateAsync(
          {
            _id: { $ne: siteId },
            orgid,
            "stateInformation.schoolNumber": rosteringSchoolNumber,
            "stateInformation.localSchoolNumber": rosteringSchoolNumber
          },
          {
            $set: {
              "stateInformation.schoolNumber": `${rosteringSchoolNumber}_old_${shortDate}`,
              "stateInformation.localSchoolNumber": `${rosteringSchoolNumber}_old_${shortDate}`,
              lastModified
            }
          },
          {
            multi: true
          }
        );
        await Sites.updateAsync(
          { _id: siteId },
          {
            $set: {
              "stateInformation.districtNumber": getNormalizedId(districtId || orgid),
              "stateInformation.schoolNumber": rosteringSchoolNumber,
              "stateInformation.localSchoolNumber": rosteringSchoolNumber,
              lastModified
            }
          }
        );
      }

      // eslint-disable-next-line no-restricted-syntax
      for await (const [rosteringSectionId, sectionIdToBeReplaced] of Object.entries(
        sectionIdAssignmentByClassSectionId
      )) {
        await StudentGroups.updateAsync(
          { sectionId: sectionIdToBeReplaced, orgid },
          {
            $set: {
              sectionId: rosteringSectionId,
              lastModified
            }
          }
        );
      }
    }
  });
}

export async function insertFakeOneRosterData(orgid) {
  const rosterImportId = await initRosterDocument(orgid);
  (await validateAndImportRowData(orgid, rosterImportId)).catch(e => {
    console.log("* LOG * e", e);
    // failRosterImport({ error: e.message || e.reason, rosterImportId, shouldThrow: false });
  });
}

export async function getNamesOfStudentsWithIndividualInterventionInProgress(orgid) {
  return (await getStudentsWithIndividualInterventionInProgress({ orgid })).map(
    student => `${student.identity.name.lastName}, ${student.identity.name.firstName}`
  );
}

export async function getNamesOfGroupsWithClasswideInterventionInProgress(orgid) {
  return (await getStudentGroupsWithClasswideInterventionInProgress(orgid)).map(studentGroup => studentGroup.name);
}

export async function getNamesOfGroupsWithBenchmarkScreeningInProgress(orgid) {
  return (await getStudentGroupsWithBenchmarkScreeningInProgress(orgid)).map(studentGroup => studentGroup.name);
}

export async function getIdsOfStudentsInClasswideIntervention(orgid) {
  const idsOfGroupsInClasswideIntervention = (await getStudentGroupsWithClasswideInterventionInProgress(orgid)).map(
    group => group._id
  );
  return (
    await StudentGroupEnrollments.find({
      studentGroupId: { $in: idsOfGroupsInClasswideIntervention },
      isActive: true
    }).fetchAsync()
  ).map(enrollment => enrollment.studentId);
}

export async function getStudentsWithIndividualInterventionInProgress({ orgid }) {
  const schoolYear = await getCurrentSchoolYear(await getMeteorUser(), orgid);
  const assessmentResultIds = (
    await AssessmentResults.find(
      {
        orgid,
        type: "individual",
        status: "OPEN",
        schoolYear
      },
      { fields: { _id: 1 } }
    ).fetchAsync()
  ).map(assessmentResult => assessmentResult._id);

  if (!assessmentResultIds.length) {
    return [];
  }

  const students = await Students.find(
    {
      orgid,
      "currentSkill.assessmentResultId": { $in: assessmentResultIds },
      schoolYear
    },
    { fields: { "identity.name": 1 } }
  ).fetchAsync();

  const activeEnrollments = (
    await StudentGroupEnrollments.find({
      studentId: { $in: students.map(student => student._id) },
      isActive: true
    }).fetchAsync()
  ).map(enrollment => enrollment.studentId);

  return students.filter(student => activeEnrollments.includes(student._id));
}

export async function getStudentGroupsWithClasswideInterventionInProgress(orgid) {
  const schoolYear = await getCurrentSchoolYear(await getMeteorUser(), orgid);
  return StudentGroups.find({
    orgid,
    "currentClasswideSkill.assessmentResultId": { $exists: true },
    isActive: true,
    schoolYear
  }).fetchAsync();
}

export async function getStudentGroupsWithBenchmarkScreeningInProgress(orgid, schoolYear) {
  // eslint-disable-next-line no-param-reassign
  schoolYear ??= await getCurrentSchoolYear(await getMeteorUser(), orgid);
  const studentGroupIds = (
    await AssessmentResults.find(
      {
        orgid,
        type: "benchmark",
        status: "OPEN",
        schoolYear
      },
      { fields: { studentGroupId: 1 } }
    ).fetchAsync()
  ).map(assessmentResult => assessmentResult.studentGroupId);

  return StudentGroups.find({
    _id: { $in: studentGroupIds },
    isActive: true,
    schoolYear
  }).fetchAsync();
}

export async function getOrganizationRosterFor(orgid, schoolYear) {
  const studentGroupEnrollments = await StudentGroupEnrollments.find({
    orgid,
    isActive: true,
    schoolYear
  }).fetchAsync();
  const { siteIds, studentIds, studentGroupIds } = extractDataFromStudentGroupEnrollments(studentGroupEnrollments);

  const sitesById = await getSitesById(siteIds);

  const studentGroups = await getStudentGroups(studentGroupIds);
  const studentGroupsById = keyBy(studentGroups, "_id");

  const studentsById = await getStudentsById(studentIds);

  const teacherIds = new Set();
  studentGroups.forEach(group => {
    teacherIds.add(get(group, "ownerIds[0]", "teacherNotFound"));
    group.secondaryTeachers?.forEach(teacherId => {
      teacherIds.add(teacherId);
    });
  });

  const teachersById = await getTeachersById(teacherIds);

  const organizationName = (await Organizations.findOneAsync(orgid, { fields: { name: 1 } })).name;

  const roster = getStudentRosterItems({
    studentGroupEnrollments,
    studentGroupsById,
    sitesById,
    teachersById,
    studentsById,
    organizationName
  });
  return roster.sort(
    (a, b) =>
      a.SchoolID - b.SchoolID ||
      normalizeGrade(a.SpringMathGrade) - normalizeGrade(b.SpringMathGrade) ||
      a.ClassName.localeCompare(b.ClassName) ||
      a.StudentLastName.localeCompare(b.StudentLastName)
  );
}

export function transformGrade(grade) {
  let transformedGrade = "";
  switch (grade) {
    case "09":
    case "10":
    case "11":
    case "12":
      transformedGrade = "HS";
      break;
    default:
      transformedGrade = grade;
  }

  return transformedGrade;
}

function extractDataFromStudentGroupEnrollments(studentGroupEnrollments) {
  const siteIds = new Set();
  const studentIds = new Set();
  const studentGroupIds = new Set();

  studentGroupEnrollments.forEach(studentGroupEnrollment => {
    siteIds.add(studentGroupEnrollment.siteId);
    studentIds.add(studentGroupEnrollment.studentId);
    studentGroupIds.add(studentGroupEnrollment.studentGroupId);
  });

  return { siteIds, studentIds, studentGroupIds };
}

async function getStudentGroups(studentGroupIds) {
  return StudentGroups.find(
    { _id: { $in: Array.from(studentGroupIds) } },
    {
      fields: { sectionId: 1, name: 1, ownerIds: 1, secondaryTeachers: 1, isActive: 1 },
      transform: null
    }
  ).fetchAsync();
}

async function getSitesById(siteIds) {
  const sites = await Sites.find(
    { _id: { $in: Array.from(siteIds) } },
    {
      fields: {
        name: 1,
        stateInformation: 1
      }
    }
  ).fetchAsync();
  return keyBy(sites, "_id");
}

async function getStudentsById(studentIds) {
  const students = await Students.find(
    { _id: { $in: Array.from(studentIds) } },
    {
      fields: {
        grade: 1,
        identity: 1,
        demographic: 1,
        studentGrade: 1
      }
    }
  ).fetchAsync();
  return keyBy(students, "_id");
}

async function getTeachersById(teacherIds) {
  const teachers = await Users.find(
    { _id: { $in: Array.from(teacherIds) } },
    {
      fields: {
        "profile.localId": 1,
        "profile.name": 1,
        emails: 1
      }
    }
  ).fetchAsync();

  return keyBy(teachers, "_id");
}

export function filterRosterFromUnusedFields(fullRoster) {
  const optionalFieldsToCheck = [...optionalRosterFields];
  const optionalFieldHasValue = {};
  optionalFieldsToCheck.forEach(fieldName => {
    optionalFieldHasValue[fieldName] = false;
  });

  fullRoster.forEach(row => {
    optionalFieldsToCheck.forEach(field => {
      if (row[field] !== "") {
        optionalFieldHasValue[field] = true;
        pull(optionalFieldsToCheck, field);
      }
    });
  });
  const rosterWithoutEmptyFields = [];
  const fieldsToRemove = Object.keys(optionalFieldHasValue).filter(fieldName => !optionalFieldHasValue[fieldName]);
  if (fieldsToRemove.length) {
    fullRoster.forEach(row => {
      rosterWithoutEmptyFields.push(pickBy(row, (value, key) => !fieldsToRemove.includes(key)));
    });
    return rosterWithoutEmptyFields;
  }
  return fullRoster;
}

export function getStudentRosterItems({
  studentGroupEnrollments,
  studentGroupsById,
  sitesById,
  teachersById,
  studentsById,
  organizationName
}) {
  const roster = [];
  const studentGroupSectionIdsWithSecondaryTeachers = [];

  studentGroupEnrollments.forEach(studentGroupEnrollment => {
    const studentGroup = studentGroupsById[studentGroupEnrollment.studentGroupId];
    if (!studentGroup || !studentGroup.isActive) return;

    const site = sitesById[studentGroupEnrollment.siteId];
    const primaryTeacherId = studentGroup.ownerIds[0];
    const secondaryTeacherIds = studentGroup.secondaryTeachers || [];

    const student = studentsById[studentGroupEnrollment.studentId];

    if (student) {
      const studentGrade = get(student, "studentGrade", "").replace(/^0+/, "");
      const getStudentItem = (teacher = teachersById[primaryTeacherId]) => ({
        DistrictID: site.stateInformation.districtNumber,
        DistrictName: organizationName,
        SchoolID: site.stateInformation.schoolNumber,
        SchoolName: site.name,
        TeacherID: get(teacher, "profile.localId", "teacherNotFound"),
        TeacherLastName: get(teacher, "profile.name.last", "teacherNotFound"),
        TeacherFirstName: get(teacher, "profile.name.first", "teacherNotFound"),
        TeacherEmail: get(teacher, "emails[0].address", "teacherNotFound"),
        ClassName: get(studentGroup.name.match(/(^.+)\(/), "[1]", "").trim(),
        ClassSectionID: studentGroup.sectionId,
        StudentLocalID: student.identity.identification.localId,
        StudentStateID: student.identity.identification.stateId,
        StudentLastName: student.identity.name.lastName,
        StudentFirstName: student.identity.name.firstName,
        StudentBirthDate: student.demographic.birthDate
          ? new Date(student.demographic.birthDate).toISOString().substr(0, 10)
          : "",
        ...(studentGrade && { StudentGrade: studentGrade }),
        SpringMathGrade: studentGroupEnrollment.grade.replace(/^0+/, "")
      });
      roster.push(getStudentItem());
      if (!studentGroupSectionIdsWithSecondaryTeachers.includes(studentGroup.sectionId) && secondaryTeacherIds.length) {
        studentGroupSectionIdsWithSecondaryTeachers.push(studentGroup.sectionId);
        roster.push(...secondaryTeacherIds.map(secondaryTeacherId => getStudentItem(teachersById[secondaryTeacherId])));
      }
    }
  });
  return filterRosterFromUnusedFields(roster);
}
