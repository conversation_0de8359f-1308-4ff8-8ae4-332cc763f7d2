import React, { Component } from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "react-bootstrap";
import { difference } from "lodash";
import Select from "react-select";

import { getValuesByKey } from "../utilities/utilities";
import { getSelectCustomStyles } from "../../ui/utilities";

const createNewSiteSelectOption = {
  label: "Create new Site",
  value: "default"
};

const defaultStudentGroupSelectOption = {
  label: "Create new Student Group",
  value: "default"
};

export default class MergeDataModal extends Component {
  constructor(props) {
    super(props);
    const siteIdBySchoolNumber = getValuesByKey(props.existingSites, "stateInformation.schoolNumber", "_id");
    const siteIdAssignmentBySchoolId = {};
    let sectionIdAssignmentByClassSectionId = {};
    Object.values(props.rosteringData).forEach(rosterElement => {
      const {
        stateInformation: { schoolNumber },
        studentGroups: rosterStudentGroups
      } = rosterElement;
      if (siteIdBySchoolNumber[schoolNumber]) {
        const siteId = siteIdBySchoolNumber[schoolNumber];
        siteIdAssignmentBySchoolId[schoolNumber] = siteId;
        const studentGroupsInSite = props.existingStudentGroupsBySiteId[siteId] || [];
        sectionIdAssignmentByClassSectionId = rosterStudentGroups.reduce((a, sg) => {
          const selectedStudentGroup = studentGroupsInSite.find(studentGroup => {
            const studentGroupId = sg.classSectionId;
            return studentGroup.sectionId === studentGroupId;
          });
          if (selectedStudentGroup?.sectionId) {
            // eslint-disable-next-line no-param-reassign
            a[sg.classSectionId] = selectedStudentGroup.sectionId;
          }
          return a;
        }, {});
      }
    });

    this.initialMatchedSiteIds = Object.keys(siteIdAssignmentBySchoolId);
    this.state = {
      siteIdAssignmentBySchoolId,
      sectionIdAssignmentByClassSectionId,
      siteIdBySchoolNumber
    };
  }

  close = () => {
    this.props.onCloseModal();
  };

  getSchoolLabel = (name, schoolNumber) => {
    return `${name}${schoolNumber ? ` (${schoolNumber})` : ""}`;
  };

  siteOnChange = (rosterStudentGroups, schoolNumber) => e => {
    const siteId = e.value;

    const studentGroupsInSite = this.props.existingStudentGroupsBySiteId[siteId] || [];
    const matchedSectionIdAssignmentByClassSectionId = rosterStudentGroups.reduce((a, sg) => {
      const selectedStudentGroup = studentGroupsInSite.find(studentGroup => {
        const studentGroupId = this.state.sectionIdAssignmentByClassSectionId[sg.classSectionId] || sg.classSectionId;
        return studentGroup.sectionId === studentGroupId;
      });
      if (selectedStudentGroup?.sectionId) {
        // eslint-disable-next-line no-param-reassign
        a[sg.classSectionId] = selectedStudentGroup.sectionId;
      }
      return a;
    }, {});

    this.setState(state => ({
      siteIdAssignmentBySchoolId: {
        ...state.siteIdAssignmentBySchoolId,
        [schoolNumber]: siteId
      },
      sectionIdAssignmentByClassSectionId: {
        ...state.sectionIdAssignmentByClassSectionId,
        ...matchedSectionIdAssignmentByClassSectionId
      }
    }));
  };

  studentGroupOnChange = classSectionId => e => {
    const existingStudentGroupSectionId = e.value;
    this.setState(state => ({
      sectionIdAssignmentByClassSectionId: {
        ...state.sectionIdAssignmentByClassSectionId,
        [classSectionId]: existingStudentGroupSectionId
      }
    }));
  };

  renderSchoolRow = ({ schoolName, schoolNumber, rosterStudentGroups = [], index, isDisabled = false }) => {
    const { existingSites } = this.props;
    const { siteIdAssignmentBySchoolId, siteIdBySchoolNumber } = this.state;
    const availableSiteIds = difference(
      existingSites.map(s => s._id),
      Object.values(siteIdAssignmentBySchoolId)
    );
    const availableSites = existingSites
      .filter(es => availableSiteIds.includes(es._id) || es._id === siteIdAssignmentBySchoolId[schoolNumber])
      .map(({ _id: siteId, name: siteName, stateInformation }) => {
        const label = this.getSchoolLabel(siteName, stateInformation?.schoolNumber);
        return {
          label,
          value: siteId
        };
      });

    const selectedSite = existingSites.find(site => {
      const siteId = siteIdAssignmentBySchoolId[schoolNumber] || siteIdBySchoolNumber[schoolNumber];
      return site._id === siteId;
    });

    const selectedSiteOption = selectedSite
      ? {
          value: selectedSite._id,
          label: this.getSchoolLabel(selectedSite.name, selectedSite.stateInformation?.schoolNumber)
        }
      : createNewSiteSelectOption;

    return (
      <tr key={`${schoolName}_${index}`}>
        <td className="w7 vertical-align-middle">{this.getSchoolLabel(schoolName, schoolNumber)}</td>
        <td data-testid={`site-select-${index}`}>
          <Select
            value={selectedSiteOption}
            className="react-select-container"
            classNamePrefix="react-select"
            styles={{
              ...getSelectCustomStyles(),
              control: base => ({ ...base, height: 35 })
            }}
            isSearchable={true}
            options={[createNewSiteSelectOption, ...availableSites]}
            isDisabled={isDisabled}
            onChange={this.siteOnChange(rosterStudentGroups, schoolNumber)}
          />
        </td>
      </tr>
    );
  };

  renderStudentGroupsForSchool = ({ rosterStudentGroups, studentGroupsInSite, index }) => {
    const { sectionIdAssignmentByClassSectionId } = this.state;
    const classSectionIds = studentGroupsInSite.map(sgs => sgs.sectionId);
    const availableSectionIds = classSectionIds.filter(
      csi => !Object.values(sectionIdAssignmentByClassSectionId).includes(csi)
    );
    return rosterStudentGroups.map((sg, sgIndex) => {
      const availableStudentGroups = studentGroupsInSite
        .filter(sgI => availableSectionIds.includes(sgI.sectionId))
        .map(({ sectionId, name }) => {
          return {
            label: name,
            value: sectionId
          };
        });

      const selectedStudentGroup = studentGroupsInSite.find(studentGroup => {
        const studentGroupId = sectionIdAssignmentByClassSectionId[sg.classSectionId] || sg.classSectionId;
        return studentGroup.sectionId === studentGroupId;
      });
      const selectedStudentGroupOption = selectedStudentGroup
        ? {
            value: selectedStudentGroup._id,
            label: selectedStudentGroup.name
          }
        : defaultStudentGroupSelectOption;

      return (
        <tr key={sg.classSectionId}>
          <td className="p-l-30 vertical-align-middle">{sg.courseName}</td>
          <td data-testid={`student-group-select-${index}-${sgIndex}`}>
            <Select
              value={selectedStudentGroupOption}
              className="react-select-container"
              classNamePrefix="react-select"
              styles={{ ...getSelectCustomStyles(), control: base => ({ ...base, height: 35 }) }}
              isSearchable={true}
              options={[defaultStudentGroupSelectOption, ...availableStudentGroups]}
              onChange={this.studentGroupOnChange(sg.classSectionId)}
            />
          </td>
        </tr>
      );
    });
  };

  areSchoolAndStudentGroupsMatching = (selectedSite, rosterStudentGroups, studentGroupsInSite) => {
    const studentGroupSectionIds = rosterStudentGroups.map(({ classSectionId }) => classSectionId);
    const existingStudentGroupSectionIds = studentGroupsInSite.map(({ sectionId }) => sectionId);
    const areAnyStudentGroupsUnmatched =
      difference(existingStudentGroupSectionIds, studentGroupSectionIds).length > 0 &&
      difference(studentGroupSectionIds, existingStudentGroupSectionIds).length > 0;
    return selectedSite && !areAnyStudentGroupsUnmatched;
  };

  render() {
    const {
      showModal,
      headerText,
      confirmText,
      cancelText,
      size,
      rosteringData = {},
      existingSites,
      existingStudentGroupsBySiteId
    } = this.props;
    const { siteIdAssignmentBySchoolId, siteIdBySchoolNumber } = this.state;

    return (
      <Modal
        show={showModal}
        onHide={this.close}
        dialogClassName="modal-80w"
        backdrop={confirmText.length ? "static" : true}
        data-testid="merge-data-modal"
        size={size}
      >
        <ModalHeader>
          <h3 className="w9 d-flex">
            <i className="fa fa-2x fa-warning text-warning pull-left" />
            <div>{headerText}</div>
          </h3>
        </ModalHeader>

        <ModalBody>
          <p>
            Unselected school/class will create new school/class.
            <br />
            Data will be imported to selected schools/classes. <br />
            <span className="text-danger">
              If imported rostering data matches different school, then student groups and students will be moved over.
              Any student groups that are not selected will be archived and not available after the merge.
            </span>
          </p>
          <Table hover size="sm">
            <thead>
              <tr>
                <td>Rostering</td>
                <td>Merge with</td>
              </tr>
            </thead>
            <tbody>
              {Object.values(rosteringData).map((rosterElement, index) => {
                const {
                  stateInformation: { schoolNumber },
                  studentGroups: rosterStudentGroups
                } = rosterElement;
                const studentGroupsInSite =
                  existingStudentGroupsBySiteId[
                    siteIdAssignmentBySchoolId[schoolNumber] || siteIdBySchoolNumber[schoolNumber]
                  ] || [];

                const selectedSite = existingSites.find(site => {
                  const siteId = siteIdAssignmentBySchoolId[schoolNumber] || siteIdBySchoolNumber[schoolNumber];
                  return site._id === siteId;
                });

                if (this.areSchoolAndStudentGroupsMatching(selectedSite, rosterStudentGroups, studentGroupsInSite)) {
                  return this.renderSchoolRow({
                    schoolName: rosterElement.schoolName,
                    schoolNumber: rosterElement.stateInformation?.schoolNumber,
                    index,
                    isDisabled: this.initialMatchedSiteIds.includes(selectedSite?.stateInformation.schoolNumber)
                  });
                }

                return (
                  <React.Fragment key={index}>
                    {this.renderSchoolRow({
                      schoolName: rosterElement.schoolName,
                      schoolNumber: rosterElement.stateInformation?.schoolNumber,
                      rosterStudentGroups: rosterElement.studentGroups,
                      index
                    })}
                    {!!selectedSite &&
                      this.renderStudentGroupsForSchool({
                        rosterStudentGroups: rosterElement.studentGroups,
                        studentGroupsInSite,
                        index
                      })}
                  </React.Fragment>
                );
              })}
            </tbody>
          </Table>
        </ModalBody>

        <ModalFooter className="d-flex justify-content-center">
          {confirmText.length ? (
            <Button
              variant="success"
              onClick={() =>
                this.props.confirmAction(
                  this.state.siteIdAssignmentBySchoolId,
                  this.state.sectionIdAssignmentByClassSectionId
                )
              }
              data-testid="confirm-modal-btn"
            >
              {confirmText}
            </Button>
          ) : null}
          {cancelText.length ? (
            <Button variant="default" onClick={this.close} data-testid="cancel-modal-btn">
              {cancelText}
            </Button>
          ) : null}
        </ModalFooter>
      </Modal>
    );
  }
}

MergeDataModal.propTypes = {
  showModal: PropTypes.bool.isRequired,
  confirmAction: PropTypes.func.isRequired,
  onCloseModal: PropTypes.func.isRequired,
  headerText: PropTypes.string,
  confirmText: PropTypes.string,
  cancelText: PropTypes.string,
  size: PropTypes.string,
  orgid: PropTypes.string,
  rosteringData: PropTypes.object,
  existingSites: PropTypes.array,
  existingStudentGroupsBySiteId: PropTypes.object
};

MergeDataModal.defaultProps = {
  headerText: "Select Schools/Classes to merge",
  confirmText: "Merge",
  cancelText: "Cancel"
};
