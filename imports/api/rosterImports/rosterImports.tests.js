import { assert } from "chai";

import { RosterImports } from "./rosterImports.js";
import {
  getNamesOfStudentsWithIndividualInterventionInProgress,
  getNamesOfGroupsWithClasswideInterventionInProgress,
  getNamesOfGroupsWithBenchmarkScreeningInProgress
} from "./methods.js";
import {
  rosterImport,
  getStudent,
  getStudentGroup,
  getAssessmentResult,
  getStudentGroupEnrollment
} from "../../test-helpers/data/rosterImports";

import { Students } from "../students/students.js";
import { StudentGroups } from "../studentGroups/studentGroups.js";
import { AssessmentResults } from "../assessmentResults/assessmentResults.js";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";

const schoolYear = 2018;
jest.mock("../utilities/utilities", () => ({
  ...jest.requireActual("../utilities/utilities"),
  getCurrentSchoolYear: jest.fn(() => schoolYear)
}));

describe("RosterImports", () => {
  afterAll(async () => {
    jest.restoreAllMocks();
  });
  describe("Should pass schema validation method", () => {
    it("validate", () => {
      assert.isUndefined(RosterImports.validate(rosterImport()));
    });
    it("isValid", () => {
      assert.isTrue(RosterImports.isValid(rosterImport()));
    });
  });
  describe("Should fail schema validation method", () => {
    it("isValid", () => {
      const rosterImportDoc = rosterImport();
      rosterImportDoc._id = 1234;
      assert.isFalse(RosterImports.isValid(rosterImportDoc));
    });
  });

  describe("Interventions or screenings in progress", () => {
    afterEach(async () => {
      await Students.removeAsync({});
      await StudentGroups.removeAsync({});
      await AssessmentResults.removeAsync({});
    });

    describe("getNamesOfStudentsWithIndividualInterventionInProgress", () => {
      it("should return names of students with an individual intervention in progress", async () => {
        const orgid = "test_orgid";

        const student = await getStudent({
          orgid,
          firstName: "John",
          lastName: "Wilson"
        });
        const studentWithIndividualIntervention = await getStudent({
          orgid,
          firstName: "Fred",
          lastName: "Cannon",
          hasIndividualIntervention: true,
          assessmentResultId: "someIndividualAssessmentResultId"
        });
        const [, studentId] = await Students.insertAsync([student, studentWithIndividualIntervention]);
        const assessmentResult = await getAssessmentResult({
          _id: "someIndividualAssessmentResultId",
          orgid,
          type: "individual"
        });
        const studentEnrollment = getStudentGroupEnrollment({ studentId });
        await StudentGroupEnrollments.insertAsync(studentEnrollment);
        await AssessmentResults.insertAsync(assessmentResult);

        const students = await getNamesOfStudentsWithIndividualInterventionInProgress(orgid);

        expect(students).toEqual(["Cannon, Fred"]);
      });
    });

    describe("getNamesOfGroupsWithClasswideInterventionInProgress", () => {
      it("should return names of groups with a classwide intervention in progress", async () => {
        const orgid = "test_orgid";

        const studentGroup = await getStudentGroup({ orgid, name: "Group A" });
        const studentGroupWithClasswideIntervention = await getStudentGroup({
          orgid,
          name: "Group B",
          hasClasswideIntervention: true
        });
        await StudentGroups.insertAsync([studentGroup, studentGroupWithClasswideIntervention]);

        const studentGroups = await getNamesOfGroupsWithClasswideInterventionInProgress(orgid);

        expect(studentGroups).toEqual(["Group B"]);
      });
    });

    describe("getNamesOfGroupsWithBenchmarkScreeningInProgress", () => {
      it("should return names of groups with a benchmark screening in progress", async () => {
        const orgid = "test_orgid";

        const studentGroup = await getStudentGroup({ orgid, name: "Group A" });
        const studentGroupWithBenchmarkScreening = await getStudentGroup({
          orgid,
          name: "Group B"
        });
        const [, studentGroupId] = await StudentGroups.insertAsync([studentGroup, studentGroupWithBenchmarkScreening]);

        await AssessmentResults.insertAsync(await getAssessmentResult({ orgid, studentGroupId }));

        const studentGroups = await getNamesOfGroupsWithBenchmarkScreeningInProgress(orgid);

        expect(studentGroups).toEqual(["Group B"]);
      });
    });
  });
});
