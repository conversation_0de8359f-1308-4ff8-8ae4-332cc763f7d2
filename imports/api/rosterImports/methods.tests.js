import {
  getOrganizationRosterFor,
  checkIfLocalAndStateIdsAreUnique,
  transformGrade,
  insertRoster,
  getRosterStats,
  getStudentRosterItems
} from "./methods";
import { Students } from "../students/students";
import {
  addActiveAndInactiveEnrollmentToGroup,
  addCurrentAndPastEnrollmentToGroup,
  addEnrollmentToInactiveGroup,
  addGroupWithoutOwner,
  addOrganizationTestData,
  getExpectedCurrentRoster,
  insertTestStudents
} from "./methods.testHelpers";
import { clearCollections } from "./rosterImportsProcessor.testHelpers";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";
import { StudentGroups } from "../studentGroups/studentGroups";
import { Organizations } from "../organizations/organizations";
import { BenchmarkPeriods } from "../benchmarkPeriods/benchmarkPeriods";
import { getBenchmarkPeriods } from "../../test-helpers/data/benchmarkPeriods";
import { Sites } from "../sites/sites";
import { Users } from "../users/users";
import { generateMultipleSpringMathGradesDataPackage } from "../../test-helpers/data/rosterImports";
import { prepareSitesWithPotentialNormalization } from "./helpers";
import { SCHOOL_NUMBER_NORMALIZATION_TIMESTAMP } from "../constants";

jest.mock("../utilities/utilities", () => ({
  getCurrentSchoolYear: jest.fn(() => 2022),
  getLatestAvailableSchoolYear: jest.fn(() => 2022),
  idValidation: { regex: /.*/, description: "" },
  validateSchemaProperty: jest.fn(() => () => undefined),
  getMeteorUser: jest.fn(() => Promise.resolve({ _id: "test-user-id", profile: { orgid: "test-org" } })),
  getMeteorUserSync: jest.fn(() => ({ _id: "test-user-id", profile: { orgid: "test-org" } })),
  getMeteorUserId: jest.fn(customUserId => customUserId || "test-user-id"),
  initializeEmptyRosterStats: jest.fn(() => ({
    students: { added: 0, updated: 0, removed: 0 },
    teachers: { added: 0, updated: 0, removed: 0 },
    sites: { added: 0, updated: 0, removed: 0 },
    studentGroups: { added: 0, updated: 0, removed: 0 }
  })),
  fetchRosteringType: jest.fn(() => Promise.resolve("rosterImport"))
}));

describe("getOrganizationRosterFor", () => {
  const orgid = "spec_organization";
  const schoolYear = 2022;
  afterEach(async () => {
    await clearCollections();
  });

  it("should return current roster for chosen organization", async () => {
    await addOrganizationTestData(orgid);

    const roster = await getOrganizationRosterFor(orgid, schoolYear);

    const expectedRoster = getExpectedCurrentRoster();
    expect(roster.length).toEqual(expectedRoster.length);
    expect(roster).toEqual(expectedRoster);
  });

  it("should only export students from the current school year", async () => {
    await addCurrentAndPastEnrollmentToGroup(orgid);

    const roster = await getOrganizationRosterFor(orgid, schoolYear);

    expect(roster.length).toEqual(1);
    expect(roster[0].StudentFirstName).toEqual("Current");
    expect(roster[0].StudentLastName).toEqual("Student");
  });

  it("should not export students that do not have any active enrollment", async () => {
    await addActiveAndInactiveEnrollmentToGroup(orgid);

    const roster = await getOrganizationRosterFor(orgid, schoolYear);

    expect(roster.length).toEqual(1);
    expect(roster[0].StudentFirstName).toEqual("Active");
    expect(roster[0].StudentLastName).toEqual("Student");
  });

  it("should not export students with active enrollments in inactive groups", async () => {
    await addEnrollmentToInactiveGroup(orgid);

    const roster = await getOrganizationRosterFor(orgid, schoolYear);

    expect(roster.length).toEqual(0);
  });

  it("should return teacherNotFound for teacher name and teacher email when a group does not have any teacher assigned to it", async () => {
    await addGroupWithoutOwner(orgid);

    const roster = await getOrganizationRosterFor(orgid, schoolYear);

    expect(roster.length).toEqual(1);
    expect(roster[0].TeacherEmail).toEqual("teacherNotFound");
    expect(roster[0].TeacherID).toEqual("teacherNotFound");
    expect(roster[0].TeacherLastName).toEqual("teacherNotFound");
  });
});

describe("checkIfLocalAndStateIdsAreUnique", () => {
  afterAll(async () => {
    await Students.removeAsync({});
  });

  const testOrgId = "org1";
  const testLocalId1 = "localId1";
  const testLocalId2 = "localId2";
  const testStateId1 = "stateId1";
  const testStateId2 = "stateId2";
  const testStateId3 = "stateId3";

  const identificationDetails = [
    { localId: testLocalId1, stateId: testStateId1, orgid: testOrgId },
    { localId: testLocalId2, stateId: testStateId2, orgid: testOrgId },
    { localId: "localId3", stateId: testStateId3, orgid: testOrgId },
    { localId: "localId4", stateId: "stateId4", orgid: testOrgId },
    { localId: "localId5", stateId: testStateId1, orgid: "org2" },
    { localId: "localId2", stateId: "stateId5", orgid: "org2" }
  ];

  beforeAll(async () => {
    const schoolYear = 2022;
    await insertTestStudents(identificationDetails, schoolYear);
  });
  it("should return list of errors for students with duplicated localId", async () => {
    const testData = [
      { localId: testLocalId1, stateId: "otherStateId" },
      { localId: "newLocalId", stateId: "newStateId" }
    ];

    const result = await checkIfLocalAndStateIdsAreUnique(testData, testOrgId);

    expect(result.length).toEqual(1);
    expect(result[0]).toEqual(`Student with ${testLocalId1} aready exists, please ensure to insert unique LocalID`);
  });
  it("should return list of errors for students with duplicated stateId", async () => {
    const testData = [
      { localId: "otherLocalId", stateId: testStateId1 },
      { localId: "newLocalId", stateId: "newStateId" }
    ];

    const result = await checkIfLocalAndStateIdsAreUnique(testData, testOrgId);

    expect(result.length).toEqual(1);
    expect(result[0]).toEqual(`Student with ${testStateId1} aready exists, please ensure to insert unique StateID`);
  });
  it("should return list of errors for students with duplicated localId and stateId", async () => {
    const testData = [
      { localId: testLocalId1, stateId: "otherStateId1" },
      { localId: "otherLocalId1", stateId: testStateId2 },
      { localId: testLocalId2, stateId: "stateId3" },
      { localId: "otherLocalId2", stateId: "otherStateId2" }
    ];

    const result = await checkIfLocalAndStateIdsAreUnique(testData, testOrgId);

    expect(result.length).toEqual(4);
    expect(result).toEqual(
      expect.arrayContaining([
        `Student with ${testLocalId1} aready exists, please ensure to insert unique LocalID`,
        `Student with ${testLocalId2} aready exists, please ensure to insert unique LocalID`,
        `Student with ${testStateId2} aready exists, please ensure to insert unique StateID`,
        `Student with ${testStateId3} aready exists, please ensure to insert unique StateID`
      ])
    );
  });
  it("should not return errors when localId is duplicated in other organization", async () => {
    const testData = [{ localId: "localId5", stateId: "newStateId" }];

    const result = await checkIfLocalAndStateIdsAreUnique(testData, testOrgId);

    expect(result.length).toEqual(0);
  });
  it("should not return errors when stateId is duplicated in other organization", async () => {
    const testData = [{ localId: "newLocalId", stateId: "stateId5" }];

    const result = await checkIfLocalAndStateIdsAreUnique(testData, testOrgId);

    expect(result.length).toEqual(0);
  });
});

const gradeMappings = [
  {
    grade: "K",
    expectedGrade: "K"
  },
  {
    grade: "01",
    expectedGrade: "01"
  },
  {
    grade: "02",
    expectedGrade: "02"
  },
  {
    grade: "03",
    expectedGrade: "03"
  },
  {
    grade: "04",
    expectedGrade: "04"
  },
  {
    grade: "05",
    expectedGrade: "05"
  },
  {
    grade: "06",
    expectedGrade: "06"
  },
  {
    grade: "07",
    expectedGrade: "07"
  },
  {
    grade: "08",
    expectedGrade: "08"
  },
  {
    grade: "09",
    expectedGrade: "HS"
  },
  {
    grade: "10",
    expectedGrade: "HS"
  },
  {
    grade: "11",
    expectedGrade: "HS"
  },
  {
    grade: "12",
    expectedGrade: "HS"
  },
  {
    grade: "HS",
    expectedGrade: "HS"
  }
];

describe("transformGrade", () => {
  it("should return the correct grade", () => {
    gradeMappings.forEach(({ grade, expectedGrade }) => {
      expect(transformGrade(grade)).toEqual(expectedGrade);
    });
  });
});

jest.mock("../helpers/getTimestampInfo", () => ({
  getTimestampInfo: jest.fn(() => ({
    by: "userId",
    on: new Date().getTime(),
    date: new Date()
  }))
}));

jest.mock("../rosterImportItems/rosterImportItems", () => ({
  RosterImportItems: {
    rawCollection: () => ({
      initializeUnorderedBulkOp: () => ({
        execute: () => {},
        insert: () => {}
      })
    }),
    validate: () => {},
    remove: () => 0
  }
}));

describe("insertRoster", () => {
  const orgid = "spec_organization";
  afterEach(async () => {
    await clearCollections();
  });

  beforeAll(async () => {
    await BenchmarkPeriods.insertAsync(getBenchmarkPeriods());
  });
  it("should by default insert students with the same section id into two separate groups because of different SpringMathGrade", async () => {
    await Organizations.insertAsync({
      _id: orgid,
      name: "File Upload"
    });
    await insertRoster(generateMultipleSpringMathGradesDataPackage(["1", "2"]), "userId", orgid);

    const studentsCount = await Students.find().countAsync();
    const enrollmentsCount = await StudentGroupEnrollments.find().countAsync();
    const studentGroupsCount = await StudentGroups.find().countAsync();
    expect(studentsCount).toEqual(2);
    expect(enrollmentsCount).toEqual(2);
    expect(studentGroupsCount).toEqual(2);
  });
  it("should insert students with the same section id and different SpringMathGrade into the same group when allowMultipleGradeLevels is true", async () => {
    await Organizations.insertAsync({
      _id: orgid,
      name: "File Upload",
      allowMultipleGradeLevels: true
    });
    await insertRoster(generateMultipleSpringMathGradesDataPackage(["1", "2"]), "userId", orgid);

    const studentsCount = await Students.find().countAsync();
    const enrollmentsCount = await StudentGroupEnrollments.find().countAsync();
    const studentGroupsCount = await StudentGroups.find().countAsync();
    expect(studentsCount).toEqual(2);
    expect(enrollmentsCount).toEqual(2);
    expect(studentGroupsCount).toEqual(1);
  });
});

describe("getRosterStats", () => {
  const orgid = "testOrgId";
  const schoolYear = 2022;
  const students = [
    { _id: "studentId1", identity: { identification: { stateId: "stateId1" } } },
    { _id: "studentId2", identity: { identification: { stateId: "stateId2" } } },
    { _id: "studentId3", identity: { identification: { stateId: "stateId3" } } }
  ];
  const studentGroupEnrollments = [
    { _id: "sgeId1", orgid, schoolYear, isActive: true, studentId: "studentId1", studentGroupId: "studentGroupId1" },
    { _id: "sgeId2", orgid, schoolYear, isActive: true, studentId: "studentId2", studentGroupId: "studentGroupId1" },
    { _id: "sgeId3", orgid, schoolYear, isActive: true, studentId: "studentId3", studentGroupId: "studentGroupId2" }
  ];
  const teachers = [
    { _id: "userId1", profile: { orgid, localId: "localId1", siteAccess: [{ role: "arbitraryIdteacher" }] } },
    { _id: "userId2", profile: { orgid, localId: "localId2", siteAccess: [{ role: "arbitraryIdadmin" }] } }
  ];
  const sites = [{ _id: "siteId1", orgid, schoolYear, stateInformation: { schoolNumber: "1" } }];
  const studentGroups = [
    { _id: "studentGroupId1", orgid, siteId: "siteId1", schoolYear, isActive: true, sectionId: "sId1", grade: "01" },
    { _id: "studentGroupId2", orgid, siteId: "siteId1", schoolYear, isActive: true, sectionId: "sId2", grade: "01" }
  ];
  beforeEach(async () => {
    await Sites.insertAsync(sites);
    await StudentGroupEnrollments.insertAsync(studentGroupEnrollments);
    await StudentGroups.insertAsync(studentGroups);
    await Students.insertAsync(students);
    await Users.insertAsync(teachers);
  });
  afterEach(async () => {
    await clearCollections();
  });
  it("should properly calculate added statistic for empty site", async () => {
    const stats = await getRosterStats({
      data: [
        { TeacherID: "localId1", SchoolID: "1" },
        { TeacherID: "localId2", SchoolID: "1" }
      ],
      dataBeforeImport: {
        siteIds: [],
        teacherIds: [],
        studentGroupSectionIds: [],
        studentStateIds: []
      },
      orgid
    });
    expect(getStatCategory(stats, "added")).toEqual({
      students: { added: 3 },
      teachers: { added: 2 },
      sites: { added: 1 },
      studentGroups: { added: 2 }
    });
    expect(getStatCategory(stats, "updated")).toEqual({
      students: { updated: 0 },
      teachers: { updated: 0 },
      sites: { updated: 0 },
      studentGroups: { updated: 0 }
    });
    expect(getStatCategory(stats, "removed")).toEqual({
      students: { removed: 0 },
      teachers: { removed: 0 },
      sites: { removed: 0 },
      studentGroups: { removed: 0 }
    });
  });
  it("should properly calculate updated statistic when reusing same data", async () => {
    const stats = await getRosterStats({
      data: [
        { TeacherID: "localId1", SchoolID: "1" },
        { TeacherID: "localId2", SchoolID: "1" }
      ],
      dataBeforeImport: {
        siteIds: ["siteId1"],
        teacherIds: ["userId1", "userId2"],
        studentGroupSectionIds: ["siteId1_sId1_01", "siteId1_sId2_01"],
        studentStateIds: ["stateId1", "stateId2", "stateId3"]
      },
      orgid
    });
    expect(getStatCategory(stats, "added")).toEqual({
      students: { added: 0 },
      teachers: { added: 0 },
      sites: { added: 0 },
      studentGroups: { added: 0 }
    });
    expect(getStatCategory(stats, "updated")).toEqual({
      students: { updated: 3 },
      teachers: { updated: 2 },
      sites: { updated: 1 },
      studentGroups: { updated: 2 }
    });
    expect(getStatCategory(stats, "removed")).toEqual({
      students: { removed: 0 },
      teachers: { removed: 0 },
      sites: { removed: 0 },
      studentGroups: { removed: 0 }
    });
  });

  it("should properly calculate removed statistic", async () => {
    await Users.insertAsync({
      _id: "userId3",
      profile: { orgid, localId: "localId3", siteAccess: [{ role: "arbitraryIdteacher" }] }
    });
    const stats = await getRosterStats({
      data: [
        { TeacherID: "localId1", SchoolID: "1" },
        { TeacherID: "localId2", SchoolID: "1" }
      ],
      dataBeforeImport: {
        siteIds: ["siteId1"],
        teacherIds: ["userId1", "userId2", "userId3"],
        studentGroupSectionIds: ["siteId1_sId1_01", "siteId1_sId2_01", "siteId1_sId3_01"],
        studentStateIds: ["stateId1", "stateId2", "stateId3", "stateId4"]
      },
      orgid
    });
    expect(getStatCategory(stats, "added")).toEqual({
      students: { added: 0 },
      teachers: { added: 0 },
      sites: { added: 0 },
      studentGroups: { added: 0 }
    });
    expect(getStatCategory(stats, "updated")).toEqual({
      students: { updated: 3 },
      teachers: { updated: 2 },
      sites: { updated: 1 },
      studentGroups: { updated: 2 }
    });
    expect(getStatCategory(stats, "removed")).toEqual({
      students: { removed: 1 },
      teachers: { removed: 1 },
      sites: { removed: 0 },
      studentGroups: { removed: 1 }
    });
  });
});

describe("getStudentRosterItems", () => {
  const orgid = "testOrgId";
  const orgName = "Test Org";
  const schoolYear = 2022;
  const studentsById = {
    studentId1: {
      _id: "studentId1",
      identity: {
        name: { firstName: "TestName1", lastName: "Test1" },
        identification: { stateId: "stateId1", localId: "localId1" }
      },
      demographic: { birthDate: "2012-04-12" }
    },
    studentId2: {
      _id: "studentId2",
      identity: {
        name: { firstName: "TestName2", lastName: "Test2" },
        identification: { stateId: "stateId2", localId: "localId2" }
      },
      demographic: { birthDate: "2012-04-12" }
    },
    studentId3: {
      _id: "studentId3",
      identity: {
        name: { firstName: "TestName3", lastName: "Test3" },
        identification: { stateId: "stateId3", localId: "localId3" }
      },
      demographic: { birthDate: "2012-04-12" }
    }
  };
  const studentGroupEnrollments = [
    {
      _id: "sgeId1",
      orgid,
      schoolYear,
      siteId: "siteId1",
      isActive: true,
      grade: "01",
      studentId: "studentId1",
      studentGroupId: "studentGroupId1"
    },
    {
      _id: "sgeId2",
      orgid,
      schoolYear,
      siteId: "siteId1",
      isActive: true,
      grade: "01",
      studentId: "studentId2",
      studentGroupId: "studentGroupId1"
    },
    {
      _id: "sgeId3",
      orgid,
      schoolYear,
      siteId: "siteId1",
      isActive: true,
      grade: "01",
      studentId: "studentId3",
      studentGroupId: "studentGroupId2"
    }
  ];
  const teachersById = {
    userId1: {
      _id: "userId1",
      emails: [{ address: "<EMAIL>" }],
      profile: {
        name: { last: "Last1", first: "First1" },
        orgid,
        localId: "localId1",
        siteAccess: [{ role: "arbitraryIdteacher" }]
      }
    },
    userId2: {
      _id: "userId2",
      emails: [{ address: "<EMAIL>" }],
      profile: {
        name: { last: "Last2", first: "First2" },
        orgid,
        localId: "localId2",
        siteAccess: [{ role: "arbitraryIdadmin" }]
      }
    }
  };
  const sitesById = {
    siteId1: {
      _id: "siteId1",
      name: "testSite1",
      orgid,
      schoolYear,
      stateInformation: {
        schoolNumber: "1",
        districtNumber: "1"
      }
    }
  };
  const studentGroupsById = {
    studentGroupId1: {
      _id: "studentGroupId1",
      name: "Test 01 (sId1)",
      ownerIds: ["userId1"],
      orgid,
      schoolYear,
      isActive: true,
      sectionId: "sId1",
      grade: "01"
    },
    studentGroupId2: {
      _id: "studentGroupId2",
      name: "Test 02 (sId2)",
      ownerIds: ["userId2"],
      orgid,
      schoolYear,
      isActive: true,
      sectionId: "sId2",
      grade: "01"
    }
  };
  it("should return correct CSV rows when there are no secondary teachers", () => {
    expect(
      getStudentRosterItems({
        studentGroupEnrollments,
        studentGroupsById,
        sitesById,
        teachersById,
        studentsById,
        organizationName: orgName
      })
    ).toEqual([
      {
        DistrictID: "1",
        DistrictName: "Test Org",
        SchoolID: "1",
        SchoolName: "testSite1",
        TeacherID: "localId1",
        TeacherLastName: "Last1",
        TeacherFirstName: "First1",
        TeacherEmail: "<EMAIL>",
        ClassName: "Test 01",
        ClassSectionID: "sId1",
        StudentLocalID: "localId1",
        StudentStateID: "stateId1",
        StudentLastName: "Test1",
        StudentFirstName: "TestName1",
        StudentBirthDate: "2012-04-12",
        SpringMathGrade: "1"
      },
      {
        DistrictID: "1",
        DistrictName: "Test Org",
        SchoolID: "1",
        SchoolName: "testSite1",
        TeacherID: "localId1",
        TeacherLastName: "Last1",
        TeacherFirstName: "First1",
        TeacherEmail: "<EMAIL>",
        ClassName: "Test 01",
        ClassSectionID: "sId1",
        StudentLocalID: "localId2",
        StudentStateID: "stateId2",
        StudentLastName: "Test2",
        StudentFirstName: "TestName2",
        StudentBirthDate: "2012-04-12",
        SpringMathGrade: "1"
      },
      {
        DistrictID: "1",
        DistrictName: "Test Org",
        SchoolID: "1",
        SchoolName: "testSite1",
        TeacherID: "localId2",
        TeacherLastName: "Last2",
        TeacherFirstName: "First2",
        TeacherEmail: "<EMAIL>",
        ClassName: "Test 02",
        ClassSectionID: "sId2",
        StudentLocalID: "localId3",
        StudentStateID: "stateId3",
        StudentLastName: "Test3",
        StudentFirstName: "TestName3",
        StudentBirthDate: "2012-04-12",
        SpringMathGrade: "1"
      }
    ]);
  });
  it("should return correct CSV rows when there are some secondary teachers", () => {
    studentGroupsById.studentGroupId1.secondaryTeachers = ["userId2"];
    expect(
      getStudentRosterItems({
        studentGroupEnrollments,
        studentGroupsById,
        sitesById,
        teachersById,
        studentsById,
        organizationName: orgName
      })
    ).toEqual([
      {
        DistrictID: "1",
        DistrictName: "Test Org",
        SchoolID: "1",
        SchoolName: "testSite1",
        TeacherID: "localId1",
        TeacherLastName: "Last1",
        TeacherFirstName: "First1",
        TeacherEmail: "<EMAIL>",
        ClassName: "Test 01",
        ClassSectionID: "sId1",
        StudentLocalID: "localId1",
        StudentStateID: "stateId1",
        StudentLastName: "Test1",
        StudentFirstName: "TestName1",
        StudentBirthDate: "2012-04-12",
        SpringMathGrade: "1"
      },
      {
        ClassName: "Test 01",
        ClassSectionID: "sId1",
        DistrictID: "1",
        DistrictName: "Test Org",
        SchoolID: "1",
        SchoolName: "testSite1",
        SpringMathGrade: "1",
        StudentBirthDate: "2012-04-12",
        StudentFirstName: "TestName1",
        StudentLastName: "Test1",
        StudentLocalID: "localId1",
        StudentStateID: "stateId1",
        TeacherEmail: "<EMAIL>",
        TeacherFirstName: "First2",
        TeacherID: "localId2",
        TeacherLastName: "Last2"
      },
      {
        DistrictID: "1",
        DistrictName: "Test Org",
        SchoolID: "1",
        SchoolName: "testSite1",
        TeacherID: "localId1",
        TeacherLastName: "Last1",
        TeacherFirstName: "First1",
        TeacherEmail: "<EMAIL>",
        ClassName: "Test 01",
        ClassSectionID: "sId1",
        StudentLocalID: "localId2",
        StudentStateID: "stateId2",
        StudentLastName: "Test2",
        StudentFirstName: "TestName2",
        StudentBirthDate: "2012-04-12",
        SpringMathGrade: "1"
      },
      {
        DistrictID: "1",
        DistrictName: "Test Org",
        SchoolID: "1",
        SchoolName: "testSite1",
        TeacherID: "localId2",
        TeacherLastName: "Last2",
        TeacherFirstName: "First2",
        TeacherEmail: "<EMAIL>",
        ClassName: "Test 02",
        ClassSectionID: "sId2",
        StudentLocalID: "localId3",
        StudentStateID: "stateId3",
        StudentLastName: "Test3",
        StudentFirstName: "TestName3",
        StudentBirthDate: "2012-04-12",
        SpringMathGrade: "1"
      }
    ]);
  });
});

describe("prepareSitesWithPotentialLeadingZeros", () => {
  const orgid = "orgid";
  const timestampBefore = SCHOOL_NUMBER_NORMALIZATION_TIMESTAMP - 5;
  const timestampAfter = SCHOOL_NUMBER_NORMALIZATION_TIMESTAMP + 5;
  const schoolYear = 2022; // Use the mocked value directly
  const existingSites = [
    {
      _id: "modifiedBefore",
      orgid,
      name: "testSite1",
      schoolYear,
      stateInformation: {
        schoolNumber: "60" // NOTE(fmazur) - could have been normalized to 60 from 060 or 60test etc
      },
      lastModified: {
        on: timestampBefore
      }
    },
    {
      _id: "modifiedAfter",
      orgid,
      name: "testSite2",
      schoolYear,
      stateInformation: {
        schoolNumber: "60test"
      },
      lastModified: {
        on: timestampAfter
      }
    },
    {
      _id: "importingWithLeadingZero",
      orgid,
      name: "testSite3",
      schoolYear,
      stateInformation: {
        schoolNumber: "61"
      },
      lastModified: {
        on: timestampBefore
      }
    },
    {
      _id: "importingMatchingExistingBefore",
      orgid,
      name: "testSite4",
      schoolYear,
      stateInformation: {
        schoolNumber: "62"
      },
      lastModified: {
        on: timestampBefore
      }
    },
    {
      _id: "importingMatchingExistingAfter",
      orgid,
      name: "testSite5",
      schoolYear,
      stateInformation: {
        schoolNumber: "63"
      },
      lastModified: {
        on: timestampAfter
      }
    }
  ];
  beforeEach(async () => {
    await Sites.insertAsync(existingSites);
  });

  afterEach(async () => {
    await Sites.removeAsync({});
  });

  it("should not do anything when there are no existing sites", async () => {
    await Sites.removeAsync({});
    prepareSitesWithPotentialNormalization([], []);
    const sites = await Sites.find({ orgid }).fetchAsync();
    expect(sites.length).toEqual(0);
  });

  it("should process school numbers for sites", async () => {
    const incomingSchoolNumbers = ["061", "62", "63", "060test", "60test"].sort();

    prepareSitesWithPotentialNormalization(existingSites, incomingSchoolNumbers);
    const processedSiteNumbers = (await Sites.find({ orgid }).fetchAsync())
      .map(s => s.stateInformation.schoolNumber)
      .sort();
    expect(processedSiteNumbers).toEqual(incomingSchoolNumbers);
  });

  it("should return matched existing school numbers with imported", () => {
    const incomingSchoolNumbers = ["061", "62", "63", "060test", "60test"];

    const result = prepareSitesWithPotentialNormalization(existingSites, incomingSchoolNumbers, false);
    expect(result).toEqual({
      "60": "060test",
      "60test": "60test",
      "61": "061",
      "62": "62",
      "63": "63"
    });
  });

  it("should not match existing schools where more than one school number is matching based on normalization", () => {
    const incomingSchoolNumbers = ["060test", "61", "62", "60"];

    const result = prepareSitesWithPotentialNormalization(existingSites, incomingSchoolNumbers, false);
    expect(result).toEqual({
      "61": "61",
      "62": "62"
    });
  });
});

function getStatCategory(stats, categoryName) {
  return Object.entries(stats).reduce((a, [key, categories]) => {
    const temp = a;
    if (!temp[key]) {
      temp[key] = {};
    }
    temp[key][categoryName] = categories[categoryName];
    return temp;
  }, {});
}
