/* eslint-disable global-require */
// Set up mocks before imports
import { groupBy } from "lodash";
import RosterImportsProcessor from "./rosterImportsProcessor";
import { Organizations } from "../organizations/organizations";
import { Users } from "../users/users";
import { Sites } from "../sites/sites";
import { Students } from "../students/students";
import { StudentGroups } from "../studentGroups/studentGroups";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";
import {
  getAllRosterImportItems,
  getAllSunnySlopeSiteItems,
  getCookieGroupTeacherUploadData,
  getUpdatedItemsWithAddedEnrollments,
  moveStudentItemsBetweenGroups
} from "../../test-helpers/data/rosterImportItems";
import { getStudent, getStudentGroup } from "../../test-helpers/data/rosterImports";
import { AssessmentResults } from "../assessmentResults/assessmentResults";
import {
  clearCollections,
  getNumberOfStudentScores,
  setupDb,
  setupTestContext,
  TestGroup,
  areAllScoresStarted
} from "./rosterImportsProcessor.testHelpers";
import { getTimestampInfo } from "../helpers/getTimestampInfo";

jest.mock("../benchmarkPeriods/methods.js", () => ({
  __esModule: true,
  default: {
    getBenchmarkPeriodByDate: jest.fn(() => ({ _id: "mockBenchmarkPeriodId" }))
  }
}));

jest.mock("../benchmarkWindows/methods.js", () => ({
  getCurrentBenchmarkWindowWithSiteId: jest.fn(() => ({
    schoolYear: 2023,
    orgid: "mockOrgId",
    benchmarkPeriodId: "mockBenchmarkPeriodId"
  })),
  createCurrentBenchmarkWindowForSite: jest.fn(() => ({
    _id: "mockBenchmarkWindowId",
    schoolYear: 2023,
    orgid: "mockOrgId",
    benchmarkPeriodId: "mockBenchmarkPeriodId",
    siteId: "mockSiteId"
  }))
}));

jest.mock("../utilities/utilities", () => ({
  ...jest.requireActual("../utilities/utilities"),
  getCurrentSchoolYear: jest.fn(() => 2023),
  idValidation: { regex: /.*/, description: "" }
}));

describe("RosterImportsProcessor", () => {
  const { schoolYear, orgid, siteId, benchmarkPeriodId, districtName, districtID } = setupTestContext();

  // Update mock functions with actual values from setupTestContext
  const mockGetCurrentBenchmarkWindowWithSiteId = jest.fn(() => ({
    schoolYear,
    orgid,
    benchmarkPeriodId
  }));
  const mockCreateCurrentBenchmarkWindowForSite = jest.fn(() => ({
    _id: "mockBenchmarkWindowId",
    schoolYear,
    orgid,
    benchmarkPeriodId,
    siteId
  }));

  // Update the mocks with the actual values
  jest.requireMock(
    "../benchmarkWindows/methods.js"
  ).getCurrentBenchmarkWindowWithSiteId = mockGetCurrentBenchmarkWindowWithSiteId;
  jest.requireMock(
    "../benchmarkWindows/methods.js"
  ).createCurrentBenchmarkWindowForSite = mockCreateCurrentBenchmarkWindowForSite;
  jest.requireMock("../benchmarkPeriods/methods.js").default.getBenchmarkPeriodByDate = jest.fn(() => ({
    _id: benchmarkPeriodId
  }));
  jest.requireMock("../utilities/utilities").getCurrentSchoolYear = jest.fn(() => schoolYear);

  const rosterImportId = "test_rosterImportId";
  let byDateOn;

  const items = [
    {
      data: {
        schoolYear,
        districtID,
        districtName
      }
    }
  ];

  let rosterImportsProcessor;
  beforeAll(async () => {
    byDateOn = await getTimestampInfo("db_mock", orgid);
    await Organizations.insertAsync({ _id: orgid, name: districtName });
    rosterImportsProcessor = await RosterImportsProcessor.init({
      items,
      orgid,
      byDateOn,
      rosterImportId
    });
  });

  afterAll(async () => {
    await clearCollections();
    jest.restoreAllMocks();
  });

  describe("process", () => {
    const getCountQuery = (sy, otherQueryParams) => ({
      schoolYear: sy,
      ...otherQueryParams
    });
    const getCount = async (collection, query) => collection.find(query).countAsync();
    beforeEach(async () => {
      await clearCollections();
      await Organizations.insertAsync({ _id: orgid, name: districtName });
    });
    afterAll(async () => {
      await clearCollections();
    });

    it("should add new documents to DB from the initial CSV roster import", async () => {
      const rosterImportItems = await getAllRosterImportItems();
      await (
        await RosterImportsProcessor.init({
          items: rosterImportItems,
          orgid,
          byDateOn,
          rosterImportId
        })
      ).process();

      const sitesCount = await Sites.find({ orgid }).countAsync();
      expect(sitesCount).toEqual(2);

      const studentsCount = await Students.find({ orgid }).countAsync();
      expect(studentsCount).toEqual(9);

      const studentGroupsCount = await StudentGroups.find({
        orgid,
        isActive: true
      }).countAsync();
      expect(studentGroupsCount).toEqual(4);

      const studentGroupEnrollmentsCount = await StudentGroupEnrollments.find({
        orgid,
        isActive: true
      }).countAsync();
      expect(studentGroupEnrollmentsCount).toEqual(9);
    });

    it("should deactivate all items that are not present in the subsequent CSV roster import", async () => {
      const rosterImportItems = await getAllRosterImportItems();
      await (
        await RosterImportsProcessor.init({
          items: rosterImportItems,
          orgid,
          byDateOn,
          rosterImportId
        })
      ).process();

      await (
        await RosterImportsProcessor.init({
          items: [rosterImportItems[0]],
          orgid,
          byDateOn,
          rosterImportId: "next_rosterImportId"
        })
      ).process();

      const studentGroupsCount = await StudentGroups.find({
        orgid,
        isActive: true
      }).countAsync();
      expect(studentGroupsCount).toEqual(1);

      const inactiveStudentGroupsCount = await StudentGroups.find({
        orgid,
        isActive: false
      }).countAsync();
      expect(inactiveStudentGroupsCount).toEqual(3);

      const studentGroupEnrollmentsCount = await StudentGroupEnrollments.find({
        orgid,
        isActive: true
      }).countAsync();
      expect(studentGroupEnrollmentsCount).toEqual(1);

      const inactiveStudentGroupEnrollmentsCount = await StudentGroupEnrollments.find({
        orgid,
        isActive: false
      }).countAsync();
      expect(inactiveStudentGroupEnrollmentsCount).toEqual(8);
    });

    it("should make possible creating a teacher with more than one site access", async () => {
      const rosterImportItems = await getAllRosterImportItems();
      const teacher = getCookieGroupTeacherUploadData();
      const itemsWithSingleTeacher = [];
      rosterImportItems.forEach(item => {
        const newItem = item;
        newItem.data = Object.assign(item.data, teacher);
        itemsWithSingleTeacher.push(newItem);
      });

      await (
        await RosterImportsProcessor.init({
          items: itemsWithSingleTeacher,
          orgid,
          byDateOn,
          rosterImportId
        })
      ).process();

      const user = await Users.findOneAsync({ "profile.localId": teacher.teacherID });
      expect(user.profile.siteAccess.length).toEqual(2);
    });

    it("should allow having groups with the same properties in two sites", async () => {
      const sunnySlopeItems = await getAllSunnySlopeSiteItems();
      const modifiedSunnySlopeItems = [];
      (await getAllSunnySlopeSiteItems()).forEach(item => {
        const newItem = item;
        const updatedItemData = {
          schoolID: "99",
          schoolName: "Other School",
          studentLocalID: `${item.data.studentLocalID}0`,
          studentStateID: `${item.data.studentStateID}0`
        };
        newItem.data = Object.assign(item.data, updatedItemData);
        modifiedSunnySlopeItems.push(newItem);
      });

      await (
        await RosterImportsProcessor.init({
          items: [...sunnySlopeItems, ...modifiedSunnySlopeItems],
          orgid,
          byDateOn,
          rosterImportId
        })
      ).process();

      expect(await Sites.find().countAsync()).toEqual(2);
      expect(await StudentGroups.find().countAsync()).toEqual(2);
      const enrollmentsCursor = StudentGroupEnrollments.find();
      const groupedEnrollmentsBySite = groupBy(await enrollmentsCursor.fetchAsync(), "siteId");
      Object.values(groupedEnrollmentsBySite).forEach(siteGroups => {
        expect(siteGroups.length).toEqual(3);
      });
      expect(await enrollmentsCursor.countAsync()).toEqual(6);
    });

    it("should keep student's individual grade but use springMathGrade as student's grade ", async () => {
      const studentItem = (await getAllSunnySlopeSiteItems())[0];
      await (
        await RosterImportsProcessor.init({
          items: [studentItem],
          orgid,
          byDateOn,
          rosterImportId
        })
      ).process();

      studentItem.data.springMathGrade = "04";
      studentItem.data.classSectionID = "4F";
      studentItem.data.className = "Forest 4";
      await (
        await RosterImportsProcessor.init({
          items: [studentItem],
          orgid,
          byDateOn,
          rosterImportId: "next_file_upload_id"
        })
      ).process();

      const studentsCursor = Students.find();
      const updatedStudent = (await studentsCursor.fetchAsync())[0];
      const enrollmentsCursor = StudentGroupEnrollments.find();
      const activeEnrollment = (await enrollmentsCursor.fetchAsync()).find(e => e.isActive === true);
      expect(await studentsCursor.countAsync()).toEqual(1);
      expect(await enrollmentsCursor.countAsync()).toEqual(2);
      expect(updatedStudent.grade).toEqual("04");
      expect(activeEnrollment.grade).toEqual("04");
    });

    it("should not override documents from the previous years", async () => {
      const sunnySlopeItems = await getAllSunnySlopeSiteItems();
      const priorSchoolYear = 2018;
      const nextSchoolYear = 2019;
      await (
        await RosterImportsProcessor.init({
          items: sunnySlopeItems,
          orgid,
          byDateOn,
          rosterImportId
        })
      ).process(); // 2018
      const nextYearSunnySlopeItems = (await getAllSunnySlopeSiteItems()).map(item => {
        const newItem = item;
        newItem.data = { ...item.data, schoolYear: nextSchoolYear };
        return newItem;
      });

      await (
        await RosterImportsProcessor.init({
          items: nextYearSunnySlopeItems,
          orgid,
          byDateOn,
          rosterImportId: "next_file_upload_id"
        })
      ).process(); // 2019

      const priorYearStudentsCount = await getCount(Students, getCountQuery(priorSchoolYear));
      const nextYearStudentsCount = await getCount(Students, getCountQuery(nextSchoolYear));
      const priorYearSitesCount = await getCount(Sites, getCountQuery(priorSchoolYear));
      const nextYearSitesCount = await getCount(Sites, getCountQuery(nextSchoolYear));
      const priorYearStudentGroupsCount = await getCount(
        StudentGroups,
        getCountQuery(priorSchoolYear, { isActive: true })
      );
      const nextYearStudentGroupsCount = await getCount(
        StudentGroups,
        getCountQuery(nextSchoolYear, { isActive: true })
      );
      const priorYearEnrollmentsCount = await getCount(
        StudentGroupEnrollments,
        getCountQuery(priorSchoolYear, { isActive: true })
      );
      const nextYearEnrollmentsCount = await getCount(
        StudentGroupEnrollments,
        getCountQuery(nextSchoolYear, { isActive: true })
      );
      expect(priorYearStudentsCount).toEqual(3);
      expect(nextYearStudentsCount).toEqual(3);
      expect(priorYearStudentGroupsCount).toEqual(1);
      expect(nextYearStudentGroupsCount).toEqual(1);
      expect(priorYearEnrollmentsCount).toEqual(3);
      expect(nextYearEnrollmentsCount).toEqual(3);
      expect(priorYearSitesCount).toEqual(0); // SITE IS THE ONLY ONE THAT SHOULD GET UPDATED
      expect(nextYearSitesCount).toEqual(1);
    });
    it("should add a secondary teacher for group", async () => {
      const studentItems = [
        ...(await getAllSunnySlopeSiteItems()),
        {
          ...(
            await getAllSunnySlopeSiteItems({
              teacherData: {
                teacherID: "1234",
                teacherLastName: "Jane",
                teacherFirstName: "Doe",
                teacherEmail: "<EMAIL>"
              }
            })
          )[2]
        }
      ];
      await (
        await RosterImportsProcessor.init({
          items: studentItems,
          orgid,
          byDateOn,
          rosterImportId
        })
      ).process();

      const studentsCursor = Students.find();
      const enrollmentsCursor = StudentGroupEnrollments.find();
      const studentGroupCursor = StudentGroups.find();
      expect(await studentsCursor.countAsync()).toEqual(3);
      expect(await enrollmentsCursor.countAsync()).toEqual(3);
      expect(await studentGroupCursor.countAsync()).toEqual(1);
      expect((await studentGroupCursor.fetchAsync())[0].secondaryTeachers?.length).toEqual(1);
    });
  });

  describe("processUser", () => {
    it("adds a teacher to users collection", async () => {
      const teacherLocalId = "test_teacherLocalId";
      const teacher = {
        firstName: "First",
        lastName: "Last",
        email: "<EMAIL>"
      };

      await rosterImportsProcessor.processUser({
        teacherLocalId,
        siteIds: [siteId],
        teacher
      });

      const newUser = await Users.findOneAsync({
        "profile.orgid": orgid,
        "profile.localId": teacherLocalId,
        "profile.name.first": teacher.firstName,
        "profile.name.last": teacher.lastName,
        "profile.siteAccess.siteId": siteId,
        "profile.siteAccess.schoolYear": schoolYear,
        "emails.address": teacher.email
      });
      expect(newUser).toBeTruthy();
    });
  });

  describe("processSite", () => {
    afterEach(async () => {
      await Sites.removeAsync({});
    });

    it("should add a new site if given site is a new site", async () => {
      const newSite = {
        id: "222",
        name: "Example Elementary",
        grades: new Set(["K", "1", "2", "8"]),
        isHighSchool: false
      };

      await rosterImportsProcessor.processSite(newSite);

      const newSiteDocument = await Sites.findOneAsync({
        orgid,
        schoolYear,
        stateInformation: {
          districtNumber: "111",
          districtName,
          schoolNumber: "222",
          localSchoolNumber: "222"
        },
        name: "Example Elementary",
        grades: ["K", "1", "2", "8"],
        isVisible: true,
        rosterImportId,
        isHighSchool: false
      });
      expect(newSiteDocument).toBeTruthy();
    });

    it("should modify an existing site if the site was changed", async () => {
      const timestampInfo = await getTimestampInfo("spec", orgid);
      await Sites.insertAsync({
        _id: "test_id",
        orgid,
        schoolYear,
        stateInformation: {
          districtNumber: "111",
          districtName,
          schoolNumber: "222",
          localSchoolNumber: "222"
        },
        name: "Example Elementary",
        grades: ["K", "1", "2", "8"],
        created: timestampInfo,
        lastModified: timestampInfo,
        isVisible: true,
        rosterImportId: "old_rosterImportId"
      });
      const siteUpdates = {
        id: "222",
        name: "Example High School",
        grades: new Set(["6", "7", "8"])
      };

      await rosterImportsProcessor.processSite(siteUpdates);

      const modifiedSite = await Sites.findOneAsync({ _id: "test_id" });
      expect(modifiedSite.name).toEqual("Example Elementary");
      expect(modifiedSite.grades).toEqual(["6", "7", "8"]);
      expect(modifiedSite.rosterImportId).toEqual("test_rosterImportId");
      expect(modifiedSite.lastModified.by).toEqual("db_mock");
    });

    it("should not modify rosterImportId and other site data if there are no changes to the site", async () => {
      await Sites.insertAsync({
        _id: "test_id",
        orgid,
        schoolYear,
        stateInformation: {
          districtNumber: "111",
          districtName,
          schoolNumber: "222",
          localSchoolNumber: "222"
        },
        name: "Example Elementary",
        grades: ["K", "1", "2", "8"],
        created: {},
        lastModified: {},
        isVisible: true,
        rosterImportId: "old_rosterImportId",
        isHighSchool: false
      });
      const sameSite = {
        id: "222",
        name: "Example Elementary",
        grades: new Set(["K", "1", "2", "8"])
      };

      await rosterImportsProcessor.processSite(sameSite);

      const unmodifiedSite = await Sites.findOneAsync({ _id: "test_id" });
      expect(unmodifiedSite.name).toEqual("Example Elementary");
      expect(unmodifiedSite.grades).toEqual(["K", "1", "2", "8"]);
      expect(unmodifiedSite.rosterImportId).toEqual("old_rosterImportId");
    });
  });

  describe("processStudent", () => {
    afterEach(async () => {
      await Students.removeAsync({});
    });

    it("should add a new student if a given student is a new student", async () => {
      const birthDate = new Date("2010-01-01");
      const newStudent = {
        identity: {
          identification: {
            localId: "12",
            stateId: "123456789"
          },
          name: {
            firstName: "Student",
            lastName: "Fortest",
            middleName: ""
          }
        },
        grade: "01",
        demographic: {
          birthDate,
          ethnicity: "",
          gender: "M",
          gt: "",
          sped: "",
          ell: "",
          title: "",
          birthDateTimeStamp: birthDate.getTime()
        }
      };

      await rosterImportsProcessor.processStudent(newStudent);

      const newStudentDocument = await Students.findOneAsync({
        orgid,
        schoolYear,
        districtNumber: districtID,
        rosterImportId,
        identity: newStudent.identity,
        demographic: newStudent.demographic
      });
      expect(newStudentDocument).toBeTruthy();
    });

    it("should modify an existing student if the student was changed", async () => {
      const birthDate = new Date("2010-01-01");
      const studentId = await Students.insertAsync(await getStudent());
      const updatedStudent = {
        identity: {
          identification: {
            localId: "7078576",
            stateId: "6495769"
          },
          name: {
            firstName: "Student",
            lastName: "Fortest"
          }
        },
        grade: "01",
        demographic: {
          birthDate,
          birthDateTimeStamp: birthDate.getTime()
        }
      };

      await rosterImportsProcessor.processStudent(updatedStudent);

      const updatedStudentDocument = await Students.findOneAsync({ _id: studentId });
      expect(updatedStudentDocument.identity.name.firstName).toEqual("Student");
      expect(updatedStudentDocument.grade).toEqual("01");
      expect(updatedStudentDocument.rosterImportId).toEqual("test_rosterImportId");
      expect(updatedStudentDocument.lastModified.by).toEqual("db_mock");
    });

    it("should not modify rosterImportId and other student data if there are no changes to the student", async () => {
      const student = await getStudent();
      const studentId = await Students.insertAsync(student);
      const sameStudent = {
        identity: {
          identification: {
            localId: "7078576",
            stateId: "6495769"
          },
          name: {
            firstName: "Student",
            lastName: "Fortest"
          }
        },
        grade: "K",
        demographic: {
          birthDate: "",
          birthDateTimeStamp: null
        }
      };

      await rosterImportsProcessor.processStudent(sameStudent);

      const unmodifiedStudentDocument = await Students.findOneAsync({ _id: studentId });
      expect(unmodifiedStudentDocument.rosterImportId).toEqual("someRosterImportsId");
    });
  });

  describe("processStudentGroup", () => {
    const teacherId = "test_teacherId";

    afterEach(async () => {
      await StudentGroups.removeAsync({});
    });

    it("should add a new student group if a given student group is a new student group", async () => {
      const newStudentGroup = {
        name: "StudentGroup Name",
        grade: "03",
        sectionId: "test_sectionId"
      };

      await rosterImportsProcessor.processStudentGroup({
        studentGroup: newStudentGroup,
        siteId,
        teacherId
      });

      const newStudentGroupDocument = await StudentGroups.findOneAsync({
        orgid,
        schoolYear,
        type: "CLASS",
        ownerIds: [teacherId],
        name: "StudentGroup Name",
        grade: "03",
        sectionId: "test_sectionId"
      });
      expect(newStudentGroupDocument).toBeTruthy();
    });

    it("should modify an existing student group if the student group name was changed and grade stayed the same", async () => {
      const groupId = await StudentGroups.insertAsync(await getStudentGroup({ rosterImportId }));
      const updatedTeacherId = "updated_teacherId";
      const updatedStudentGroup = {
        name: "Different StudentGroup Name",
        grade: "03",
        sectionId: "test_sectionId"
      };

      await rosterImportsProcessor.processStudentGroup({
        studentGroup: updatedStudentGroup,
        siteId,
        teacherId: updatedTeacherId
      });

      const updatedStudentGroupDocument = await StudentGroups.findOneAsync({
        _id: groupId
      });
      expect(updatedStudentGroupDocument.name).toEqual("Different StudentGroup Name");
      expect(updatedStudentGroupDocument.rosterImportId).toEqual("test_rosterImportId");
      expect(updatedStudentGroupDocument.ownerIds).toEqual([updatedTeacherId]);
      expect(updatedStudentGroupDocument.lastModified.by).toEqual("db_mock");
    });

    it("should create a new student group if the student group grade changed", async () => {
      await StudentGroups.insertAsync(await getStudentGroup({ rosterImportId }));
      const updatedStudentGroup = {
        name: "StudentGroup Name",
        grade: "04",
        sectionId: "test_sectionId"
      };

      await rosterImportsProcessor.processStudentGroup({
        studentGroup: updatedStudentGroup,
        siteId
      });

      const query = {
        grade: updatedStudentGroup.grade,
        sectionId: updatedStudentGroup.sectionId
      };

      const updatedStudentGroupDocument = await StudentGroups.findOneAsync(query);
      expect(updatedStudentGroupDocument.name).toEqual("StudentGroup Name");
      expect(updatedStudentGroupDocument.grade).toEqual("04");
    });

    it("should not modify rosterImportId and other student group data if there are no changes to the student group", async () => {
      const groupId = await StudentGroups.insertAsync(await getStudentGroup({ rosterImportId }));

      const sameStudentGroup = {
        name: "StudentGroup Name",
        grade: "03",
        sectionId: "test_sectionId"
      };

      await rosterImportsProcessor.processStudentGroup({
        studentGroup: sameStudentGroup,
        siteId,
        teacherId
      });

      const unmodifiedStudentGroupDocument = await StudentGroups.findOneAsync({
        _id: groupId
      });
      expect(unmodifiedStudentGroupDocument.rosterImportId).toEqual("test_rosterImportId");
    });
  });

  describe("processStudentGroupEnrollment", () => {
    const studentGroupId = "test_studentGroupId";
    const studentId = "test_studentId";
    const defaultStudentGroupEnrollment = {
      grade: "02",
      giftedAndTalented: "GT",
      ELL: "1",
      title1: "Y",
      freeReducedLunch: "Free"
    };

    afterEach(async () => {
      await StudentGroupEnrollments.removeAsync({});
    });

    it(
      "should add a new student group enrollment if a given student group enrollment is a new student group enrollment " +
        "and deactivate previous student group enrollments for this student",
      async () => {
        const timestampInfo = await getTimestampInfo("spec", orgid);
        const existingEnrollment = {
          _id: "test_enrollment_id",
          orgid,
          rosterImportId: "old_rosterImportId",
          siteId,
          grade: "02",
          studentGroupId,
          studentId,
          isActive: true,
          schoolYear,
          giftedAndTalented: false,
          ELL: false,
          IEP: false,
          title1: "",
          freeReducedLunch: "",
          created: timestampInfo,
          lastModified: timestampInfo
        };
        await StudentGroupEnrollments.insertAsync(existingEnrollment);

        await rosterImportsProcessor.processStudentGroupEnrollment({
          studentId,
          studentGroupEnrollment: defaultStudentGroupEnrollment,
          siteId,
          studentGroupId: "newStudentGroupId"
        });

        const existingEnrollmentDocument = await StudentGroupEnrollments.findOneAsync({
          _id: "test_enrollment_id"
        });
        const newEnrollmentDocument = await StudentGroupEnrollments.findOneAsync({
          orgid,
          rosterImportId: "test_rosterImportId",
          siteId,
          studentId,
          schoolYear,
          isActive: true,
          "lastModified.by": "db_mock"
        });
        expect(existingEnrollmentDocument.isActive).toEqual(false);
        expect(existingEnrollmentDocument.rosterImportId).toEqual("test_rosterImportId");
        expect(newEnrollmentDocument).toBeTruthy();
      }
    );

    it("should modify a student group enrollment if student group enrollment is changed", async () => {
      const timestampInfo = await getTimestampInfo("spec", orgid);
      const studentGroupEnrollment = {
        _id: "test_enrollment_id",
        orgid,
        rosterImportId: "old_rosterImportId",
        siteId,
        grade: "02",
        studentGroupId,
        studentId,
        isActive: true,
        schoolYear: 2018,
        giftedAndTalented: false,
        ELL: false,
        IEP: false,
        title1: "",
        freeReducedLunch: "",
        created: timestampInfo,
        lastModified: timestampInfo
      };
      await StudentGroupEnrollments.insertAsync(studentGroupEnrollment);

      await rosterImportsProcessor.processStudentGroupEnrollment({
        studentId,
        studentGroupEnrollment: {
          ...defaultStudentGroupEnrollment,
          grade: "03"
        },
        siteId,
        studentGroupId
      });

      const newEnrollmentDocument = await StudentGroupEnrollments.findOneAsync({
        _id: "test_enrollment_id"
      });
      expect(newEnrollmentDocument.isActive).toEqual(true);
      expect(newEnrollmentDocument.grade).toEqual("03");
      expect(newEnrollmentDocument.rosterImportId).toEqual("test_rosterImportId");
    });
  });

  describe("deactivateStudentGroups", () => {
    afterEach(async () => {
      await StudentGroups.removeAsync({});
    });

    it("should deactivate student groups that are not active anymore", async () => {
      const timestampInfo = await getTimestampInfo("spec", "test_orgid");
      const groupToDeactivate1 = {
        orgid: "test_orgid",
        rosterImportId: "old_rosterImportId",
        type: "CLASS",
        schoolYear: 2018,
        created: timestampInfo,
        lastModified: timestampInfo,
        isActive: true,
        siteId: "test_siteId",
        ownerIds: ["test_teacherId"],
        name: "StudentGroup Name",
        grade: "03",
        sectionId: "test_sectionId",
        _id: "first_group_id"
      };
      const groupToDeactivate2 = {
        ...groupToDeactivate1,
        _id: "second_group_id",
        grade: "04",
        name: "Other Group Name"
      };
      const groupToKeep = { ...groupToDeactivate1, _id: "third_group_id", grade: "05", name: "Other Group Name" };
      await StudentGroups.insertAsync([groupToDeactivate1, groupToDeactivate2, groupToKeep]);

      await rosterImportsProcessor.deactivateStudentGroups(["third_group_id"]);

      const firstDeactivatedGroupDocument = await StudentGroups.findOneAsync({
        _id: "first_group_id"
      });
      expect(firstDeactivatedGroupDocument.isActive).toEqual(false);
      expect(firstDeactivatedGroupDocument.rosterImportId).toEqual("test_rosterImportId");

      const secondDeactivatedGroupDocument = await StudentGroups.findOneAsync({
        _id: "second_group_id"
      });
      expect(secondDeactivatedGroupDocument.isActive).toEqual(false);
      expect(secondDeactivatedGroupDocument.rosterImportId).toEqual("test_rosterImportId");

      const keptGroupDocument = await StudentGroups.findOneAsync({
        _id: "third_group_id"
      });
      expect(keptGroupDocument.isActive).toEqual(true);
      expect(keptGroupDocument.rosterImportId).toEqual("old_rosterImportId");
    });
  });

  describe("deactivateStudentGroupEnrollments", () => {
    afterEach(async () => {
      await StudentGroupEnrollments.removeAsync({});
    });

    it("should deactivate student group enrollments that are not active anymore", async () => {
      const timestampInfo = await getTimestampInfo("spec", "test_orgid");
      const enrollmentToDeactivate1 = {
        orgid: "test_orgid",
        rosterImportId: "old_rosterImportId",
        schoolYear: 2018,
        siteId: "test_siteId",
        grade: "03",
        studentGroupId: "first_studentGroupId",
        studentId: "first_studentId",
        isActive: true,
        giftedAndTalented: null,
        ELL: null,
        IEP: null,
        title1: "",
        freeReducedLunch: null,
        created: timestampInfo,
        lastModified: timestampInfo,
        _id: "first_groupEnrollment_id"
      };
      const enrollmentToDeactivate2 = {
        ...enrollmentToDeactivate1,
        _id: "second_groupEnrollment_id",
        studentGroupId: "second_studentGroupId",
        studentId: "second_studentId"
      };
      const enrollmentToKeep = {
        ...enrollmentToDeactivate1,
        _id: "third_groupEnrollment_id",
        studentGroupId: "third_studentGroupId",
        studentId: "third_studentId"
      };
      await StudentGroupEnrollments.insertAsync([enrollmentToDeactivate1, enrollmentToDeactivate2, enrollmentToKeep]);

      await rosterImportsProcessor.deactivateStudentGroupEnrollments(["third_studentId"]);

      const firstDeactivatedEnrollmentDocument = await StudentGroupEnrollments.findOneAsync({
        _id: "first_groupEnrollment_id"
      });
      expect(firstDeactivatedEnrollmentDocument.isActive).toEqual(false);
      expect(firstDeactivatedEnrollmentDocument.rosterImportId).toEqual("test_rosterImportId");

      const secondDeactivatedEnrollmentDocument = await StudentGroupEnrollments.findOneAsync({
        _id: "second_groupEnrollment_id"
      });
      expect(secondDeactivatedEnrollmentDocument.isActive).toEqual(false);
      expect(secondDeactivatedEnrollmentDocument.rosterImportId).toEqual("test_rosterImportId");

      const keptEnrollmentDocument = await StudentGroupEnrollments.findOneAsync({
        _id: "third_groupEnrollment_id"
      });
      expect(keptEnrollmentDocument.isActive).toEqual(true);
      expect(keptEnrollmentDocument.rosterImportId).toEqual("old_rosterImportId");
    });
  });

  describe("getSetToUpdateDocumentAndLastModifiedData", () => {
    it("should return properly formatted set object", () => {
      const newDocument = {
        schoolYear: 2017,
        name: "Some Site Name",
        grades: ["0", "1", "2"],
        stateInformation: {
          districtName: "Some District Name"
        }
      };
      const addedOption = {
        isVisible: true
      };

      const result = rosterImportsProcessor.getSetToUpdateDocumentAndLastModifiedData(newDocument, addedOption);

      expect(result).toEqual({
        $set: {
          schoolYear: 2017,
          name: "Some Site Name",
          grades: ["0", "1", "2"],
          "stateInformation.districtName": "Some District Name",
          isVisible: true,
          rosterImportId: "test_rosterImportId",
          lastModified: byDateOn
        }
      });
    });
  });

  describe("manageStudentsScores", () => {
    describe("for students coming from a group in classwide intervention", () => {
      afterEach(async () => {
        await clearCollections();
      });

      it("should remove students from the active classwide intervention when they are moved to a new group", async () => {
        const rosterImportItems = await setupDb(byDateOn, rosterImportId);
        const previousGroup = await TestGroup.init("cookie");
        await previousGroup.addActiveClasswideIntervention();

        const updatedRosterImportsItems = moveStudentItemsBetweenGroups({
          from: "cookie",
          to: "golden",
          rosterImportItems
        });

        await (
          await RosterImportsProcessor.init({
            items: updatedRosterImportsItems,
            orgid,
            byDateOn,
            rosterImportId: "next_rosterImportId"
          })
        ).process();

        const nextGroup = await TestGroup.init("golden");
        const nextGroupCurrentScreening = await nextGroup.getCurrentScreening();
        // should not open any new assessments when moving students to a new group
        expect(nextGroupCurrentScreening).toBeUndefined();
        expect(await nextGroup.getCurrentAssessmentResultIds()).toBeUndefined();

        const previousGroupClasswideInt = await previousGroup.getCurrentClasswideIntervention();
        const hasMovedStudentScores = getNumberOfStudentScores(previousGroupClasswideInt, previousGroup.studentIds);
        expect(hasMovedStudentScores).toBeFalsy();
      });

      it("should attach students to a new group's classwide intervention if the new group has a classwide intervention in progress", async () => {
        const rosterImportItems = await setupDb(byDateOn, rosterImportId);
        // Add active classwide intervention to both groups
        const previousGroup = await (await TestGroup.init("cookie")).addActiveClasswideIntervention();
        const nextGroup = await (await TestGroup.init("golden")).addActiveClasswideIntervention();

        const updatedRosterImportsItems = moveStudentItemsBetweenGroups({
          from: "cookie",
          to: "golden",
          rosterImportItems
        });

        await (
          await RosterImportsProcessor.init({
            items: updatedRosterImportsItems,
            orgid,
            byDateOn,
            rosterImportId: "next_rosterImportId"
          })
        ).process();

        // expect that old groups current classwide skill will have scores of moved student removed
        const previousGroupClasswideInt = await previousGroup.getCurrentClasswideIntervention();
        const hasMovedStudentScores = getNumberOfStudentScores(previousGroupClasswideInt, previousGroup.studentIds);
        expect(hasMovedStudentScores).toBeFalsy();

        // expect that a new group will have scores of new student in current classwide skill
        const nextGroupGroupClasswideInt = await nextGroup.getCurrentClasswideIntervention();
        const hasNewStudentsScores = getNumberOfStudentScores(nextGroupGroupClasswideInt, nextGroup.studentIds);
        expect(hasNewStudentsScores).toBeTruthy();
      });

      it("should attach the students to a new group's benchmark screening if there is a benchmark screening in progress", async () => {
        const rosterImportItems = await setupDb(byDateOn, rosterImportId);

        // add active classwide intervention to previous group and active screening to new group
        const previousGroup = await (await TestGroup.init("cookie")).addActiveClasswideIntervention();
        const nextGroup = await (await TestGroup.init("golden")).addActiveScreening();

        const updatedRosterImportsItems = moveStudentItemsBetweenGroups({
          from: "cookie",
          to: "golden",
          rosterImportItems
        });

        await (
          await RosterImportsProcessor.init({
            items: updatedRosterImportsItems,
            orgid,
            byDateOn,
            rosterImportId: "next_rosterImportId"
          })
        ).process();

        const nextGroupCurrentScreening = await nextGroup.getCurrentScreening();
        const newStudentsScoresLength = getNumberOfStudentScores(nextGroupCurrentScreening, previousGroup.studentIds);
        expect(newStudentsScoresLength).toEqual(4); // 2 students and there are 2 assessments for 1 student = 4
      });
    });

    describe("for students in individual intervention", () => {
      afterEach(async () => {
        await clearCollections();
      });
      it("should add the student's active individual intervention to a new group's current assessments", async () => {
        const rosterImportItems = await setupDb(byDateOn, rosterImportId);
        const previousGroup = await (await TestGroup.init("cookie")).addIndividualIntervention();
        const nextGroup = await (await TestGroup.init("golden")).addIndividualIntervention();

        const updatedRosterImportsItems = moveStudentItemsBetweenGroups({
          from: "cookie",
          to: "golden",
          rosterImportItems
        });

        await (
          await RosterImportsProcessor.init({
            items: updatedRosterImportsItems,
            orgid,
            byDateOn,
            rosterImportId: "next_rosterImportId"
          })
        ).process();

        // Check if individual intervention was added to currentAssessments list
        const nextGroupCurrentAssessmentResultIds = await nextGroup.getCurrentAssessmentResultIds();
        const previousGroupCurrentAssessmentResultIds = await previousGroup.getCurrentAssessmentResultIds();
        expect(nextGroupCurrentAssessmentResultIds.length).toEqual(4);
        expect(previousGroupCurrentAssessmentResultIds.length).toEqual(0);

        // Check if individual intervention assessments changed their studentGroupId to new group's id
        const updatedIndividualAssessments = await AssessmentResults.find({
          studentId: { $in: previousGroup.studentIds }
        }).fetchAsync();
        expect(updatedIndividualAssessments.every(ass => ass.studentGroupId === nextGroup.studentGroupId)).toBeTruthy();
      });
      it("should add the individual interventions of previously archived students to a new group's current assessments", async () => {
        const rosterImportItems = await setupDb(byDateOn, rosterImportId);
        const studentGroup = await TestGroup.init("cookie");
        await studentGroup.addIndividualInterventionsToGroupAndStudents({
          idsOfStudentsInIntervention: studentGroup.studentIds,
          previousAssessmentResultId: ""
        });

        expect((await studentGroup.getCurrentAssessmentResultIds()).length).toEqual(2);

        await studentGroup.archive();

        expect((await studentGroup.getCurrentAssessmentResultIds()).length).toEqual(0);

        await (
          await RosterImportsProcessor.init({
            items: rosterImportItems,
            orgid,
            byDateOn,
            rosterImportId: "next_rosterImportId"
          })
        ).process();

        const individualAssessmentResults = await AssessmentResults.find({
          studentId: { $in: studentGroup.studentIds }
        }).fetchAsync();
        const activeIndividualInterventionIds = (await studentGroup.getActiveIndividualInterventions()).map(
          ({ _id }) => _id
        );
        expect((await studentGroup.getCurrentAssessmentResultIds()).length).toEqual(2);
        expect((await studentGroup.getActiveIndividualInterventions()).length).toEqual(2);
        expect(
          individualAssessmentResults.every(ass => activeIndividualInterventionIds.includes(ass._id))
        ).toBeTruthy();
      });
      it("should discontinue the student's active individual intervention if the student is moved to a different grade", async () => {
        const rosterImportItems = await setupDb(byDateOn, rosterImportId);
        const previousGroup = await (await TestGroup.init("cookie")).addIndividualIntervention();
        const nextGroup = await TestGroup.init("next"); // next group is of different grade than cookie group
        await nextGroup.addCompletedScreening();

        const updatedRosterImportsItems = moveStudentItemsBetweenGroups({
          from: "cookie",
          to: "next",
          rosterImportItems
        });

        await (
          await RosterImportsProcessor.init({
            items: updatedRosterImportsItems,
            orgid,
            byDateOn,
            rosterImportId: "next_rosterImportId"
          })
        ).process();

        expect((await previousGroup.getCurrentAssessmentResultIds()).length).toEqual(0);
        expect(await nextGroup.getCurrentAssessmentResultIds()).toBeUndefined();
        const studentsWithRemovedIndividualIntervention = await Students.find({
          _id: { $in: previousGroup.studentIds }
        }).fetchAsync();
        const isCurrentSkillRemoved = studentsWithRemovedIndividualIntervention.every(student => !student.currentSkill);
        expect(isCurrentSkillRemoved).toBeTruthy();
        const openIndividualAssessments = await AssessmentResults.find({
          studentId: { $in: previousGroup.studentIds },
          status: "OPEN"
        }).fetchAsync();
        expect(openIndividualAssessments.length).toBeFalsy();
        const activeEnrollmentsOfMovedStudents = await StudentGroupEnrollments.find({
          studentId: { $in: previousGroup.studentIds },
          isActive: true
        }).fetchAsync();
        expect(
          activeEnrollmentsOfMovedStudents.every(enrollment => enrollment.wasIndividualInterventionClosed === true)
        ).toBeTruthy();
      });
    });

    describe("for new and removed students and for students coming from groups without any active assessments", () => {
      const cookieGroupStudentIndex = 6;
      const numberOfStudentsToRemove = 1;
      afterEach(async () => {
        await clearCollections();
      });
      it("should not open any new assessments when students are moved from a group with complete assessments to a new group", async () => {
        const rosterImportItems = await setupDb(byDateOn, rosterImportId);
        const previousGroup = await TestGroup.init("cookie");
        await previousGroup.addCompletedScreening();

        const updatedRosterImportsItems = moveStudentItemsBetweenGroups({
          from: "cookie",
          to: "golden",
          rosterImportItems
        });

        await (
          await RosterImportsProcessor.init({
            items: updatedRosterImportsItems,
            orgid,
            byDateOn,
            rosterImportId: "next_rosterImportId"
          })
        ).process();

        const nextGroup = await TestGroup.init("golden");
        expect(await nextGroup.getCurrentScreening()).toBeUndefined();
        expect(await nextGroup.getNumberOfAssessments()).toBe(0);
      });

      it("should add moved students to the active screening assessment of the new group without moving their scores", async () => {
        const rosterImportItems = await setupDb(byDateOn, rosterImportId);
        const previousGroup = await TestGroup.init("cookie");
        await previousGroup.addCompletedScreening();
        const nextGroup = await (await TestGroup.init("golden")).addActiveScreening();

        const updatedRosterImportsItems = moveStudentItemsBetweenGroups({
          from: "cookie",
          to: "golden",
          rosterImportItems
        });

        await (
          await RosterImportsProcessor.init({
            items: updatedRosterImportsItems,
            orgid,
            byDateOn,
            rosterImportId: "next_rosterImportId"
          })
        ).process();

        expect((await nextGroup.getCurrentAssessmentResultIds()).length).toEqual(1);
        const benchmarkAssessmentOfNextGroup = await nextGroup.getCurrentScreening();
        expect(benchmarkAssessmentOfNextGroup.scores.length).toEqual(8); // 2 assessments scores for 4 students = 8

        const numberOfCompletedScores = benchmarkAssessmentOfNextGroup.scores.filter(
          score => score.status === "COMPLETE"
        ).length;
        expect(numberOfCompletedScores).toEqual(0);
      });

      it("should attach the moved students without any screening scores to the active screening assessment of the new group", async () => {
        const rosterImportItems = await setupDb(byDateOn, rosterImportId);
        const nextGroup = await (await TestGroup.init("golden")).addActiveScreening();

        const updatedRosterImportsItems = moveStudentItemsBetweenGroups({
          from: "cookie",
          to: "golden",
          rosterImportItems
        });

        await (
          await RosterImportsProcessor.init({
            items: updatedRosterImportsItems,
            orgid,
            byDateOn,
            rosterImportId: "next_rosterImportId"
          })
        ).process();

        expect((await nextGroup.getCurrentAssessmentResultIds()).length).toEqual(1);
        const benchmarkAssessmentOfNextGroup = await nextGroup.getCurrentScreening();
        expect(benchmarkAssessmentOfNextGroup.scores.length).toEqual(8); // 2 assessments scores for 4 students = 8
        expect(areAllScoresStarted(benchmarkAssessmentOfNextGroup)).toEqual(true);
      });

      it("should attach the new students to the active screening assessment of the new group", async () => {
        const rosterImportItems = await setupDb(byDateOn, rosterImportId);
        const newGroup = await (await TestGroup.init("cookie")).addActiveScreening();

        const updatedRosterImportsItems = getUpdatedItemsWithAddedEnrollments({
          to: "cookie",
          rosterImportItems
        });

        await (
          await RosterImportsProcessor.init({
            items: updatedRosterImportsItems,
            orgid,
            byDateOn,
            rosterImportId: "next_rosterImportId"
          })
        ).process();

        expect((await newGroup.getCurrentAssessmentResultIds()).length).toEqual(1);
        const benchmarkAssessmentOfNextGroup = await newGroup.getCurrentScreening();
        expect(benchmarkAssessmentOfNextGroup.scores.length).toEqual(8); // 2 assessments scores for 4 students = 8
        expect(areAllScoresStarted(benchmarkAssessmentOfNextGroup)).toEqual(true);
      });

      it("should attach the new students to active classwide intervention of a new group", async () => {
        const rosterImportItems = await setupDb(byDateOn, rosterImportId);
        const newGroup = await (await TestGroup.init("cookie")).addActiveClasswideIntervention();

        const updatedRosterImportsItems = getUpdatedItemsWithAddedEnrollments({
          to: "cookie",
          rosterImportItems
        });

        await (
          await RosterImportsProcessor.init({
            items: updatedRosterImportsItems,
            orgid,
            byDateOn,
            rosterImportId: "next_rosterImportId"
          })
        ).process();

        expect((await newGroup.getCurrentClasswideIntervention()).scores.length).toEqual(4); // 1 classwide assessments score for 4 students = 4
      });

      it("should attach the new students to all active assessments of a new group", async () => {
        const rosterImportItems = await setupDb(byDateOn, rosterImportId);
        const newGroup = await TestGroup.init("cookie");
        await newGroup.addActiveClasswideIntervention();
        await newGroup.addActiveScreening();

        const updatedRosterImportsItems = getUpdatedItemsWithAddedEnrollments({
          to: "cookie",
          rosterImportItems
        });

        await (
          await RosterImportsProcessor.init({
            items: updatedRosterImportsItems,
            orgid,
            byDateOn,
            rosterImportId: "next_rosterImportId"
          })
        ).process();

        expect((await newGroup.getCurrentAssessmentResultIds()).length).toEqual(2);
        expect((await newGroup.getCurrentClasswideIntervention()).scores.length).toEqual(4); // 1 classwide assessments score for 4 students = 4
        expect((await newGroup.getCurrentScreening()).scores.length).toEqual(8); // 2 assessments scores for 4 students = 8
      });

      it("should remove the individual assessments of removed students from current group assessments", async () => {
        const rosterImportItems = await setupDb(byDateOn, rosterImportId);
        const currentGroup = await (await TestGroup.init("cookie")).addIndividualIntervention();

        rosterImportItems.splice(cookieGroupStudentIndex, numberOfStudentsToRemove); // removes one student from cookie group

        await (
          await RosterImportsProcessor.init({
            items: rosterImportItems,
            orgid,
            byDateOn,
            rosterImportId: "next_rosterImportId"
          })
        ).process();

        expect((await currentGroup.getCurrentAssessmentResultIds()).length).toEqual(1); // one of two students was removed
        const activeInterventions = await currentGroup.getActiveIndividualInterventions();
        expect(activeInterventions.length).toEqual(1);
        const studentInIntervention = await StudentGroupEnrollments.findOneAsync({
          studentId: activeInterventions[0].studentId
        });
        expect(studentInIntervention.isActive).toBe(true); // active intervention belongs to actively enrolled student
      });

      it("should remove the removed students from active screening assessment", async () => {
        const rosterImportItems = await setupDb(byDateOn, rosterImportId);
        const currentGroup = await (await TestGroup.init("cookie")).addActiveScreening();
        rosterImportItems.splice(cookieGroupStudentIndex, numberOfStudentsToRemove); // removes a student from cookie group

        await (
          await RosterImportsProcessor.init({
            items: rosterImportItems,
            orgid,
            byDateOn,
            rosterImportId: "next_rosterImportId"
          })
        ).process();

        expect((await currentGroup.getCurrentAssessmentResultIds()).length).toEqual(1);
        expect((await currentGroup.getCurrentScreening()).scores.length).toEqual(2); // 2 assessments of removed student were removed
      });

      it("should remove the removed students from active classwide assessment", async () => {
        const rosterImportItems = await setupDb(byDateOn, rosterImportId);
        const currentGroup = await (await TestGroup.init("cookie")).addActiveClasswideIntervention();
        rosterImportItems.splice(cookieGroupStudentIndex, numberOfStudentsToRemove); // removes a student from cookie group

        await (
          await RosterImportsProcessor.init({
            items: rosterImportItems,
            orgid,
            byDateOn,
            rosterImportId: "next_rosterImportId"
          })
        ).process();

        expect((await currentGroup.getCurrentAssessmentResultIds()).length).toEqual(1);
        expect((await currentGroup.getCurrentClasswideIntervention()).scores.length).toEqual(1); // 1 classwide assessments score was removed
      });

      it("should move the individual intervention eligibility of students to a new group", async () => {
        const rosterImportItems = await setupDb(byDateOn, rosterImportId);
        const previousGroup = await TestGroup.init("cookie");
        await previousGroup.addCompletedScreening({
          studentIdsNotMeetingTarget: previousGroup.studentIds
        });
        const nextGroup = await TestGroup.init("golden");
        await nextGroup.addCompletedScreening();

        const updatedRosterImportsItems = moveStudentItemsBetweenGroups({
          from: "cookie",
          to: "golden",
          rosterImportItems
        });

        await (
          await RosterImportsProcessor.init({
            items: updatedRosterImportsItems,
            orgid,
            byDateOn,
            rosterImportId: "next_rosterImportId"
          })
        ).process();

        const previousGroupIndividualInterventionQueue = await previousGroup.getIndividualInterventionEligibility();
        expect(previousGroupIndividualInterventionQueue.length).toEqual(0); // has eligibility been removed from an old group

        const nextGroupIndividualInterventionQueue = await nextGroup.getIndividualInterventionEligibility();
        expect(nextGroupIndividualInterventionQueue).toEqual(previousGroup.studentIds); // has eligibility been moved to a new group
      });
    });
  });

  describe("manageGroupScores", () => {
    describe("for groups with started benchmark that change grade", () => {
      it("should create a new group and archive the previous one", async () => {
        const rosterImportItems = await setupDb(byDateOn, rosterImportId);
        const rosterImportItemsWithChangedGrade = rosterImportItems.map(item => {
          const itemToBeUpdated = { ...item };
          if (itemToBeUpdated.data.className === "Cookie 3") {
            itemToBeUpdated.data.springMathGrade = "06";
          }
          return itemToBeUpdated;
        });

        await (
          await RosterImportsProcessor.init({
            items: rosterImportItemsWithChangedGrade,
            orgid,
            byDateOn,
            rosterImportId: "next_rosterImportId"
          })
        ).process();

        const newStudentGroup = await StudentGroups.findOneAsync({ name: "Cookie 3 (3)", grade: "06" });
        expect(newStudentGroup.currentAssessmentResultIds).toBeUndefined();
      });
    });
  });
});
