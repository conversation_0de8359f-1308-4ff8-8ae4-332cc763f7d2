import React from "react";
import { render } from "@testing-library/react";
import MergeDataModal from "./merge-data-modal";
import "@testing-library/jest-dom";

describe("MergeDataModal", () => {
  const orgid = "someOrgId";

  function generateStateInformation(schoolNumber) {
    return {
      districtNumber: orgid,
      schoolNumber,
      localSchoolNumber: schoolNumber
    };
  }

  function generateSite({ schoolNumber, name }) {
    return {
      _id: `siteId${schoolNumber}`,
      stateInformation: { schoolNumber },
      name: name || `Site ${schoolNumber}`
    };
  }

  function generateStudentGroup({ schoolNumber, sectionId }) {
    return {
      _id: `studentGroupId${sectionId}`,
      siteId: `siteId${schoolNumber}`,
      name: `Student Group (${sectionId})`,
      sectionId,
      classSectionId: sectionId
    };
  }

  function generateRosteringItem(schoolNumber, sectionIds = []) {
    return {
      stateInformation: generateStateInformation(schoolNumber),
      schoolName: `Site ${schoolNumber}`,
      studentGroups: sectionIds.map(sectionId => generateStudentGroup({ schoolNumber, sectionId }))
    };
  }

  const componentData = {
    headerText: "Select Schools/Classes to merge",
    confirmText: "Merge",
    cancelText: "Cancel",
    onCloseModal: () => {},
    showModal: true,
    confirmAction: () => {},
    orgid
  };
  it("should render modal", () => {
    const props = {
      rosteringData: {
        1: generateRosteringItem("1", ["11", "12", "14"]),
        2: generateRosteringItem("2", ["21", "22", "24"])
      },
      existingSites: [
        generateSite({ schoolNumber: "1", name: "Existing Site 1" }),
        generateSite({ schoolNumber: "2", name: "Existing Site 2" })
      ],
      existingStudentGroupsBySiteId: {
        siteId1: [
          generateStudentGroup({ schoolNumber: "1", sectionId: "11" }),
          generateStudentGroup({ schoolNumber: "1", sectionId: "12" }),
          generateStudentGroup({ schoolNumber: "1", sectionId: "13" })
        ],
        siteId2: [
          generateStudentGroup({ schoolNumber: "2", sectionId: "21" }),
          generateStudentGroup({ schoolNumber: "2", sectionId: "22" }),
          generateStudentGroup({ schoolNumber: "2", sectionId: "23" })
        ]
      }
    };
    const { getAllByTestId, getByText } = render(<MergeDataModal {...componentData} {...props} />);
    expect(getByText("Select Schools/Classes to merge")).toBeVisible();
    expect(getByText("Merge", { exact: true })).toBeVisible();
    expect(getByText("Cancel")).toBeVisible();
    expect(getAllByTestId("site-select-", { exact: false })).toHaveLength(2);
    expect(getAllByTestId("student-group-select-", { exact: false })).toHaveLength(6);
  });
});
