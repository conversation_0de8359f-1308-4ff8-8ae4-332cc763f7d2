import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";
import { parseBoolean, shouldUseDevMode } from "./utilities/utilities";

import { BenchmarkPeriods } from "./benchmarkPeriods/benchmarkPeriods";
import { Interventions } from "./interventions/interventions";
import { Roles } from "./roles/roles";
import { Grades } from "./grades/grades";

const extractSystemEnv = keys => {
  return keys.reduce((envs, envProperty) => {
    const envValue = process.env[envProperty] || "";
    // eslint-disable-next-line no-param-reassign
    envs[envProperty] = ["true", "false"].includes(envValue.toLowerCase()) ? parseBoolean(envValue) : envValue;
    return envs;
  }, {});
};
// TODO(fmazur) - load environment variables to local storage on client startup
Meteor.methods({
  getEnvironmentVariables(keys) {
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    check(keys, Array);

    return extractSystemEnv(keys);
  },
  getMeteorPrivateSettings(keys) {
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    check(keys, Array);

    return keys.reduce((settings, settingKey) => {
      const settingValue = Meteor.settings[settingKey] || "";
      settings[settingKey] = ["true", "false"].includes(settingValue.toLowerCase())
        ? parseBoolean(settingValue)
        : settingValue;
      return settings;
    }, {});
  },
  async "StaticData:getStaticData"() {
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    const env = extractSystemEnv(["CI", "METEOR_ENVIRONMENT"]);

    // TODO(fmazur) - parallel Promise.all settled
    return {
      // rules: await Rules.find({}, { fields: { name: 1, rootRuleId: 1, attributeValues: 1, outcomes: 1 } }).fetchAsync(),
      // TODO(fmazur) - separate into separate call and fetch when needed
      rules: [], // TODO(fmazur) - this can be 1500 documents, do we need to always fetch this?
      roleDefinitions: await Roles.find({}, { fields: { _id: 1, name: 1 } }).fetchAsync(),
      news: [],
      grades: await Grades.find({}, { sort: { sortorder: 1 } }).fetchAsync(),
      instructionalVideos: [],
      settings: [],
      interventions: await Interventions.find({}).fetchAsync(),
      groupedAssessments: [],
      benchmarkPeriods: await BenchmarkPeriods.find(
        { name: { $exists: true } },
        {
          sort: { sortOrder: 1 },
          fields: {
            name: 1,
            label: 1,
            sortOrder: 1,
            startDate: 1,
            endDate: 1
          }
        }
      ).fetchAsync(),
      env,
      isDevMode: shouldUseDevMode(env.CI, env.METEOR_ENVIRONMENT)
    };
  }
});
