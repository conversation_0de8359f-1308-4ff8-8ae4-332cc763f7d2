import { getBenchmarkPeriodToIdMap, getGrowthResults, getParsedGrowthResultsForChart } from "./utilities";
import {
  assessmentComparisonMap,
  expectedChartDataForLowClasswideScore,
  expectedChartDataForMultipleGroupsGrowth,
  expectedChartDataForMultipleGroupsWithIncompleteGrowth,
  expectedChartDataForOverInstructionalClasswideResults,
  expectedChartDataForValidResults,
  expectedNoScoreChartData,
  fakeAssessmentsCollection,
  groupHistory,
  lowClasswideScoreGrowthResult,
  multipleGroupsGrowthResults,
  multipleGroupsWithIncompleteGrowthResults,
  noScoresGrowthResult,
  overInstructionalClasswideGrowthResults,
  validScoresGrowthResult,
  simpleComparison,
  historyWithInstructionalTargetVal,
  commonGroupHistory1,
  commonGroupHistory2,
  commonGroupHistory3
} from "../../test-helpers/data/growthData";

const fallPeriodId = "8S52Gz5o85hRkECgq";
const winterPeriodId = "nEsbWokBWutTZFkTh";
const springPeriodId = "cjCMnZKARBJmG8suT";
const fallPeriodName = "fall";
const winterPeriodName = "winter";
const springPeriodName = "spring";

describe("utilities", () => {
  describe("getBenchmarkPeriodToIdMap", () => {
    it("should return a map between benchmark period names and ids", () => {
      const bmPeriods = [
        { _id: fallPeriodId, name: fallPeriodName },
        { _id: winterPeriodId, name: winterPeriodName },
        { _id: springPeriodId, name: springPeriodName }
      ];
      const expectedObject = {
        fall: fallPeriodId,
        winter: winterPeriodId,
        spring: springPeriodId
      };

      const result = getBenchmarkPeriodToIdMap(bmPeriods);

      expect(result).toMatchObject(expectedObject);
    });

    it("should return empty object when no benchmark periods provided", () => {
      const result = getBenchmarkPeriodToIdMap();
      expect(result).toEqual({});
    });

    it("should return empty object when empty array provided", () => {
      const result = getBenchmarkPeriodToIdMap([]);
      expect(result).toEqual({});
    });
  });

  describe("getGrowthResults", () => {
    const mockBenchmarkPeriods = [
      { _id: fallPeriodId, name: fallPeriodName },
      { _id: winterPeriodId, name: winterPeriodName },
      { _id: springPeriodId, name: springPeriodName }
    ];
    it("should return false when benchmark periods are not available", () => {
      expect(getGrowthResults({ assessmentComparisonMap: {}, assessments: [], bmPeriods: [] })).toBeFalsy();
    });
    it("should throw an error when some of the compared assessments cannot be found in available assessments list", () => {
      const unavailableAssessmentId = "non-existent-assessment-id";
      const comparison = {
        fallToWinter: [
          {
            fall: "yxBaWDvbNjBJLcaHQ",
            classwide: unavailableAssessmentId
          }
        ],
        winterToSpring: []
      };
      const assessments = [{ _id: "yxBaWDvbNjBJLcaHQ", name: "Assessment Name" }];

      expect(() =>
        getGrowthResults({
          assessmentComparisonMap: comparison,
          assessments,
          bmPeriods: mockBenchmarkPeriods
        })
      ).toThrow(unavailableAssessmentId);
    });
    it("should return comparison map with score and totalStudentsAssessed values as null if group history is empty/non-existant", () => {
      const result = getGrowthResults({
        history: undefined,
        assessmentComparisonMap,
        assessments: fakeAssessmentsCollection,
        bmPeriods: mockBenchmarkPeriods
      });

      expect(result).toMatchObject(noScoresGrowthResult);
    });
    it("should return the data and the categories for each comparison and period no matter whether it contains valid scores", () => {
      const result = getGrowthResults({
        history: groupHistory,
        assessmentComparisonMap,
        assessments: fakeAssessmentsCollection,
        bmPeriods: mockBenchmarkPeriods
      });

      expect(result).toMatchObject(validScoresGrowthResult);
    });
    it(
      "should display N/A text if compared classwide item is was not mastered by group (percentMeetingTarget is below 50%) " +
        "or when the classwide or benchmark item is not present in the class history",
      () => {
        const failedClasswideHistory = [
          {
            assessmentId: "FxYDJWbMdT8QiZwcD",
            benchmarkPeriodId: winterPeriodId,
            assessmentResultMeasures: [
              {
                assessmentId: "FxYDJWbMdT8QiZwcD",
                percentMeetingTarget: 49, // below 50 is failing
                benchmarkPeriodId: winterPeriodId,
                targetScores: [20, 50, 250]
              }
            ],
            enrolledStudentIds: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11"],
            type: "classwide"
          }
        ];

        const result = getGrowthResults({
          history: failedClasswideHistory,
          assessmentComparisonMap: simpleComparison,
          assessments: fakeAssessmentsCollection,
          bmPeriods: mockBenchmarkPeriods
        });

        expect(result).toMatchObject(lowClasswideScoreGrowthResult);
      }
    );
    it("should display the % of students above the instructional target when displaying classwide scores", () => {
      const result = getGrowthResults({
        history: historyWithInstructionalTargetVal,
        assessmentComparisonMap: simpleComparison,
        assessments: fakeAssessmentsCollection,
        bmPeriods: mockBenchmarkPeriods
      });

      expect(result).toMatchObject(overInstructionalClasswideGrowthResults);
    });

    describe("for all groups within a grade", () => {
      it("should calculate an average growth when all groups have relevant history", () => {
        const result = getGrowthResults({
          history: [...commonGroupHistory1, ...commonGroupHistory2, ...commonGroupHistory3],
          assessmentComparisonMap: simpleComparison,
          assessments: fakeAssessmentsCollection,
          bmPeriods: mockBenchmarkPeriods
        });

        expect(result).toMatchObject(multipleGroupsGrowthResults);
      });
      it("should calculate an average growth when not every group has relevant history", async () => {
        const historyWithMissingBenchmarkAndFailedClasswide = [
          {
            assessmentId: "FxYDJWbMdT8QiZwcD", // failed classwide history should not be considered
            benchmarkPeriodId: winterPeriodId,
            assessmentResultMeasures: [
              {
                assessmentId: "FxYDJWbMdT8QiZwcD",
                percentMeetingTarget: 40, // below 50 is failing
                numberMeetingTarget: 4,
                totalStudentsAssessed: 20,
                benchmarkPeriodId: winterPeriodId,
                studentScores: [10, 10, 10, 10, 10, 10, 50, 50, 50, 50, 10, 10, 10, 10, 10, 10, 50, 50, 50, 50],
                targetScores: [20, 40, 300]
              }
            ],
            enrolledStudentIds: [
              "1",
              "2",
              "3",
              "4",
              "5",
              "6",
              "7",
              "8",
              "9",
              "10",
              "11",
              "12",
              "13",
              "14",
              "15",
              "16",
              "17",
              "18",
              "19",
              "20"
            ],
            type: "classwide",
            targets: [20, 40, 300]
          }
        ];

        const result = getGrowthResults({
          history: [...commonGroupHistory1, ...commonGroupHistory2, ...historyWithMissingBenchmarkAndFailedClasswide],
          assessmentComparisonMap: simpleComparison,
          assessments: fakeAssessmentsCollection,
          bmPeriods: mockBenchmarkPeriods
        });

        expect(result).toMatchObject(multipleGroupsWithIncompleteGrowthResults);
      });
    });
  });

  describe("getParsedGrowthResultsForChart", () => {
    const growthResultsToChartDataList = [
      {
        name: "No Results",
        growthResult: noScoresGrowthResult,
        expectedChartData: expectedNoScoreChartData
      },
      {
        name: "Valid Results",
        growthResult: validScoresGrowthResult,
        expectedChartData: expectedChartDataForValidResults
      },
      {
        name: "Low Classwide Results",
        growthResult: lowClasswideScoreGrowthResult,
        expectedChartData: expectedChartDataForLowClasswideScore
      },
      {
        name: "Over Instructional Classwide Results",
        growthResult: overInstructionalClasswideGrowthResults,
        expectedChartData: expectedChartDataForOverInstructionalClasswideResults
      },
      {
        name: "Multiple Groups Results",
        growthResult: multipleGroupsGrowthResults,
        expectedChartData: expectedChartDataForMultipleGroupsGrowth
      },
      {
        name: "Multiple Groups Incomplete Results",
        growthResult: multipleGroupsWithIncompleteGrowthResults,
        expectedChartData: expectedChartDataForMultipleGroupsWithIncompleteGrowth
      }
    ];
    growthResultsToChartDataList.forEach(comparison => {
      it(`should return parsed data for "${comparison.name}" scenario`, () => {
        expect(getParsedGrowthResultsForChart(comparison.growthResult)).toMatchObject(comparison.expectedChartData);
      });
    });
  });
});
