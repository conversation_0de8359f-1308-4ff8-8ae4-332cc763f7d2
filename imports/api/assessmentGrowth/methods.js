import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";

import { groupBy } from "lodash";

import { getBenchmarkAndClasswideHistory } from "./utilities";
import { StudentGroups } from "../studentGroups/studentGroups";
import * as auth from "../authorization/server/methods";
import { hasGroupPassedClasswideIntervention } from "../utilities/utilities";

export async function getHSClasswideInterventionProgress({ studentGroupIds }) {
  const result = [];
  const growthData = {};

  if (!studentGroupIds) {
    return null;
  }

  const studentGroups = await Promise.all(
    studentGroupIds.map(studentGroupId => StudentGroups.findOneAsync({ _id: studentGroupId }))
  );

  studentGroups.forEach(studentGroup => {
    if (!studentGroup?.history) {
      return;
    }

    const { classwideHistory } = getBenchmarkAndClasswideHistory(studentGroup.history);

    Object.entries(groupBy(classwideHistory, "assessmentId")).forEach(
      ([assessmentId, specificAssessmentHistoryForStudentGroup]) => {
        const finalAssessmentResultMeasure = specificAssessmentHistoryForStudentGroup[0].assessmentResultMeasures[0];
        const initialAssessmentResultMeasure =
          specificAssessmentHistoryForStudentGroup[specificAssessmentHistoryForStudentGroup.length - 1]
            .assessmentResultMeasures[0];
        const { enrolledStudentIds } = specificAssessmentHistoryForStudentGroup[0];
        const { medianScore, totalStudentsAssessed, targetScores, studentScores } = finalAssessmentResultMeasure;
        if (
          hasGroupPassedClasswideIntervention({
            medianScore,
            totalStudentsAssessed,
            targetScores,
            studentScores,
            numberOfEnrolledStudents: enrolledStudentIds.length
          })
        ) {
          if (!growthData[assessmentId]) {
            growthData[assessmentId] = [];
          }
          growthData[assessmentId].push({
            assessmentId,
            assessmentName: initialAssessmentResultMeasure.assessmentName,
            initial: {
              atOrAbove: initialAssessmentResultMeasure.percentMeetingTarget,
              totalStudentsAssessed: initialAssessmentResultMeasure.totalStudentsAssessed,
              studentResults: initialAssessmentResultMeasure.studentResults
            },
            final: {
              atOrAbove: finalAssessmentResultMeasure.percentMeetingTarget,
              totalStudentsAssessed: finalAssessmentResultMeasure.totalStudentsAssessed,
              studentResults: finalAssessmentResultMeasure.studentResults
            }
          });
        }
      }
    );
  });

  Object.entries(growthData).forEach(([assessmentId, specificAssessmentHistory]) => {
    const uniqStudents = { initial: [], final: [] };
    const assessmentStats = specificAssessmentHistory.reduce(
      (acc, current) => {
        const uniqInitialStudentResults = current.initial.studentResults.filter(
          studentResult => !uniqStudents.initial.includes(studentResult.studentId)
        );
        const uniqFinalStudentResults = current.final.studentResults.filter(
          studentResult => !uniqStudents.final.includes(studentResult.studentId)
        );

        const initialAtOrAboveForUniqStudents = uniqInitialStudentResults.reduce(
          (total, x) => (x.individualRuleOutcome !== "below" ? total + 1 : total),
          0
        );
        const finalAtOrAboveForUniqStudents = uniqFinalStudentResults.reduce(
          (total, x) => (x.individualRuleOutcome !== "below" ? total + 1 : total),
          0
        );

        uniqStudents.initial.push(...uniqInitialStudentResults.map(studentResult => studentResult.studentId));
        uniqStudents.final.push(...uniqFinalStudentResults.map(studentResult => studentResult.studentId));

        return {
          assessmentId,
          assessmentName: current.assessmentName,
          initial: {
            numberOfStudentsAtOrAbove: acc.initial.numberOfStudentsAtOrAbove + initialAtOrAboveForUniqStudents,
            totalStudentsAssessed: uniqStudents.initial.length
          },
          final: {
            numberOfStudentsAtOrAbove: acc.final.numberOfStudentsAtOrAbove + finalAtOrAboveForUniqStudents,
            totalStudentsAssessed: uniqStudents.final.length
          }
        };
      },
      {
        assessmentId,
        initial: {
          numberOfStudentsAtOrAbove: 0,
          totalStudentsAssessed: 0
        },
        final: {
          numberOfStudentsAtOrAbove: 0,
          totalStudentsAssessed: 0
        }
      }
    );
    result.push({
      assessmentId: assessmentStats.assessmentId,
      assessmentName: assessmentStats.assessmentName,
      initial: {
        atOrAbove: Math.round(
          (assessmentStats.initial.numberOfStudentsAtOrAbove / assessmentStats.initial.totalStudentsAssessed) * 100
        ),
        totalStudentsAssessed: assessmentStats.initial.totalStudentsAssessed
      },
      final: {
        atOrAbove: Math.round(
          (assessmentStats.final.numberOfStudentsAtOrAbove / assessmentStats.final.totalStudentsAssessed) * 100
        ),
        totalStudentsAssessed: assessmentStats.final.totalStudentsAssessed
      }
    });
  });
  return result;
}

Meteor.methods({
  async "Growth:getHSClasswideInterventionProgress"({ orgid, siteId, studentGroupIds }) {
    check(studentGroupIds, Array.of(String));
    check(orgid, String);

    const { userId } = this;
    if (!userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    if (
      !(await auth.hasAccess(["teacher", "admin", "universalCoach", "support"], {
        userId,
        siteId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "User is not authorized to use Growth:getHSClasswideInterventionProgress");
    }
    try {
      const result = await getHSClasswideInterventionProgress({ studentGroupIds });
      return result.reverse();
    } catch (e) {
      throw new Meteor.Error(e.error);
    }
  }
});
