import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";
import { StudentGroups } from "../studentGroups/studentGroups";
import { getHSClasswideInterventionProgress } from "./methods";
import addStudentGroupWithClasswideIntervention from "../studentGroups/helpers";
import { AssessmentResults } from "../assessmentResults/assessmentResults";
import { studentGroupWithBenchmarkHistory } from "../../test-helpers/data/studentGroupWithHistory";
import { buildHistory } from "../../test-helpers/data/fourWeekRuleDataGenerators";

describe("methods", () => {
  const studentGroupId = "testHSStudentGroupId";
  const assessmentId = "testAssessmentId";
  const assessmentResultId = "testAssessmentResultId";
  const nextAssessmentId = "nextAssessmentId";

  const addStudentGroupWithClasswideInterventionParams = {
    assessmentId,
    nextAssessmentId,
    assessmentResultId,
    studentGroupId
  };
  describe("getHSClasswideInterventionProgress", () => {
    afterEach(async () => {
      await StudentGroups.removeAsync({});
      await StudentGroupEnrollments.removeAsync({});
      await AssessmentResults.removeAsync({});
    });
    it(`returns empty array when there is no history provided`, async () => {
      await StudentGroups.insertAsync({ _id: studentGroupId, history: [] });
      expect(await getHSClasswideInterventionProgress({ studentGroupIds: [studentGroupId] })).toEqual([]);
    });

    it(`returns correct results when history includes classwide assessments`, async () => {
      await addStudentGroupWithClasswideIntervention({
        isFailing: false,
        ...addStudentGroupWithClasswideInterventionParams
      });
      expect(await getHSClasswideInterventionProgress({ studentGroupIds: [studentGroupId] })).toEqual([
        {
          assessmentId,
          assessmentName: "assessmentName_testAssessmentId",
          initial: { atOrAbove: 67, totalStudentsAssessed: 3 },
          final: { atOrAbove: 67, totalStudentsAssessed: 3 }
        }
      ]);
    });

    it(`returns empty array when student group has only benchmark results`, async () => {
      await StudentGroups.insertAsync(studentGroupWithBenchmarkHistory);
      expect(
        await getHSClasswideInterventionProgress({
          studentGroupIds: [studentGroupWithBenchmarkHistory._id]
        })
      ).toEqual([]);
    });

    it(`returns correct array when student group has unfinished classwide interventions`, async () => {
      await addStudentGroupWithClasswideIntervention({
        isFailing: true,
        ...addStudentGroupWithClasswideInterventionParams
      });
      expect(
        await getHSClasswideInterventionProgress({
          studentGroupIds: [studentGroupId]
        })
      ).toEqual([]);
    });

    it("returns correct stats for multiple student groups", async () => {
      const studentGroupId2 = "studentGroupId2";

      const history = buildHistory({
        numberOfWeeks: 4,
        failingIndexes: [2],
        numberOfStudents: 4,
        assessments: ["123", "234", "140", "908"]
      });

      const history2 = buildHistory({
        numberOfWeeks: 3,
        failingIndexes: [2],
        numberOfStudents: 6,
        assessments: ["123", "234", "140"],
        studentIdPostfix: "B"
      });

      await addStudentGroupWithClasswideIntervention({
        isFailing: true,
        ...addStudentGroupWithClasswideInterventionParams
      });
      await StudentGroups.updateAsync({ _id: studentGroupId }, { $set: { history } });

      await addStudentGroupWithClasswideIntervention({
        isFailing: true,
        ...addStudentGroupWithClasswideInterventionParams,
        studentGroupId: studentGroupId2,
        assessmentResultId: "assessmentResultId2",
        currentAssessmentResultId: "currentAssessmentResultId2"
      });
      await StudentGroups.updateAsync({ _id: studentGroupId2 }, { $set: { history: history2 } });

      expect(
        await getHSClasswideInterventionProgress({
          studentGroupIds: [studentGroupId, studentGroupId2]
        })
      ).toEqual([
        {
          assessmentId: "123",
          assessmentName: "assessmentName_123",
          final: {
            atOrAbove: 80,
            totalStudentsAssessed: 10
          },
          initial: {
            atOrAbove: 80,
            totalStudentsAssessed: 10
          }
        },
        {
          assessmentId: "140",
          assessmentName: "assessmentName_140",
          final: {
            atOrAbove: 80,
            totalStudentsAssessed: 10
          },
          initial: {
            atOrAbove: 80,
            totalStudentsAssessed: 10
          }
        },
        {
          assessmentId: "234",
          assessmentName: "assessmentName_234",
          final: {
            atOrAbove: 80,
            totalStudentsAssessed: 10
          },
          initial: {
            atOrAbove: 80,
            totalStudentsAssessed: 10
          }
        },
        {
          assessmentId: "908",
          assessmentName: "assessmentName_908",
          final: {
            atOrAbove: 75,
            totalStudentsAssessed: 4
          },
          initial: {
            atOrAbove: 75,
            totalStudentsAssessed: 4
          }
        }
      ]);
    });
  });
});
