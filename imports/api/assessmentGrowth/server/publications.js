import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";
import { AssessmentGrowth } from "../assessmentGrowth";
import { isUserLoggedOut } from "../../utilities/utilities";

Meteor.publish("AssessmentGrowth", function(grade) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(grade, String);

  const query = grade === "all" ? {} : { grade };
  return AssessmentGrowth.find(query, {
    fields: { grade: 1, winterToSpring: 1, fallToWinter: 1, programEvaluation: 1 }
  });
});
