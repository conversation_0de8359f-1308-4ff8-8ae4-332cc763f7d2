import { Mongo } from "meteor/mongo";
import SimpleSchema from "simpl-schema";

export const GroupedAssessments = new Mongo.Collection("GroupedAssessments");

GroupedAssessments.schema = new SimpleSchema({
  _id: { type: String, optional: true },
  skillName: { type: String },
  assessmentMeasures: [String]
});

GroupedAssessments.validate = ga => {
  GroupedAssessments.schema.validate(ga);
};
GroupedAssessments.isValid = ga => {
  return GroupedAssessments.schema.namedContext("checkGroupedAssessments").validate(ga);
};
