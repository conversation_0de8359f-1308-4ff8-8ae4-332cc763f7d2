import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";
import { ScreeningAssignments } from "../screeningAssignments";
import { StudentGroups } from "../../studentGroups/studentGroups";
import BenchmarkPeriodHelpers from "../../benchmarkPeriods/methods";
import { isUserLoggedOut } from "../../utilities/utilities";

const fields = { grade: 1, benchmarkPeriodId: 1, assessmentIds: 1 };
Meteor.publish("ScreeningAssignments", function screeningAssignmentsPublication() {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }

  return ScreeningAssignments.find({}, { fields });
});

Meteor.publish("ScreeningAssignmentsByGrade", function(grade) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(grade, String);

  return ScreeningAssignments.find({ grade }, { fields });
});

Meteor.publish("ScreeningAssignmentsByGroupIdAndCurrentPeriod", async function screeningAssignmentsPublication(
  studentGroupId,
  orgid
) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(studentGroupId, String);
  check(orgid, String);

  const { grade } = await StudentGroups.findOneAsync({ _id: studentGroupId });
  const currentPeriod = await BenchmarkPeriodHelpers.getBenchmarkPeriodByDate({ orgid });
  const currentPeriodId = currentPeriod._id;
  // This should only return one document
  return ScreeningAssignments.find({ grade, benchmarkPeriodId: currentPeriodId }, { fields });
});
