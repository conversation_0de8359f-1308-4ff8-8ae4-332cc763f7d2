import { Meteor } from "meteor/meteor";
import { assert } from "chai";

import { ScreeningAssignments } from "./screeningAssignments.js";
import { screeningAssignment } from "../../test-helpers/data/screeningAssignments.js";

if (Meteor.isServer) {
  describe("ScreeningAssignments", () => {
    describe("Winter", () => {
      describe("Should pass schema validation method", () => {
        it("validate", () => {
          assert.isUndefined(ScreeningAssignments.validate(screeningAssignment()));
        });
        it("isValid", () => {
          assert.isTrue(ScreeningAssignments.isValid(screeningAssignment()));
        });
      });
      describe("Should fail schema validation method", () => {
        it("isValid", () => {
          const screeningAssignmentDoc = screeningAssignment();
          screeningAssignmentDoc._id = 1234;
          assert.isFalse(ScreeningAssignments.isValid(screeningAssignmentDoc));
        });
      });
    });
    // TODO: add tests for other seasons and grades
    // describe('Spring', () => {
    //   describe('Should pass schema validation method', () => {
    //     it('validate', () => {
    //       assert.isUndefined(ScreeningAssignments.validate(screeningAssignment()));
    //     });
    //     it('isValid', () => {
    //       assert.isTrue(ScreeningAssignments.isValid(screeningAssignment()));
    //     });
    //   });
    //   describe('Should fail schema validation method', () => {
    //     it('isValid', () => {
    //       const screeningAssignmentDoc = screeningAssignment();
    //       screeningAssignmentDoc._id = 1234;
    //       assert.isFalse(ScreeningAssignments.isValid(screeningAssignmentDoc));
    //     });
    //   });
    // });
    //
    // describe('Fall', () => {
    //   describe('Should pass schema validation method', () => {
    //     it('validate', () => {
    //       assert.isUndefined(ScreeningAssignments.validate(screeningAssignment()));
    //     });
    //     it('isValid', () => {
    //       assert.isTrue(ScreeningAssignments.isValid(screeningAssignment()));
    //     });
    //   });
    //   describe('Should fail schema validation method', () => {
    //     it('isValid', () => {
    //       const screeningAssignmentDoc = screeningAssignment();
    //       screeningAssignmentDoc._id = 1234;
    //       assert.isFalse(ScreeningAssignments.isValid(screeningAssignmentDoc));
    //     });
    //   });
    // });
  });
}
