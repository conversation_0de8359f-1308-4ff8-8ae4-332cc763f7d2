import { Mongo } from "meteor/mongo";
import SimpleSchema from "simpl-schema";
import { ByDateOn } from "../helpers/schemas/byDateOn/byDateOn.js";
import { idValidation, validateSchemaProperty } from "../utilities/utilities";

export const Students = new Mongo.Collection("Students");

Students.schema = new SimpleSchema({
  _id: { type: String, optional: true },
  orgid: { type: String },
  created: { type: ByDateOn },
  districtNumber: { type: String },
  grade: { type: String },
  studentGrade: { type: String, optional: true },
  schoolYear: { type: Number },
  lastModified: { type: ByDateOn },
  comments: { type: Array, optional: true },
  "comments.$": { type: String, optional: true },
  demographic: { type: Object },
  "demographic.gender": { type: String },
  "demographic.ethnicity": { type: String },
  "demographic.birthDate": { type: String, optional: true },
  "demographic.birthDateTimeStamp": { type: Number, optional: true },
  "demographic.gt": { type: String, optional: true },
  "demographic.sped": { type: String, optional: true },
  "demographic.ell": { type: String, optional: true },
  "demographic.title": { type: String, optional: true },
  identity: { type: Object },
  "identity.name": { type: Object },
  "identity.name.firstName": { type: String },
  "identity.name.lastName": { type: String },
  "identity.name.middleName": { type: String, optional: true },
  "identity.name.generationCodeOrSuffix": { type: String, optional: true },
  "identity.identification": { type: Object },
  "identity.identification.localId": {
    type: String,
    min: 1,
    custom: validateSchemaProperty(idValidation.regex, "invalidStudentLocalID")
  },
  "identity.identification.stateId": {
    type: String,
    min: 1,
    custom: validateSchemaProperty(idValidation.regex, "invalidStudentStateID")
  },
  rosterImportId: { type: String },
  status: { type: String, optional: true }
});

Students.validate = student => {
  Students.schema.validate(student);
};
Students.isValid = student => Students.schema.namedContext("checkStudent").validate(student);

Students.schema.messageBox.messages({
  en: {
    invalidStudentLocalID: `Invalid Student Local ID format - ${idValidation.description}`,
    invalidStudentStateID: `Invalid Student State ID format - ${idValidation.description}`
  }
});
