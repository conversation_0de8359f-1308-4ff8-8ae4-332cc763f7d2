import { Meteor } from "meteor/meteor";
import { assert } from "chai";

import { getCurrentEnrolledGrade } from "./utils.js";
import { getStudent } from "../../test-helpers/data/students.js";

jest.mock("../utilities/utilities", () => ({
  ...jest.requireActual("../utilities/utilities"),
  idValidation: { regex: /.*/, description: "" }
}));

if (Meteor.isClient) {
  describe("Student Utility Functions", () => {
    describe("getCurrentEnrolledGrade", () => {
      it("Should return 03", () => {
        assert.equal(getCurrentEnrolledGrade(getStudent().grade), "3rd Grade");
      });
    });
  });
}
