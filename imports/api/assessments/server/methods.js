import path from "path";
import { Meteor } from "meteor/meteor";
import { check, Match } from "meteor/check";
import {
  countBy,
  difference,
  each,
  get,
  groupBy,
  intersection,
  intersectionBy,
  isArray,
  isNumber,
  keyBy,
  map,
  mapValues,
  mean,
  mergeWith,
  omitBy,
  orderBy,
  set,
  sortBy,
  sumBy
} from "lodash";
import moment from "moment";
import { BenchmarkPeriods } from "../../benchmarkPeriods/benchmarkPeriods";
import { Grades } from "../../grades/grades";
import { Assessments } from "../assessments";
import { isHighSchoolGrade } from "../../../ui/utilities";
import { AssessmentResults } from "../../assessmentResults/assessmentResults";
import { AssessmentScoresUpload } from "../../assessmentScoresUpload/assessmentScoresUpload";
import { hasDashboard } from "../../users/server/methods";
import {
  getCurrentSchoolYear,
  getMeteorUser,
  getOrganizationIdsForState,
  getOrganizationIdsWithStates,
  roundTo
} from "../../utilities/utilities";
import { StudentGroupEnrollments } from "../../studentGroupEnrollments/studentGroupEnrollments";
import { normalizeGrade } from "../../utilities/sortingHelpers/normalizeSortItem";
import { StudentGroups } from "../../studentGroups/studentGroups";
import { AssessmentGrowth } from "../../assessmentGrowth/assessmentGrowth";
import { ScreeningAssignments } from "../../screeningAssignments/screeningAssignments";
import * as auth from "../../authorization/server/methods";
import { GroupedAssessments } from "../../groupedAssessments/groupedAssessments";
import fetchVideoSkillList from "../../utilities/server/s3-bucket";

const mongoQueryStringForTargets = "strands.0.scores.0.targets";

async function getAssessmentTargets(assessmentId) {
  const assessmentDocument = await Assessments.findOneAsync(assessmentId);
  const getQueryStringForTargets = "strands[0].scores[0].targets";
  return get(assessmentDocument, getQueryStringForTargets);
}

function generateNewTarget({
  assessmentTargets,
  grade,
  targetName,
  targets: [instructionalTarget = null, masteryTarget = null] = []
}) {
  const gradeTargetPlaceholder = assessmentTargets.find(at => at.grade === grade);
  const newTarget = {
    ...gradeTargetPlaceholder,
    periods: gradeTargetPlaceholder.periods.map(periodScore => {
      const values =
        instructionalTarget && masteryTarget
          ? [instructionalTarget, masteryTarget, periodScore.values[2] || 300]
          : periodScore.values;
      return {
        ...periodScore,
        values
      };
    }),
    assessmentType: targetName
  };
  if (targetName === "default") {
    delete newTarget.assessmentType;
  }
  return newTarget;
}

function getTarget({ assessmentTargets, grade, targetName }) {
  return assessmentTargets.find(target => target.grade === grade && target.assessmentType === targetName);
}

async function addNewTarget({ newTarget, assessmentId }) {
  return Assessments.updateAsync(assessmentId, {
    $push: {
      [mongoQueryStringForTargets]: {
        $each: [newTarget],
        $sort: { grade: 1 }
      }
    }
  });
}

export async function addTargetToAssessment({ assessmentId, targetName, grade }) {
  const assessmentTargets = await getAssessmentTargets(assessmentId);
  if (getTarget({ assessmentTargets, grade, targetName })) {
    return null;
  }
  const newTarget = generateNewTarget({ assessmentTargets, grade, targetName });
  return addNewTarget({ newTarget, assessmentId });
}

function verifyUpdatedTargetsPeriods(updatedTargets, availablePeriods) {
  Object.keys(updatedTargets)
    .map(t => t)
    .forEach(targetType => {
      const setPeriods = Object.getOwnPropertyNames(updatedTargets[targetType]);
      const unMatchingPeriods = difference(setPeriods, availablePeriods);
      if (unMatchingPeriods.length) {
        throw new Meteor.Error("updateTargets", `Unknown benchmark periods found: ${unMatchingPeriods.join(", ")}`);
      }
      setPeriods.forEach(periodName => {
        Object.values(updatedTargets[targetType][periodName]).forEach(value => {
          if (!isNumber(value) || value <= 0) {
            throw new Meteor.Error("updateTargets", "Please make sure all the provided targets are positive numbers");
          }
        });
      });
    });
}

function getUpdatedGradeTargets(allGradeTargets, newTargets) {
  return allGradeTargets.map(currentGradeTarget => {
    const { assessmentType = "default" } = currentGradeTarget;
    const updatedTarget = newTargets[assessmentType];
    if (updatedTarget) {
      const updatedPeriods = currentGradeTarget.periods.map(periodTarget => {
        const { name: periodName } = periodTarget;
        const updatedPeriod = updatedTarget[periodName];
        const updatedValues = [
          updatedPeriod.instructionalTarget,
          updatedPeriod.masteryTarget,
          periodTarget.values[2] || 300
        ];
        return {
          ...periodTarget,
          values: updatedValues
        };
      });
      return {
        ...currentGradeTarget,
        periods: updatedPeriods
      };
    }
    return currentGradeTarget;
  });
}

async function verifyAssessmentTargets(currentGradeTargets, updatedTargets) {
  const currentlySetTargetTypes = currentGradeTargets.map(target => target.assessmentType || "default");
  const targetTypesToUpdate = Object.keys(updatedTargets).map(t => t);
  const differentTargets = difference(targetTypesToUpdate, currentlySetTargetTypes);
  if (differentTargets.length) {
    throw new Meteor.Error(
      "updateTargets",
      `The following targets has not yet been created: ${differentTargets.join(", ")}`
    );
  }
  const availablePeriods = (await BenchmarkPeriods.find({}, { fields: { name: 1 } }).fetchAsync()).map(bp => bp.name);
  verifyUpdatedTargetsPeriods(updatedTargets, availablePeriods);
}

async function updateAssessmentTargetTypeForGrade({ currentGradeTargets, newTargets, assessmentId, grade }) {
  const updatedGradeTargets = getUpdatedGradeTargets(currentGradeTargets, newTargets);
  await Assessments.updateAsync(assessmentId, {
    $pull: { [mongoQueryStringForTargets]: { grade } }
  });
  return Assessments.updateAsync(assessmentId, {
    $push: {
      [mongoQueryStringForTargets]: {
        $each: updatedGradeTargets,
        $sort: { grade: 1 }
      }
    }
  });
}

function getGradeTargets(assessmentTargets, grade) {
  return assessmentTargets.filter(target => target.grade === grade);
}

export async function updateTargets({ assessmentId, grade, targets: newTargets }) {
  const assessmentTargets = await getAssessmentTargets(assessmentId);
  const currentGradeTargets = getGradeTargets(assessmentTargets, grade);
  await verifyAssessmentTargets(currentGradeTargets, newTargets);
  return updateAssessmentTargetTypeForGrade({ currentGradeTargets, newTargets, assessmentId, grade });
}

export async function removeTargetFromAssessment({ assessmentId, targetName, grade }) {
  return Assessments.updateAsync(assessmentId, {
    $pull: {
      [mongoQueryStringForTargets]: {
        grade,
        assessmentType: targetName
      }
    }
  });
}

function getClasswideTargetsUpdate(grade, instructionalTarget, masteryTarget) {
  const targetsToUpdate = {
    classwide: {}
  };
  if (isHighSchoolGrade(grade)) {
    targetsToUpdate.classwide = {
      All: {
        instructionalTarget,
        masteryTarget
      }
    };
  } else {
    targetsToUpdate.classwide = {
      Fall: {
        instructionalTarget,
        masteryTarget
      },
      Spring: {
        instructionalTarget,
        masteryTarget
      },
      Winter: {
        instructionalTarget,
        masteryTarget
      }
    };
  }
  return targetsToUpdate;
}

export async function updateOrCreateClasswideTargets({
  assessmentId,
  gradeId: grade,
  instructionalTarget,
  masteryTarget
}) {
  const assessmentTargets = await getAssessmentTargets(assessmentId);
  const targetName = "classwide";
  const hasClasswideTarget = getTarget({ assessmentTargets, grade, targetName });
  if (!hasClasswideTarget) {
    const newTarget = generateNewTarget({
      assessmentTargets,
      grade,
      targetName,
      targets: [instructionalTarget, masteryTarget]
    });
    return addNewTarget({ newTarget, assessmentId });
  }
  const classwideTarget = getClasswideTargetsUpdate(grade, instructionalTarget, masteryTarget);
  const currentGradeTargets = getGradeTargets(assessmentTargets, grade);
  return updateAssessmentTargetTypeForGrade({
    currentGradeTargets,
    newTargets: classwideTarget,
    assessmentId,
    grade
  });
}

function getDefaultStudentCountByProblemTypeAndProficiency() {
  return {
    classwideProblem: {
      notProficient: {
        belowTarget: 0,
        total: 0
      },
      proficient: {
        belowTarget: 0,
        total: 0
      }
    },
    noClasswideProblem: {
      notProficient: {
        belowTarget: 0,
        total: 0
      },
      proficient: {
        belowTarget: 0,
        total: 0
      }
    }
  };
}

function areNewAssessmentResultMeasuresGreater(oldMeasures, newMeasures) {
  if (oldMeasures.assessmentId !== newMeasures.assessmentId) {
    return false;
  }
  const oldStudentScoresByStudentId = oldMeasures.studentResults.reduce(
    (resultsByStudentId, { studentId, score }) => ({
      ...resultsByStudentId,
      [studentId]: parseInt(score)
    }),
    {}
  );
  let numberOfGreaterStudentScores = 0;
  newMeasures.studentResults.forEach(({ studentId, score }) => {
    const oldStudentScore = oldStudentScoresByStudentId[studentId];
    if (oldStudentScore && parseInt(score) > oldStudentScore) {
      numberOfGreaterStudentScores += 1;
    }
  });

  const percentOfGreaterStudentScores = (numberOfGreaterStudentScores / newMeasures.studentResults.length) * 100;

  return percentOfGreaterStudentScores >= 80;
}

function getStudentGroupInterventionIntegrityStatsByStudentGroupId(studentGroups) {
  const weekAgoDate = moment().subtract(1, "week");
  const previousMondayDate = weekAgoDate.clone().startOf("isoWeek");
  const previousSundayDate = weekAgoDate.clone().endOf("isoWeek");

  const studentGroupStatsByStudentGroupId = {};
  studentGroups.forEach(studentGroup => {
    const hasAllClasswideSkillsCompleted = get(studentGroup, "currentClasswideSkill.message.messageCode") === "5";
    studentGroupStatsByStudentGroupId[studentGroup._id] = {
      screenedStudentIds: new Set(),
      hasAtLeast1ScoreEntry: false,
      hasAllClasswideSkillsCompleted,
      numberOfClasswideSkillsCompleted: 0,
      hasScoreEnteredForPreviousWeek: false,
      hasCompletedSkillPreviousWeek: false,
      hasScoresGreaterThanPreviousWeek: false
    };

    let firstClasswideAssessmentResultForCurrentWeek;
    let hasComparedClasswideAssessmentResultsForPreviousAndCurrentWeek = false;
    if (studentGroup.history && studentGroup.history.length) {
      studentGroup.history.forEach(historyItem => {
        if (historyItem.type === "benchmark") {
          historyItem.enrolledStudentIds.forEach(
            Set.prototype.add,
            studentGroupStatsByStudentGroupId[studentGroup._id].screenedStudentIds
          );
        } else if (historyItem.type === "classwide") {
          studentGroupStatsByStudentGroupId[studentGroup._id].hasAtLeast1ScoreEntry = true;
          const isSkillCompleted = historyItem.message.messageCode === "4";
          if (moment(historyItem.whenEnded.on).isBetween(previousMondayDate, previousSundayDate, undefined, "[]")) {
            studentGroupStatsByStudentGroupId[studentGroup._id].hasScoreEnteredForPreviousWeek = true;
            if (isSkillCompleted) {
              studentGroupStatsByStudentGroupId[studentGroup._id].hasCompletedSkillPreviousWeek = true;
            } else if (
              firstClasswideAssessmentResultForCurrentWeek &&
              !hasComparedClasswideAssessmentResultsForPreviousAndCurrentWeek
            ) {
              studentGroupStatsByStudentGroupId[
                studentGroup._id
              ].hasScoresGreaterThanPreviousWeek = areNewAssessmentResultMeasuresGreater(
                historyItem.assessmentResultMeasures[0],
                firstClasswideAssessmentResultForCurrentWeek
              );
              hasComparedClasswideAssessmentResultsForPreviousAndCurrentWeek = true;
            }
          } else if (moment(historyItem.whenEnded.on).isAfter(previousSundayDate)) {
            [firstClasswideAssessmentResultForCurrentWeek] = historyItem.assessmentResultMeasures;
          }
          if (isSkillCompleted) {
            studentGroupStatsByStudentGroupId[studentGroup._id].numberOfClasswideSkillsCompleted += 1;
          }
        }
      });
    }
  });
  return studentGroupStatsByStudentGroupId;
}

function calculateInterventionIntegrityStatsForOrgs(studentGroupEnrollments, studentGroupStatsByStudentGroupId) {
  const stats = {};
  studentGroupEnrollments.forEach(enrollment => {
    if (!stats[enrollment.orgid]) {
      stats[enrollment.orgid] = {};
    }
    if (!stats[enrollment.orgid][enrollment.siteId]) {
      stats[enrollment.orgid][enrollment.siteId] = {};
    }
    if (!stats[enrollment.orgid][enrollment.siteId][enrollment.grade]) {
      stats[enrollment.orgid][enrollment.siteId][enrollment.grade] = {
        numberOfStudents: 0,
        numberOfStudentsScreened: 0,
        numberOfClasswideSkillsCompletedMap: new Map(),
        numberOfClassesWithAtLeast1ScoreEntry: 0,
        numberOfClassesWithAllSkillsCompleted: 0
      };
    }
    stats[enrollment.orgid][enrollment.siteId][enrollment.grade].numberOfStudents += 1;
    if (studentGroupStatsByStudentGroupId[enrollment.studentGroupId]) {
      if (studentGroupStatsByStudentGroupId[enrollment.studentGroupId].screenedStudentIds.has(enrollment.studentId)) {
        stats[enrollment.orgid][enrollment.siteId][enrollment.grade].numberOfStudentsScreened += 1;
      }
      if (
        !stats[enrollment.orgid][enrollment.siteId][enrollment.grade].numberOfClasswideSkillsCompletedMap.has(
          enrollment.studentGroupId
        )
      ) {
        stats[enrollment.orgid][enrollment.siteId][enrollment.grade].numberOfClasswideSkillsCompletedMap.set(
          enrollment.studentGroupId,
          studentGroupStatsByStudentGroupId[enrollment.studentGroupId].numberOfClasswideSkillsCompleted
        );
        if (studentGroupStatsByStudentGroupId[enrollment.studentGroupId].hasAtLeast1ScoreEntry) {
          stats[enrollment.orgid][enrollment.siteId][enrollment.grade].numberOfClassesWithAtLeast1ScoreEntry += 1;
        }
        if (studentGroupStatsByStudentGroupId[enrollment.studentGroupId].hasAllClasswideSkillsCompleted) {
          stats[enrollment.orgid][enrollment.siteId][enrollment.grade].numberOfClassesWithAllSkillsCompleted += 1;
        }
      }
    }
  });
  return stats;
}

function calculateInterventionIntegrityStatsForStates(organizationsByState, stats) {
  const defaultStats = {
    numberOfStudentsEnrolledInSpringMath: "N/A",
    percentOfStudentsScreened: "N/A",
    percentOfScheduledClasswideInterventionsWithAtLeast1ScoreEntry: "N/A",
    percentOfScheduledClasswideInterventionsWithAllSkillsCompleted: "N/A",
    numberOfClasswideSkillsCompletedMedianAndRange: "N/A"
  };

  return map(organizationsByState, (organizations, stateName) => {
    let gradesInStateCount = 0;
    const statsForOrganizations = organizations.map(organization => {
      const sitesBySiteId = stats[organization._id];

      let gradesInOrganizationCount = 0;
      const sites = map(sitesBySiteId, (grades, siteId) => {
        const statsForGrades = map(
          grades,
          (
            {
              numberOfStudents,
              numberOfStudentsScreened,
              numberOfClasswideSkillsCompletedMap,
              numberOfClassesWithAtLeast1ScoreEntry,
              numberOfClassesWithAllSkillsCompleted
            },
            grade
          ) => {
            const numberOfClasswideSkillsCompleted = Array.from(numberOfClasswideSkillsCompletedMap.values());
            const numberOfSkillsCompletedMin = Math.min(...numberOfClasswideSkillsCompleted);
            const numberOfSkillsCompletedMax = Math.max(...numberOfClasswideSkillsCompleted);
            const numberOfSkillsCompletedMean = roundTo(mean(numberOfClasswideSkillsCompleted));
            const numberOfClasswideSkillsCompletedMedianAndRange = `${numberOfSkillsCompletedMean}/${numberOfSkillsCompletedMin}-${numberOfSkillsCompletedMax}`;
            const numberOfClasses = numberOfClasswideSkillsCompletedMap.size;
            const percentOfStudentsScreened = roundTo((numberOfStudentsScreened / numberOfStudents) * 100);
            const percentOfScheduledClasswideInterventionsWithAtLeast1ScoreEntry = roundTo(
              (numberOfClassesWithAtLeast1ScoreEntry / numberOfClasses) * 100
            );
            const percentOfScheduledClasswideInterventionsWithAllSkillsCompleted = roundTo(
              (numberOfClassesWithAllSkillsCompleted / numberOfClasses) * 100
            );
            return {
              grade,
              ...defaultStats,
              numberOfStudentsEnrolledInSpringMath: numberOfStudents,
              percentOfStudentsScreened,
              percentOfScheduledClasswideInterventionsWithAtLeast1ScoreEntry,
              percentOfScheduledClasswideInterventionsWithAllSkillsCompleted,
              numberOfClasswideSkillsCompletedMedianAndRange
            };
          }
        );

        gradesInOrganizationCount += statsForGrades.length;

        const sortedStatsForGrades = statsForGrades.sort((a, b) =>
          normalizeGrade(a.grade) < normalizeGrade(b.grade) ? -1 : 1
        );

        return {
          siteId,
          grades: sortedStatsForGrades
        };
      });

      gradesInStateCount += gradesInOrganizationCount;
      return {
        orgid: organization._id,
        rows: gradesInOrganizationCount,
        sites
      };
    });

    return {
      state: stateName,
      rows: gradesInStateCount,
      organizations: statsForOrganizations
    };
  });
}

async function getInterventionIntegrityStats(studentGroupEnrollments, studentGroups) {
  const foundOrganizationIds = Object.keys(groupBy(studentGroupEnrollments, "orgid"));
  const organizationsByState = groupBy(
    sortBy(await getOrganizationIdsWithStates(foundOrganizationIds), "state"),
    "state"
  );

  let numberOfClassesWithPreviousWeekScore = 0;
  let numberOfClassesWithPreviousWeekMedianScoreAtOrAboveMasteryTarget = 0;
  let numberOfClassesWithScoresGreaterThanPreviousWeek = 0;

  const studentGroupStatsByStudentGroupId = getStudentGroupInterventionIntegrityStatsByStudentGroupId(studentGroups);

  map(studentGroupStatsByStudentGroupId, studentGroupStats => {
    if (studentGroupStats.hasScoreEnteredForPreviousWeek) {
      numberOfClassesWithPreviousWeekScore += 1;
    }
    if (studentGroupStats.hasCompletedSkillPreviousWeek) {
      numberOfClassesWithPreviousWeekMedianScoreAtOrAboveMasteryTarget += 1;
    }
    if (studentGroupStats.hasScoresGreaterThanPreviousWeek) {
      numberOfClassesWithScoresGreaterThanPreviousWeek += 1;
    }
  });

  const stats = calculateInterventionIntegrityStatsForOrgs(studentGroupEnrollments, studentGroupStatsByStudentGroupId);
  const integrity = calculateInterventionIntegrityStatsForStates(organizationsByState, stats);

  return {
    integrity,
    numberOfClassesWithPreviousWeekScore,
    numberOfClassesWithPreviousWeekMedianScoreAtOrAboveMasteryTarget,
    numberOfClassesWithScoresGreaterThanPreviousWeek
  };
}

async function getInterventionIntegrity({ state, organizationId, siteId, grade }) {
  let organizationIds = organizationId ? [organizationId] : [];
  if (state && !organizationId) {
    organizationIds = await getOrganizationIdsForState(state);
    if (!organizationIds.length) {
      return {
        integrity: [],
        percentOfClassesWithPreviousWeekScore: "N/A",
        percentOfClassesWithPreviousWeekMedianScoreAtOrAboveMasteryTarget: "N/A",
        percentOfClassesWithScoresGreaterThanPreviousWeek: "N/A",
        totalNumberOfClasses: "-",
        numberOfClassesWithFewerThan10Students: "-"
      };
    }
  }

  const currentSchoolYear = await getCurrentSchoolYear(await getMeteorUser());
  const enrollmentsQuery = {
    schoolYear: currentSchoolYear,
    isActive: true,
    ...(organizationIds.length && { orgid: { $in: organizationIds } }),
    ...(siteId && { siteId }),
    ...(grade && { grade })
  };
  const studentGroupEnrollments = await StudentGroupEnrollments.find(enrollmentsQuery, {
    fields: { orgid: 1, siteId: 1, grade: 1, studentGroupId: 1, studentId: 1 },
    sort: { siteId: 1, grade: 1 }
  }).fetchAsync();

  const studentGroupIds = Object.keys(groupBy(studentGroupEnrollments, "studentGroupId"));
  const studentGroups = await StudentGroups.find(
    { _id: { $in: studentGroupIds }, schoolYear: currentSchoolYear, isActive: true },
    {
      fields: {
        orgid: 1,
        siteId: 1,
        grade: 1,
        "history.type": 1,
        "history.whenEnded.on": 1,
        "history.enrolledStudentIds": 1,
        "history.message.messageCode": 1,
        "history.assessmentResultMeasures.assessmentId": 1,
        "history.assessmentResultMeasures.studentResults.studentId": 1,
        "history.assessmentResultMeasures.studentResults.score": 1,
        "currentClasswideSkill.message.messageCode": 1
      }
    }
  ).fetchAsync();

  const {
    integrity,
    numberOfClassesWithPreviousWeekScore,
    numberOfClassesWithPreviousWeekMedianScoreAtOrAboveMasteryTarget,
    numberOfClassesWithScoresGreaterThanPreviousWeek
  } = await getInterventionIntegrityStats(studentGroupEnrollments, studentGroups);

  const numberOfStudentsByStudentGroup = countBy(studentGroupEnrollments, "studentGroupId");
  const totalNumberOfClasses = Object.keys(numberOfStudentsByStudentGroup).length;
  const numberOfClassesWithFewerThan10Students = Object.values(numberOfStudentsByStudentGroup).filter(
    numberOfStudents => numberOfStudents < 10
  ).length;
  const percentOfClassesWithPreviousWeekScore = roundTo(
    (numberOfClassesWithPreviousWeekScore / totalNumberOfClasses) * 100
  );
  const percentOfClassesWithPreviousWeekMedianScoreAtOrAboveMasteryTarget = roundTo(
    (numberOfClassesWithPreviousWeekMedianScoreAtOrAboveMasteryTarget / totalNumberOfClasses) * 100
  );
  const percentOfClassesWithScoresGreaterThanPreviousWeek = roundTo(
    (numberOfClassesWithScoresGreaterThanPreviousWeek / totalNumberOfClasses) * 100
  );

  return {
    integrity,
    percentOfClassesWithPreviousWeekScore,
    percentOfClassesWithPreviousWeekMedianScoreAtOrAboveMasteryTarget,
    percentOfClassesWithScoresGreaterThanPreviousWeek,
    totalNumberOfClasses,
    numberOfClassesWithFewerThan10Students
  };
}

async function getExternalAssessmentScoresForOrganizations(organizationIds, schoolYear) {
  return AssessmentScoresUpload.find(
    {
      "data.assessmentYear": schoolYear,
      orgid: { $in: organizationIds }
    },
    { sort: { grade: 1, "data.stateAssessmentScaleScore": 1 } }
  ).fetchAsync();
}

async function getAssessmentScoresByStudentId(organizationIds, schoolYear) {
  const assessmentScores = await AssessmentScoresUpload.find({
    "data.assessmentYear": schoolYear,
    orgid: { $in: organizationIds }
  }).fetchAsync();

  return keyBy(assessmentScores, "studentId");
}

async function getBenchmarkAndClasswideInterventionAssessmentResults({
  grade = null,
  benchmarkPeriodId = null,
  studentIds = null,
  organizationIds = [],
  schoolYear,
  shouldSort = true,
  fieldNames
}) {
  const query = {
    schoolYear,
    type: { $in: ["benchmark", "classwide"] },
    status: "COMPLETED"
  };

  if (organizationIds.length) {
    query.orgid = { $in: organizationIds };
  }
  if (grade) {
    query.grade = grade;
  }
  if (studentIds) {
    query["scores.studentId"] = { $in: studentIds };
  }
  if (benchmarkPeriodId) {
    query.benchmarkPeriodId = benchmarkPeriodId;
  }

  const options = {};

  if (shouldSort) {
    options.sort = { "created.on": 1 };
  }

  if (fieldNames) {
    const requiredFields = ["type", "studentScores", "measures"];
    fieldNames.push(...requiredFields);
    options.fields = fieldNames.reduce((fields, fieldName) => {
      // eslint-disable-next-line no-param-reassign
      fields[fieldName] = 1;
      return fields;
    }, {});
  }

  const assessmentResults = await AssessmentResults.find(query, options).fetchAsync();

  const { benchmark = [], classwide = [] } = groupBy(assessmentResults, "type");
  const validBenchmarkAssessmentResults = filterInvalidBenchmarkAssessmentResults(benchmark);
  return { benchmark: validBenchmarkAssessmentResults, classwide };
}

function getDistrictAssessmentStudentProficiency({ assessmentScoresByStudentId, studentId }) {
  return assessmentScoresByStudentId[studentId].data.districtAssessmentSpringProficient
    ? "proficient"
    : "notProficient";
}

function processBenchmarkAssessmentResults({
  benchmarkAssessmentResults,
  assessmentResultsByAssessment,
  studentIds,
  assessmentScoresByStudentId
}) {
  const benchmarkAssessmentIds = [];
  benchmarkAssessmentResults.forEach(assessmentResult => {
    /* eslint-disable no-param-reassign */
    const hasClasswideIntervention = !assessmentResult.ruleResults.passed;
    const problemType = hasClasswideIntervention ? "classwideProblem" : "noClasswideProblem";
    const idsOfStudentsMeetingAllTargets = new Set();
    const idsOfStudentsNotMeetingAllTargets = new Set();
    benchmarkAssessmentIds.push(...assessmentResult.assessmentIds);

    assessmentResult.measures.forEach(measure => {
      if (!assessmentResultsByAssessment[measure.assessmentName]) {
        assessmentResultsByAssessment[measure.assessmentName] = getDefaultStudentCountByProblemTypeAndProficiency();
      }
      measure.studentResults.forEach(({ studentId, meetsTarget }) => {
        if (!studentIds.includes(studentId)) {
          return;
        }
        const proficiency = getDistrictAssessmentStudentProficiency({ assessmentScoresByStudentId, studentId });

        if (!meetsTarget) {
          assessmentResultsByAssessment[measure.assessmentName][problemType][proficiency].belowTarget += 1;
          if (!idsOfStudentsNotMeetingAllTargets.has(studentId)) {
            idsOfStudentsNotMeetingAllTargets.add(studentId);
          }
          if (idsOfStudentsMeetingAllTargets.has(studentId)) {
            idsOfStudentsMeetingAllTargets.delete(studentId);
          }
        } else if (
          !idsOfStudentsNotMeetingAllTargets.has(studentId) &&
          !idsOfStudentsMeetingAllTargets.has(studentId)
        ) {
          idsOfStudentsMeetingAllTargets.add(studentId);
        }
        assessmentResultsByAssessment[measure.assessmentName][problemType][proficiency].total += 1;
      });
    });

    const compositeAssessmentProblem = assessmentResultsByAssessment["Composite/Any risk"][problemType];
    idsOfStudentsNotMeetingAllTargets.forEach(studentId => {
      const proficiency = getDistrictAssessmentStudentProficiency({ assessmentScoresByStudentId, studentId });
      compositeAssessmentProblem[proficiency].belowTarget += 1;
      compositeAssessmentProblem[proficiency].total += 1;
    });
    idsOfStudentsMeetingAllTargets.forEach(studentId => {
      const proficiency = getDistrictAssessmentStudentProficiency({ assessmentScoresByStudentId, studentId });
      compositeAssessmentProblem[proficiency].total += 1;
    });
  });

  return benchmarkAssessmentIds;
}

function processClasswideInterventionAssessmentResults({
  classwideInterventionAssessmentResults,
  benchmarkAssessmentIds,
  studentIds,
  assessmentScoresByStudentId,
  studentResultsByStudentId
}) {
  classwideInterventionAssessmentResults.forEach(assessmentResult => {
    /* eslint-disable no-param-reassign */
    const { assessmentId } = assessmentResult.measures[0];
    const isAssessmentFromScreening = benchmarkAssessmentIds.includes(assessmentId);

    assessmentResult.measures[0].studentResults.forEach(({ studentId, meetsTarget }) => {
      if (!studentIds.includes(studentId)) {
        return;
      }
      const proficiency = getDistrictAssessmentStudentProficiency({ assessmentScoresByStudentId, studentId });
      if (!studentResultsByStudentId[studentId]) {
        studentResultsByStudentId[studentId] = {
          proficiency,
          numberOfTimesAtRisk: 0,
          numberOfScores: 0,
          hasRetriedAnyAssessmentFromScreening: isAssessmentFromScreening,
          hasMetTargetInLatestRetriedAssessmentFromScreening: false
        };
      }
      const studentResults = studentResultsByStudentId[studentId];
      if (isAssessmentFromScreening) {
        studentResults.hasRetriedAnyAssessmentFromScreening = true;
        studentResults.hasMetTargetInLatestRetriedAssessmentFromScreening = meetsTarget;
      }
      if (!meetsTarget) {
        studentResults.numberOfTimesAtRisk += 1;
      }
      studentResults.numberOfScores += 1;
    });
  });
}

function processStudentResultsByStudentId({ studentResultsByStudentId, assessmentResultsByAssessment }) {
  const classwideInterventionRiskThreshold = -0.1;
  Object.values(studentResultsByStudentId).forEach(
    ({
      proficiency,
      numberOfTimesAtRisk,
      numberOfScores,
      hasRetriedAnyAssessmentFromScreening,
      hasMetTargetInLatestRetriedAssessmentFromScreening
    }) => {
      /* eslint-disable no-param-reassign */
      // ((Number of times at risk during classwide intervention + 1) / Number of Progress Monitoring Scores) * -1 is less than -.10
      const studentRiskMeasure = ((numberOfTimesAtRisk + 1) / numberOfScores) * -1;
      if (studentRiskMeasure < classwideInterventionRiskThreshold) {
        assessmentResultsByAssessment["Classwide risk>-.10"].classwideProblem[proficiency].belowTarget += 1;
      }
      assessmentResultsByAssessment["Classwide risk>-.10"].classwideProblem[proficiency].total += 1;
      if (hasRetriedAnyAssessmentFromScreening) {
        if (!hasMetTargetInLatestRetriedAssessmentFromScreening) {
          assessmentResultsByAssessment["Final classwide intervention score"].classwideProblem[
            proficiency
          ].belowTarget += 1;
        }
        assessmentResultsByAssessment["Final classwide intervention score"].classwideProblem[proficiency].total += 1;
      }
    }
  );
}

function calculateStandardDeviationForStudentResults({ studentResults = [], scoreMean }) {
  return Math.sqrt(sumBy(studentResults, ({ score }) => (score - scoreMean) ** 2) / studentResults.length);
}

function calculateScreeningAssessmentsStats({ assessmentName, studentResultsByStudentId, organizationIds = [] }) {
  const isCompositeScore = assessmentName === "Composite score";
  const isClasswideRisk = assessmentName === "Classwide risk";

  const studentResultsFromAllStates = Object.values(studentResultsByStudentId);
  const numberOfStudentsTestedFromAllStates = studentResultsFromAllStates.length;
  const meanFromAllStates = sumBy(studentResultsFromAllStates, "score") / numberOfStudentsTestedFromAllStates;
  const standardDeviationFromAllStates = calculateStandardDeviationForStudentResults({
    studentResults: studentResultsFromAllStates,
    scoreMean: meanFromAllStates
  });

  const studentResultsFromSelectedState = studentResultsFromAllStates.filter(sr => organizationIds.includes(sr.orgid));
  const numberOfStudentsTestedFromSelectedState = studentResultsFromSelectedState.length;
  const meanFromSelectedState =
    sumBy(studentResultsFromSelectedState, "score") / numberOfStudentsTestedFromSelectedState;
  const standardDeviationFromSelectedState = calculateStandardDeviationForStudentResults({
    studentResults: studentResultsFromSelectedState,
    scoreMean: meanFromSelectedState
  });

  let percentAtRiskFromAllStates = "";
  let percentAtRiskFromSelectedState = "";
  if (!isClasswideRisk) {
    const numberOfStudentsAtRiskFromAllStates = studentResultsFromAllStates.filter(sr =>
      isCompositeScore ? sr.isAtRisk : sr.individualRuleOutcome === "below"
    ).length;
    percentAtRiskFromAllStates = (numberOfStudentsAtRiskFromAllStates / numberOfStudentsTestedFromAllStates) * 100;

    const numberOfStudentsAtRiskFromSelectedState = studentResultsFromSelectedState.filter(sr =>
      isCompositeScore ? sr.isAtRisk : sr.individualRuleOutcome === "below"
    ).length;
    percentAtRiskFromSelectedState =
      (numberOfStudentsAtRiskFromSelectedState / numberOfStudentsTestedFromSelectedState) * 100;
  }

  return {
    numberOfStudentsTestedFromAllStates,
    meanFromAllStates,
    standardDeviationFromAllStates,
    percentAtRiskFromAllStates,
    numberOfStudentsTestedFromSelectedState,
    meanFromSelectedState,
    standardDeviationFromSelectedState,
    percentAtRiskFromSelectedState
  };
}

function getScreeningAssessmentsStats({ assessmentName, studentResultsByStudentId, organizationIds = [] }) {
  const defaultStats = {
    numberOfStudentsTestedFromAllStates: "N/A",
    meanFromAllStates: "N/A",
    standardDeviationFromAllStates: "N/A",
    percentAtRiskFromAllStates: "N/A",
    numberOfStudentsTestedFromSelectedState: "N/A",
    meanFromSelectedState: "N/A",
    standardDeviationFromSelectedState: "N/A",
    percentAtRiskFromSelectedState: "N/A"
  };

  if (!studentResultsByStudentId) {
    return defaultStats;
  }

  const stats = calculateScreeningAssessmentsStats({ assessmentName, studentResultsByStudentId, organizationIds });

  return {
    ...defaultStats,
    ...mapValues(omitBy(stats, Number.isNaN), n => roundTo(n))
  };
}

function processBenchmarkAssessmentResultsForScreeningAssessments(
  benchmarkAssessmentResults,
  studentResultsByAssessment
) {
  benchmarkAssessmentResults.forEach(assessmentResult => {
    assessmentResult.measures.forEach(measure => {
      measure.studentResults.forEach(studentResult => {
        if (!studentResultsByAssessment[measure.assessmentName]) {
          studentResultsByAssessment[measure.assessmentName] = {};
        }
        const studentResultForComposite = studentResultsByAssessment["Composite score"][studentResult.studentId];
        if (!studentResultForComposite || studentResultForComposite.assessmentResultId !== assessmentResult._id) {
          studentResultsByAssessment["Composite score"][studentResult.studentId] = {
            assessmentResultId: assessmentResult._id,
            orgid: assessmentResult.orgid,
            studentId: studentResult.studentId,
            score: 0,
            isAtRisk: false
          };
        }
        studentResultsByAssessment[measure.assessmentName][studentResult.studentId] = {
          orgid: assessmentResult.orgid,
          studentId: studentResult.studentId,
          score: parseInt(studentResult.score),
          individualRuleOutcome: studentResult.individualRuleOutcome
        };
        studentResultsByAssessment["Composite score"][studentResult.studentId].score += parseInt(studentResult.score);
        if (studentResult.individualRuleOutcome === "below") {
          studentResultsByAssessment["Composite score"][studentResult.studentId].isAtRisk = true;
        }
      });
    });
  });
}

function processClasswideAssessmentResultsForScreeningAssessments(
  classwideAssessmentResults,
  studentResultsByAssessment
) {
  classwideAssessmentResults.forEach(assessmentResult => {
    assessmentResult.measures.forEach(measure => {
      measure.studentResults.forEach(studentResult => {
        const studentResultForClasswideRisk = studentResultsByAssessment["Classwide risk"][studentResult.studentId];
        if (!studentResultForClasswideRisk) {
          studentResultsByAssessment["Classwide risk"][studentResult.studentId] = {
            orgid: assessmentResult.orgid,
            studentId: studentResult.studentId,
            numberOfTimesAtRisk: 0,
            numberOfClasswideInterventions: 0,
            score: 0
          };
        }
        studentResultsByAssessment["Classwide risk"][studentResult.studentId].numberOfClasswideInterventions += 1;
        if (studentResult.individualRuleOutcome === "below") {
          studentResultsByAssessment["Classwide risk"][studentResult.studentId].numberOfTimesAtRisk += 1;
        }
        studentResultsByAssessment["Classwide risk"][studentResult.studentId].score =
          ((studentResultsByAssessment["Classwide risk"][studentResult.studentId].numberOfTimesAtRisk + 1) /
            studentResultsByAssessment["Classwide risk"][studentResult.studentId].numberOfClasswideInterventions) *
          -1;
      });
    });
  });
}

function getScreeningAssessmentsForGrade({
  grade,
  benchmarkAssessmentResults = [],
  classwideAssessmentResults = [],
  organizationIds = []
}) {
  let assessmentNames = [];
  if (benchmarkAssessmentResults.length) {
    assessmentNames = benchmarkAssessmentResults[0].measures.map(measure => measure.assessmentName);
    assessmentNames.push("Composite score");
  }
  assessmentNames.push("Classwide risk");

  const studentResultsByAssessment = {
    "Composite score": {},
    "Classwide risk": {}
  };

  processBenchmarkAssessmentResultsForScreeningAssessments(benchmarkAssessmentResults, studentResultsByAssessment);
  processClasswideAssessmentResultsForScreeningAssessments(classwideAssessmentResults, studentResultsByAssessment);

  return assessmentNames.map(assessmentName => ({
    assessmentId: `${grade}_${assessmentName}`,
    grade,
    assessmentName,
    ...getScreeningAssessmentsStats({
      assessmentName,
      studentResultsByStudentId: studentResultsByAssessment[assessmentName],
      organizationIds
    })
  }));
}

async function getScreeningAssessments({ benchmarkPeriodId, state, schoolYear }) {
  const organizationIds = await getOrganizationIdsForState(state);

  if (!organizationIds.length) {
    return [];
  }

  const benchmarkAssessmentResults = await AssessmentResults.find(
    {
      schoolYear,
      benchmarkPeriodId,
      type: "benchmark",
      status: "COMPLETED"
    },
    {
      fields: { grade: 1, orgid: 1, measures: 1, created: 1 }
    }
  ).fetchAsync();

  const classwideAssessmentResults = await AssessmentResults.find(
    {
      schoolYear,
      benchmarkPeriodId,
      type: "classwide",
      status: "COMPLETED"
    },
    {
      fields: { grade: 1, orgid: 1, measures: 1 }
    }
  ).fetchAsync();

  const validBenchmarkAssessmentResults = filterInvalidBenchmarkAssessmentResults(benchmarkAssessmentResults).sort(
    (a, b) => a.created.on - b.created.on
  );

  if (!validBenchmarkAssessmentResults.length) {
    return [];
  }

  const benchmarkAssessmentResultsByGrade = groupBy(validBenchmarkAssessmentResults, "grade");
  const classwideAssessmentResultsByGrade = groupBy(classwideAssessmentResults, "grade");
  const grades = (
    await Grades.find({ _id: { $ne: "HS" } }, { fields: { _id: 1 }, sort: { sortorder: 1 } }).fetchAsync()
  ).map(g => g._id);

  const screeningAssessments = [];
  grades.forEach(grade => {
    if (
      (benchmarkAssessmentResultsByGrade[grade] && benchmarkAssessmentResultsByGrade[grade].length) ||
      (classwideAssessmentResultsByGrade[grade] && classwideAssessmentResultsByGrade[grade].length)
    ) {
      screeningAssessments.push(
        ...getScreeningAssessmentsForGrade({
          grade,
          benchmarkAssessmentResults: benchmarkAssessmentResultsByGrade[grade],
          classwideAssessmentResults: classwideAssessmentResultsByGrade[grade],
          organizationIds
        })
      );
    }
  });
  return screeningAssessments;
}

async function getAssessmentsFunctioning({ grade, benchmarkPeriodId, state, schoolYear }) {
  const organizationIds = await getOrganizationIdsForState(state);

  if (!organizationIds.length) {
    return [];
  }

  const assessmentScoresByStudentId = await getAssessmentScoresByStudentId(organizationIds, schoolYear);
  const studentIds = Object.keys(assessmentScoresByStudentId);

  const {
    benchmark: benchmarkAssessmentResults,
    classwide: classwideInterventionAssessmentResults
  } = await getBenchmarkAndClasswideInterventionAssessmentResults({
    grade,
    benchmarkPeriodId,
    studentIds,
    organizationIds,
    schoolYear
  });

  if (!benchmarkAssessmentResults.length) {
    return [];
  }

  const assessmentResultsByAssessment = {};

  assessmentResultsByAssessment["Composite/Any risk"] = getDefaultStudentCountByProblemTypeAndProficiency();
  assessmentResultsByAssessment["Classwide risk>-.10"] = getDefaultStudentCountByProblemTypeAndProficiency();
  assessmentResultsByAssessment[
    "Final classwide intervention score"
  ] = getDefaultStudentCountByProblemTypeAndProficiency();

  const benchmarkAssessmentIds = processBenchmarkAssessmentResults({
    benchmarkAssessmentResults,
    assessmentResultsByAssessment,
    studentIds,
    assessmentScoresByStudentId
  });

  const studentResultsByStudentId = {};

  processClasswideInterventionAssessmentResults({
    classwideInterventionAssessmentResults,
    benchmarkAssessmentIds,
    studentIds,
    assessmentScoresByStudentId,
    studentResultsByStudentId
  });

  processStudentResultsByStudentId({ studentResultsByStudentId, assessmentResultsByAssessment });

  const assessmentNames = benchmarkAssessmentResults[0].measures.map(measure => measure.assessmentName);
  assessmentNames.push("Composite/Any risk", "Classwide risk>-.10", "Final classwide intervention score");

  return assessmentNames.map(assessmentName => ({
    assessment: assessmentName,
    ...getAllAssessmentsFunctioningStats(assessmentResultsByAssessment[assessmentName])
  }));
}

export function getAssessmentsFunctioningStats(data, baseRate = "N/A") {
  const isValidBaseRate = isNumber(baseRate) && !Number.isNaN(baseRate);
  const defaultStats = {
    sensitivity: "N/A",
    specificity: "N/A",
    ppv: "N/A",
    npv: "N/A",
    falsePositiveRate: "N/A",
    falseNegativeRate: "N/A",
    positiveLikelihoodRatio: "N/A",
    negativeLikelihoodRatio: "N/A",
    baseRate: isValidBaseRate ? roundTo(baseRate) : "N/A",
    pptp: "N/A",
    nptp: "N/A"
  };
  if (!data) {
    return defaultStats;
  }
  const { notProficient, proficient } = data;
  const notProficientAtOrAboveTarget = notProficient.total - notProficient.belowTarget;
  const proficientAtOrAboveTarget = proficient.total - proficient.belowTarget;

  const sensitivity = notProficient.belowTarget / notProficient.total;
  const specificity = proficientAtOrAboveTarget / (proficient.belowTarget + proficient.total);
  const ppv = notProficient.belowTarget / (notProficient.belowTarget + proficient.belowTarget);
  const npv = proficientAtOrAboveTarget / (notProficientAtOrAboveTarget + proficientAtOrAboveTarget);
  const falsePositiveRate = proficient.belowTarget / proficient.total;
  const falseNegativeRate = notProficientAtOrAboveTarget / notProficient.total;
  const positiveLikelihoodRatio = sensitivity / (1 - specificity);
  const negativeLikelihoodRatio = (1 - sensitivity) / specificity;

  const preTestOdds = baseRate / (1 - baseRate);
  const positivePostTestOdds = preTestOdds * positiveLikelihoodRatio;
  const negativePostTestOdds = preTestOdds * negativeLikelihoodRatio;

  const pptp = positivePostTestOdds / (1 + positivePostTestOdds);
  const nptp = negativePostTestOdds / (1 + negativePostTestOdds);

  const stats = {
    sensitivity,
    specificity,
    ppv,
    npv,
    falsePositiveRate,
    falseNegativeRate,
    positiveLikelihoodRatio,
    negativeLikelihoodRatio,
    pptp,
    nptp
  };

  return {
    ...defaultStats,
    ...mapValues(omitBy(stats, Number.isNaN), n => roundTo(n))
  };
}

function getAllAssessmentsFunctioningStats(assessmentData) {
  const stats = {
    classwideProblem: {
      sensitivity: "N/A",
      specificity: "N/A",
      ppv: "N/A",
      npv: "N/A",
      falsePositiveRate: "N/A",
      falseNegativeRate: "N/A",
      positiveLikelihoodRatio: "N/A",
      negativeLikelihoodRatio: "N/A",
      baseRate: "N/A",
      pptp: "N/A",
      nptp: "N/A"
    },
    noClasswideProblem: {
      sensitivity: "N/A",
      specificity: "N/A",
      ppv: "N/A",
      npv: "N/A",
      falsePositiveRate: "N/A",
      falseNegativeRate: "N/A",
      positiveLikelihoodRatio: "N/A",
      negativeLikelihoodRatio: "N/A",
      baseRate: "N/A",
      pptp: "N/A",
      nptp: "N/A"
    }
  };
  if (!assessmentData) {
    return stats;
  }
  const notProficientTotal =
    assessmentData.classwideProblem.notProficient.total + assessmentData.noClasswideProblem.notProficient.total;
  const proficientTotal =
    assessmentData.classwideProblem.proficient.total + assessmentData.noClasswideProblem.proficient.total;
  const baseRate = notProficientTotal / (notProficientTotal + proficientTotal);
  stats.classwideProblem = getAssessmentsFunctioningStats(assessmentData.classwideProblem, baseRate);
  stats.noClasswideProblem = getAssessmentsFunctioningStats(assessmentData.noClasswideProblem, baseRate);
  return stats;
}

function filterInvalidBenchmarkAssessmentResults(benchmarkAssessmentResults) {
  return benchmarkAssessmentResults.filter(benchmarkAssessmentResult => {
    const invalidMeasures = benchmarkAssessmentResult.measures.filter(assessmentResultMeasures => {
      if (assessmentResultMeasures.studentScores.length < 5) {
        return false;
      }
      return assessmentResultMeasures.studentScores.every(score => {
        return score <= assessmentResultMeasures.targetScores[0] && score === assessmentResultMeasures.studentScores[0];
      });
    });
    return !invalidMeasures.length;
  });
}

async function getNciiValues({ benchmarkPeriodId, state, schoolYear }) {
  const organizationIds = await getOrganizationIdsForState(state);

  if (!organizationIds.length) {
    return [];
  }

  const externalAssessmentScoresForOrganizations = await getExternalAssessmentScoresForOrganizations(
    organizationIds,
    schoolYear
  );
  const studentIds = externalAssessmentScoresForOrganizations.map(assessmentScore => assessmentScore.studentId);
  const benchmarkAssessmentResults = await AssessmentResults.find(
    {
      schoolYear,
      type: "benchmark",
      status: "COMPLETED",
      "scores.studentId": { $in: studentIds },
      orgid: { $in: organizationIds }
    },
    {
      fields: { grade: 1, benchmarkPeriodId: 1, scores: 1, studentGroupId: 1, measures: 1 },
      sort: { "created.on": 1 }
    }
  ).fetchAsync();
  if (!benchmarkAssessmentResults.length) {
    return [];
  }

  const validBenchmarkAssessmentResults = filterInvalidBenchmarkAssessmentResults(benchmarkAssessmentResults);

  const externalAssessmentScoresGroupedByGrade = groupBy(externalAssessmentScoresForOrganizations, "grade");
  const benchmarkAssessmentResultsByGrade = groupBy(validBenchmarkAssessmentResults, "grade");
  const grades = (
    await Grades.find({ _id: { $ne: "HS" } }, { fields: { _id: 1 }, sort: { sortorder: 1 } }).fetchAsync()
  ).map(g => g._id);

  return grades.map(grade => ({
    grade,
    ...getNciiValuesForGrade({
      grade,
      assessmentResults: benchmarkAssessmentResultsByGrade[grade],
      externalAssessmentScores: externalAssessmentScoresGroupedByGrade[grade],
      benchmarkPeriodId
    })
  }));
}

function getStudentBenchmarkScoresByStudentId(springBenchmarkAssessmentResults, studentIds) {
  const studentScoresByStudentId = {};
  springBenchmarkAssessmentResults.forEach(assessmentResult => {
    assessmentResult.scores.forEach(({ studentId }) => {
      if (!studentIds.includes(studentId)) {
        return;
      }
      studentScoresByStudentId[studentId] = 0;
    });
    assessmentResult.scores.forEach(({ studentId, value }) => {
      if (!studentIds.includes(studentId)) {
        return;
      }
      const score = parseInt(value);
      if (score > 0) {
        studentScoresByStudentId[studentId] += score;
      }
    });
  });
  return studentScoresByStudentId;
}

function getCutPointCriterionMeasureOnSpringComposite(assessmentResults, studentIds, percentileRank) {
  const springPeriodId = "cjCMnZKARBJmG8suT";
  const springBenchmarkAssessmentResults = assessmentResults.filter(
    assessmentResult => assessmentResult.benchmarkPeriodId === springPeriodId
  );

  if (!springBenchmarkAssessmentResults.length) {
    return {
      cutPointCriterionMeasure: NaN,
      studentsBelowCriterionMeasure: [],
      studentsAtOrAboveCriterionMeasure: []
    };
  }

  const studentScoresByStudentId = getStudentBenchmarkScoresByStudentId(springBenchmarkAssessmentResults, studentIds);
  const studentScores = sortBy(
    map(studentScoresByStudentId, (score, studentId) => ({ studentId, score })),
    "score"
  );
  const cutPointCriterionMeasureIndex = Math.ceil(studentScores.length * (percentileRank / 100)) - 1;

  return {
    cutPointCriterionMeasure: studentScores[cutPointCriterionMeasureIndex].score,
    studentsBelowCriterionMeasure: studentScores.slice(0, cutPointCriterionMeasureIndex),
    studentsAtOrAboveCriterionMeasure: studentScores.slice(cutPointCriterionMeasureIndex)
  };
}

function getNciiValuesForGrade({ grade, assessmentResults = [], externalAssessmentScores = [], benchmarkPeriodId }) {
  const defaultStats = {
    cutPointCriterionMeasure: "N/A",
    cutPointScreeningMeasure: "N/A",
    baseRate: 0.2,
    falsePositiveRate: "N/A",
    falseNegativeRate: "N/A",
    sensitivity: "N/A",
    specificity: "N/A",
    positivePredictivePower: "N/A",
    negativePredictivePower: "N/A"
  };

  if (!externalAssessmentScores.length) {
    return defaultStats;
  }

  const percentileRank = 20;
  let cutPointCriterionMeasure;
  let studentsBelowCriterionMeasure = [];
  let studentsAtOrAboveCriterionMeasure = [];
  const studentIds = externalAssessmentScores.map(assessmentScore => assessmentScore.studentId);

  const gradesUsingSpringComposite = ["K", "01", "02"];
  if (gradesUsingSpringComposite.includes(grade)) {
    ({
      cutPointCriterionMeasure,
      studentsBelowCriterionMeasure,
      studentsAtOrAboveCriterionMeasure
    } = getCutPointCriterionMeasureOnSpringComposite(assessmentResults, studentIds, percentileRank));
  } else {
    const studentWithSelectedPercentileRank = externalAssessmentScores.find(
      assessmentScore => assessmentScore.data.stateAssessmentPercentileScore === percentileRank
    );
    cutPointCriterionMeasure = get(studentWithSelectedPercentileRank, "data.stateAssessmentScaleScore", NaN);

    externalAssessmentScores.forEach(({ studentId, data }) => {
      const score = get(data, "stateAssessmentScaleScore");
      if (score >= 0) {
        if (score < cutPointCriterionMeasure) {
          studentsBelowCriterionMeasure.push({ studentId, score });
        } else {
          studentsAtOrAboveCriterionMeasure.push({ studentId, score });
        }
      }
    });
  }

  const selectedBenchmarkAssessmentResults = assessmentResults.filter(
    assessmentResult => assessmentResult.benchmarkPeriodId === benchmarkPeriodId
  );

  if (!selectedBenchmarkAssessmentResults.length) {
    return defaultStats;
  }

  const studentScoresForSelectedBenchmarkByStudentId = getStudentBenchmarkScoresByStudentId(
    selectedBenchmarkAssessmentResults,
    studentIds
  );
  const studentScoresForSelectedBenchmark = sortBy(
    map(studentScoresForSelectedBenchmarkByStudentId, (score, studentId) => ({ studentId, score })),
    "score"
  );
  const cutPointScreeningMeasureIndex =
    Math.ceil(studentScoresForSelectedBenchmark.length * (percentileRank / 100)) - 1;
  const cutPointScreeningMeasure = studentScoresForSelectedBenchmark[cutPointScreeningMeasureIndex].score;
  const studentsBelowScreeningMeasure = studentScoresForSelectedBenchmark.slice(0, cutPointScreeningMeasureIndex);
  const studentsAtOrAboveScreeningMeasure = studentScoresForSelectedBenchmark.slice(cutPointScreeningMeasureIndex);

  const a = intersectionBy(studentsBelowCriterionMeasure, studentsBelowScreeningMeasure, "studentId").length;
  const b = intersectionBy(studentsAtOrAboveCriterionMeasure, studentsBelowScreeningMeasure, "studentId").length;
  const c = intersectionBy(studentsBelowCriterionMeasure, studentsAtOrAboveScreeningMeasure, "studentId").length;
  const d = intersectionBy(studentsAtOrAboveCriterionMeasure, studentsAtOrAboveScreeningMeasure, "studentId").length;

  const stats = {
    ...getNciiValuesStats({ a, b, c, d }),
    cutPointCriterionMeasure,
    cutPointScreeningMeasure
  };

  return {
    ...defaultStats,
    ...mapValues(omitBy(stats, Number.isNaN), n => roundTo(n))
  };
}

export function getNciiValuesStats(data) {
  // SPRIN-1780
  // a - count of students scoring below the cut point: criterion measure and below the cut point: screening measure
  // b - count of students scoring at or above the cut point: criterion measure and below the cut point: screening measure
  // c - count of students scoring below the cut point: criterion measure and at or above the cut point: screening measure
  // d - count of students scoring at or above the cut point: criterion measure and at or above the cut point: screening measure

  const defaultStats = {
    falsePositiveRate: "N/A",
    falseNegativeRate: "N/A",
    sensitivity: "N/A",
    specificity: "N/A",
    positivePredictivePower: "N/A",
    negativePredictivePower: "N/A"
  };

  if (!data) {
    return defaultStats;
  }
  const { a, b, c, d } = data;

  const stats = {
    falsePositiveRate: b / (b + d),
    falseNegativeRate: c / (a + c),
    sensitivity: a / (a + c),
    specificity: d / (d + b),
    positivePredictivePower: a / (a + b),
    negativePredictivePower: d / (d + c)
  };
  return {
    ...defaultStats,
    ...mapValues(omitBy(stats, Number.isNaN), n => roundTo(n))
  };
}

async function getInterventionProgress({ grade, schoolYear, orgIds }) {
  const classwideAssessmentResults = (
    await AssessmentResults.find(
      {
        schoolYear,
        grade,
        ...(orgIds.length && { orgid: { $in: orgIds } }),
        type: "classwide",
        status: "COMPLETED",
        "measures.0": { $exists: true }
      },
      {
        fields: {
          studentGroupId: 1,
          "measures.assessmentId": 1,
          "measures.assessmentName": 1,
          "measures.studentResults": 1,
          "measures.medianScore": 1,
          "measures.targetScores": 1,
          "scores.status": 1,
          "scores.studentId": 1,
          "ruleResults.passed": 1,
          lastScoreUpdatedAt: 1,
          "created.on": 1,
          isAdditional: 1
        }
      }
    ).fetchAsync()
  ).sort((a, b) => a.created.on - b.created.on);

  if (!classwideAssessmentResults.length) {
    return {
      students: [],
      classes: []
    };
  }

  return getInterventionProgressStats(classwideAssessmentResults);
}

function getInterventionProgressStats(classwideAssessmentResults = []) {
  const classwideAssessmentResultsByAssessmentId = new Map();

  const gradeLevelSkills = [];
  classwideAssessmentResults.forEach(ar => {
    const { assessmentId, assessmentName } = ar.measures[0];
    if (!classwideAssessmentResultsByAssessmentId.has(assessmentId)) {
      gradeLevelSkills.push({
        assessmentId,
        assessmentName,
        isAdditional: ar.isAdditional
      });
      classwideAssessmentResultsByAssessmentId.set(assessmentId, []);
    }
    classwideAssessmentResultsByAssessmentId.get(assessmentId).push(ar);
  });

  const interventionProgressStats = {
    numberOfSkillsMasteredMin: "N/A",
    numberOfSkillsMasteredMax: "N/A",
    numberOfSkillsMasteredMean: "N/A",
    students: [],
    classes: [],
    numberOfDefaultSkills: gradeLevelSkills.filter(skill => !skill.isAdditional).length
  };

  const numberOfSkillsMasteredByStudentGroupId = {};

  gradeLevelSkills.forEach((skill, skillIndex) => {
    const classwideAssessmentResultsForSkill = classwideAssessmentResultsByAssessmentId.get(skill.assessmentId);
    const studentIds = new Set();
    const studentIdsForStudentsAtOrAboveMasteryTarget = new Set();
    const studentIdsForStudentsAtOrAboveInstructionalTargetAndBelowMasteryTarget = new Set();
    const studentIdsForStudentsBelowInstructionalTarget = new Set();
    const studentIdsForStudentsAbsent = new Set();
    const classwideAssessmentResultsByStudentGroupId = {};
    const classesIds = new Set();
    const classesIdsWhereMedianForFirstScoreIsAtOrAboveInstructionalTarget = new Set();

    orderBy(
      classwideAssessmentResultsForSkill.filter(c => c.ruleResults.passed),
      "lastScoreUpdatedAt",
      "desc"
    ).forEach(({ measures, scores, studentGroupId }) => {
      const { studentResults } = measures[0];

      numberOfSkillsMasteredByStudentGroupId[studentGroupId] = skillIndex + 1;

      studentResults.forEach(result => {
        if (result.individualRuleOutcome === "above") {
          studentIdsForStudentsAtOrAboveMasteryTarget.add(result.studentId);
        } else if (result.individualRuleOutcome === "at") {
          studentIdsForStudentsAtOrAboveInstructionalTargetAndBelowMasteryTarget.add(result.studentId);
        } else if (result.individualRuleOutcome === "below") {
          studentIdsForStudentsBelowInstructionalTarget.add(result.studentId);
        }
      });

      const studentIdsForStudentsAbsentForAssessmentResult = scores
        .filter(score => score.status === "CANCELLED")
        .map(score => score.studentId);
      studentIdsForStudentsAbsentForAssessmentResult.forEach(studentId => {
        studentIdsForStudentsAbsent.add(studentId);
      });
      const studentIdsForAssessmentResult = scores.map(result => result.studentId);
      studentIdsForAssessmentResult.forEach(Set.prototype.add, studentIds);
    });

    classwideAssessmentResultsForSkill.forEach(({ lastScoreUpdatedAt, ruleResults, measures, studentGroupId }) => {
      if (!classwideAssessmentResultsByStudentGroupId[studentGroupId]) {
        classwideAssessmentResultsByStudentGroupId[studentGroupId] = {
          startedAt: lastScoreUpdatedAt
        };
      }

      if (ruleResults.passed) {
        classwideAssessmentResultsByStudentGroupId[studentGroupId].masteredAt = lastScoreUpdatedAt;
        const millisecondsInWeek = 604800000; // 1000 * 60 * 60 * 24 * 7
        let weeksToMastery = Math.floor(
          (lastScoreUpdatedAt - classwideAssessmentResultsByStudentGroupId[studentGroupId].startedAt) /
            millisecondsInWeek
        );
        weeksToMastery = Math.max(0.5, weeksToMastery);
        classwideAssessmentResultsByStudentGroupId[studentGroupId].weeksToMastery = weeksToMastery;
      }

      if (lastScoreUpdatedAt < classwideAssessmentResultsByStudentGroupId[studentGroupId].startedAt) {
        classwideAssessmentResultsByStudentGroupId[studentGroupId].startedAt = lastScoreUpdatedAt;
      }

      if (!classesIds.has(studentGroupId)) {
        const { medianScore, targetScores } = measures[0];
        if (medianScore >= targetScores[0]) {
          classesIdsWhereMedianForFirstScoreIsAtOrAboveInstructionalTarget.add(studentGroupId);
        }
      }
      classesIds.add(studentGroupId);
    });

    interventionProgressStats.students.push(
      getInterventionProgressStatsForStudents({
        skill,
        studentIds,
        classwideAssessmentResultsByStudentGroupId,
        studentIdsForStudentsAtOrAboveMasteryTarget,
        studentIdsForStudentsBelowInstructionalTarget,
        studentIdsForStudentsAtOrAboveInstructionalTargetAndBelowMasteryTarget,
        studentIdsForStudentsAbsent
      })
    );
    interventionProgressStats.classes.push(
      getInterventionProgressStatsForClasses({
        skill,
        classesIds,
        classesIdsWhereMedianForFirstScoreIsAtOrAboveInstructionalTarget
      })
    );
  });

  const numberOfSkillsMastered = Object.values(numberOfSkillsMasteredByStudentGroupId);
  interventionProgressStats.numberOfSkillsMasteredMin = Math.min(...numberOfSkillsMastered);
  interventionProgressStats.numberOfSkillsMasteredMax = Math.max(...numberOfSkillsMastered);
  interventionProgressStats.numberOfSkillsMasteredMean = roundTo(mean(numberOfSkillsMastered), 1);

  return interventionProgressStats;
}

function getInterventionProgressStatsForStudents({
  skill,
  studentIds,
  classwideAssessmentResultsByStudentGroupId,
  studentIdsForStudentsAtOrAboveMasteryTarget,
  studentIdsForStudentsBelowInstructionalTarget,
  studentIdsForStudentsAtOrAboveInstructionalTargetAndBelowMasteryTarget,
  studentIdsForStudentsAbsent
}) {
  const defaultStats = {
    assessmentId: skill.assessmentId,
    classwideSkill: skill.assessmentName,
    numberOfStudents: "N/A",
    weeksToMasteryMinimum: "N/A",
    weeksToMasteryMaximum: "N/A",
    weeksToMasteryMean: "N/A",
    percentOfStudentsAtOrAboveMasteryTarget: "N/A",
    percentOfStudentsBelowInstructionalTarget: "N/A",
    percentOfStudentsAtOrAboveInstructionalTargetAndBelowMasteryTarget: "N/A",
    percentOfStudentsAbsent: "N/A"
  };

  const numberOfStudents = studentIds.size;
  const weeksToMastery = Object.values(classwideAssessmentResultsByStudentGroupId)
    .filter(value => value.weeksToMastery)
    .map(value => value.weeksToMastery);

  let { weeksToMasteryMinimum, weeksToMasteryMaximum, weeksToMasteryMean } = defaultStats;
  if (weeksToMastery.length) {
    weeksToMasteryMinimum = Math.min(...weeksToMastery);
    weeksToMasteryMaximum = Math.max(...weeksToMastery);
    weeksToMasteryMean = mean(weeksToMastery);
  }
  const percentOfStudentsAtOrAboveMasteryTarget =
    (studentIdsForStudentsAtOrAboveMasteryTarget.size / numberOfStudents) * 100;
  const percentOfStudentsBelowInstructionalTarget =
    (studentIdsForStudentsBelowInstructionalTarget.size / numberOfStudents) * 100;
  const percentOfStudentsAbsent = (studentIdsForStudentsAbsent.size / numberOfStudents) * 100;
  const percentOfStudentsAtOrAboveInstructionalTargetAndBelowMasteryTarget =
    (studentIdsForStudentsAtOrAboveInstructionalTargetAndBelowMasteryTarget.size / numberOfStudents) * 100;

  const stats = {
    numberOfStudents,
    weeksToMasteryMinimum,
    weeksToMasteryMaximum,
    weeksToMasteryMean,
    percentOfStudentsAtOrAboveMasteryTarget,
    percentOfStudentsBelowInstructionalTarget,
    percentOfStudentsAtOrAboveInstructionalTargetAndBelowMasteryTarget,
    percentOfStudentsAbsent
  };

  return {
    ...defaultStats,
    ...mapValues(omitBy(stats, Number.isNaN), n => roundTo(n, 1))
  };
}

function getInterventionProgressStatsForClasses({
  skill,
  classesIds,
  classesIdsWhereMedianForFirstScoreIsAtOrAboveInstructionalTarget
}) {
  const defaultStats = {
    assessmentId: skill.assessmentId,
    classwideSkill: skill.assessmentName,
    countOfAllClasses: "N/A",
    countOfClassesWhereMedianForFirstScoreIsAtOrAboveInstructionalTarget: "N/A",
    countOfClassesWhereMedianForFirstScoreIsBelowInstructionalTarget: "N/A"
  };

  const countOfAllClasses = classesIds.size;
  const countOfClassesWhereMedianForFirstScoreIsAtOrAboveInstructionalTarget =
    classesIdsWhereMedianForFirstScoreIsAtOrAboveInstructionalTarget.size;
  const countOfClassesWhereMedianForFirstScoreIsBelowInstructionalTarget =
    countOfAllClasses - countOfClassesWhereMedianForFirstScoreIsAtOrAboveInstructionalTarget;

  const stats = {
    countOfAllClasses,
    countOfClassesWhereMedianForFirstScoreIsAtOrAboveInstructionalTarget,
    countOfClassesWhereMedianForFirstScoreIsBelowInstructionalTarget
  };

  return {
    ...defaultStats,
    ...mapValues(omitBy(stats, Number.isNaN), n => roundTo(n, 1))
  };
}

async function getInterventionGrowth({ grade, schoolYear, orgIds, state }) {
  const organizationIds = orgIds.length ? orgIds : await getOrganizationIdsForState(state);

  const listOfRequiredAssessmentResultFields = [
    "_id",
    "studentGroupId",
    "benchmarkPeriodId",
    "assessmentIds",
    "classwideResults",
    "studentIdsNotMeetingTarget",
    "measures",
    "schoolYear",
    "created.on"
  ];

  const assessmentResultsForSelectedYear = await getBenchmarkAndClasswideInterventionAssessmentResults({
    grade,
    organizationIds,
    schoolYear,
    shouldSort: false,
    fieldNames: listOfRequiredAssessmentResultFields
  });

  const assessmentResultsForSelectedYearForPreviousYear = await getBenchmarkAndClasswideInterventionAssessmentResults({
    grade,
    organizationIds,
    schoolYear: schoolYear - 1,
    shouldSort: false,
    fieldNames: listOfRequiredAssessmentResultFields
  });

  const assessmentGrowth = await AssessmentGrowth.findOneAsync({ grade });

  return getInterventionGrowthStats(
    assessmentResultsForSelectedYear,
    assessmentResultsForSelectedYearForPreviousYear,
    assessmentGrowth,
    grade
  );
}

async function getInterventionGrowthStats(
  assessmentResultsForSelectedYear,
  assessmentResultsForSelectedYearForPreviousYear,
  assessmentGrowth,
  grade
) {
  const benchmarkAssessmentResultsByBenchmarkPeriodId = groupBy(
    assessmentResultsForSelectedYear.benchmark,
    "benchmarkPeriodId"
  );
  const benchmarkAssessmentResultsByBenchmarkPeriodIdForPreviousYear = groupBy(
    assessmentResultsForSelectedYearForPreviousYear.benchmark,
    "benchmarkPeriodId"
  );

  const benchmarkPeriods = await BenchmarkPeriods.find(
    { name: { $ne: "All" } },
    { fields: { name: 1 }, sort: { sortOrder: 1 } }
  ).fetchAsync();

  return {
    benchmarkPeriodLabelsById: benchmarkPeriods.reduce((benchmarkPeriodLabelsById, benchmarkPeriod) => {
      benchmarkPeriodLabelsById[benchmarkPeriod._id] = benchmarkPeriod.name;
      return benchmarkPeriodLabelsById;
    }, {}),
    ...calculateBenchmarkStats({
      benchmarkAssessmentResultsByBenchmarkPeriodId,
      classwideInterventionAssessmentResults: assessmentResultsForSelectedYear.classwide,
      benchmarkPeriods,
      benchmarkAssessmentResultsByBenchmarkPeriodIdForPreviousYear,
      classwideInterventionAssessmentResultsForPreviousYear: assessmentResultsForSelectedYearForPreviousYear.classwide,
      assessmentGrowth,
      grade
    })
  };
}

export function calculateYearlyGrowthStats({
  finalAssessments,
  studentGroupId,
  assessmentId,
  assessmentName,
  benchmarkPeriodId,
  assessmentGrowthMap,
  statsAccumulator = {},
  studentIdsAssessed,
  validFinalClasswideAssessmentsIdsFromScreenings
}) {
  const defaultForMeasureStats = {
    assessmentName,
    percentOfStudentsProficient: 0,
    numberOfStudentsProficient: 0,
    totalStudentsAssessedOnAllMeasures: 0
  };
  const finalClasswideAssessmentId = getFinalClasswideAssessmentId({
    assessmentId,
    studentGroupId,
    assessmentGrowthMap,
    validFinalClasswideAssessmentsIdsFromScreenings
  });

  if (get(finalAssessments, [studentGroupId, finalClasswideAssessmentId])) {
    if (!statsAccumulator[benchmarkPeriodId][finalClasswideAssessmentId]) {
      statsAccumulator[benchmarkPeriodId][finalClasswideAssessmentId] = {
        ...defaultForMeasureStats
      };
    }
    const studentIdsMeetingTargetOnFinalClasswideAssessment = [];
    const studentIdsOfAllAssessedInThisFinalMeasure = [];
    finalAssessments[studentGroupId][finalClasswideAssessmentId].measures[0].studentResults
      .filter(sr => sr.status === "COMPLETE")
      .forEach(sr => {
        studentIdsOfAllAssessedInThisFinalMeasure.push(sr.studentId);
        if (sr.meetsTarget) {
          studentIdsMeetingTargetOnFinalClasswideAssessment.push(sr.studentId);
        }
      });
    statsAccumulator[benchmarkPeriodId][finalClasswideAssessmentId].numberOfStudentsProficient += intersection(
      studentIdsMeetingTargetOnFinalClasswideAssessment,
      studentIdsAssessed
    ).length;
    statsAccumulator[benchmarkPeriodId][finalClasswideAssessmentId].totalStudentsAssessedOnAllMeasures += intersection(
      studentIdsOfAllAssessedInThisFinalMeasure,
      studentIdsAssessed
    ).length;

    if (statsAccumulator[benchmarkPeriodId][finalClasswideAssessmentId].totalStudentsAssessedOnAllMeasures > 0) {
      statsAccumulator[benchmarkPeriodId][finalClasswideAssessmentId].percentOfStudentsProficient = roundTo(
        (statsAccumulator[benchmarkPeriodId][finalClasswideAssessmentId].numberOfStudentsProficient /
          statsAccumulator[benchmarkPeriodId][finalClasswideAssessmentId].totalStudentsAssessedOnAllMeasures) *
          100
      );
    } else {
      statsAccumulator[benchmarkPeriodId][finalClasswideAssessmentId].percentOfStudentsProficient = 0;
    }
  }
}

function getScreeningAssessmentsForAssessmentResults({
  benchmarkAssessmentResultsByBenchmarkIds,
  grade,
  benchmarkPeriods
}) {
  const result = {};

  benchmarkPeriods.forEach(async benchmarkPeriod => {
    result[benchmarkPeriod._id] = {};
    if (benchmarkAssessmentResultsByBenchmarkIds[benchmarkPeriod._id]) {
      benchmarkAssessmentResultsByBenchmarkIds[benchmarkPeriod._id].forEach(assessmentResult => {
        result[benchmarkPeriod._id][assessmentResult.studentGroupId] = assessmentResult.assessmentIds;
      });
    }

    result[benchmarkPeriod._id].default = (
      await ScreeningAssignments.findOneAsync({
        grade,
        benchmarkPeriodId: benchmarkPeriod._id
      })
    ).assessmentIds;
  });

  return result;
}

export function getFinalClasswideAssessmentId({
  assessmentResult,
  assessmentId = assessmentResult.assessmentIds[0],
  studentGroupId = assessmentResult.studentGroupId,
  benchmarkPeriodId,
  assessmentGrowthMap,
  validFinalClasswideAssessmentsIdsFromScreenings
}) {
  const fallbackGrowthAssessmentId = assessmentGrowthMap[assessmentId];
  let validFinalClasswideAssessmentsIdsFromScreeningsForSelectedBenchmarkPeriod =
    validFinalClasswideAssessmentsIdsFromScreenings[benchmarkPeriodId];

  if (!benchmarkPeriodId) {
    validFinalClasswideAssessmentsIdsFromScreeningsForSelectedBenchmarkPeriod = Object.values(
      validFinalClasswideAssessmentsIdsFromScreenings
    ).reduce((acc, value) => {
      mergeWith(acc, value, (objValue, srcValue) => {
        if (isArray(objValue)) {
          return objValue.concat(srcValue);
        }
        return undefined;
      });
      return acc;
    }, {});
  }

  const {
    default: defaultFinalClasswideAssessments,
    ...groupSpecificFinalClasswideAssessments
  } = validFinalClasswideAssessmentsIdsFromScreeningsForSelectedBenchmarkPeriod;
  const possibleValidClasswideAssessmentIdsForScreeningsForStudentGroup =
    groupSpecificFinalClasswideAssessments[studentGroupId] ||
    Object.values(groupSpecificFinalClasswideAssessments)[0] ||
    defaultFinalClasswideAssessments;

  if (possibleValidClasswideAssessmentIdsForScreeningsForStudentGroup.includes(assessmentId)) {
    return assessmentId;
  }

  return possibleValidClasswideAssessmentIdsForScreeningsForStudentGroup.includes(fallbackGrowthAssessmentId)
    ? fallbackGrowthAssessmentId
    : null;
}

function generateAssessmentGrowthMap(assessmentGrowth) {
  const assessmentGrowthMap = {};
  ["fallToWinter", "winterToSpring"].forEach(periodKey => {
    assessmentGrowth[periodKey].forEach(relatedAssessments => {
      Object.values(relatedAssessments).forEach(assessmentId => {
        if (relatedAssessments.classwide !== assessmentId) {
          assessmentGrowthMap[assessmentId] = relatedAssessments.classwide;
        }
      });
    });
  });
  return assessmentGrowthMap;
}

function generateFinalAssessmentCandidates(classwideInterventionAssessmentResults) {
  const orderedClasswideInterventionAssessmentResults = orderBy(
    classwideInterventionAssessmentResults,
    "created.on",
    "desc"
  );

  return orderedClasswideInterventionAssessmentResults.reduce((accumulator, currentValue) => {
    return get(accumulator, [currentValue.studentGroupId, currentValue.assessmentIds[0]])
      ? accumulator
      : set(accumulator, [currentValue.studentGroupId, currentValue.assessmentIds[0]], currentValue);
  }, {});
}

function calculateYearlyFinalClasswideStats({
  finalAssessmentsCandidates,
  assessmentGrowthMap,
  validFinalClasswideAssessmentsIdsFromScreenings
}) {
  const resultingStats = {
    percentOfStudentsProficient: 0,
    numberOfStudentsProficient: 0,
    totalStudentsAssessedOnAllMeasures: 0
  };

  Object.values(finalAssessmentsCandidates).forEach(assessmentResultsByAssessmentId => {
    Object.keys(assessmentResultsByAssessmentId)
      .filter(assessmentId => {
        const fallbackGrowthAssessmentId = assessmentGrowthMap[assessmentId];
        return !(fallbackGrowthAssessmentId && assessmentResultsByAssessmentId[fallbackGrowthAssessmentId]);
      })
      .forEach(assessmentId => {
        const assessmentResult = assessmentResultsByAssessmentId[assessmentId];
        const finalClasswideAssessmentId = getFinalClasswideAssessmentId({
          assessmentResult,
          assessmentGrowthMap,
          validFinalClasswideAssessmentsIdsFromScreenings
        });
        if (finalClasswideAssessmentId) {
          if (!resultingStats.schoolYear) {
            resultingStats.schoolYear = assessmentResult.schoolYear;
          }
          assessmentResult.measures.forEach(measure => {
            measure.studentResults
              .filter(sr => sr.status === "COMPLETE")
              .forEach(sr => {
                if (sr.meetsTarget) {
                  resultingStats.numberOfStudentsProficient++;
                }
                resultingStats.totalStudentsAssessedOnAllMeasures++;
                resultingStats.percentOfStudentsProficient = roundTo(
                  (resultingStats.numberOfStudentsProficient / resultingStats.totalStudentsAssessedOnAllMeasures) * 100
                );
              });
          });
        }
      });
  });

  return resultingStats;
}

function calculateBenchmarkStats({
  benchmarkAssessmentResultsByBenchmarkPeriodId,
  classwideInterventionAssessmentResults,
  benchmarkPeriods,
  benchmarkAssessmentResultsByBenchmarkPeriodIdForPreviousYear,
  classwideInterventionAssessmentResultsForPreviousYear,
  assessmentGrowth,
  grade
}) {
  const result = {};
  const assessmentGrowthMap = generateAssessmentGrowthMap(assessmentGrowth);

  const finalAssessmentsCandidates = generateFinalAssessmentCandidates(classwideInterventionAssessmentResults);

  const finalAssessmentsCandidatesForPreviousSchoolYear = generateFinalAssessmentCandidates(
    classwideInterventionAssessmentResultsForPreviousYear
  );

  result.allScreeningMeasures = [];
  result.fallScreeningAndFinalClasswideMeasures = [];
  result.winterScreeningAndFinalClasswideMeasures = [];
  result.finalClasswideInterventionByYear = [];
  result.seasonalScreeningMeasures = [];
  result.finalClasswideInterventionByYear = [];

  const totalMeasureStats = {};
  const benchmarkAndFinalMeasureStats = {};

  const defaults = {
    percentOfStudentsProficient: 0,
    numberOfStudentsProficient: 0,
    totalStudentsAssessedOnAllMeasures: 0
  };

  const finalClasswideAssessmentsIdsByBenchmarkPeriodIdsAndStudentGroups = getScreeningAssessmentsForAssessmentResults({
    benchmarkAssessmentResultsByBenchmarkIds: benchmarkAssessmentResultsByBenchmarkPeriodId,
    grade,
    benchmarkPeriods
  });

  const finalClasswideAssessmentsIdsByBenchmarkPeriodIdsAndStudentGroupsForPreviousYear = getScreeningAssessmentsForAssessmentResults(
    {
      benchmarkAssessmentResultsByBenchmarkIds: benchmarkAssessmentResultsByBenchmarkPeriodIdForPreviousYear,
      grade,
      benchmarkPeriods
    }
  );

  benchmarkPeriods.forEach(benchmarkPeriod => {
    const benchmarkPeriodId = benchmarkPeriod._id;

    const totalStatsForBenchmarkPeriod = { ...defaults, benchmarkPeriodId };
    const assessmentResultsForBenchmarkPeriod = benchmarkAssessmentResultsByBenchmarkPeriodId[benchmarkPeriodId];

    if (assessmentResultsForBenchmarkPeriod) {
      if (!totalMeasureStats[benchmarkPeriodId]) {
        totalMeasureStats[benchmarkPeriodId] = {};
        benchmarkAndFinalMeasureStats[benchmarkPeriodId] = {};
      }

      assessmentResultsForBenchmarkPeriod.forEach(assessmentResult => {
        totalStatsForBenchmarkPeriod.numberOfStudentsProficient +=
          assessmentResult.classwideResults.totalStudentsMeetingAllTargets;
        totalStatsForBenchmarkPeriod.totalStudentsAssessedOnAllMeasures +=
          assessmentResult.classwideResults.totalStudentsAssessedOnAllMeasures;

        assessmentResult.measures.forEach(measure => {
          const defaultForMeasureStats = {
            assessmentName: measure.assessmentName,
            measureId: measure.assessmentId,
            ...defaults
          };

          if (!totalMeasureStats[benchmarkPeriodId][measure.assessmentId]) {
            totalMeasureStats[benchmarkPeriodId][measure.assessmentId] = { ...defaultForMeasureStats };
          }

          const studentIdsPassingMeasure = [];
          const studentIdsAssessed = [];
          measure.studentResults
            .filter(sr => sr.status === "COMPLETE")
            .forEach(sr => {
              studentIdsAssessed.push(sr.studentId);
              if (sr.meetsTarget) {
                totalMeasureStats[benchmarkPeriodId][measure.assessmentId].numberOfStudentsProficient++;
                studentIdsPassingMeasure.push(sr.studentId);
              }
              totalMeasureStats[benchmarkPeriodId][measure.assessmentId].totalStudentsAssessedOnAllMeasures++;
              totalMeasureStats[benchmarkPeriodId][measure.assessmentId].percentOfStudentsProficient = roundTo(
                (totalMeasureStats[benchmarkPeriodId][measure.assessmentId].numberOfStudentsProficient /
                  totalMeasureStats[benchmarkPeriodId][measure.assessmentId].totalStudentsAssessedOnAllMeasures) *
                  100
              );
            });
          calculateYearlyGrowthStats({
            finalAssessments: finalAssessmentsCandidates,
            studentGroupId: assessmentResult.studentGroupId,
            assessmentId: measure.assessmentId,
            assessmentName: measure.assessmentName,
            benchmarkPeriodId,
            statsAccumulator: benchmarkAndFinalMeasureStats,
            assessmentGrowthMap,
            studentIdsPassingMeasure,
            studentIdsAssessed,
            validFinalClasswideAssessmentsIdsFromScreenings: finalClasswideAssessmentsIdsByBenchmarkPeriodIdsAndStudentGroups
          });
        });
      });

      totalStatsForBenchmarkPeriod.percentOfStudentsProficient = roundTo(
        (totalStatsForBenchmarkPeriod.numberOfStudentsProficient /
          totalStatsForBenchmarkPeriod.totalStudentsAssessedOnAllMeasures) *
          100
      );

      result.allScreeningMeasures.push(totalStatsForBenchmarkPeriod);
    }
  });

  each(totalMeasureStats, (benchmarkPeriodStats, curBenchmarkPeriodId) => {
    each(benchmarkPeriodStats, (assessmentStats, assessmentId) => {
      result.seasonalScreeningMeasures.push({
        benchmarkPeriodId: curBenchmarkPeriodId,
        assessmentId,
        ...assessmentStats
      });
    });
  });

  result.fallScreeningAndFinalClasswideMeasures = Object.values(
    benchmarkAndFinalMeasureStats["8S52Gz5o85hRkECgq"] || {}
  );
  result.winterScreeningAndFinalClasswideMeasures = Object.values(
    benchmarkAndFinalMeasureStats.nEsbWokBWutTZFkTh || {}
  );

  const yearlyFinalClasswideStatsForSelectedYearForPreciousYear = calculateYearlyFinalClasswideStats({
    finalAssessmentsCandidates: finalAssessmentsCandidatesForPreviousSchoolYear,
    assessmentGrowthMap,
    validFinalClasswideAssessmentsIdsFromScreenings: finalClasswideAssessmentsIdsByBenchmarkPeriodIdsAndStudentGroupsForPreviousYear
  });
  if (yearlyFinalClasswideStatsForSelectedYearForPreciousYear.totalStudentsAssessedOnAllMeasures !== 0) {
    result.finalClasswideInterventionByYear.push(yearlyFinalClasswideStatsForSelectedYearForPreciousYear);
  }
  const yearlyFinalClasswideStatsForSelectedYear = calculateYearlyFinalClasswideStats({
    finalAssessmentsCandidates,
    assessmentGrowthMap,
    validFinalClasswideAssessmentsIdsFromScreenings: finalClasswideAssessmentsIdsByBenchmarkPeriodIdsAndStudentGroups
  });
  if (yearlyFinalClasswideStatsForSelectedYear.totalStudentsAssessedOnAllMeasures !== 0) {
    result.finalClasswideInterventionByYear.push(yearlyFinalClasswideStatsForSelectedYear);
  }

  return result;
}

export async function updateAvailableVideoList() {
  let measureNumbers = [];
  if (!Meteor.settings.S3_ACCESS_KEY_ID?.length) {
    return null;
  }
  const assessmentMeasures = (await Assessments.find({}, { fields: { monitorAssessmentMeasure: 1 } }).fetchAsync()).map(
    a => a.monitorAssessmentMeasure
  );
  return fetchVideoSkillList()
    .then(async data => {
      measureNumbers = data.Contents.filter(c => c.Key !== "protocol-images/Skills/").map(c =>
        path.basename(c.Key, ".mp4").toString()
      );

      const assessmentsWithoutVideo = difference(assessmentMeasures, measureNumbers);
      await Assessments.updateAsync(
        { monitorAssessmentMeasure: { $in: assessmentsWithoutVideo } },
        { $set: { hasVideo: false } },
        { multi: true }
      );
      await Assessments.updateAsync(
        { monitorAssessmentMeasure: { $in: measureNumbers } },
        { $set: { hasVideo: true } },
        { multi: true }
      );
    })
    .catch(e => {
      console.log("Assessments:updateAvailableVideoList Error: ", e.message);
    });
}

Meteor.methods({
  async "Assessments:addTargetToAssessment"({ assessmentId, targetName, grade }) {
    check(assessmentId, String);
    check(targetName, String);
    check(grade, String);
    if (!this.userId) {
      throw new Meteor.Error("403", "No logged in user found!");
    }
    if (!(await auth.hasAccess(["superAdmin"], { userId: this.userId }))) {
      throw new Meteor.Error("403", "You are not authorized to update scores");
    }
    return addTargetToAssessment({ assessmentId, targetName, grade });
  },
  async "Assessments:updateTargets"({ assessmentId, grade, targets }) {
    check(assessmentId, String);
    check(targets, Object);
    check(grade, String);
    if (!this.userId) {
      throw new Meteor.Error("403", "No logged in user found!");
    }
    if (!(await auth.hasAccess(["superAdmin"], { userId: this.userId }))) {
      throw new Meteor.Error("403", "You are not authorized to update scores");
    }
    return updateTargets({ assessmentId, targets, grade });
  },
  async "Assessments:removeTargetFromAssessment"({ assessmentId, grade, targetName }) {
    check(assessmentId, String);
    check(targetName, String);
    check(grade, String);
    if (!this.userId) {
      throw new Meteor.Error("403", "No logged in user found!");
    }
    if (!(await auth.hasAccess(["superAdmin"], { userId: this.userId }))) {
      throw new Meteor.Error("403", "You are not authorized to remove target scores");
    }
    return removeTargetFromAssessment({ assessmentId, targetName, grade });
  },
  async "Assessments:updateOrCreateClasswideTargets"({ assessmentId, gradeId, instructionalTarget, masteryTarget }) {
    check(assessmentId, String);
    check(gradeId, String);
    check(instructionalTarget, Number);
    check(masteryTarget, Number);
    if (!this.userId) {
      throw new Meteor.Error("403", "No logged in user found!");
    }
    if (!(await auth.hasAccess(["superAdmin"], { userId: this.userId }))) {
      throw new Meteor.Error("403", "You are not authorized to update classwide targets");
    }
    return updateOrCreateClasswideTargets({ assessmentId, gradeId, instructionalTarget, masteryTarget });
  },
  async getInterventionIntegrity({ state, organizationId, siteId, grade }) {
    check(state, String);
    check(organizationId, String);
    check(siteId, String);
    check(grade, String);

    if (!this.userId) {
      throw new Meteor.Error("403", "No logged in user found!");
    }
    if (!(await auth.hasAccess(["superAdmin"], { userId: this.userId })) && !(await hasDashboard())) {
      throw new Meteor.Error("403", "You are not authorized to see Intervention Integrity");
    }

    return getInterventionIntegrity({ state, organizationId, siteId, grade });
  },
  async getScreeningAssessments({ benchmarkPeriodId, state, schoolYear }) {
    check(benchmarkPeriodId, String);
    check(state, String);
    check(schoolYear, Number);

    if (!this.userId) {
      throw new Meteor.Error("403", "No logged in user found!");
    }
    if (!(await auth.hasAccess(["superAdmin"], { userId: this.userId })) && !(await hasDashboard())) {
      throw new Meteor.Error("403", "You are not authorized to see Screening Assessments");
    }

    return getScreeningAssessments({ benchmarkPeriodId, state, schoolYear });
  },
  async getAssessmentsFunctioning({ grade, benchmarkPeriodId, state, schoolYear }) {
    check(grade, String);
    check(benchmarkPeriodId, String);
    check(state, String);
    check(schoolYear, Number);

    if (!this.userId) {
      throw new Meteor.Error("403", "No logged in user found!");
    }
    if (!(await auth.hasAccess(["superAdmin"], { userId: this.userId })) && !(await hasDashboard())) {
      throw new Meteor.Error("403", "You are not authorized to see Assessments Functioning");
    }

    return getAssessmentsFunctioning({ grade, benchmarkPeriodId, state, schoolYear });
  },
  async getNciiValues({ benchmarkPeriodId, state, schoolYear }) {
    check(benchmarkPeriodId, String);
    check(state, String);
    check(schoolYear, Number);

    if (!this.userId) {
      throw new Meteor.Error("403", "No logged in user found!");
    }
    if (!(await auth.hasAccess(["superAdmin"], { userId: this.userId })) && !(await hasDashboard())) {
      throw new Meteor.Error("403", "You are not authorized to see NCII Values");
    }

    return getNciiValues({ benchmarkPeriodId, state, schoolYear });
  },
  async getInterventionProgress({ grade, schoolYear, orgIds }) {
    check(grade, String);
    check(schoolYear, Number);
    check(orgIds, [String]);

    if (!this.userId) {
      throw new Meteor.Error("403", "No logged in user found!");
    }
    if (!(await auth.hasAccess(["superAdmin"], { userId: this.userId })) && !(await hasDashboard())) {
      throw new Meteor.Error("403", "You are not authorized to see Intervention Progress");
    }

    return getInterventionProgress({ grade, schoolYear, orgIds });
  },
  async getInterventionGrowth({ grade, schoolYear, orgIds, state }) {
    check(grade, String);
    check(schoolYear, Number);
    check(orgIds, Match.Maybe([String]));
    check(state, String);

    if (!this.userId) {
      throw new Meteor.Error("403", "No logged in user found!");
    }
    if (!(await auth.hasAccess(["superAdmin"], { userId: this.userId })) && !(await hasDashboard())) {
      throw new Meteor.Error("403", "You are not authorized to see Intervention Growth");
    }

    return getInterventionGrowth({ grade, schoolYear, orgIds, state });
  },
  async getAvailableAndGroupedAssessments() {
    if (!this.userId) {
      throw new Meteor.Error("403", "No logged in user found!");
    }
    if (!(await auth.hasAccess(["superAdmin"], { userId: this.userId }))) {
      throw new Meteor.Error("403", "You are not authorized to fetch available and grouped assessments");
    }

    const availableAssessments = await Assessments.find(
      { monitorAssessmentMeasure: { $exists: true } },
      { fields: { name: 1, monitorAssessmentMeasure: 1 } }
    ).fetchAsync();
    const groupedAssessments = await GroupedAssessments.find(
      {},
      { fields: { skillName: 1, assessmentMeasures: 1 } }
    ).fetchAsync();

    return {
      groupedAssessments,
      availableAssessments
    };
  },
  async "Assessments:updateAvailableVideoList"() {
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    if (!(await auth.hasAccess(["superAdmin"], { userId: this.userId }))) {
      throw new Meteor.Error(
        403,
        "User is not authorized to use Assessments:updateAvailableVideoList for this organization"
      );
    }
    if (!Meteor.settings.S3_ACCESS_KEY_ID?.length) {
      throw new Meteor.Error(401, "S3 bucket credentials not found");
    }
    return updateAvailableVideoList();
  }
});
