import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";
import { Assessments } from "../assessments";
import { isUserLoggedOut } from "../../utilities/utilities";

const defaultAssessmentFields = {
  _id: 1,
  name: 1,
  associatedGrades: 1,
  strands: 1,
  monitorAssessmentMeasure: 1,
  hasVideo: 1,
  protocolMeasures: 1,
  ir: 1
};

// Used to find the INTA assessments from the MN Database
Meteor.publish("Assessments", function assessmentsPublication() {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }

  return Assessments.find({}, { fields: defaultAssessmentFields });
});

Meteor.publish("AssessmentsForGrade", function assessmentsPublication(grade) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(grade, String);

  return Assessments.find(
    { associatedGrades: grade },
    {
      fields: defaultAssessmentFields
    }
  );
});

Meteor.publish("Assessments:ProgressMonitoringManagement", function() {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }

  return Assessments.find(
    {},
    {
      fields: {
        name: 1,
        strands: 1,
        monitorAssessmentMeasure: 1,
        associatedGrades: 1
      }
    }
  );
});

Meteor.publish("Assessment:Names", function() {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  return Assessments.find({}, { fields: { name: 1 } });
});
