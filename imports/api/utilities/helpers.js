import { Meteor } from "meteor/meteor";
import { difference, get, isEmpty } from "lodash";
import moment from "moment";
import { getTimestampInfo } from "../helpers/getTimestampInfo";
import { Students } from "../students/students";
import {
  addStudentsToCurrentClasswideActivities,
  manageStudentsScores,
  removeIndividualInterventionsFromActiveAssessments,
  removeStudentsFromActiveAssessments
} from "../rosterImports/manageStudentsScores";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";
import { Users } from "../users/users";
import { normalizeEmail, sendEnrollmentEmail } from "../users/server/methods";
import { Sites } from "../sites/sites";
import { getCourseSection, getNormalizedGroupName } from "../rosterImportItems/normalizeRosterImportItems";
import { isMissingSiteAccess } from "../users/server/utilities";
import { AssessmentResults } from "../assessmentResults/assessmentResults";
import { getCurrentSchoolYear, getMeteorUser, getMeteorUserId, isEmailValid } from "./utilities";
import { StudentGroups } from "../studentGroups/studentGroups";
import { Organizations } from "../organizations/organizations";
import { isHighSchoolGrade } from "../../ui/utilities";

export async function addTeacher({ teacher, orgid, siteId, schoolYear }) {
  if (
    await Users.findOneAsync({
      "profile.orgid": orgid,
      "profile.localId": teacher.localId
    })
  ) {
    throw new Meteor.Error("403", "User with this local ID already exists");
  }

  if (await Users.findOneAsync({ "emails.address": teacher.email })) {
    throw new Meteor.Error("403", "User with this email already exists");
  }

  const byDateOn = await getTimestampInfo(getMeteorUserId(), orgid);
  const newTeacher = {
    createdAt: byDateOn.date,
    services: {},
    emails: [{ address: normalizeEmail(teacher.email), verified: false }],
    profile: {
      onboarded: false,
      orgid,
      localId: teacher.localId,
      siteAccess: [
        {
          role: "arbitraryIdteacher",
          siteId,
          schoolYear,
          isActive: true,
          isDefault: true
        }
      ],
      name: { first: teacher.firstName, last: teacher.lastName },
      created: byDateOn,
      lastModified: byDateOn
    }
  };
  Users.validate({ profile: newTeacher.profile });
  const userId = await Users.insertAsync(newTeacher);
  const org = await Organizations.findOneAsync({ _id: orgid });
  if (!org?.useSSOOnly) {
    sendEnrollmentEmail(userId);
  }
  return userId;
}

async function addStudentGroup({ studentGroup, orgid, siteId, schoolYear, teacherId, secondaryTeachers = [] }) {
  const byDateOn = await getTimestampInfo(getMeteorUserId(), orgid);
  const courseSection = getCourseSection({
    classSectionID: studentGroup.sectionId
  });
  const studentGroupName = getNormalizedGroupName(studentGroup.name, courseSection);

  const newStudentGroup = {
    orgid,
    rosterImportId: byDateOn.by,
    type: "CLASS",
    schoolYear,
    created: byDateOn,
    lastModified: byDateOn,
    isActive: true,
    siteId,
    ownerIds: [teacherId],
    name: studentGroupName,
    grade: studentGroup.grade,
    sectionId: studentGroup.sectionId,
    secondaryTeachers
  };

  return StudentGroups.insertAsync(newStudentGroup);
}

export async function updateIsHighSchoolValueForSite(siteId) {
  const site = (await Sites.findOneAsync({ _id: siteId }, { fields: { isHighSchool: 1, orgid: 1 } })) || {};
  const lastModified = getTimestampInfo(getMeteorUserId(), site?.orgid, "updateIsHighSchoolValueForSite");
  const uniqueGrades = [
    ...new Set(
      await StudentGroups.find(
        {
          siteId,
          isActive: true
        },
        { fields: { grade: 1 } }
      ).mapAsync(({ grade }) => grade)
    )
  ];
  const shouldBeHighSchoolOnly = uniqueGrades.every(grade => isHighSchoolGrade(grade));
  if ((site.isHighSchool && !shouldBeHighSchoolOnly) || (!site.isHighSchool && shouldBeHighSchoolOnly)) {
    await Sites.updateAsync({ _id: siteId }, { $set: { isHighSchool: shouldBeHighSchoolOnly, lastModified } });
  }
}

export async function addClassToSite({ studentGroup, teacher, orgid, siteId, secondaryTeachers }) {
  const schoolYear = await getCurrentSchoolYear(await getMeteorUser(), orgid);
  let teacherId = studentGroup.teacher;
  const { sectionId } = studentGroup;

  if (
    await StudentGroups.findOneAsync({
      orgid,
      siteId,
      sectionId,
      schoolYear
    })
  ) {
    throw new Meteor.Error("403", "Class with this section ID already exists");
  }

  if (!isEmpty(teacher)) {
    if (!isEmailValid(teacher.email)) {
      throw new Meteor.Error("403", "Teacher email is not valid");
    }

    teacherId = await addTeacher({ teacher, orgid, siteId, schoolYear });
  }

  const groupOwner = await Users.findOneAsync(teacherId);
  const lastModified = await getTimestampInfo(getMeteorUserId(), orgid, "insertAssessmentsAndScores");
  if (isMissingSiteAccess(groupOwner.profile.siteAccess, siteId, schoolYear)) {
    const newSiteAccess = {
      role: groupOwner.profile.siteAccess[0].role,
      siteId,
      schoolYear,
      isActive: true,
      isDefault: true
    };
    const updatedSiteAccess = [newSiteAccess, ...groupOwner.profile.siteAccess];
    await Users.updateAsync(
      { _id: groupOwner._id },
      { $set: { "profile.siteAccess": updatedSiteAccess, "profile.lastModified": lastModified } }
    );
  } else if (
    groupOwner.profile.siteAccess.find(
      sa => sa.siteId === siteId && sa.schoolYear === schoolYear && sa.isActive === false
    )
  ) {
    await Users.updateAsync(
      { _id: groupOwner._id, "profile.siteAccess": { $elemMatch: { siteId, schoolYear } } },
      {
        $set: {
          "profile.siteAccess.$.isActive": true,
          "profile.lastModified": lastModified
        }
      }
    );
  }

  const newStudentGroupId = await addStudentGroup({
    studentGroup: { ...studentGroup, sectionId },
    orgid,
    siteId,
    schoolYear,
    teacherId,
    secondaryTeachers
  });

  await updateIsHighSchoolValueForSite(siteId);

  return newStudentGroupId;
}

export async function addStudentToGroup({ student, studentGroup }) {
  const { orgid, schoolYear, siteId, _id: studentGroupId, grade } = studentGroup;
  const { lastName, firstName, birthDate, localId, stateId, studentGrade } = student;

  if (birthDate?.length && (birthDate.length < 8 || birthDate.length > 10 || !Date.parse(birthDate))) {
    throw new Meteor.Error(422, 'Student birth date should be in the "YYYY-MM-DD" format');
  }

  const studentDocument = await Students.findOneAsync({
    orgid,
    schoolYear,
    $or: [{ "identity.identification.localId": localId }, { "identity.identification.stateId": stateId }]
  });

  if (studentDocument) {
    const studentEnrollment = await StudentGroupEnrollments.findOneAsync({
      studentId: studentDocument._id,
      siteId,
      schoolYear
    });
    const hasActiveEnrollment = studentEnrollment ? studentEnrollment.isActive : false;
    const studentIdentity = `${studentDocument.identity.name.lastName}, ${studentDocument.identity.name.firstName}${
      hasActiveEnrollment ? "" : " (Archived)"
    }`;
    if (studentDocument.identity.identification.localId === localId) {
      throw new Meteor.Error(
        422,
        `Student with Local ID: ${localId} - ${studentIdentity}, already exists in this organization`
      );
    }
    throw new Meteor.Error(
      422,
      `Student with State ID: ${stateId} - ${studentIdentity}, already exists in this organization`
    );
  }

  const {
    stateInformation: { districtNumber }
  } = await Sites.findOneAsync(siteId);
  const rosterImportId = "add_student_ui";
  const byDateOn = await getTimestampInfo(getMeteorUserId(), orgid);

  const demographic = {
    ethnicity: "",
    gender: "",
    gt: "",
    sped: "",
    ell: "",
    title: ""
  };
  if (birthDate?.length) {
    const birthDateObject = moment.utc(birthDate).toDate();
    const formattedBirthDate = birthDateObject?.toISOString().substr(0, 10);
    const birthDateTimeStamp = birthDateObject?.getTime();
    demographic.birthDate = formattedBirthDate;
    demographic.birthDateTimeStamp = birthDateTimeStamp;
  }
  const studentDoc = {
    orgid,
    schoolYear,
    districtNumber,
    created: byDateOn,
    lastModified: byDateOn,
    rosterImportId,
    grade,
    ...(studentGrade && { studentGrade }),
    demographic,
    identity: {
      name: { firstName, lastName, middleName: "" },
      identification: { localId, stateId }
    }
  };
  // required to make possible passing specific Simple Schema error to the client
  try {
    Students.validate(studentDoc);
  } catch (e) {
    throw new Meteor.Error("Schema validation Error", get(e, "details[0].message"));
  }
  const studentId = await Students.insertAsync(studentDoc);

  const newStudentGroupEnrollment = {
    orgid,
    siteId,
    grade,
    studentGroupId,
    studentId,
    schoolYear
  };

  await insertStudentGroupEnrollment(newStudentGroupEnrollment, byDateOn, rosterImportId);

  await addStudentsToCurrentClasswideActivities({
    currentGroup: studentGroup,
    newStudentsIds: [studentId]
  });
}

export async function archiveStudents({ studentIds, studentGroup }) {
  const { orgid, schoolYear, siteId, _id: studentGroupId } = studentGroup;

  const byDateOn = await getTimestampInfo(getMeteorUserId(), orgid, "archiveStudents");

  const archivedStudents = [];
  // eslint-disable-next-line no-restricted-syntax
  for await (const studentId of studentIds) {
    const studentGroupEnrollment = await getCurrentStudentGroupEnrollment({
      orgid,
      siteId,
      studentGroupId,
      studentId,
      schoolYear
    });
    if (studentGroupEnrollment) {
      await deactivateStudentGroupEnrollment(studentGroupEnrollment._id, byDateOn, "archive_students_ui");
    }
    await Students.updateAsync(studentId, { $unset: { currentSkill: "" }, $set: { lastModified: byDateOn } });
    archivedStudents.push({ studentId, studentGroupId });
  }

  await removeIndividualInterventionsFromActiveAssessments(archivedStudents);
  await removeStudentsFromActiveAssessments(archivedStudents);
  const { wasGroupDeactivated } = await deactivateEmptyStudentGroup(studentGroup, schoolYear, byDateOn);

  if (wasGroupDeactivated) {
    await updateIsHighSchoolValueForSite(studentGroup.siteId);
  }
  return { wasGroupDeactivated };
}

export async function deactivateEmptyStudentGroup(previousGroup, schoolYear, byDateOn) {
  const isGroupWithoutActiveEnrollment =
    previousGroup &&
    (await StudentGroupEnrollments.find({
      studentGroupId: previousGroup._id,
      isActive: true,
      schoolYear
    }).countAsync()) === 0;
  if (isGroupWithoutActiveEnrollment) {
    await StudentGroups.updateAsync(
      { _id: previousGroup._id },
      { $set: { isActive: false, lastModified: { ...byDateOn, context: "deactivateEmptyStudentGroup" } } }
    );
  }
  return {
    wasGroupDeactivated: isGroupWithoutActiveEnrollment
  };
}

export async function moveStudentsBetweenGroups({ students, previousGroup, nextGroup, isUnarchiveOperation = false }) {
  const { orgid, schoolYear } = nextGroup;
  const byDateOn = await getTimestampInfo(getMeteorUserId(), orgid, "moveStudentsBetweenGroups");
  // eslint-disable-next-line no-restricted-syntax
  for await (const student of students) {
    let studentGroupEnrollment;
    if (isUnarchiveOperation) {
      studentGroupEnrollment = await getLatestStudentGroupEnrollment({
        orgid,
        studentId: student._id,
        schoolYear
      });
    } else {
      studentGroupEnrollment = await getCurrentStudentGroupEnrollment({
        orgid,
        siteId: previousGroup.siteId,
        studentGroupId: previousGroup._id,
        studentId: student._id,
        schoolYear
      });
      if (studentGroupEnrollment) {
        await deactivateStudentGroupEnrollment(studentGroupEnrollment._id, byDateOn);
      }
    }
    const newStudentGroupEnrollment = {
      ...(studentGroupEnrollment || {}),
      orgid: nextGroup.orgid,
      siteId: nextGroup.siteId,
      grade: nextGroup.grade,
      studentGroupId: nextGroup._id,
      studentId: student._id,
      giftedAndTalented: studentGroupEnrollment?.giftedAndTalented ?? false,
      ELL: studentGroupEnrollment?.ELL ?? false,
      title1: studentGroupEnrollment?.title1 ?? false,
      freeReducedLunch: studentGroupEnrollment?.freeReducedLunch ?? "no"
    };
    await insertStudentGroupEnrollment(newStudentGroupEnrollment, byDateOn);
    await Students.updateAsync(student._id, { $set: { grade: nextGroup.grade, lastModified: byDateOn } });
  }
  const studentIdsByGroupIdsForHistoryCleanup = await getStudentIdsByGroupIdsForHistoryCleanup({
    students,
    previousGroupId: previousGroup ? previousGroup._id : null,
    schoolYear
  });
  await manageStudentsScores(studentIdsByGroupIdsForHistoryCleanup, orgid);

  const { wasGroupDeactivated } = await deactivateEmptyStudentGroup(previousGroup, schoolYear, byDateOn);

  if (wasGroupDeactivated) {
    await updateIsHighSchoolValueForSite(previousGroup.siteId);

    if (previousGroup.siteId !== nextGroup.siteId) {
      await updateIsHighSchoolValueForSite(nextGroup.siteId);
    }
  }

  return { wasGroupDeactivated };
}

async function getStudentIdsByGroupIdsForHistoryCleanup({ students, previousGroupId, schoolYear }) {
  const studentIdsByGroupIdsForHistoryCleanup = {
    classwide: {},
    individual: {},
    other: {}
  };
  const studentIds = students.map(student => student._id);
  const idsOfStudentsInIntervention = [];

  if (previousGroupId) {
    const isPreviousStudentGroupInClasswideIntervention = await isStudentGroupInClasswideIntervention(
      previousGroupId,
      schoolYear
    );

    if (isPreviousStudentGroupInClasswideIntervention) {
      studentIdsByGroupIdsForHistoryCleanup.classwide[previousGroupId] = studentIds;
      idsOfStudentsInIntervention.push(...studentIds);
    }
  }

  const groupIdOrUnenrolled = previousGroupId || "unenrolled";

  const idsOfStudentsInIndividualIntervention = (
    await AssessmentResults.find(
      {
        studentId: { $in: studentIds },
        type: "individual",
        status: "OPEN",
        schoolYear
      },
      { fields: { studentId: 1 } }
    ).fetchAsync()
  ).map(assessmentResult => assessmentResult.studentId);
  if (idsOfStudentsInIndividualIntervention.length) {
    studentIdsByGroupIdsForHistoryCleanup.individual[groupIdOrUnenrolled] = idsOfStudentsInIndividualIntervention;
    idsOfStudentsInIntervention.push(...idsOfStudentsInIndividualIntervention);
  }

  const idsOfStudentsWithoutIntervention = difference(studentIds, idsOfStudentsInIntervention);
  if (idsOfStudentsWithoutIntervention.length) {
    studentIdsByGroupIdsForHistoryCleanup.other[groupIdOrUnenrolled] = idsOfStudentsWithoutIntervention;
  }

  return studentIdsByGroupIdsForHistoryCleanup;
}

async function isStudentGroupInClasswideIntervention(studentGroupId, schoolYear) {
  return !!(await StudentGroups.findOneAsync({
    _id: studentGroupId,
    "currentClasswideSkill.assessmentResultId": { $exists: true },
    isActive: true,
    schoolYear
  }));
}

async function getCurrentStudentGroupEnrollment({
  orgid,
  siteId,
  studentGroupId,
  studentId,
  schoolYear,
  isActive = true
}) {
  return StudentGroupEnrollments.findOneAsync({
    orgid,
    siteId,
    studentGroupId,
    studentId,
    isActive,
    schoolYear
  });
}

async function getLatestStudentGroupEnrollment({ orgid, studentId, schoolYear, isActive = false }) {
  return StudentGroupEnrollments.findOneAsync(
    {
      orgid,
      studentId,
      isActive,
      schoolYear
    },
    { sort: { "created.on": -1 } }
  );
}

async function deactivateStudentGroupEnrollment(
  studentGroupEnrollmentId,
  byDateOn,
  rosterImportId = "move_students_ui"
) {
  return StudentGroupEnrollments.updateAsync(studentGroupEnrollmentId, {
    $set: {
      isActive: false,
      lastModified: byDateOn,
      rosterImportId
    }
  });
}

async function insertStudentGroupEnrollment(studentGroupEnrollment, byDateOn, rosterImportId = "move_students_ui") {
  const newStudentGroupEnrollment = {
    ...studentGroupEnrollment,
    rosterImportId,
    isActive: true,
    IEP: null,
    created: byDateOn,
    lastModified: byDateOn,
    wasIndividualInterventionClosed: false
  };

  delete newStudentGroupEnrollment._id;

  return StudentGroupEnrollments.insertAsync(newStudentGroupEnrollment);
}
