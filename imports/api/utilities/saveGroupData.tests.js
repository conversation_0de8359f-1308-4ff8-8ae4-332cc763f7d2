/* eslint-disable global-require */
import { Organizations } from "../organizations/organizations";
import { Students } from "../students/students";
import { StudentGroups } from "../studentGroups/studentGroups";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";
import { Sites } from "../sites/sites";
import { Users } from "../users/users";
import { changeGroupGrade, getTeachersToAddAndRemoveSiteAccess, saveGroupData } from "./saveGroupData";
import { setupTestContext } from "../rosterImports/rosterImportsProcessor.testHelpers";
import { getTestGroupInContext, getUserInContext } from "../../test-helpers/data/saveGroupData.testData";
import { addStudentToGroup } from "./helpers";

const { schoolYear, orgid, siteId, districtName } = setupTestContext();

// Mock utilities globally for all tests
jest.mock("./utilities", () => ({
  getCurrentSchoolYear: jest.fn(() => 2018), // Use the same school year as setupTestContext
  getMeteorUserId: jest.fn(() => "someUserId"),
  getMeteorUser: jest.fn(() => ({ profile: {} })),
  idValidation: { regex: /.*/, description: "" },
  validateSchemaProperty: jest.fn(() => () => undefined)
}));

// Mock getTimestampInfo globally
jest.mock("../helpers/getTimestampInfo", () => ({
  getTimestampInfo: jest.fn(() => ({
    by: "testUserId",
    on: new Date().getTime(),
    date: new Date()
  }))
}));

// Mock benchmark windows methods globally
jest.mock("../benchmarkWindows/methods.js", () => ({
  getCurrentBenchmarkWindowWithSiteId: jest.fn(() => ({
    schoolYear: 2018,
    orgid: "test_orgid",
    benchmarkPeriodId: "winter"
  })),
  createCurrentBenchmarkWindowForSite: jest.fn(() => {})
}));
describe("changeGroupGrade", () => {
  afterAll(() => {
    jest.restoreAllMocks();
  });
  const studentGroupId = "studentGroupId";
  const studentLocalId = "123456";
  const studentStateId = "*********";
  const groupGrade = "07";
  const teacherAccountId = "testOwnerId";
  const organization = {
    _id: orgid,
    name: districtName
  };
  const student = {
    lastName: "Lastname",
    firstName: "Firstname",
    grade: groupGrade,
    birthDate: "2000-01-01",
    localId: studentLocalId,
    stateId: studentStateId
  };
  const studentGroup = {
    orgid,
    schoolYear,
    siteId,
    grade: groupGrade,
    _id: studentGroupId,
    isActive: true,
    ownerIds: [teacherAccountId],
    sectionId: "testSectionId",
    name: "testName"
  };
  const site = {
    _id: siteId,
    orgid,
    name: "Test Elementary Site",
    schoolYear,
    stateInformation: {
      districtNumber: "1"
    }
  };
  const teacherAccount = {
    _id: teacherAccountId,
    profile: {
      siteAccess: [
        {
          siteId,
          schoolYear
        }
      ]
    }
  };
  beforeEach(async () => {
    await Organizations.insertAsync(organization);
    await StudentGroups.insertAsync(studentGroup);
    await Sites.insertAsync(site);
    await Users.insertAsync(teacherAccount);

    await addStudentToGroup({ student, studentGroup });
  });
  afterEach(async () => {
    await Students.removeAsync({});
    await StudentGroups.removeAsync({});
    await StudentGroupEnrollments.removeAsync({});
    await Sites.removeAsync({});
    await Users.removeAsync({});
    await Organizations.removeAsync({});
  });
  const targetGrade = "02";
  it("should move group from one grade to another along with students", async () => {
    const newGroupId = await changeGroupGrade({
      studentGroupId,
      grade: targetGrade
    });

    const oldStudentGroup = await StudentGroups.findOneAsync(studentGroupId);
    const newStudentGroup = await StudentGroups.findOneAsync(newGroupId);
    const oldStudentEnrollment = await StudentGroupEnrollments.findOneAsync({
      studentGroupId
    });
    const newStudentEnrollment = await StudentGroupEnrollments.findOneAsync({
      studentGroupId: newGroupId
    });
    expect(newStudentGroup.grade).toEqual(targetGrade);
    expect(newStudentGroup.isActive).toEqual(true);
    expect(oldStudentGroup.isActive).toEqual(false);
    expect(newStudentGroup.ownerIds[0]).toEqual(teacherAccount._id);
    expect(oldStudentGroup.sectionId).not.toEqual(newStudentGroup.sectionId);

    expect(oldStudentEnrollment.isActive).toEqual(false);
    expect(newStudentEnrollment.isActive).toEqual(true);
    expect(newStudentEnrollment.grade).toEqual(targetGrade);
  });

  it("should move students to existing student group if it has the same sectionId and target grade", async () => {
    const studentGroup2 = { ...studentGroup, _id: `${studentGroupId}2`, grade: targetGrade };
    const studentGroup3 = { ...studentGroup, _id: `${studentGroupId}1_2017`, grade: targetGrade, schoolYear: 2017 };
    const student2 = { ...student, grade: targetGrade, localId: `${studentLocalId}2`, stateId: `${studentStateId}2` };
    const student3 = { ...student, grade: targetGrade, localId: `${studentLocalId}3`, stateId: `${studentStateId}3` };

    await StudentGroups.insertAsync(studentGroup2);
    await StudentGroups.insertAsync(studentGroup3);

    await addStudentToGroup({ student: student2, studentGroup: studentGroup2 });
    await addStudentToGroup({ student: student3, studentGroup: studentGroup3 });

    const newGroupId = await changeGroupGrade({
      studentGroupId,
      grade: targetGrade
    });

    const oldStudentGroup = await StudentGroups.findOneAsync(studentGroupId);
    const newStudentGroup = await StudentGroups.findOneAsync(newGroupId);
    const oldStudentEnrollment = await StudentGroupEnrollments.findOneAsync({
      studentGroupId
    });
    const newStudentEnrollment = await StudentGroupEnrollments.findOneAsync({
      studentGroupId: newGroupId
    });
    expect(newStudentGroup.grade).toEqual(targetGrade);
    expect(newStudentGroup.isActive).toEqual(true);
    expect(oldStudentGroup.isActive).toEqual(false);
    expect(newStudentGroup.ownerIds[0]).toEqual(teacherAccount._id);
    expect(oldStudentGroup.sectionId).not.toEqual(newStudentGroup.sectionId);

    expect(oldStudentEnrollment.isActive).toEqual(false);
    expect(newStudentEnrollment.isActive).toEqual(true);
    expect(newStudentEnrollment.grade).toEqual(targetGrade);
  });
});

describe("saveGroupData", () => {
  const teacherRole = "arbitraryIdteacher";
  const adminRole = "arbitraryIdadmin";
  const getTeacherWithSiteAccess = getUserInContext({ role: teacherRole, schoolYear });
  const getAdminWithSiteAccess = getUserInContext({ role: adminRole, schoolYear });
  const getTestStudentGroup = getTestGroupInContext({ schoolYear });
  const studentGroupId = "sgId";
  const studentGroup = { _id: studentGroupId };
  beforeEach(async () => {
    await StudentGroups.insertAsync(studentGroup);
  });
  afterEach(async () => {
    await StudentGroups.removeAsync({});
    await Users.removeAsync({});
  });
  it("should change primary teacher if primary teacher changed", async () => {
    const newOwnerId = "newOwner";

    await saveGroupData({
      studentGroupId,
      hasPrimaryTeacherChanged: true,
      newTeacherId: newOwnerId
    });

    const updatedGroup = await StudentGroups.findOneAsync(studentGroupId);
    expect(updatedGroup.ownerIds).toEqual([newOwnerId]);
  });
  it("should update secondary teachers", async () => {
    const newSecondaryTeachers = ["1", "2"];
    const existingSecondaryTeachers = ["existing"];
    await StudentGroups.updateAsync(
      { _id: studentGroupId },
      { $set: { secondaryTeachers: existingSecondaryTeachers } }
    );
    await saveGroupData({
      studentGroupId,
      secondaryTeachers: newSecondaryTeachers
    });

    const updatedGroup = await StudentGroups.findOneAsync(studentGroupId);
    expect(updatedGroup.secondaryTeachers).toEqual(newSecondaryTeachers);
  });
  it("should update group name", async () => {
    const existingName = "existingName";
    await StudentGroups.updateAsync({ _id: studentGroupId }, { $set: { name: existingName } });
    const newName = "newName";

    await saveGroupData({
      studentGroupId,
      studentGroupName: newName
    });

    const updatedGroup = await StudentGroups.findOneAsync(studentGroupId);
    expect(updatedGroup.name).toEqual(newName);
  });
  it("should add site access to a new primary teacher if the teacher doesn't already have access to the site", async () => {
    const previousSiteId = "previousSiteId";
    const teacherId = "teacherId";
    const teacher = getTeacherWithSiteAccess({ _id: teacherId, siteIds: [previousSiteId] });
    await Users.insertAsync(teacher);
    const newSiteId = "newSiteId";

    await saveGroupData({
      studentGroupId,
      hasPrimaryTeacherChanged: true,
      newTeacherId: teacherId,
      siteId: newSiteId
    });

    const updatedTeacher = await Users.findOneAsync(teacherId);
    expect(updatedTeacher.profile.siteAccess).toHaveLength(2);
    expect(
      updatedTeacher.profile.siteAccess.find(sa => sa.siteId === newSiteId && sa.schoolYear === schoolYear)
    ).toBeTruthy();
  });
  it("should not remove primary teacher site access when the teacher still owns group(s) in the site", async () => {
    const teacherId = "teacherId";
    const firstSiteId = "firstSiteId";
    const secondSiteId = "secondSiteId";
    const teacher = getTeacherWithSiteAccess({ _id: teacherId, siteIds: [firstSiteId, secondSiteId] });
    await Users.insertAsync(teacher);
    const firstGroupFirstSite = "firstGroupFirstSite";
    const ownerGroups = [
      getTestStudentGroup({ _id: firstGroupFirstSite, ownerIds: [teacherId], siteId: firstSiteId }),
      getTestStudentGroup({ ownerIds: [teacherId], siteId: firstSiteId }),
      getTestStudentGroup({ ownerIds: [teacherId], siteId: secondSiteId })
    ];
    await StudentGroups.insertAsync(ownerGroups);

    // Removing ownership from one of the groups in a site where a teacher owns 2 groups
    await saveGroupData({
      studentGroupId: firstGroupFirstSite,
      hasPrimaryTeacherChanged: true,
      newTeacherId: "someOtherTeacher",
      siteId: firstSiteId
    });

    const updatedTeacher = await Users.findOneAsync(teacherId);
    expect(updatedTeacher.profile.siteAccess).toEqual(teacher.profile.siteAccess);
  });
  it("should deactivate primary teacher site access when the teacher no longer owns any group in the site", async () => {
    const teacherId = "teacherId";
    const firstSiteId = "firstSiteId";
    const secondSiteId = "secondSiteId";
    const teacher = getTeacherWithSiteAccess({ _id: teacherId, siteIds: [firstSiteId, secondSiteId] });
    await Users.insertAsync(teacher);
    const firstGroupSecondSite = "firstGroupSecondSite";
    const ownerGroups = [
      getTestStudentGroup({ ownerIds: [teacherId], siteId: firstSiteId }),
      getTestStudentGroup({ ownerIds: [teacherId], siteId: firstSiteId }),
      getTestStudentGroup({ _id: firstGroupSecondSite, ownerIds: [teacherId], siteId: secondSiteId })
    ];
    await StudentGroups.insertAsync(ownerGroups);

    // Removing ownership from the group in the site where a teacher owns only one group
    await saveGroupData({
      studentGroupId: firstGroupSecondSite,
      hasPrimaryTeacherChanged: true,
      newTeacherId: "someOtherTeacher",
      siteId: secondSiteId
    });

    const updatedTeacher = await Users.findOneAsync(teacherId);
    expect(updatedTeacher.profile.siteAccess).toHaveLength(2);
    expect(
      updatedTeacher.profile.siteAccess.find(sa => sa.siteId === secondSiteId && sa.isActive === false)
    ).toBeTruthy();
  });
  it("should not remove a primary teacher site access when the teacher has access to only one site", async () => {
    const teacherId = "teacherId";
    const firstSiteId = "firstSiteId";
    const teacher = getTeacherWithSiteAccess({ _id: teacherId, siteIds: [firstSiteId] });
    await Users.insertAsync(teacher);
    const firstGroupFirstSite = "firstGroupFirstSite";
    const ownerGroups = [{ _id: firstGroupFirstSite, ownerIds: [teacherId], siteId: firstSiteId }];
    await StudentGroups.insertAsync(ownerGroups);

    // Removing ownership from the single group in the site where a teacher owns only one group
    await saveGroupData({
      studentGroupId: firstGroupFirstSite,
      hasPrimaryTeacherChanged: true,
      newTeacherId: "someOtherTeacher",
      siteId: firstSiteId
    });

    const updatedTeacher = await Users.findOneAsync(teacherId);
    expect(updatedTeacher.profile.siteAccess).toHaveLength(1);
    expect(
      updatedTeacher.profile.siteAccess.find(sa => sa.siteId === firstSiteId && sa.schoolYear === schoolYear)
    ).toBeTruthy();
  });
  // SECONDARY TEACHERS
  it("should add site access to a new secondary teacher if the teacher doesn't already have access to the site", async () => {
    const previousSiteId = "previousSiteId";
    const secondaryTeacherId = "teacherId";
    const teacher = getTeacherWithSiteAccess({ _id: secondaryTeacherId, siteIds: [previousSiteId] });
    await Users.insertAsync(teacher);

    const newSiteId = "newSiteId";
    await saveGroupData({
      studentGroupId,
      secondaryTeachers: [secondaryTeacherId],
      siteId: newSiteId
    });

    const updatedTeacher = await Users.findOneAsync(secondaryTeacherId);
    expect(updatedTeacher.profile.siteAccess).toHaveLength(2);
    expect(
      updatedTeacher.profile.siteAccess.find(sa => sa.siteId === newSiteId && sa.schoolYear === schoolYear)
    ).toBeTruthy();
  });
  // NOTE(fmazur) - { isActive: false } cannot be tested unless "profile.siteAccess.$.isActive" positional operator is refactored
  // NOTE(fmazur) - in addSiteAccessToUser due to netb-promise being based on netb 2.x doesn't support positional operators
  // it(
  //   "should add site access to a secondary teacher if the teacher already had inactive access to the site",
  //   async () => {
  //     const secondaryTeacherId = "teacherId";
  //     const teacher = getTeacherWithSiteAccess({ _id: secondaryTeacherId, siteIds: [siteId], isActive: false });
  //     await Users.insertAsync(teacher);
  //
  //     await saveGroupData({
  //       studentGroupId,
  //       secondaryTeachers: [secondaryTeacherId],
  //       siteId
  //     });
  //
  //     const updatedTeacher = await Users.findOneAsync(secondaryTeacherId);
  //     expect(updatedTeacher.profile.siteAccess).toHaveLength(2);
  //     const updatedSiteAccess = updatedTeacher.profile.siteAccess.find(
  //       sa => sa.siteId === siteId && sa.schoolYear === schoolYear
  //     );
  //     expect(updatedSiteAccess).toBeTruthy();
  //     expect(updatedSiteAccess.isActive).toBe(true);
  //   })
  // );
  it("should not remove secondary teacher site access when the teacher is still a secondary teacher in other group(s) in the site", async () => {
    const secondaryTeacherId = "teacherId";
    const firstSiteId = "firstSiteId";
    const secondSiteId = "secondSiteId";
    const teacher = getTeacherWithSiteAccess({ _id: secondaryTeacherId, siteIds: [firstSiteId, secondSiteId] });
    await Users.insertAsync(teacher);
    const firstGroupFirstSite = "firstGroupFirstSite";
    const secondaryTeacherGroups = [
      getTestStudentGroup({
        _id: firstGroupFirstSite,
        secondaryTeachers: [secondaryTeacherId],
        siteId: firstSiteId
      }),
      getTestStudentGroup({
        secondaryTeachers: [secondaryTeacherId],
        siteId: firstSiteId
      }),
      getTestStudentGroup({
        secondaryTeachers: [secondaryTeacherId],
        siteId: secondSiteId
      })
    ];
    await StudentGroups.insertAsync(secondaryTeacherGroups);

    // Removing ownership from one of the groups in a site where a teacher owns 2 groups
    await saveGroupData({
      studentGroupId: firstGroupFirstSite,
      secondaryTeachers: [],
      siteId: firstSiteId
    });

    const updatedTeacher = await Users.findOneAsync(secondaryTeacherId);
    expect(updatedTeacher.profile.siteAccess).toEqual(teacher.profile.siteAccess);
  });
  it("should remove secondary teacher site access when the teacher is no longer a secondary teacher in other group(s) in the site", async () => {
    const secondaryTeacherId = "teacherId";
    const firstSiteId = "firstSiteId";
    const secondSiteId = "secondSiteId";
    const teacher = getTeacherWithSiteAccess({ _id: secondaryTeacherId, siteIds: [firstSiteId, secondSiteId] });
    await Users.insertAsync(teacher);
    const firstGroupSecondSite = "firstGroupSecondSite";
    const ownerGroups = [
      getTestStudentGroup({
        secondaryTeachers: [secondaryTeacherId],
        siteId: firstSiteId
      }),
      getTestStudentGroup({
        secondaryTeachers: [secondaryTeacherId],
        siteId: firstSiteId
      }),
      getTestStudentGroup({
        _id: firstGroupSecondSite,
        secondaryTeachers: [secondaryTeacherId],
        siteId: secondSiteId
      })
    ];
    await StudentGroups.insertAsync(ownerGroups);

    // Removing secondary teacher from the group in the site where a teacher owns only one group
    await saveGroupData({
      studentGroupId: firstGroupSecondSite,
      secondaryTeachers: [],
      siteId: secondSiteId
    });

    const updatedTeacher = await Users.findOneAsync(secondaryTeacherId);
    expect(updatedTeacher.profile.siteAccess).toHaveLength(2);
    expect(
      updatedTeacher.profile.siteAccess.find(sa => sa.siteId === secondSiteId && sa.isActive === false)
    ).toBeTruthy();
  });
  it("should not remove a secondary teacher site access when the teacher has access to only one site", async () => {
    const secondaryTeacherId = "teacherId";
    const firstSiteId = "firstSiteId";
    const teacher = getTeacherWithSiteAccess({ _id: secondaryTeacherId, siteIds: [firstSiteId] });
    await Users.insertAsync(teacher);
    const firstGroupFirstSite = "firstGroupFirstSite";
    const ownerGroups = [
      getTestStudentGroup({
        _id: firstGroupFirstSite,
        secondaryTeachers: [secondaryTeacherId],
        siteId: firstSiteId
      })
    ];
    await StudentGroups.insertAsync(ownerGroups);

    // Removing ownership from the single group in the site where a teacher owns only one group
    await saveGroupData({
      studentGroupId: firstGroupFirstSite,
      secondaryTeachers: [secondaryTeacherId],
      siteId: firstSiteId
    });

    const updatedTeacher = await Users.findOneAsync(secondaryTeacherId);
    expect(updatedTeacher.profile.siteAccess).toHaveLength(1);
    expect(
      updatedTeacher.profile.siteAccess.find(sa => sa.siteId === firstSiteId && sa.schoolYear === schoolYear)
    ).toBeTruthy();
  });
  // ADMINS
  it("should add site access to admin if the admin doesn't already have access to the site", async () => {
    const previousSiteId = "previousSiteId";
    const adminId = "teacherId";
    const admin = getAdminWithSiteAccess({ _id: adminId, siteIds: [previousSiteId] });
    await Users.insertAsync(admin);
    const newSiteId = "newSiteId";

    await saveGroupData({
      studentGroupId,
      hasPrimaryTeacherChanged: true,
      newTeacherId: adminId,
      siteId: newSiteId
    });

    const updatedAdmin = await Users.findOneAsync(adminId);
    expect(updatedAdmin.profile.siteAccess).toHaveLength(2);
    expect(
      updatedAdmin.profile.siteAccess.find(
        sa => sa.siteId === newSiteId && sa.schoolYear === schoolYear && sa.role === adminRole
      )
    ).toBeTruthy();
  });
  it("should not remove admin's site access when admin no longer owns any group in the site", async () => {
    const adminId = "teacherId";
    const firstSiteId = "firstSiteId";
    const secondSiteId = "secondSiteId";
    const admin = getAdminWithSiteAccess({ _id: adminId, siteIds: [firstSiteId, secondSiteId] });
    await Users.insertAsync(admin);
    const firstGroupSecondSite = "firstGroupSecondSite";
    const adminOwnedGroups = [
      getTestStudentGroup({ ownerIds: [adminId], siteId: firstSiteId }),
      getTestStudentGroup({ ownerIds: [adminId], siteId: firstSiteId }),
      getTestStudentGroup({ _id: firstGroupSecondSite, ownerIds: [adminId], siteId: secondSiteId })
    ];
    await StudentGroups.insertAsync(adminOwnedGroups);

    // Don't remove the site access for admins, it should be done differently for them
    await saveGroupData({
      studentGroupId: firstGroupSecondSite,
      hasPrimaryTeacherChanged: true,
      newTeacherId: "someOtherTeacher",
      siteId: secondSiteId
    });

    const updatedTeacher = await Users.findOneAsync(adminId);
    expect(updatedTeacher.profile.siteAccess).toHaveLength(2);
    expect(updatedTeacher.profile.siteAccess.find(sa => sa.siteId === secondSiteId)).toBeTruthy();
  });
});
describe("getTeachersToAddAndRemoveSiteAccess", () => {
  it("should return empty lists by default ", () => {
    const { userIdsGainingAccess, userIdsLosingAccess } = getTeachersToAddAndRemoveSiteAccess({});

    expect(userIdsGainingAccess).toEqual([]);
    expect(userIdsLosingAccess).toEqual([]);
  });
  it("should return new teacher id in usersGainingAccess when provided and when no previous owners are available", () => {
    const newTeacherId = "newTeacherId";
    const previousOwnerIds = [];

    const { userIdsGainingAccess, userIdsLosingAccess } = getTeachersToAddAndRemoveSiteAccess({
      newTeacherId,
      previousOwnerIds
    });

    expect(userIdsGainingAccess).toEqual([newTeacherId]);
    expect(userIdsLosingAccess).toEqual([]);
  });
  it("should return all new secondary teachers provided in usersGainingAccess when previous secondary teachers are empty", () => {
    const newSecondaryTeachers = ["newSecondaryTeacher1", "newSecondaryTeacher2"];
    const previousSecondaryTeachers = [];

    const { userIdsGainingAccess, userIdsLosingAccess } = getTeachersToAddAndRemoveSiteAccess({
      newSecondaryTeachers,
      previousSecondaryTeachers
    });

    expect(userIdsGainingAccess).toEqual(newSecondaryTeachers);
    expect(userIdsLosingAccess).toEqual([]);
  });
  it("should combine new secondary teachers with new teacher in usersGainingAccess when when both are provided and when previous values do not exist", () => {
    const newSecondaryTeachers = ["newSecondaryTeacher1", "newSecondaryTeacher2"];
    const previousSecondaryTeachers = [];
    const newTeacherId = "newTeacherId";
    const previousOwnerIds = [];

    const { userIdsGainingAccess, userIdsLosingAccess } = getTeachersToAddAndRemoveSiteAccess({
      newSecondaryTeachers,
      previousSecondaryTeachers,
      newTeacherId,
      previousOwnerIds
    });

    expect(userIdsGainingAccess).toEqual([newTeacherId, ...newSecondaryTeachers]);
    expect(userIdsLosingAccess).toEqual([]);
  });
  it("should return previous owner id in usersLosingAccess when new teacher is provided", () => {
    const newTeacherId = "newTeacherId";
    const previousTeacherId = "previousTeacherId";
    const previousOwnerIds = [previousTeacherId];

    const { userIdsGainingAccess, userIdsLosingAccess } = getTeachersToAddAndRemoveSiteAccess({
      newTeacherId,
      previousOwnerIds
    });

    expect(userIdsGainingAccess).toEqual([newTeacherId]);
    expect(userIdsLosingAccess).toEqual(previousOwnerIds);
  });
  it("should return newly added secondary teachers as compared to previous secondary teachers in usersGainingAccess", () => {
    const newlyAddedSecondaryTeacher = "newlyAdded";
    const newSecondaryTeachers = [newlyAddedSecondaryTeacher, "existingBefore"];
    const previousSecondaryTeachers = ["existingBefore"];

    const { userIdsGainingAccess, userIdsLosingAccess } = getTeachersToAddAndRemoveSiteAccess({
      newSecondaryTeachers,
      previousSecondaryTeachers
    });

    expect(userIdsGainingAccess).toEqual([newlyAddedSecondaryTeacher]);
    expect(userIdsLosingAccess).toEqual([]);
  });
  it("should return removed secondary teachers as compared to new secondary teachers in usersLosingAccess", () => {
    const removedSecondaryTeacher = "removedTeacher";
    const newSecondaryTeachers = ["existingBefore"];
    const previousSecondaryTeachers = ["existingBefore", removedSecondaryTeacher];

    const { userIdsGainingAccess, userIdsLosingAccess } = getTeachersToAddAndRemoveSiteAccess({
      newSecondaryTeachers,
      previousSecondaryTeachers
    });

    expect(userIdsGainingAccess).toEqual([]);
    expect(userIdsLosingAccess).toEqual([removedSecondaryTeacher]);
  });
  it("should return removed secondary teachers as compared to new secondary teachers in usersLosingAccess", () => {
    const removedSecondaryTeacher = "removedTeacher";
    const newSecondaryTeachers = ["existingBefore"];
    const previousSecondaryTeachers = ["existingBefore", removedSecondaryTeacher];

    const { userIdsGainingAccess, userIdsLosingAccess } = getTeachersToAddAndRemoveSiteAccess({
      newSecondaryTeachers,
      previousSecondaryTeachers
    });

    expect(userIdsGainingAccess).toEqual([]);
    expect(userIdsLosingAccess).toEqual([removedSecondaryTeacher]);
  });
  it("should not return a secondary teacher gaining owner access in any of the lists", () => {
    const teacherMovedFromSecondaryToOwner = "newSecondaryTeacher2";
    const previousSecondaryTeacher = "newSecondaryTeacher1";
    const newSecondaryTeachers = [previousSecondaryTeacher];
    const previousSecondaryTeachers = [previousSecondaryTeacher, teacherMovedFromSecondaryToOwner];
    const previousOwnerId = "previousOwnerId";
    const previousOwnerIds = [previousOwnerId];

    const { userIdsGainingAccess, userIdsLosingAccess } = getTeachersToAddAndRemoveSiteAccess({
      newSecondaryTeachers,
      previousSecondaryTeachers,
      newTeacherId: teacherMovedFromSecondaryToOwner,
      previousOwnerIds
    });

    expect(userIdsGainingAccess).toEqual([]);
    expect(userIdsLosingAccess).toEqual([previousOwnerId]);
  });
  it("should not return an owner moving to secondary teachers in any of the lists", () => {
    const ownerMovingToSecondaryTeachers = "ownerMovingToSecondaryTeachers";
    const previousSecondaryTeacher = "newSecondaryTeacher1";
    const newSecondaryTeachers = [previousSecondaryTeacher, ownerMovingToSecondaryTeachers];
    const previousSecondaryTeachers = [previousSecondaryTeacher];
    const previousOwnerIds = [ownerMovingToSecondaryTeachers];
    const newTeacherId = "newTeacherId";

    const { userIdsGainingAccess, userIdsLosingAccess } = getTeachersToAddAndRemoveSiteAccess({
      newSecondaryTeachers,
      previousSecondaryTeachers,
      newTeacherId,
      previousOwnerIds
    });

    expect(userIdsGainingAccess).toEqual([newTeacherId]);
    expect(userIdsLosingAccess).toEqual([]);
  });
});
