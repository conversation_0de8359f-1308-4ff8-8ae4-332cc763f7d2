// processIndividualRule refactoring -> current IO for this method
/*
inputs used:
    {
      assessmentResultId,
      studentId,
      measures,
      classwideResults,
    }

possible return statements:

  return { passed: true, nextSkill: nextBenchmarkSkill, nextAssessmentResultId: newAssRes._id };
  const retVal = {
    passed,
    nextSkill: ruleResultsToUse || nextSkill,
    nextAssessmentResultId: newAssessmentResult._id,
  };

  return retVal;
  return { passed: true, nextSkill: null };
  return false... wtf

current potential side effects:
   DB UPDATES -> Students
                 StudentGroups

  current external calls:
   createNewIndividualinterventionassessmentresultforstudent from /assessmentResults/methods.js

   Meteor.call('StudentGroupEnrollments:isInIndividualIntervention', {
      studentGroupId,
      studentId,
   });

 */

import { assert } from "chai";

import { AssessmentResults } from "../../assessmentResults/assessmentResults.js";
import * as individualRuleProcessingMethods from "./individualRuleProcessing.js";
import {
  grade5And6Assessment,
  grade5FallBenchmarkAssessment,
  grade5FallAssessment1,
  grade5FallRootRule,
  grade5FallRules,
  grade6WinterBenchmarkAssessment,
  grade6WinterRootRule,
  grade6WinterRules,
  screeningAssignments,
  interventions,
  interventionRC,
  interventionTT,
  interventionCCC,
  interventionGP,
  assignNextTreeStudentPassedTestSetup,
  setupProcessIndivualRuleHSData,
  assessmentResultLastBenchmarkSkillPassed,
  studentGroupPassed
} from "../../../test-helpers/data/individualRuleProcessing.testData";
import { ScreeningAssignments } from "../../screeningAssignments/screeningAssignments";
import { Rules } from "../../rules/rules";
import { Students } from "../../students/students";
import { Assessments } from "../../assessments/assessments";
import { Interventions } from "../../interventions/interventions";
import { StudentGroups } from "../../studentGroups/studentGroups";

describe("assignNextNode", () => {
  const grade = "05";
  const type = "individual";
  const idOfStudentWithoutSkill = "idOfStudentWithoutSkill";
  const idOfStudentWithDrillDown = "idOfStudentWithDrillDown";
  const idOfStudentWithIntervention = "idOfStudentWithIntervention";
  const idOfStudentFromGrade6WithIntervention = "idOfStudentFromGrade6WithIntervention";
  const studentGroup = { _id: "testStudentGroupId", grade, history: [] };
  const studentGroupGrade6 = { _id: "testStudentGroupIdGrade6", grade: "06", history: [] };
  const defaultAssessmentResultForGrade5 = {
    grade,
    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
    type,
    studentGroupId: studentGroup._id,
    rootRuleId: grade5FallRootRule._id,
    individualSkills: { benchmarkAssessmentId: grade5FallBenchmarkAssessment._id }
  };
  const defaultAssessmentResultForGrade6 = {
    grade: "06",
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    type,
    studentGroupId: studentGroupGrade6._id,
    rootRuleId: grade6WinterRootRule._id,
    individualSkills: { benchmarkAssessmentId: grade6WinterBenchmarkAssessment._id }
  };

  beforeAll(async () => {
    await Assessments.insertAsync([
      grade5And6Assessment,
      grade5FallBenchmarkAssessment,
      grade5FallAssessment1,
      grade6WinterBenchmarkAssessment
    ]);
    await ScreeningAssignments.insertAsync(screeningAssignments);
    await Students.insertAsync([
      { _id: idOfStudentWithoutSkill },
      { _id: idOfStudentWithDrillDown, currentSkill: { assessmentId: grade5And6Assessment._id, interventions: [] } },
      {
        _id: idOfStudentWithIntervention,
        currentSkill: { assessmentId: grade5And6Assessment._id, interventions: [interventionTT] }
      },
      {
        _id: idOfStudentFromGrade6WithIntervention,
        currentSkill: { assessmentId: grade5And6Assessment._id, interventions: [interventionTT] }
      }
    ]);
    await Interventions.insertAsync(interventions);
    await Rules.insertAsync([...grade5FallRules, ...grade6WinterRules]);
  });

  afterAll(async () => {
    await Assessments.removeAsync({});
    await ScreeningAssignments.removeAsync({});
    await Students.removeAsync({});
    await Interventions.removeAsync({});
    await Rules.removeAsync({});
  });

  afterEach(async () => {
    await AssessmentResults.removeAsync({});
  });

  it("throws an error when target measures are missing", async () => {
    await expect(
      individualRuleProcessingMethods.assignNextNode({
        failedResult: null,
        failedMeasure: null,
        assessmentResult: defaultAssessmentResultForGrade5,
        studentGroup,
        studentId: idOfStudentWithoutSkill
      })
    ).rejects.toThrow(/Missing targetMeasure for intervention/i);
  });

  it("throws an error when there is no rule to apply", async () => {
    await expect(
      individualRuleProcessingMethods.assignNextNode({
        failedResult: null,
        failedMeasure: null,
        assessmentResult: {
          ...defaultAssessmentResultForGrade5,
          measures: [{ assessmentId: "testAssessmentId", studentResults: [] }]
        },
        studentGroup,
        studentId: idOfStudentWithoutSkill
      })
    ).rejects.toThrow(/Could not find a rule to apply to this assessment score/i);
  });

  it("throws an error when there is no next assessment for outcome", async () => {
    await expect(
      individualRuleProcessingMethods.assignNextNode({
        failedResult: null,
        failedMeasure: null,
        assessmentResult: {
          ...defaultAssessmentResultForGrade5,
          measures: [
            {
              assessmentId: grade5FallAssessment1._id,
              studentResults: [
                {
                  studentId: idOfStudentWithoutSkill,
                  status: "COMPLETE",
                  score: "0",
                  meetsTarget: false,
                  individualRuleOutcome: "below"
                }
              ]
            }
          ]
        },
        studentGroup,
        studentId: idOfStudentWithoutSkill
      })
    ).rejects.toThrow(/Could not find next assessment for outcome/i);
  });

  describe("for Drill-Downs", () => {
    it("returns the easier Drill-Down skill if student scored below instructional target", async () => {
      const nextNode = await individualRuleProcessingMethods.assignNextNode({
        failedResult: null,
        failedMeasure: null,
        assessmentResult: {
          ...defaultAssessmentResultForGrade5,
          scores: [
            {
              assessmentId: grade5FallBenchmarkAssessment._id,
              status: "COMPLETE",
              studentId: idOfStudentWithDrillDown,
              value: "0"
            }
          ],
          measures: [
            {
              assessmentId: grade5FallBenchmarkAssessment._id,
              studentResults: [
                {
                  studentId: idOfStudentWithDrillDown,
                  status: "COMPLETE",
                  score: "0",
                  meetsTarget: false,
                  individualRuleOutcome: "below"
                }
              ]
            }
          ]
        },
        studentGroup,
        studentId: idOfStudentWithDrillDown
      });
      const expectedNode = {
        passed: false,
        nextSkill: {
          assessmentId: grade5FallAssessment1._id,
          assessmentName: grade5FallAssessment1.name,
          interventions: []
        }
      };

      assert.equal(nextNode.passed, expectedNode.passed);
      assert.equal(nextNode.nextSkill.assessmentId, expectedNode.nextSkill.assessmentId);
      assert.equal(nextNode.nextSkill.assessmentName, expectedNode.nextSkill.assessmentName);
      expect(nextNode.nextSkill.interventions).toEqual(expectedNode.nextSkill.interventions);
      assert.isDefined(nextNode.nextAssessmentResultId);
    });

    it("returns the Individual Intervention for the current skill if student scored above an instructional target and below a mastery target", async () => {
      const nextNode = await individualRuleProcessingMethods.assignNextNode({
        failedResult: null,
        failedMeasure: null,
        assessmentResult: {
          ...defaultAssessmentResultForGrade5,
          scores: [
            {
              assessmentId: grade5FallBenchmarkAssessment._id,
              status: "COMPLETE",
              studentId: idOfStudentWithDrillDown,
              value: "30"
            }
          ],
          measures: [
            {
              assessmentId: grade5FallBenchmarkAssessment._id,
              studentResults: [
                {
                  studentId: idOfStudentWithDrillDown,
                  status: "COMPLETE",
                  score: "30",
                  meetsTarget: false,
                  individualRuleOutcome: "at"
                }
              ]
            }
          ]
        },
        studentGroup,
        studentId: idOfStudentWithDrillDown
      });
      const expectedNode = {
        passed: false,
        nextSkill: {
          assessmentId: grade5FallBenchmarkAssessment._id,
          assessmentName: grade5FallBenchmarkAssessment.name,
          interventions: [
            {
              interventionId: interventionTT._id,
              interventionLabel: interventionTT.name,
              interventionAbbrv: interventionTT.abbreviation
            }
          ]
        }
      };

      assert.equal(nextNode.passed, expectedNode.passed);
      assert.equal(nextNode.nextSkill.assessmentId, expectedNode.nextSkill.assessmentId);
      assert.equal(nextNode.nextSkill.assessmentName, expectedNode.nextSkill.assessmentName);
      expect(nextNode.nextSkill.interventions).toEqual(expectedNode.nextSkill.interventions);
      assert.isDefined(nextNode.nextAssessmentResultId);
    });

    it("returns the Individual Intervention for the next skill with acquisition materials if student scored above a mastery target", async () => {
      const nextNode = await individualRuleProcessingMethods.assignNextNode({
        failedResult: null,
        failedMeasure: null,
        assessmentResult: {
          ...defaultAssessmentResultForGrade5,
          scores: [
            {
              assessmentId: grade5And6Assessment._id,
              status: "COMPLETE",
              studentId: idOfStudentWithDrillDown,
              value: "50"
            }
          ],
          measures: [
            {
              assessmentId: grade5And6Assessment._id,
              studentResults: [
                {
                  studentId: idOfStudentWithDrillDown,
                  status: "COMPLETE",
                  score: "50",
                  meetsTarget: true,
                  individualRuleOutcome: "above"
                }
              ]
            }
          ]
        },
        studentGroup,
        studentId: idOfStudentWithDrillDown
      });
      const expectedNode = {
        passed: true,
        nextSkill: {
          assessmentId: grade5FallAssessment1._id,
          assessmentName: grade5FallAssessment1.name,
          interventions: [
            {
              interventionId: interventionCCC._id,
              interventionLabel: interventionCCC.name,
              interventionAbbrv: interventionCCC.abbreviation
            },
            {
              interventionId: interventionGP._id,
              interventionLabel: interventionGP.name,
              interventionAbbrv: interventionGP.abbreviation
            }
          ]
        }
      };

      assert.equal(nextNode.passed, expectedNode.passed);
      assert.equal(nextNode.nextSkill.assessmentId, expectedNode.nextSkill.assessmentId);
      assert.equal(nextNode.nextSkill.assessmentName, expectedNode.nextSkill.assessmentName);
      expect(nextNode.nextSkill.interventions).toEqual(expectedNode.nextSkill.interventions);
      assert.isDefined(nextNode.nextAssessmentResultId);
    });
  });

  describe("for Individual Interventions", () => {
    it("returns the Individual Intervention for the current skill with acquisition materials if student scored below an instructional target", async () => {
      const nextNode = await individualRuleProcessingMethods.assignNextNode({
        failedResult: null,
        failedMeasure: null,
        assessmentResult: {
          ...defaultAssessmentResultForGrade5,
          scores: [
            {
              assessmentId: grade5FallBenchmarkAssessment._id,
              status: "COMPLETE",
              studentId: idOfStudentWithIntervention,
              value: "0"
            },
            {
              assessmentId: grade5And6Assessment._id,
              status: "COMPLETE",
              studentId: idOfStudentWithIntervention,
              value: "10"
            }
          ],
          measures: [
            {
              assessmentId: grade5FallBenchmarkAssessment._id,
              studentResults: [
                {
                  studentId: idOfStudentWithIntervention,
                  status: "COMPLETE",
                  score: "0",
                  meetsTarget: false,
                  individualRuleOutcome: "below"
                }
              ]
            },
            {
              assessmentId: grade5And6Assessment._id,
              studentResults: [
                {
                  studentId: idOfStudentWithIntervention,
                  status: "COMPLETE",
                  score: "10",
                  meetsTarget: false,
                  individualRuleOutcome: "below"
                }
              ]
            }
          ]
        },
        studentGroup,
        studentId: idOfStudentWithIntervention
      });
      const expectedNode = {
        passed: false,
        nextSkill: {
          assessmentId: grade5And6Assessment._id,
          assessmentName: grade5And6Assessment.name,
          interventions: [
            {
              interventionId: interventionCCC._id,
              interventionLabel: interventionCCC.name,
              interventionAbbrv: interventionCCC.abbreviation
            },
            {
              interventionId: interventionGP._id,
              interventionLabel: interventionGP.name,
              interventionAbbrv: interventionGP.abbreviation
            }
          ]
        }
      };

      assert.equal(nextNode.passed, expectedNode.passed);
      assert.equal(nextNode.nextSkill.assessmentId, expectedNode.nextSkill.assessmentId);
      assert.equal(nextNode.nextSkill.assessmentName, expectedNode.nextSkill.assessmentName);
      expect(nextNode.nextSkill.interventions).toEqual(expectedNode.nextSkill.interventions);
      assert.isDefined(nextNode.nextAssessmentResultId);
    });

    it("returns the Individual Intervention for the current skill with acquisition materials if student from grade 6 scored below an instructional target", async () => {
      const nextNode = await individualRuleProcessingMethods.assignNextNode({
        failedResult: null,
        failedMeasure: null,
        assessmentResult: {
          ...defaultAssessmentResultForGrade6,
          scores: [
            {
              assessmentId: grade6WinterBenchmarkAssessment._id,
              status: "COMPLETE",
              studentId: idOfStudentFromGrade6WithIntervention,
              value: "0"
            },
            {
              assessmentId: grade5And6Assessment._id,
              status: "COMPLETE",
              studentId: idOfStudentFromGrade6WithIntervention,
              value: "10"
            }
          ],
          measures: [
            {
              assessmentId: grade6WinterBenchmarkAssessment._id,
              studentResults: [
                {
                  studentId: idOfStudentFromGrade6WithIntervention,
                  status: "COMPLETE",
                  score: "0",
                  meetsTarget: false,
                  individualRuleOutcome: "below"
                }
              ]
            },
            {
              assessmentId: grade5And6Assessment._id,
              studentResults: [
                {
                  studentId: idOfStudentFromGrade6WithIntervention,
                  status: "COMPLETE",
                  score: "10",
                  meetsTarget: false,
                  individualRuleOutcome: "below"
                }
              ]
            }
          ]
        },
        studentGroup: studentGroupGrade6,
        studentId: idOfStudentFromGrade6WithIntervention
      });
      const expectedNode = {
        passed: false,
        nextSkill: {
          assessmentId: grade5And6Assessment._id,
          assessmentName: grade5And6Assessment.name,
          interventions: [
            {
              interventionId: interventionCCC._id,
              interventionLabel: interventionCCC.name,
              interventionAbbrv: interventionCCC.abbreviation
            },
            {
              interventionId: interventionGP._id,
              interventionLabel: interventionGP.name,
              interventionAbbrv: interventionGP.abbreviation
            }
          ]
        }
      };

      assert.equal(nextNode.passed, expectedNode.passed);
      assert.equal(nextNode.nextSkill.assessmentId, expectedNode.nextSkill.assessmentId);
      assert.equal(nextNode.nextSkill.assessmentName, expectedNode.nextSkill.assessmentName);
      expect(nextNode.nextSkill.interventions).toEqual(expectedNode.nextSkill.interventions);
      assert.isDefined(nextNode.nextAssessmentResultId);
    });

    it("returns the Individual Intervention for the current skill with fluency materials if student scored above an instructional target and below a mastery target", async () => {
      const nextNode = await individualRuleProcessingMethods.assignNextNode({
        failedResult: null,
        failedMeasure: null,
        assessmentResult: {
          ...defaultAssessmentResultForGrade5,
          scores: [
            {
              assessmentId: grade5FallBenchmarkAssessment._id,
              status: "COMPLETE",
              studentId: idOfStudentWithIntervention,
              value: "10"
            },
            {
              assessmentId: grade5And6Assessment._id,
              status: "COMPLETE",
              studentId: idOfStudentWithIntervention,
              value: "30"
            }
          ],
          measures: [
            {
              assessmentId: grade5FallBenchmarkAssessment._id,
              studentResults: [
                {
                  studentId: idOfStudentWithIntervention,
                  status: "COMPLETE",
                  score: "10",
                  meetsTarget: false,
                  individualRuleOutcome: "below"
                }
              ]
            },
            {
              assessmentId: grade5And6Assessment._id,
              studentResults: [
                {
                  studentId: idOfStudentWithIntervention,
                  status: "COMPLETE",
                  score: "30",
                  meetsTarget: false,
                  individualRuleOutcome: "at"
                }
              ]
            }
          ]
        },
        studentGroup,
        studentId: idOfStudentWithIntervention
      });
      const expectedNode = {
        passed: false,
        nextSkill: {
          assessmentId: grade5And6Assessment._id,
          assessmentName: grade5And6Assessment.name,
          interventions: [
            {
              interventionId: interventionRC._id,
              interventionLabel: interventionRC.name,
              interventionAbbrv: interventionRC.abbreviation
            },
            {
              interventionId: interventionTT._id,
              interventionLabel: interventionTT.name,
              interventionAbbrv: interventionTT.abbreviation
            }
          ]
        }
      };

      assert.equal(nextNode.passed, expectedNode.passed);
      assert.equal(nextNode.nextSkill.assessmentId, expectedNode.nextSkill.assessmentId);
      assert.equal(nextNode.nextSkill.assessmentName, expectedNode.nextSkill.assessmentName);
      expect(nextNode.nextSkill.interventions).toEqual(expectedNode.nextSkill.interventions);
      assert.isDefined(nextNode.nextAssessmentResultId);
    });

    it("returns the Individual Intervention for the next skill with acquisition materials if student scored above a mastery target", async () => {
      const nextNode = await individualRuleProcessingMethods.assignNextNode({
        failedResult: null,
        failedMeasure: null,
        assessmentResult: {
          ...defaultAssessmentResultForGrade5,
          scores: [
            {
              assessmentId: grade5FallBenchmarkAssessment._id,
              status: "COMPLETE",
              studentId: idOfStudentWithIntervention,
              value: "10"
            },
            {
              assessmentId: grade5And6Assessment._id,
              status: "COMPLETE",
              studentId: idOfStudentWithIntervention,
              value: "50"
            }
          ],
          measures: [
            {
              assessmentId: grade5FallBenchmarkAssessment._id,
              studentResults: [
                {
                  studentId: idOfStudentWithIntervention,
                  status: "COMPLETE",
                  score: "10",
                  meetsTarget: false,
                  individualRuleOutcome: "below"
                }
              ]
            },
            {
              assessmentId: grade5And6Assessment._id,
              studentResults: [
                {
                  studentId: idOfStudentWithIntervention,
                  status: "COMPLETE",
                  score: "50",
                  meetsTarget: true,
                  individualRuleOutcome: "above"
                }
              ]
            }
          ]
        },
        studentGroup,
        studentId: idOfStudentWithIntervention
      });
      const expectedNode = {
        passed: true,
        nextSkill: {
          assessmentId: grade5FallAssessment1._id,
          assessmentName: grade5FallAssessment1.name,
          interventions: [
            {
              interventionId: interventionCCC._id,
              interventionLabel: interventionCCC.name,
              interventionAbbrv: interventionCCC.abbreviation
            },
            {
              interventionId: interventionGP._id,
              interventionLabel: interventionGP.name,
              interventionAbbrv: interventionGP.abbreviation
            }
          ]
        }
      };

      assert.equal(nextNode.passed, expectedNode.passed);
      assert.equal(nextNode.nextSkill.assessmentId, expectedNode.nextSkill.assessmentId);
      assert.equal(nextNode.nextSkill.assessmentName, expectedNode.nextSkill.assessmentName);
      expect(nextNode.nextSkill.interventions).toEqual(expectedNode.nextSkill.interventions);
      assert.isDefined(nextNode.nextAssessmentResultId);
    });
  });
});

describe("assignNextTree", () => {
  afterEach(async () => {
    await ScreeningAssignments.removeAsync({}, { multi: true });
    await Rules.removeAsync({}, { multi: true });
    await Students.removeAsync({}, { multi: true });
    await Assessments.removeAsync({}, { multi: true });
    await Interventions.removeAsync({}, { multi: true });
    await AssessmentResults.removeAsync({}, { multi: true });
  });
  it("should not assign any next skill when the last individual intervention goal skill from benchmark is passed", async () => {
    await assignNextTreeStudentPassedTestSetup();

    const studentId = "FH75tvoSZAe9RDbhe";
    const nextTree = await individualRuleProcessingMethods.assignNextTree(
      assessmentResultLastBenchmarkSkillPassed,
      studentGroupPassed,
      studentId
    );

    expect(nextTree).toEqual({ passed: true, nextSkill: null });
    const student = await Students.findOneAsync({ _id: studentId });
    const [lastHistoryItem] = student.history;
    expect(lastHistoryItem.message.messageCode).toEqual("56");
    expect(lastHistoryItem.benchmarkAssessmentId).toEqual("RW74o5pNzjfbadBGZ");
  });
  it("should not assign any next skill and not modify student history when the last individual intervention goal skill tree is already finished", async () => {
    await assignNextTreeStudentPassedTestSetup();

    const studentId = "FH75tvoSZAe9RDbhe";
    await individualRuleProcessingMethods.assignNextTree(
      assessmentResultLastBenchmarkSkillPassed,
      studentGroupPassed,
      studentId
    );
    const nextTree = await individualRuleProcessingMethods.assignNextTree(
      assessmentResultLastBenchmarkSkillPassed,
      studentGroupPassed,
      studentId
    );

    expect(nextTree).toEqual({ passed: true, nextSkill: null });
    const student = await Students.findOneAsync({ _id: studentId });
    const [lastHistoryItem] = student.history;
    expect(lastHistoryItem.message.messageCode).toEqual("56");
    expect(lastHistoryItem.benchmarkAssessmentId).toEqual("RW74o5pNzjfbadBGZ");
  });
});

describe("imports/api/utilities/individualRuleProcessing.js tests", () => {
  // It is expected to be able to call the main functionality
  // in this file with the side effects passed in as callbacks
  // This is a temporary solution to have tests for the current
  // functionality and add new features to the system

  // We need to rethink a few things about what it means to
  // process rules at all and how we can make this more versatile
  // so that we are not treating individual and classwide as two
  // entirely separated concepts.

  describe("processIndividualRule", () => {
    describe("for a non-HS grade student", () => {
      const studentId = "studentId";
      const studentId2 = "studentId2";
      const studentGroupId = "studentGroupId";
      const assessmentId = "testAssessmentId";
      const assessmentName = "Test Assessment Name";
      const assessmentResultId = "startingAssessmentResultId";
      const benchmarkPeriodId = "nEsbWokBWutTZFkTh";
      const grade = "05";
      const assessment = {
        _id: assessmentId,
        name: assessmentName,
        strands: [
          {
            scores: [
              {
                name: "Number Correct",
                externalId: "number_correct",
                targets: [{ grade: "07", periods: [{ benchmarkPeriodId, values: [1, 5, 10] }] }]
              }
            ]
          }
        ]
      };
      const assessmentResult = generateFailingBenchmarkAssessmentResult({
        assessmentResultId,
        assessmentId,
        benchmarkPeriodId,
        studentGroupId,
        grade,
        studentId
      });
      const assessmentResult2 = generateFailingBenchmarkAssessmentResult({
        assessmentResultId,
        assessmentId,
        benchmarkPeriodId,
        studentGroupId,
        grade: "07",
        studentId: studentId2
      });
      const allowNoScore = true;
      const rule = {
        _id: "ruleId",
        rootRuleId: "ruleId",
        attributeValues: { grade: "07", benchmarkPeriod: "winter-period", assessmentId },
        outcomes: { above: null, at: null, below: { assessmentId } }
      };

      beforeAll(async () => {
        await Assessments.insertAsync(assessment);
        await AssessmentResults.insertAsync({ previousAssessmentResultId: assessmentResultId, studentId, grade });
        await ScreeningAssignments.insertAsync({
          assessmentIds: [assessmentId],
          benchmarkPeriodId,
          grade: "07"
        });
        await Students.insertAsync([
          { _id: studentId, grade: "07" },
          { _id: studentId2, grade: "07" }
        ]);
        await StudentGroups.insertAsync({ _id: studentGroupId, grade: "07" });
        await Rules.insertAsync(rule);
      });
      afterAll(async () => {
        await Assessments.removeAsync({});
        await AssessmentResults.removeAsync({});
        await ScreeningAssignments.removeAsync({});
        await Students.removeAsync({});
        await StudentGroups.removeAsync({});
        await Rules.removeAsync({});
      });

      it("should assign an individual skill if a student doesn't have any individual assessment results", async () => {
        const result = await individualRuleProcessingMethods.processIndividualRule({
          assessmentResult: assessmentResult2,
          allowNoScore,
          studentId: studentId2
        });

        expect(result.passed).toEqual(false);
        expect(result.nextSkill.assessmentId).toEqual(assessmentId);
        expect(result.nextSkill.assessmentName).toEqual(assessmentName);
      });

      it("throws an error if the current assessment result has already been used to create an assessment result for this student", async () => {
        await expect(
          individualRuleProcessingMethods.processIndividualRule({
            assessmentResult,
            allowNoScore,
            studentId
          })
        ).rejects.toThrow(/student already has an assessment result document created from this assessment result/i);
      });

      it("should assign an individual skill if the current assessment result has already been used to create an assessment result for this student but in a different grade", async () => {
        const result = await individualRuleProcessingMethods.processIndividualRule({
          assessmentResult: { ...assessmentResult, grade: "07" },
          allowNoScore,
          studentId
        });

        expect(result.passed).toEqual(false);
        expect(result.nextSkill.assessmentId).toEqual(assessmentId);
        expect(result.nextSkill.assessmentName).toEqual(assessmentName);
      });
    });

    describe("for a HS grade student", () => {
      const studentId = "studentId";
      const studentId2 = "studentId2";
      const fourweekRuleStudentId = "fourweekRuleStudentId";
      const studentGroupId = "studentGroupId";
      const studentGroupId2 = "studentGroupId2";
      const rootRuleId = "rootRuleId";
      const allowNoScore = true;

      const passingLastAssessmentResultId = "passingLastAssessmentResultId";
      const benchmarkPeriodId = "allPeriods";

      const {
        skillHierarchy,
        assessments,
        getCustomStudentIdAssessmentResult,
        passingLastAssessmentResult,
        fullSkillTreeRules,
        assessmentResults,
        students,
        studentGroups,
        screeningAssignmentDoc
      } = setupProcessIndivualRuleHSData({
        studentId,
        studentId2,
        fourweekRuleStudentId,
        studentGroupId,
        studentGroupId2,
        rootRuleId,
        benchmarkPeriodId,
        passingLastAssessmentResultId
      });

      beforeAll(async () => {
        await Assessments.insertAsync(assessments);
        await AssessmentResults.insertAsync(assessmentResults);
        await ScreeningAssignments.insertAsync(screeningAssignmentDoc);
        await Students.insertAsync(students);
        await StudentGroups.insertAsync(studentGroups);
        await Rules.insertAsync(fullSkillTreeRules);
      });
      afterAll(async () => {
        await Assessments.removeAsync({});
        await AssessmentResults.removeAsync({});
        await ScreeningAssignments.removeAsync({});
        await Students.removeAsync({});
        await StudentGroups.removeAsync({});
        await Rules.removeAsync({});
      });

      it("should assign a correct individual skill if a student doesn't have any individual assessment results", async () => {
        const result = await individualRuleProcessingMethods.processIndividualRule({
          assessmentResult: getCustomStudentIdAssessmentResult(
            studentId,
            skillHierarchy.secondTree.node0.assessmentId,
            rootRuleId,
            studentGroupId
          ),
          allowNoScore,
          studentId
        });

        expect(result.passed).toEqual(false);
        expect(result.nextSkill.assessmentId).toEqual(skillHierarchy.secondTree.node1.assessmentId);
        expect(result.nextSkill.assessmentName).toEqual(skillHierarchy.secondTree.node1.name);
      });

      it("should end individual intervention when a student is passing a goal skill in a progress monitoring tree", async () => {
        const result = await individualRuleProcessingMethods.processIndividualRule({
          assessmentResult: passingLastAssessmentResult,
          allowNoScore,
          studentId: studentId2
        });

        expect(result).toEqual({ passed: true, nextSkill: null });
      });

      it("should assign previous skill when student is scheduled from four week rule", async () => {
        await StudentGroups.updateAsync(
          { _id: studentGroupId2 },
          { $set: { history: [{ type: "classwide", assessmentId: skillHierarchy.secondTree.node0.assessmentId }] } }
        );
        const result = await individualRuleProcessingMethods.processIndividualRule({
          assessmentResult: getCustomStudentIdAssessmentResult(
            fourweekRuleStudentId,
            skillHierarchy.thirdTree.node0.assessmentId,
            null,
            studentGroupId2
          ),
          allowNoScore,
          studentId: fourweekRuleStudentId
        });
        expect(result.nextSkill.assessmentId).toEqual(skillHierarchy.secondTree.node1.assessmentId);
        expect(result.nextSkill.interventions.length).toEqual(0);
      });

      it("should assign current skill when student is scheduled from four week rule but completed skill that four week rule got scheduled", async () => {
        await StudentGroups.updateAsync(
          { _id: studentGroupId2 },
          { $set: { history: [{ type: "classwide", assessmentId: skillHierarchy.secondTree.node0.assessmentId }] } }
        );
        await Students.updateAsync(
          { _id: fourweekRuleStudentId },
          {
            $set: {
              history: [{ benchmarkAssessmentId: skillHierarchy.secondTree.node0.assessmentId }],
              currentSkill: {
                benchmarkPeriodId
              }
            }
          }
        );
        const result = await individualRuleProcessingMethods.processIndividualRule({
          assessmentResult: getCustomStudentIdAssessmentResult(
            fourweekRuleStudentId,
            skillHierarchy.thirdTree.node0.assessmentId,
            null,
            studentGroupId2
          ),
          allowNoScore,
          studentId: fourweekRuleStudentId
        });
        expect(result.nextSkill.assessmentId).toEqual(skillHierarchy.thirdTree.node1.assessmentId);
        expect(result.nextSkill.interventions.length).toEqual(0);
      });

      it("should throw error when student completed current group skill as individual intervention", async () => {
        await Students.updateAsync(
          { _id: studentId },
          {
            $set: {
              history: [{ benchmarkAssessmentId: skillHierarchy.secondTree.node0.assessmentId }],
              currentSkill: {
                benchmarkPeriodId
              }
            }
          }
        );

        let error = {};
        try {
          await individualRuleProcessingMethods.processIndividualRule({
            assessmentResult: getCustomStudentIdAssessmentResult(
              studentId,
              skillHierarchy.secondTree.node0.assessmentId,
              null,
              studentGroupId
            ),
            allowNoScore,
            studentId
          });
        } catch (e) {
          error = e;
        }

        expect(error.details).toEqual({
          studentId,
          skillName: skillHierarchy.secondTree.node0.name
        });
      });

      it("should throw error when fourweek student completed previous and current group skill as individual intervention", async () => {
        await StudentGroups.updateAsync(
          { _id: studentGroupId2 },
          { $set: { history: [{ type: "classwide", assessmentId: skillHierarchy.secondTree.node0.assessmentId }] } }
        );
        await Students.updateAsync(
          { _id: fourweekRuleStudentId },
          {
            $set: {
              history: [
                { benchmarkAssessmentId: skillHierarchy.secondTree.node0.assessmentId },
                { benchmarkAssessmentId: skillHierarchy.thirdTree.node0.assessmentId }
              ],
              currentSkill: {
                benchmarkPeriodId
              }
            }
          }
        );

        let error = {};
        try {
          await individualRuleProcessingMethods.processIndividualRule({
            assessmentResult: getCustomStudentIdAssessmentResult(
              fourweekRuleStudentId,
              skillHierarchy.thirdTree.node0.assessmentId,
              null,
              studentGroupId2
            ),
            allowNoScore,
            studentId: fourweekRuleStudentId
          });
        } catch (e) {
          error = e;
        }

        expect(error.details).toEqual({
          studentId: fourweekRuleStudentId,
          skillName: skillHierarchy.secondTree.node0.name
        });
      });
    });
  });

  describe("findStudentsFirstFailingMeasureAndResultFromAssessmentResult", () => {
    it("throws an error if student is not present in any of the measures", async () => {
      const testAssessmentResult = {
        measures: [
          {
            studentResults: []
          }
        ]
      };
      expect(() =>
        individualRuleProcessingMethods.findStudentsFirstFailingMeasureAndResultFromAssessmentResult(
          testAssessmentResult,
          "testStudentId"
        )
      ).toThrow(/student not present in any measures/i);
    });
    it("returns result and measure if didn't fail benchmark", () => {
      const testStudentId = "testStudentId";
      const testAssessmentResult = {
        measures: [
          {
            studentResults: [],
            fakeMeasure: true
          },
          {
            studentResults: [
              {
                studentId: testStudentId,
                meetsTarget: false
              }
            ]
          }
        ],
        type: "individual"
      };
      const testResult = individualRuleProcessingMethods.findStudentsFirstFailingMeasureAndResultFromAssessmentResult(
        testAssessmentResult,
        testStudentId
      );
      assert.isDefined(testResult);
      assert.isFalse(testResult.didFailBenchmark);
      assert.equal(testResult.targetMeasure, testAssessmentResult.measures[1]);
      assert.equal(testResult.targetResult, testAssessmentResult.measures[1].studentResults[0]);
    });
    it("returns no result or measure if student did fail benchmark on an individual assessment result", () => {
      const testStudentId = "testStudentId";
      const testAssessmentResult = {
        measures: [
          {
            studentResults: [
              {
                studentId: testStudentId,
                meetsTarget: false
              }
            ]
          }
        ],
        type: "individual"
      };
      const testResult = individualRuleProcessingMethods.findStudentsFirstFailingMeasureAndResultFromAssessmentResult(
        testAssessmentResult,
        testStudentId
      );
      assert.isDefined(testResult.targetMeasure);
      assert.isNull(testResult.targetMeasure);
      assert.isDefined(testResult.targetResult);
      assert.isNull(testResult.targetResult);
    });
    it("returns lowest index measure and the students result in that measure if student didn't fail benchmark and result had > 1 measure", () => {
      const testStudentId = "testStudentId";
      const testAssessmentResult = {
        measures: [
          {
            studentResults: [],
            fakeMeasure: true,
            testIndex: 0
          },
          {
            studentResults: [
              {
                studentId: testStudentId,
                meetsTarget: true,
                testIndex: 1
              }
            ],
            testIndex: 1
          },
          {
            studentResults: [
              {
                studentId: testStudentId,
                meetsTarget: true,
                testIndex: 2
              }
            ],
            testIndex: 2
          },
          {
            studentResults: [
              {
                studentId: testStudentId,
                meetsTarget: false,
                testIndex: 3
              }
            ],
            testIndex: 3 // should be this one
          },
          {
            studentResults: [
              {
                studentId: testStudentId,
                meetsTarget: false,
                testIndex: 4
              }
            ],
            testIndex: 4
          },
          {
            studentResults: [
              {
                studentId: testStudentId,
                meetsTarget: true,
                testIndex: 5
              }
            ],
            testIndex: 5
          }
        ],
        type: "individual"
      };
      const testResult = individualRuleProcessingMethods.findStudentsFirstFailingMeasureAndResultFromAssessmentResult(
        testAssessmentResult,
        testStudentId
      );
      assert.equal(testResult.targetMeasure.testIndex, 3);
      assert.equal(testResult.targetResult.testIndex, 3);
    });
    it("returns first failed measure if the assessment result type is not individual", () => {
      const testStudentId = "testStudentId";
      const testAssessmentResult = {
        measures: [
          {
            studentResults: [
              {
                studentId: testStudentId,
                meetsTarget: false
              }
            ]
          }
        ],
        type: "benchmark"
      };
      const testResult = individualRuleProcessingMethods.findStudentsFirstFailingMeasureAndResultFromAssessmentResult(
        testAssessmentResult,
        testStudentId
      );
      assert.isNotNull(testResult.targetMeasure);
      assert.isNotNull(testResult.targetResult);
    });
  });
});

function generateFailingBenchmarkAssessmentResult({
  assessmentResultId,
  assessmentId,
  benchmarkPeriodId,
  studentGroupId,
  grade,
  studentId,
  rootRuleId = "rootRuleId"
}) {
  return {
    _id: assessmentResultId,
    assessmentIds: [assessmentId],
    benchmarkPeriodId,
    studentGroupId,
    grade,
    type: "benchmark",
    status: "COMPLETED",
    rootRuleId,
    measures: [
      {
        assessmentId,
        medianScore: 0,
        studentScores: [0],
        percentMeetingTarget: 0,
        numberMeetingTarget: 0,
        totalStudentsAssessed: 1,
        studentResults: [
          {
            studentId,
            status: "COMPLETE",
            score: "0",
            meetsTarget: false,
            individualRuleOutcome: "below"
          }
        ]
      }
    ],
    scores: [{ siteId: "siteId" }]
  };
}
