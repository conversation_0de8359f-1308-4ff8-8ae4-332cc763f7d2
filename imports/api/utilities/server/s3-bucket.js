import { ListObjectsV2Command, S3Client } from "@aws-sdk/client-s3";

export default function fetchVideoSkillList() {
  const key = Meteor.settings.S3_ACCESS_KEY_ID;
  if (!key?.length) {
    return [];
  }
  const secret = Meteor.settings.S3_SECRET_ACCESS_KEY;
  const region = Meteor.settings.S3_REGION;
  const bucketName = "edspring-inta";
  const folderPath = "protocol-images/Skills";

  const client = new S3Client({
    credentials: {
      accessKeyId: key,
      secretAccessKey: secret
    },
    region
  });
  const input = {
    Bucket: bucketName,
    MaxKeys: 200,
    Prefix: folderPath
  };
  const command = new ListObjectsV2Command(input);
  return client.send(command);
}
