import { assert } from "chai";
import { StudentGroups } from "../../studentGroups/studentGroups";
import { Assessments } from "../../assessments/assessments";
import {
  endCurrentIndividualIntervention,
  getStartingAssessmentResult,
  removeIndividualInterventionProgress
} from "./individualRuleProcessing";
import { AssessmentResults } from "../../assessmentResults/assessmentResults";
import { Students } from "../../students/students";
import { clearCollections } from "../../rosterImports/rosterImportsProcessor.testHelpers";
import { ScreeningAssignments } from "../../screeningAssignments/screeningAssignments";
import {
  generateHighSchoolAssessment,
  getExpectedStartingAssessmentResult
} from "../../../test-helpers/data/manageIndividualInterventions.testData";

describe("endCurrentIndividualIntervention", () => {
  const studentGroupId = "studentGroupId";
  const studentId = "studentId";
  const individualInterventionAssessmentResultId = "individualInterventionAssessmentResultId";
  const studentInIntervention = {
    _id: studentId,
    schoolYear: 2018,
    grade: "03",
    history: null,
    currentSkill: {
      benchmarkAssessmentId: "benchmark_assessment_id",
      assessmentResultId: individualInterventionAssessmentResultId
    }
  };

  const studentGroupData = {
    _id: studentGroupId,
    grade: "03",
    isActive: true,
    schoolYear: 2018,
    history: null,
    currentAssessmentResultIds: [individualInterventionAssessmentResultId],
    individualInterventionQueue: []
  };

  const individualInterventionAssessmentResult = {
    _id: individualInterventionAssessmentResultId,
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    schoolYear: 2018,
    status: "OPEN",
    studentGroupId,
    studentId,
    type: "individual",
    grade: "03",
    scores: [],
    individualSkills: {
      benchmarkAssessmentId: "benchmark_assessment_id",
      assessmentId: "assessment_id"
    }
  };

  afterEach(async () => {
    await clearCollections();
  });

  it("should end the active individual intervention of a student", async () => {
    await StudentGroups.insertAsync(studentGroupData);
    await Students.insertAsync(studentInIntervention);
    await AssessmentResults.insertAsync(individualInterventionAssessmentResult);

    const studentGroup = await StudentGroups.findOneAsync(studentGroupId);
    await endCurrentIndividualIntervention({ studentId, studentGroup });

    expect((await Students.findOneAsync(studentId)).currentSkill).toBeUndefined();
    const updatedGroup = await StudentGroups.findOneAsync(studentGroupId);
    expect(updatedGroup.currentAssessmentResultIds.includes(individualInterventionAssessmentResultId)).toBeFalsy();
    expect(updatedGroup.individualInterventionQueue.includes(studentId)).toBeFalsy();
    const countOfOpenIndividualAssessmentsOfStudent = await AssessmentResults.find({
      studentId,
      type: "individual",
      status: "OPEN"
    }).countAsync();
    expect(countOfOpenIndividualAssessmentsOfStudent).toEqual(0);
  });

  it("should not modify the group if the group is not provided as an argument", async () => {
    await StudentGroups.insertAsync(studentGroupData);
    await Students.insertAsync(studentInIntervention);
    await AssessmentResults.insertAsync(individualInterventionAssessmentResult);

    await endCurrentIndividualIntervention({ studentId });

    expect((await Students.findOneAsync(studentId)).currentSkill).toBeUndefined();
    const countOfOpenIndividualAssessmentsOfStudent = await AssessmentResults.find({
      studentId,
      type: "individual",
      status: "OPEN"
    }).countAsync();
    expect(countOfOpenIndividualAssessmentsOfStudent).toEqual(0);
    expect(await StudentGroups.findOneAsync(studentGroupId)).toMatchObject(studentGroupData);
  });

  it("should not throw error when individual intervention assessment result is missing", async () => {
    await StudentGroups.insertAsync(studentGroupData);
    await Students.insertAsync(studentInIntervention);
    await endCurrentIndividualIntervention({ studentId });
    expect((await Students.findOneAsync(studentId)).currentSkill).toBeUndefined();
  });
});

describe("removeIndividualInterventionProgress", () => {
  afterEach(async () => {
    await clearCollections();
  });
  it("should remove the progress history of student's individual intervention", async () => {
    const studentGroupId = "studentGroupId";
    const studentId = "studentId";
    const benchmarkPeriodId = "winter";
    const schoolYear = 2018;
    const winterScreeningId = "winter_screening_id";
    const activeIndividualAssessmentId = "active_individual_assessment_id";
    await setupIndividualInterventionProgress({
      studentId,
      studentGroupId,
      benchmarkPeriodId,
      schoolYear,
      screeningId: winterScreeningId,
      activeIndividualAssessmentId
    });

    await removeIndividualInterventionProgress({
      benchmarkPeriodId,
      studentId,
      studentGroupId,
      schoolYear
    });

    const countOfStudentAssessmentsInWinterPeriod = await getCountOfStudentAssessmentsIn({
      studentId,
      studentGroupId,
      benchmarkPeriodId,
      schoolYear
    });
    assert.equal(
      countOfStudentAssessmentsInWinterPeriod,
      0,
      "The individual assessments from the provided benchmark period were not removed"
    );
    const { history, currentSkill } = await Students.findOneAsync(studentId);
    const countOfHistoryItemsFromWinterPeriod = history.filter(item => item.benchmarkPeriodId === benchmarkPeriodId)
      .length;
    assert.equal(countOfHistoryItemsFromWinterPeriod, 0, "The student intervention progress was not removed");
    assert.equal(currentSkill, undefined, "The student's current skill was not removed");
  });
});

describe("getStartingAssessmentResult", () => {
  const studentGroupId = "studentGroupId";
  const grade = "HS";
  const studentId = "studentId";
  const schoolYear = 2018;
  const orgid = "org1";
  const siteId = "site1";

  beforeAll(async () => {
    await ScreeningAssignments.insertAsync([
      {
        _id: "123",
        grade: "HS",
        benchmarkPeriodId: "allPeriods",
        assessmentIds: ["123", "234", "345"]
      }
    ]);
    await Students.insertAsync([
      {
        _id: "studentId",
        identity: {
          name: {
            first: "Test",
            last: "Test2"
          }
        }
      }
    ]);
  });
  afterEach(async () => {
    await Assessments.removeAsync({});
  });
  afterAll(async () => {
    await clearCollections();
  });

  it("should return starting assessment when strand target of default and individual assessment type exists", async () => {
    const targets = ["22", "23"];
    await Assessments.insertAsync(
      generateHighSchoolAssessment([{ values: ["20", "21"] }, { assessmentType: "individual", values: targets }])
    );

    expect(
      await getStartingAssessmentResult({
        studentId,
        grade,
        schoolYear,
        studentGroupId,
        orgid,
        siteId
      })
    ).toMatchObject(getExpectedStartingAssessmentResult(targets));
  });

  it("should return starting assessment when strand target of only default assessment type exists", async () => {
    const targets = ["20", "21"];
    await Assessments.insertAsync(generateHighSchoolAssessment([{ values: targets }]));
    expect(
      await getStartingAssessmentResult({
        studentId,
        grade,
        schoolYear,
        studentGroupId,
        orgid,
        siteId
      })
    ).toMatchObject(getExpectedStartingAssessmentResult(targets));
  });
  it("should return starting assessment when strand target of only default assessment type exists", async () => {
    const targets = ["20", "21"];
    await Assessments.insertAsync(generateHighSchoolAssessment([{ assessmentType: "classwide", values: targets }]));

    expect(
      await getStartingAssessmentResult({
        studentId,
        grade,
        schoolYear,
        studentGroupId,
        orgid,
        siteId
      })
    ).toMatchObject(getExpectedStartingAssessmentResult(targets));
  });
  it("should return starting assessment when strand target of only default assessment type exists", async () => {
    await Assessments.insertAsync(generateHighSchoolAssessment([]));

    await expect(
      getStartingAssessmentResult({
        studentId,
        grade,
        schoolYear,
        studentGroupId,
        orgid,
        siteId
      })
    ).rejects.toThrow("There are no targets defined for this assessment.");
  });

  it("should use current assessmentId when determining starting skill for individual intervention in HS grade", async () => {
    const assessments = [
      generateHighSchoolAssessment([{ assessmentType: "classwide", values: ["20", "21"] }], {
        _id: "123",
        name: "AM1",
        monitorAssessmentMeasure: "123"
      }),
      generateHighSchoolAssessment([{ assessmentType: "classwide", values: ["30", "31"] }], {
        _id: "234",
        name: "AM2",
        monitorAssessmentMeasure: "234"
      }),
      generateHighSchoolAssessment([{ assessmentType: "classwide", values: ["40", "41"] }], {
        _id: "345",
        name: "AM3",
        monitorAssessmentMeasure: "345"
      })
    ];
    await Assessments.insertAsync(assessments);
    await StudentGroups.insertAsync({
      _id: studentGroupId,
      grade: "HS",
      currentClasswideSkill: { assessmentId: "234" }
    });

    const expectedAssessmentResult = await getStartingAssessmentResult({
      studentId,
      grade,
      schoolYear,
      studentGroupId,
      orgid,
      siteId
    });
    expect(expectedAssessmentResult.scores[0].assessmentId).toEqual("234");
    expect(expectedAssessmentResult.measures[0].assessmentId).toEqual("234");
    expect(expectedAssessmentResult.measures[0].assessmentName).toEqual("AM2");
    expect(expectedAssessmentResult.measures[0].targetScores).toEqual(["30", "31"]);
  });
});

async function getCountOfStudentAssessmentsIn({ studentId, studentGroupId, benchmarkPeriodId, schoolYear }) {
  return AssessmentResults.find({
    studentId,
    schoolYear,
    benchmarkPeriodId,
    type: "individual",
    studentGroupId
  }).countAsync();
}

async function setupIndividualInterventionProgress({
  studentId,
  studentGroupId,
  benchmarkPeriodId,
  schoolYear,
  screeningId,
  activeIndividualAssessmentId
}) {
  const completedIndividualAssessmentId = "completed_individual_assessment_id";
  const completedIndividualAssessmentId2 = "completed_individual_assessment_id2";
  const previousBenchmarkPeriodAssessmentId1 = "previousBenchmarkPeriodAssessmentId1";
  const previousBenchmarkPeriodAssessmentId2 = "previousBenchmarkPeriodAssessmentId";
  const previousBenchmarkPeriodId = "fall";
  const grade = "03";
  await AssessmentResults.insertAsync([
    {
      _id: screeningId, // screening based on which individual interventions are generated
      benchmarkPeriodId,
      type: "benchmark",
      studentGroupId,
      schoolYear,
      grade
    },
    {
      _id: completedIndividualAssessmentId, // completed ind int with screening as previousAssessmentResultId
      type: "individual",
      previousAssessmentResultId: screeningId,
      studentId,
      studentGroupId,
      benchmarkPeriodId,
      status: "COMPLETED",
      schoolYear,
      grade
    },
    {
      _id: completedIndividualAssessmentId2, // completed ind int with first ind int as previousAssessmentResultId
      type: "individual",
      previousAssessmentResultId: completedIndividualAssessmentId,
      studentId,
      studentGroupId,
      benchmarkPeriodId,
      status: "COMPLETED",
      schoolYear,
      grade
    },
    {
      _id: activeIndividualAssessmentId, // active ind int
      type: "individual",
      previousAssessmentResultId: completedIndividualAssessmentId2,
      studentId,
      studentGroupId,
      benchmarkPeriodId,
      status: "OPEN",
      schoolYear,
      grade
    },
    {
      _id: previousBenchmarkPeriodAssessmentId1, // previous benchmark period ind int
      type: "individual",
      previousAssessmentResultId: "otherScreeningId",
      studentId,
      studentGroupId,
      benchmarkPeriodId: previousBenchmarkPeriodId,
      status: "COMPLETED",
      schoolYear,
      grade
    },
    {
      _id: previousBenchmarkPeriodAssessmentId2, // previous benchmark period ind int
      type: "individual",
      previousAssessmentResultId: previousBenchmarkPeriodAssessmentId1,
      studentId,
      studentGroupId,
      benchmarkPeriodId: previousBenchmarkPeriodId,
      status: "COMPLETED",
      schoolYear,
      grade
    },
    {
      _id: "previousYearAssessmentId", // previous year ind int
      type: "individual",
      previousAssessmentResultId: "previousYearAssessmentId",
      studentId,
      studentGroupId,
      benchmarkPeriodId,
      status: "COMPLETED",
      schoolYear: 2017,
      grade
    }
  ]);
  await Students.insertAsync({
    _id: studentId,
    grade,
    schoolYear,
    currentSkill: {
      assessmentResultId: activeIndividualAssessmentId,
      benchmarkPeriodId
    },
    history: [
      {
        assessmentResultId: completedIndividualAssessmentId,
        benchmarkPeriodId,
        assessmentResultMeasures: [
          {
            studentResults: []
          }
        ],
        whenEnded: {}
      },
      {
        assessmentResultId: completedIndividualAssessmentId2,
        benchmarkPeriodId,
        assessmentResultMeasures: [
          {
            studentResults: []
          }
        ],
        whenEnded: {}
      },
      {
        assessmentResultId: previousBenchmarkPeriodAssessmentId1, // previous benchmark period ind int
        benchmarkPeriodId: previousBenchmarkPeriodId,
        assessmentResultMeasures: [
          {
            studentResults: []
          }
        ],
        whenEnded: {}
      },
      {
        assessmentResultId: previousBenchmarkPeriodAssessmentId2, // previous benchmark period ind int
        benchmarkPeriodId: previousBenchmarkPeriodId,
        assessmentResultMeasures: [
          {
            studentResults: []
          }
        ],
        whenEnded: {}
      }
    ]
  });
}
