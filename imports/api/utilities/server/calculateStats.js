// @input: startWeekNumber is optional
// @input: endWeekNumber is optional
import { get } from "lodash";
import last from "lodash/last";
import { getDefaultSchoolBreakValues, getMeteorUser, ninjalog } from "../utilities";
import {
  getAbsoluteWeekNumber,
  getActiveWeeks,
  getAverageWeeksPerSkill,
  getEndWeekOfInterventions,
  getHistoryForCurrentSkill,
  getInterventionConsistency,
  getNumberOfClasswideSkillsPracticed,
  getNumberOfIndividualSkillsPracticed,
  getStartWeekOfInterventions,
  getWeekNumbersWithScores,
  getWorseScoresRatio
} from "./utilities";
import { Organizations } from "../../organizations/organizations";
import { getCurrentDate } from "../../helpers/getCurrentDate";

export function getCleanHistory(history, currentSkill) {
  // Filter out bad history items and benchmarks, only look at interventions
  const filteredHistory = (history || []).filter(h => h.whenEnded && h.type !== "benchmark");
  // Put all skills that have been practiced or are being worked on into the same list
  if (currentSkill) {
    filteredHistory.unshift(currentSkill);
  }
  return filteredHistory;
}

async function getStartWeekOfCurrentSkill(cleanHistory, schoolYear) {
  if (!cleanHistory || !cleanHistory.length) return 0;
  const currentSkillAttempts = getHistoryForCurrentSkill(cleanHistory);
  return getAbsoluteWeekNumber(last(currentSkillAttempts).whenStarted.date, schoolYear);
}

export async function calculateStats(
  history,
  currentSkill,
  type,
  studentIdsInClasswideInterventions = [],
  startWeekNumber = null,
  endWeekNumber = null,
  schoolYear,
  orgid
) {
  const organization = await Organizations.findOneAsync(
    { _id: orgid },
    { fields: { schoolBreaks: 1, schoolYearBoundary: 1 } }
  );
  const schoolBreaks = organization.schoolBreaks || getDefaultSchoolBreakValues();
  const { schoolYearBoundary } = organization;
  let cleanHistory = getCleanHistory(history, currentSkill);
  if (!startWeekNumber) {
    const currentSkillBenchmarkPeriodId =
      get(currentSkill, "benchmarkPeriodId") || get(cleanHistory, "[0].benchmarkPeriodId");
    cleanHistory = currentSkillBenchmarkPeriodId
      ? cleanHistory.filter(i => i.benchmarkPeriodId === currentSkillBenchmarkPeriodId)
      : cleanHistory;
  }
  // skills practiced includes completed and in progress skills
  ninjalog.log({ msg: "cleanHistory", val: cleanHistory, jsonString: true });

  const meteorUser = await getMeteorUser();
  const customDate = get(meteorUser, "profile.customDate");
  const currentDate = await getCurrentDate(customDate, orgid);
  const thisWeek = await getAbsoluteWeekNumber(currentDate, schoolYear);

  const startWeekOfInterventions = await getStartWeekOfInterventions(cleanHistory, schoolYear);
  // Use the passed in startWeekNumber if it is more recent than when the interventions were started,
  // otherwise use the startWeekOfInterventions (for students who started interventions after the date was set)
  let startWeek =
    startWeekNumber && startWeekNumber > startWeekOfInterventions ? startWeekNumber : startWeekOfInterventions;
  const isInterventionComplete = !get(currentSkill, "assessmentResultId");

  if (startWeek > thisWeek) {
    startWeek = thisWeek;
  }

  let endWeek =
    endWeekNumber ||
    (await getEndWeekOfInterventions({ cleanHistory, schoolYear, isInterventionComplete, currentDate }));
  if (endWeek < startWeek) {
    endWeek = startWeek;
  }
  const numberOfSkillsPracticed =
    type === "group"
      ? getNumberOfClasswideSkillsPracticed(cleanHistory, isInterventionComplete)
      : await getNumberOfIndividualSkillsPracticed(cleanHistory, startWeek, schoolYear);
  const activeWeeks = await getActiveWeeks({
    startWeek,
    endWeek,
    history: cleanHistory,
    type,
    schoolYear,
    schoolBreaks,
    schoolYearBoundary
  });
  const numberOfWeeksActive = activeWeeks.length;
  const classWeekNumbersWithScores = await getWeekNumbersWithScores(cleanHistory, type, null, schoolYear);
  const classWeekNumbersWithScoresSinceStartWeek = classWeekNumbersWithScores.filter(
    weekNumber => weekNumber >= startWeek
  );
  const interventionConsistency = getInterventionConsistency(
    classWeekNumbersWithScoresSinceStartWeek,
    numberOfWeeksActive
  );
  const averageWeeksPerSkill = getAverageWeeksPerSkill(numberOfSkillsPracticed, numberOfWeeksActive);
  const startWeekOfCurrentSkill = await getStartWeekOfCurrentSkill(cleanHistory, schoolYear);
  let numberOfWeeksPracticingCurrentSkill = 0;
  if (startWeekOfCurrentSkill !== 0) {
    numberOfWeeksPracticingCurrentSkill = thisWeek - startWeekOfCurrentSkill;
  }
  const worseScoresRatio = getWorseScoresRatio(cleanHistory, numberOfWeeksPracticingCurrentSkill);
  // Get individual stats
  // Note: Students
  const individualResults = [];
  if (
    cleanHistory &&
    cleanHistory.length &&
    type === "group" &&
    studentIdsInClasswideInterventions &&
    studentIdsInClasswideInterventions.length > 0
  ) {
    // eslint-disable-next-line no-restricted-syntax
    for await (const studentId of studentIdsInClasswideInterventions) {
      const thisStudentsParticipationHistory = cleanHistory.filter(
        groupHistory =>
          (groupHistory.enrolledStudentIds && groupHistory.enrolledStudentIds.includes(studentId)) ||
          !groupHistory.whenEnded
      ); // currentSkills
      const thisStudentsStartWeek = !startWeekNumber
        ? await getStartWeekOfInterventions(thisStudentsParticipationHistory, schoolYear)
        : startWeekNumber;
      const thisStudentsNumberOfSkillsPracticed = getNumberOfClasswideSkillsPracticed(thisStudentsParticipationHistory);
      const thisStudentsActiveWeeks = await getActiveWeeks({
        startWeek: thisStudentsStartWeek,
        endWeek,
        history: thisStudentsParticipationHistory,
        type: "individualClasswide",
        schoolYear,
        schoolBreaks,
        schoolYearBoundary
      });
      const thisStudentsNumberOfWeeksActive = thisStudentsActiveWeeks.length;
      const thisStudentsAverageWeeksPerSkill = getAverageWeeksPerSkill(
        thisStudentsNumberOfSkillsPracticed,
        thisStudentsNumberOfWeeksActive
      );
      const thisStudentsWeekNumbersWithScores = await getWeekNumbersWithScores(
        cleanHistory,
        "individualClasswide",
        studentId,
        schoolYear
      );
      const thisStudentsWeekNumbersWithScoresSinceStartWeek = thisStudentsWeekNumbersWithScores.filter(
        weekNumber => weekNumber >= startWeek
      );
      const thisStudentsInterventionConsistency = getInterventionConsistency(
        thisStudentsWeekNumbersWithScoresSinceStartWeek,
        thisStudentsNumberOfWeeksActive
      );
      individualResults.push({
        studentId,
        startWeek: thisStudentsStartWeek,
        endWeek,
        numberOfSkillsPracticed: thisStudentsNumberOfSkillsPracticed,
        activeWeekNumbers: thisStudentsActiveWeeks,
        numberOfWeeksActive: thisStudentsNumberOfWeeksActive,
        weekNumbersWithScoresSinceStartWeek: thisStudentsWeekNumbersWithScoresSinceStartWeek,
        averageWeeksPerSkill: thisStudentsAverageWeeksPerSkill,
        interventionConsistency: thisStudentsInterventionConsistency
      });
    }
  }

  return {
    startWeek,
    endWeek,
    numberOfSkillsPracticed,
    activeWeekNumbers: activeWeeks,
    numberOfWeeksActive,
    weekNumbersWithScoresSinceStartWeek: classWeekNumbersWithScoresSinceStartWeek,
    averageWeeksPerSkill,
    numberOfWeeksPracticingCurrentSkill,
    interventionConsistency,
    individualResults,
    worseScoresRatio
  };
}
