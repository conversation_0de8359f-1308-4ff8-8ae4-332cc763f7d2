import { assert } from "chai";
import MockDate from "mockdate";
import moment from "moment";
import { groupBy } from "lodash";
import {
  classwideHistoryForWorseScoresRatio,
  currentClasswideSkill1,
  currentClasswideSkill2,
  exampleClasswideHistory1,
  exampleClasswideHistory2,
  exampleClasswideHistory3,
  exampleClasswideHistory4,
  exampleIndividualCurrentSkill,
  exampleIndividualHistory1,
  otherBenchmarkIndvidualIntervention
} from "../../../test-helpers/data/interventionStatsTestData";
import { StudentGroups } from "../../studentGroups/studentGroups";
import { Students } from "../../students/students";
import { Organizations } from "../../organizations/organizations";
import {
  getAbsoluteWeekNumber,
  getActiveWeeks,
  getAverageWeeksPerSkill,
  getEndWeekOfInterventions,
  getFirstMondayOnOrAfter,
  getFirstSundayOnOrAfter,
  getInterventionConsistency,
  getNumberOfClasswideSkillsPracticed,
  getNumberOfIndividualSkillsPracticed,
  getStartWeekOfInterventions,
  getWeekNumbersWithScores,
  getWeeksInDateRange,
  getWorseScoresRatio
} from "./utilities";
import { getValidatedStatsAsOfDate } from "./getClasswideStatsForStudentGroup";
import { calculateStats, getCleanHistory } from "./calculateStats";
import { getIndividualStudentDetailData } from "./interventionStats";
import { StudentGroupEnrollments } from "../../studentGroupEnrollments/studentGroupEnrollments";
import { AssessmentResults } from "../../assessmentResults/assessmentResults";
import { Rules } from "../../rules/rules";
import { Assessments } from "../../assessments/assessments";
import { getGradeDetailData } from "../skillProgressMethods";
import { getValuesByKey } from "../utilities";

describe("Intervention Stats Functions", () => {
  const testSchoolYear = 2017;
  const orgid = "test_organization_id";

  beforeAll(async () => {
    await Organizations.insertAsync({
      _id: orgid,
      name: "DistrictName",
      schoolYearBoundary: {
        month: 6,
        day: 31
      },
      schoolBreaks: {
        winter: {},
        spring: { startMonth: 5, startDay: 1, endMonth: 5, endDay: 8 },
        other: {}
      }
    });
  });

  afterAll(async () => {
    MockDate.reset();
    await Organizations.removeAsync({});
  });

  describe("getAbsoluteWeekNumber", () => {
    it("Should return 52 when passed the date of 2016-12-27T16:29:41.733+0000 for the schoolYear 2017", async () => {
      const weekNumber = await getAbsoluteWeekNumber("2016-12-27T16:29:41.733+0000", testSchoolYear);
      assert.equal(52, weekNumber);
    });

    it("Should return 53 when passed the date of 2017-01-02T16:29:41.733+0000 for the schoolYear 2017", async () => {
      const weekNumber = await getAbsoluteWeekNumber("2017-01-02T16:29:41.733+0000", testSchoolYear);
      assert.equal(53, weekNumber);
    });

    it("Should return 54 when passed the date of 2017-01-09T16:29:41.733+0000 for the schoolYear 2017", async () => {
      const weekNumber = await getAbsoluteWeekNumber("2017-01-09T16:29:41.733+0000", testSchoolYear);
      assert.equal(54, weekNumber);
    });

    it("Should return week by week for 7 day separated dates", async () => {
      let weekNumber = await getAbsoluteWeekNumber("2020-09-02T22:00:00.000Z", 2021);
      assert.equal(36, weekNumber);

      weekNumber = await getAbsoluteWeekNumber("2020-09-09T22:00:00.000Z", 2021);
      assert.equal(37, weekNumber);

      weekNumber = await getAbsoluteWeekNumber("2020-09-16T22:00:00.000Z", 2021);
      assert.equal(38, weekNumber);

      weekNumber = await getAbsoluteWeekNumber("2020-09-23T22:00:00.000Z", 2021);
      assert.equal(39, weekNumber);

      weekNumber = await getAbsoluteWeekNumber("2020-09-30T22:00:00.000Z", 2021);
      assert.equal(40, weekNumber);

      weekNumber = await getAbsoluteWeekNumber("2020-10-07T22:00:00.000Z", 2021);
      assert.equal(41, weekNumber);
    });
  });

  describe("getCleanHistory", () => {
    it("Should not contain benchmark history items", () => {
      const history = JSON.parse(JSON.stringify(exampleClasswideHistory1));
      const cleanedHistory = getCleanHistory(history, currentClasswideSkill1);
      const historyContainsBenchmarks = cleanedHistory.some(hist => hist.type === "benchmark");
      assert.equal(false, historyContainsBenchmarks);
    });

    it("Should have a first history item with a currentSkill that has an assessmentResultId of current_skill_result_id", () => {
      const history = JSON.parse(JSON.stringify(exampleClasswideHistory1));
      const cleanedHistory = getCleanHistory(history, currentClasswideSkill1);
      assert.equal("current_skill_result_id", cleanedHistory[0].assessmentResultId);
    });
  });

  describe("getStartWeekOfInterventions", () => {
    it("Should return week 52 when the oldest intervention was assigned on week 51 but scored on the week 52", async () => {
      const history = JSON.parse(JSON.stringify(exampleClasswideHistory1));
      const cleanedHistory = getCleanHistory(history, currentClasswideSkill1);
      cleanedHistory[cleanedHistory.length - 1].whenStarted.date = "2016-12-21T16:29:31.857+0000";
      cleanedHistory[cleanedHistory.length - 1].whenEnded.date = "2016-12-28T16:29:31.857+0000";
      const startWeek = await getStartWeekOfInterventions(cleanedHistory);
      assert.equal(52, startWeek);
    });

    it("Should return week 51 when  when the oldest intervention was assigned on week 50 but scored on the week 52", async () => {
      const history = JSON.parse(JSON.stringify(exampleClasswideHistory1));
      const cleanedHistory = getCleanHistory(history, currentClasswideSkill1);
      cleanedHistory[cleanedHistory.length - 1].whenStarted.date = "2016-12-15T16:29:31.857+0000";
      cleanedHistory[cleanedHistory.length - 1].whenEnded.date = "2016-12-28T16:29:31.857+0000";
      const startWeek = await getStartWeekOfInterventions(cleanedHistory);
      assert.equal(51, startWeek);
    });

    it("Should return week 51 when  when the oldest intervention was assigned on week 51 and scored on the week 51", async () => {
      const history = JSON.parse(JSON.stringify(exampleClasswideHistory1));
      const cleanedHistory = getCleanHistory(history, currentClasswideSkill1);
      cleanedHistory[cleanedHistory.length - 1].whenStarted.date = "2016-12-21T16:29:31.857+0000";
      cleanedHistory[cleanedHistory.length - 1].whenEnded.date = "2016-12-22T16:29:31.857+0000";
      const startWeek = await getStartWeekOfInterventions(cleanedHistory);
      assert.equal(51, startWeek);
    });

    // Friday of week 1 of 2017
    it("Should return week 53 when the oldest intervention was scored on the Friday of the first week of the year", async () => {
      const history = JSON.parse(JSON.stringify(exampleClasswideHistory1));
      const cleanedHistory = getCleanHistory(history, currentClasswideSkill1);
      cleanedHistory[cleanedHistory.length - 1].whenStarted.date = "2016-12-28T16:29:31.857+0000";
      cleanedHistory[cleanedHistory.length - 1].whenEnded.date = "2017-01-06T16:29:31.857+0000";
      const startWeek = await getStartWeekOfInterventions(cleanedHistory);
      assert.equal(53, startWeek);
    });

    // Sunday of week 1 of 2017
    it("Should return week 53 when the oldest intervention was scored on the Sunday of the first week of the year", async () => {
      const history = JSON.parse(JSON.stringify(exampleClasswideHistory1));
      const cleanedHistory = getCleanHistory(history, currentClasswideSkill1);
      cleanedHistory[cleanedHistory.length - 1].whenStarted.date = "2017-01-02T16:29:31.857+0000";
      cleanedHistory[cleanedHistory.length - 1].whenEnded.date = "2017-01-08T16:29:31.857+0000";
      const startWeek = await getStartWeekOfInterventions(cleanedHistory, testSchoolYear);
      assert.equal(53, startWeek);
    });

    // Monday of week 2 of 2017
    it("Should return week 54 when the oldest intervention was scored on the Monday of the second week of the year", async () => {
      const history = JSON.parse(JSON.stringify(exampleClasswideHistory1));
      const cleanedHistory = getCleanHistory(history, currentClasswideSkill1);
      // Change the date of the oldest intervention to have been scored during the second week of the year
      cleanedHistory[cleanedHistory.length - 1].whenStarted.date = "2017-01-02T16:29:31.857+0000";
      cleanedHistory[cleanedHistory.length - 1].whenEnded.date = "2017-01-09T16:29:31.857+0000";
      const startWeek = await getStartWeekOfInterventions(cleanedHistory, testSchoolYear);
      assert.equal(54, startWeek);
    });

    // Friday of week 2 of 2017
    it("Should return week 54 when the oldest intervention was scored on the Monday of the second week of the year", async () => {
      const history = JSON.parse(JSON.stringify(exampleClasswideHistory1));
      const cleanedHistory = getCleanHistory(history, currentClasswideSkill1);
      // Change the date of the oldest intervention to have been scored during the second week of the year
      cleanedHistory[cleanedHistory.length - 1].whenStarted.date = "2017-01-02T16:29:31.857+0000";
      cleanedHistory[cleanedHistory.length - 1].whenEnded.date = "2017-01-13T16:29:31.857+0000";
      const startWeek = await getStartWeekOfInterventions(cleanedHistory, testSchoolYear);
      assert.equal(54, startWeek);
    });

    // Friday of week 3 of 2017
    it("Should return week 55 when the oldest intervention was scored on the Monday of the second week of the year", async () => {
      const history = JSON.parse(JSON.stringify(exampleClasswideHistory1));
      const cleanedHistory = getCleanHistory(history, currentClasswideSkill1);
      // Change the date of the oldest intervention to have been scored during the second week of the year
      cleanedHistory[cleanedHistory.length - 1].whenEnded.date = "2017-01-02T16:29:31.857+0000";
      cleanedHistory[cleanedHistory.length - 1].whenEnded.date = "2017-01-20T16:29:31.857+0000";
      const startWeek = await getStartWeekOfInterventions(cleanedHistory, testSchoolYear);
      assert.equal(54, startWeek);
    });
  });

  describe("getEndWeekOfInterventions", () => {
    it("Should return the previous week from now when there is no score from this week", async () => {
      MockDate.set("2017-05-01");
      const history = JSON.parse(JSON.stringify(exampleClasswideHistory1));
      const cleanHistory = getCleanHistory(history, currentClasswideSkill1);
      const endWeek = await getEndWeekOfInterventions({ cleanHistory, schoolYear: 2017 });
      const weekBeforeCurrent = (await getAbsoluteWeekNumber(new Date())) - 1;

      assert.equal(weekBeforeCurrent, endWeek);
      MockDate.reset();
    });

    it("Should return the current week when there is a score from the current week", async () => {
      const history = JSON.parse(JSON.stringify(exampleClasswideHistory1));
      const cleanHistory = getCleanHistory(history, currentClasswideSkill1);
      // Add a mock history item that was scored during the current week
      cleanHistory.unshift({
        whenEnded: {
          by: "",
          on: 0,
          date: new Date()
        }
      });
      const endWeek = await getEndWeekOfInterventions({ cleanHistory });
      const currentWeek = await getAbsoluteWeekNumber(new Date());
      assert.equal(currentWeek, endWeek);
    });
  });

  describe("getWeeksInDateRange", () => {
    it("should return an empty array if no arguments are specified", async () => {
      const result = await getWeeksInDateRange();
      expect(result).toEqual([]);
    });

    it("should return an empty array if no startMonth, startDay, endMonth, endDay values are specified in an object", async () => {
      const result = await getWeeksInDateRange({});
      expect(result).toEqual([]);
    });

    test.each([
      { season: "Fall", startMonth: 8, startDay: 13, endMonth: 9, endDay: 2, expected: [33, 34, 35] },
      { season: "Winter", startMonth: 12, startDay: 13, endMonth: 1, endDay: 2, expected: [50, 51, 52] },
      { season: "Spring", startMonth: 4, startDay: 1, endMonth: 4, endDay: 7, expected: [66] }
    ])(
      "should return an array with correct expected week numbers in $season based on passed arguments",
      async ({ startMonth, startDay, endMonth, endDay, expected }) => {
        const result = await getWeeksInDateRange({ startMonth, startDay, endMonth, endDay }, 2022, {
          month: 6,
          day: 31
        });
        expect(result).toEqual(expected);
      }
    );
  });

  describe("getFirstMondayOnOrAfter", () => {
    it("should return the same date if specified date is a Monday", () => {
      const mondayDate = moment.utc("2021-12-20", "YYYY-MM-DD");
      const mondayDate2 = moment.utc("2019-04-01", "YYYY-MM-DD");
      const result = getFirstMondayOnOrAfter(mondayDate);
      const result2 = getFirstMondayOnOrAfter(mondayDate2);
      expect(result).toEqual(mondayDate.toDate());
      expect(result2).toEqual(mondayDate2.toDate());
    });

    test.each([
      { date: "2020-04-01", expected: "2020-04-06" },
      { date: "2021-04-01", expected: "2021-04-05" },
      { date: "2022-04-01", expected: "2022-04-04" },
      { date: "2023-04-01", expected: "2023-04-03" },
      { date: "2025-04-01", expected: "2025-04-07" },
      { date: "2019-12-20", expected: "2019-12-23" },
      { date: "2020-12-20", expected: "2020-12-21" },
      { date: "2022-12-20", expected: "2022-12-26" },
      { date: "2023-12-20", expected: "2023-12-25" },
      { date: "2024-12-20", expected: "2024-12-23" },
      { date: "2025-12-20", expected: "2025-12-22" }
    ])("should return the first monday after the specified date: $date if it's not a Monday", ({ date, expected }) => {
      const specifiedDate = moment.utc(date, "YYYY-MM-DD");
      const expectedDate = moment.utc(expected, "YYYY-MM-DD");
      const result = getFirstMondayOnOrAfter(specifiedDate);
      expect(result).toEqual(expectedDate.toDate());
    });
  });

  describe("getFirstSundayOnOrAfter", () => {
    it("should return the same date if specified date is a Sunday", () => {
      const mondayDate = moment.utc("2022-01-02", "YYYY-MM-DD");
      const mondayDate2 = moment.utc("2019-04-07", "YYYY-MM-DD");
      const result = getFirstSundayOnOrAfter(mondayDate);
      const result2 = getFirstSundayOnOrAfter(mondayDate2);
      expect(result).toEqual(mondayDate.toDate());
      expect(result2).toEqual(mondayDate2.toDate());
    });

    test.each([
      { date: "2020-04-07", expected: "2020-04-12" },
      { date: "2021-04-07", expected: "2021-04-11" },
      { date: "2022-04-07", expected: "2022-04-10" },
      { date: "2023-04-07", expected: "2023-04-09" },
      { date: "2025-04-07", expected: "2025-04-13" },
      { date: "2019-01-02", expected: "2019-01-06" },
      { date: "2020-01-02", expected: "2020-01-05" },
      { date: "2021-01-02", expected: "2021-01-03" },
      { date: "2023-01-02", expected: "2023-01-08" },
      { date: "2024-01-02", expected: "2024-01-07" },
      { date: "2025-01-02", expected: "2025-01-05" }
    ])("should return the first monday after the specified date: $date if it's not a Sunday", ({ date, expected }) => {
      const specifiedDate = moment.utc(date, "YYYY-MM-DD");
      const expectedDate = moment.utc(expected, "YYYY-MM-DD");
      const result = getFirstSundayOnOrAfter(specifiedDate);
      expect(result).toEqual(expectedDate.toDate());
    });
  });

  describe("getActiveWeeks", () => {
    // Note that the test data being used has an active week starting at ISO Week 1 of 2017,
    // with the last score being recorded on 1/20/2017.  We are going to assume
    it(
      "The number of active weeks when the first intervention was assigned on 1/2/2017 and now is considered to be 2/3/2017 " +
        "but there is no score for this week should be 4",
      async () => {
        const history = JSON.parse(JSON.stringify(exampleClasswideHistory1));
        const cleanHistory = getCleanHistory(history, currentClasswideSkill1);
        const startWeek = await getStartWeekOfInterventions(cleanHistory);
        const mockNowDate = new Date("2017-02-03T16:29:31.857+0000");
        const endWeek = (await getAbsoluteWeekNumber(mockNowDate)) - 1;

        const weeksActive = await getActiveWeeks({ startWeek, endWeek, history: cleanHistory });

        assert.equal(4, weeksActive.length);
      }
    );

    it(
      "The number of active weeks when the first intervention was assigned on 1/2/2017 and now is considered to be 1/20/2017 " +
        "and this is a score for the Now week should be 3",
      async () => {
        const history = JSON.parse(JSON.stringify(exampleClasswideHistory1));
        const cleanHistory = getCleanHistory(history, currentClasswideSkill1);
        const startWeek = await getStartWeekOfInterventions(cleanHistory);
        const mockNowDate = new Date("2017-01-20T16:29:31.857+0000");
        const endWeek = await getAbsoluteWeekNumber(mockNowDate);

        const weeksActive = await getActiveWeeks({ startWeek, endWeek, history: cleanHistory });

        assert.equal(3, weeksActive.length);
      }
    );

    // Devin Potter was Not enrolled during week 55
    it("Devin Potter was NOT ENROLLED during one of the weeks that the class had scores, so it should not be counted", async () => {
      const mockNowDate = new Date("2017-02-03T16:29:31.857+0000");
      const endWeek = (await getAbsoluteWeekNumber(mockNowDate)) - 1;
      const history = JSON.parse(JSON.stringify(exampleClasswideHistory2));
      const cleanHistory = getCleanHistory(history, currentClasswideSkill1);
      const thisStudentsParticipationHistory = cleanHistory.filter(
        groupHistory =>
          groupHistory.enrolledStudentIds && groupHistory.enrolledStudentIds.includes("Devin_Potter_DEMO_STUDENT")
      );
      const startWeek = await getStartWeekOfInterventions(thisStudentsParticipationHistory);

      const weeksActive = await getActiveWeeks({ startWeek, endWeek, history: thisStudentsParticipationHistory });

      assert.equal(3, weeksActive.length);
    });
  });

  describe("getNumberOfClasswideSkillsPracticed", () => {
    it("Should find two intervention assessments (skills) when the 3rd intervention skill is still practiced", async () => {
      const cleanHistory = getCleanHistory(exampleClasswideHistory1, currentClasswideSkill1);
      const numberOfClasswideSkills = getNumberOfClasswideSkillsPracticed(cleanHistory);
      assert.equal(2, numberOfClasswideSkills);
    });

    it("Should return the full number of taken assessments (3) if the intervention is finished", () => {
      const cleanHistory = getCleanHistory(exampleClasswideHistory1, currentClasswideSkill1);
      const isInterventionComplete = true;
      const numberOfClasswideSkills = getNumberOfClasswideSkillsPracticed(cleanHistory, isInterventionComplete);
      assert.equal(3, numberOfClasswideSkills);
    });
  });

  describe("getNumberOfIndividualSkillsPracticed", () => {
    it("Should find two intervention assessments (skills)", async () => {
      const history = JSON.parse(JSON.stringify(exampleIndividualHistory1));
      const cleanHistory = getCleanHistory(history, exampleIndividualCurrentSkill);
      const startWeek = await getStartWeekOfInterventions(cleanHistory);
      const numberOfIndividualSkills = await getNumberOfIndividualSkillsPracticed(cleanHistory, startWeek);
      assert.equal(2, numberOfIndividualSkills);
    });

    it("Should find 0 intervention assessments (skills)", async () => {
      const history = [];
      const startWeek = await getStartWeekOfInterventions(history);
      const numberOfIndividualSkills = await getNumberOfIndividualSkillsPracticed(history, startWeek);
      assert.equal(0, numberOfIndividualSkills);
    });
  });

  describe("getAverageWeeksPerSkill", () => {
    it("Should return null if the numberOfWeeksActive is null", () => {
      const averageWeeksPerSkill = getAverageWeeksPerSkill(null, 4);
      assert.equal(averageWeeksPerSkill, null);
    });

    it("Should return null if the numberOfWeeksActive is 0", () => {
      const averageWeeksPerSkill = getAverageWeeksPerSkill(0, 4);
      assert.equal(averageWeeksPerSkill, null);
    });

    it("Should return 2 if the numberOfSkillsPracticed is 3 and numberOfWeeksActive is 6", () => {
      const averageWeeksPerSkill = getAverageWeeksPerSkill(3, 6);
      assert.equal(averageWeeksPerSkill, 2);
    });

    it("Should return 1 if the numberOfSkillsPracticed is 3 and numberOfWeeksActive is 3", () => {
      const averageWeeksPerSkill = getAverageWeeksPerSkill(3, 3);
      assert.equal(averageWeeksPerSkill, 1);
    });

    it("Should return 2.5 if the numberOfSkillsPracticed is 2 and numberOfWeeksActive is 5", () => {
      const averageWeeksPerSkill = getAverageWeeksPerSkill(2, 5);
      assert.equal(averageWeeksPerSkill, 2.5);
    });
  });

  describe("getInterventionConsistency", () => {
    it("Should return 100 when given an array of [53, 54, 55] for weekNumbersWithScores and 3 activeWeeks", () => {
      const interventionConsistency = getInterventionConsistency([53, 54, 55], 3);
      assert.equal(interventionConsistency, 100);
    });

    it("Should return 0 when given an array of [] for weekNumbersWithScores and 3 activeWeeks", () => {
      const interventionConsistency = getInterventionConsistency([], 3);
      assert.equal(interventionConsistency, 0);
    });

    it("Should return 0 when given an array of [] for weekNumbersWithScores and 0 activeWeeks", () => {
      const interventionConsistency = getInterventionConsistency([], 0);
      assert.equal(interventionConsistency, 0);
    });
  });

  describe("getWeekNumbersWithScores - type is group calculation (studentGroup as a whole)", () => {
    const history = JSON.parse(JSON.stringify(exampleClasswideHistory2));
    const cleanHistory = getCleanHistory(history, currentClasswideSkill1);

    it("There are classwide scores for weeks 53, 54, 55, and 56 so the class was assessed 4 times. ", async () => {
      const weekNumbersWithScores = await getWeekNumbersWithScores(cleanHistory, "group", null, testSchoolYear);
      expect(weekNumbersWithScores.length).toEqual(4);
      expect(weekNumbersWithScores.sort()).toEqual([53, 54, 55, 56]);
    });
  });

  describe("getWeekNumbersWithScores - type is individual", () => {
    const history = JSON.parse(JSON.stringify(exampleIndividualHistory1));
    const cleanHistory = getCleanHistory(history, exampleIndividualCurrentSkill);

    it("There are classwide scores for weeks 53, 54, 55, and 57 so the class was assessed 4 times. ", async () => {
      const weekNumbersWithScores = await getWeekNumbersWithScores(cleanHistory, "individual", null, testSchoolYear);
      expect(weekNumbersWithScores.length).toEqual(4);
      expect(weekNumbersWithScores.sort()).toEqual([53, 54, 55, 57]);
    });
  });

  describe("getWeekNumbersWithScores - type is individualClasswide calculation", () => {
    const history = JSON.parse(JSON.stringify(exampleClasswideHistory2));
    const cleanHistory = getCleanHistory(history, currentClasswideSkill1);

    it(
      "There are classwide scores for weeks 53, 54, 55, and 56 so the class was assessed 4 times. " +
        "Devin Potter was NOT enrolled during week 55, but came back in week 56. This should only 3 return week numbers with scores",
      async () => {
        const weekNumbersWithScoresForDevin = await getWeekNumbersWithScores(
          cleanHistory,
          "individualClasswide",
          "Devin_Potter_DEMO_STUDENT",
          testSchoolYear
        );
        expect(weekNumbersWithScoresForDevin.length).toEqual(3);
        expect(weekNumbersWithScoresForDevin.sort()).toEqual([53, 54, 56]);
      }
    );

    it(
      "There are classwide scores for weeks 53, 54, 55, and 56 so the class was assessed 4 times. " +
        "Melanie Fox was enrolled during all assessed weeks. This should return 4 week numbers with scores",
      async () => {
        const weekNumbersWithScoresForMelanie = await getWeekNumbersWithScores(
          cleanHistory,
          "individualClasswide",
          "Melanie_Fox_DEMO_STUDENT",
          testSchoolYear
        );
        expect(weekNumbersWithScoresForMelanie.length).toEqual(4);
        expect(weekNumbersWithScoresForMelanie.sort()).toEqual([53, 54, 55, 56]);
      }
    );

    it(
      "There are classwide scores for weeks 53, 54, 55, and 56 so the class was assessed 4 times. " +
        "Nina Farmer was enrolled during all assessed weeks, but was absent for two of them. " +
        "This should return 2 week numbers with scores",
      async () => {
        const weekNumbersWithScoresForNina = await getWeekNumbersWithScores(
          cleanHistory,
          "individualClasswide",
          "Nina_Farmer_DEMO_STUDENT",
          testSchoolYear
        );
        expect(weekNumbersWithScoresForNina.length).toEqual(2);
        expect(weekNumbersWithScoresForNina.sort()).toEqual([53, 54]);
      }
    );
  });

  describe("calculatStats function - classwide intervention", () => {
    const history = JSON.parse(JSON.stringify(exampleClasswideHistory2));
    const currentSkill = JSON.parse(JSON.stringify(currentClasswideSkill1));
    const currentEnrollmentStudentIds = [
      "Melanie_Fox_DEMO_STUDENT",
      "Nina_Farmer_DEMO_STUDENT",
      "Kelly_Frank_DEMO_STUDENT",
      "Pheng_Vang_DEMO_STUDENT",
      "Devin_Potter_DEMO_STUDENT",
      "Forrest_Miller_DEMO_STUDENT",
      "Spencer_Elliott_DEMO_STUDENT",
      "Lance_Fleming_DEMO_STUDENT"
    ];
    let groupStats;
    let melaniesStats;
    let ninasStats;
    let devinsStats;
    beforeEach(async () => {
      groupStats = await calculateStats(
        history,
        currentSkill,
        "group",
        currentEnrollmentStudentIds,
        null,
        58,
        testSchoolYear,
        orgid
      );
      melaniesStats = groupStats.individualResults.find(indres => indres.studentId === "Melanie_Fox_DEMO_STUDENT");

      ninasStats = groupStats.individualResults.find(indres => indres.studentId === "Nina_Farmer_DEMO_STUDENT");

      devinsStats = groupStats.individualResults.find(indres => indres.studentId === "Devin_Potter_DEMO_STUDENT");
    });

    // Whole Class
    it(
      "The class should have been assessed on weeks 53, 54, 55, 56, 57, 58 " +
        "but the class missed assessments on weeks 57 and 58.  They should have " +
        "an intervention consistency of 67% (4 scores out of 6 weeks)",
      () => {
        assert.equal(67, groupStats.interventionConsistency);
      }
    );

    it("The class should have a number of skills practiced of 2", () => {
      assert.equal(2, groupStats.numberOfSkillsPracticed);
    });

    it("The class should have a number of active weeks of 6", () => {
      assert.equal(6, groupStats.numberOfWeeksActive);
    });

    it("The class should have a weeks per skill of of 3", () => {
      assert.equal(3, groupStats.averageWeeksPerSkill);
    });

    it("The class should have a worseScoresRatio because of lacking data", () => {
      assert.equal(0, groupStats.worseScoresRatio);
    });

    // Melanie
    it(
      "Melanie Fox was assessed every time the class was assessed. " +
        "She should have the same intervention consistency as the class" +
        "- so 4 scores out of 6 weeks = 67%",
      () => {
        assert.equal(67, melaniesStats.interventionConsistency);
      }
    );

    it("Melanie should have a number of skills practiced of 2", () => {
      assert.equal(2, melaniesStats.numberOfSkillsPracticed);
    });

    it("Melanie should have a number of active weeks of 6", () => {
      assert.equal(6, melaniesStats.numberOfWeeksActive);
    });

    it("Melanie should have a weeks per skill of of 3", () => {
      assert.equal(3, melaniesStats.averageWeeksPerSkill);
    });

    // Nina
    it(
      "Nina Farmer was only assessed twice, but was enrolled " +
        "in the class the entire time. She should have an intervention consistency" +
        " of 33% (2 scores out of 6 weeks)",
      () => {
        assert.equal(33, ninasStats.interventionConsistency);
      }
    );

    it("Nina should have a number of skills practiced of 2", () => {
      assert.equal(2, ninasStats.numberOfSkillsPracticed);
    });

    it("Nina should have a number of active weeks of 6", () => {
      assert.equal(6, ninasStats.numberOfWeeksActive);
    });

    it("Nina should have a weeks per skill of of 3", () => {
      assert.equal(3, ninasStats.averageWeeksPerSkill);
    });

    // Devin
    it(
      "Devin Potter was assessed three times, but was NOT enrolled " +
        "in the class during week 55. He should have had scores for weeks " +
        " 53, 54, 56, 57(whole class missed), 58(whole class missed). " +
        "His intervention consistency should be 60% (3 scores out of 5)",
      () => {
        assert.equal(60, devinsStats.interventionConsistency);
      }
    );

    it("Devin should have a number of skills practiced of 2", () => {
      assert.equal(2, devinsStats.numberOfSkillsPracticed);
    });

    it("Devin should have a number of active weeks of 5", () => {
      assert.equal(5, devinsStats.numberOfWeeksActive);
    });

    it("Devin should have a weeks per skill of of 2.5", () => {
      assert.equal(2.5, devinsStats.averageWeeksPerSkill);
    });
  });

  describe("calculateStats function - individual intervention", () => {
    const history = JSON.parse(JSON.stringify(exampleIndividualHistory1));
    // pushing previous benchmark intervention (which should not be taken into consideration when calculating stats)
    history.push(otherBenchmarkIndvidualIntervention);
    const currentSkill = JSON.parse(JSON.stringify(exampleIndividualCurrentSkill));
    let individualStats;

    beforeAll(async () => {
      individualStats = await calculateStats(
        history,
        currentSkill,
        "individual",
        null,
        null,
        58,
        testSchoolYear,
        orgid
      );
    });

    it("Should have four weeks with scores (53, 54, 55, 57), and is missing 56, and 58", () => {
      assert.equal(4, individualStats.weekNumbersWithScoresSinceStartWeek.length);
    });

    it("Should have a number of skills practiced of 2", () => {
      assert.equal(2, individualStats.numberOfSkillsPracticed);
    });

    it("Should have a number of active weeks of 6", () => {
      assert.equal(6, individualStats.numberOfWeeksActive);
    });

    it("Should have a weeks per skill of of 3", () => {
      assert.equal(3, individualStats.averageWeeksPerSkill);
    });

    it("Should have an intervention consistency of 67% (4 scores out of 6 weeks)", () => {
      assert.equal(67, individualStats.interventionConsistency);
    });
  });

  describe("getWorseScoresRatio", () => {
    const history = [...classwideHistoryForWorseScoresRatio];
    const worseScoresRatio = getWorseScoresRatio(history, 3);
    assert.equal(worseScoresRatio, 75);
  });

  describe("getValidatedStatsAsOfDate", () => {
    const startDateBenchmarkPeriod = "fall";
    const startDate = "2018-03-26T19:00:00.000+0000";
    const twoWeeksBeforeStartDate = "2018-03-12T19:00:00.000+0000";
    const twoWeeksAfterStartDate = "2018-04-09T19:00:00.000+0000";

    describe("for student groups", () => {
      const studentGroup = {
        _id: "testGroup",
        classwideStatsAsOfDate: startDate,
        history: [
          {
            _id: "benchmarkSpring",
            type: "benchmark",
            benchmarkPeriodId: "spring",
            whenStarted: {
              date: twoWeeksAfterStartDate
            }
          },
          {
            _id: "secondFallIntervention",
            type: "classwide",
            benchmarkPeriodId: startDateBenchmarkPeriod,
            whenStarted: {
              date: startDate
            }
          },
          {
            _id: "firstFallIntervention",
            type: "classwide",
            benchmarkPeriodId: startDateBenchmarkPeriod,
            whenStarted: {
              date: startDate
            }
          },
          {
            type: "benchmark",
            benchmarkPeriodId: startDateBenchmarkPeriod,
            whenStarted: {
              date: twoWeeksBeforeStartDate
            }
          },
          {
            _id: "winterIntervention",
            type: "classwide",
            benchmarkPeriodId: "winter",
            whenStarted: {
              date: twoWeeksBeforeStartDate
            }
          }
        ],
        currentClasswideSkill: {
          benchmarkPeriodId: startDateBenchmarkPeriod
        }
      };

      afterEach(async () => {
        await StudentGroups.removeAsync({});
      });

      it("should return the set start date if there is no group history", async () => {
        const trimmedStudentGroup = {
          _id: "some_id",
          classwideStatsAsOfDate: startDate
        };

        const result = await getValidatedStatsAsOfDate(trimmedStudentGroup);

        expect(result).toEqual(startDate);
      });

      it("should return the currently set classwideStatsAsOfDate if it is not before the first intervention the currentClasswideSkill points at", async () => {
        const result = await getValidatedStatsAsOfDate(studentGroup);

        expect(result).toEqual(startDate);
      });

      it("should return the the currently set classwideStatsAsOfDate if it is not before the start date of the last intervention", async () => {
        const { currentClasswideSkill, ...updatedStudentGroup } = studentGroup;

        const result = await getValidatedStatsAsOfDate(updatedStudentGroup);

        expect(result).toEqual(startDate);
      });

      it(`should return the first intervention date the currentClasswideSkill points at and update the classwideStatsAsOfDate value 
    if it was set to before the beginning of that intervention`, async () => {
        const { ...updatedStudentGroup } = studentGroup;
        updatedStudentGroup.classwideStatsAsOfDate = twoWeeksBeforeStartDate;
        const groupId = await StudentGroups.insertAsync(updatedStudentGroup);

        const result = await getValidatedStatsAsOfDate(updatedStudentGroup);

        expect(result).toEqual(startDate);
        expect((await StudentGroups.findOneAsync(groupId)).classwideStatsAsOfDate).toEqual(startDate);
      });
    });

    describe("for individual students", () => {
      const student = {
        _id: "testStudent",
        individualStatsAsOfDate: {
          [startDateBenchmarkPeriod]: startDate
        },
        history: [
          {
            _id: "benchmarkSpring",
            type: "benchmark",
            benchmarkPeriodId: "spring",
            whenStarted: {
              date: twoWeeksAfterStartDate
            }
          },
          {
            _id: "secondFallIntervention",
            type: "classwide",
            benchmarkPeriodId: startDateBenchmarkPeriod,
            whenStarted: {
              date: startDate
            }
          },
          {
            _id: "firstFallIntervention",
            type: "classwide",
            benchmarkPeriodId: startDateBenchmarkPeriod,
            whenStarted: {
              date: startDate
            }
          },
          {
            type: "benchmark",
            benchmarkPeriodId: startDateBenchmarkPeriod,
            whenStarted: {
              date: twoWeeksBeforeStartDate
            }
          },
          {
            _id: "winterIntervention",
            type: "classwide",
            benchmarkPeriodId: "winter",
            whenStarted: {
              date: twoWeeksBeforeStartDate
            }
          }
        ],
        currentSkill: {
          benchmarkPeriodId: startDateBenchmarkPeriod
        }
      };
      afterEach(async () => {
        await Students.removeAsync({});
      });

      it("should return the currently set individualStatsAsOfDate if it is not before the first intervention the currentSkill points at", async () => {
        const result = await getValidatedStatsAsOfDate(student, "individual");

        expect(result).toEqual(startDate);
      });

      it(`should return the first intervention date the currentSkill points at and update the individualStatsAsOfDate value 
    if it was set to before the beginning of that intervention`, async () => {
        const { ...updatedStudent } = student;
        updatedStudent.individualStatsAsOfDate = {
          [startDateBenchmarkPeriod]: twoWeeksBeforeStartDate
        };
        const studentId = await Students.insertAsync(updatedStudent);

        const result = await getValidatedStatsAsOfDate(updatedStudent, "individual");

        const studentDocument = await Students.findOneAsync(studentId);
        expect(result).toEqual(startDate);
        expect(studentDocument.individualStatsAsOfDate[startDateBenchmarkPeriod]).toEqual(startDate);
      });
    });
  });
});

describe("getActiveWeeks with school breaks", () => {
  const testSchoolYear = 2017;
  it("Should count summer break weeks if intervention continues after school year end date", async () => {
    const history = JSON.parse(JSON.stringify(exampleClasswideHistory3));
    const cleanHistory = getCleanHistory(history, currentClasswideSkill2);
    const endWeek = await getEndWeekOfInterventions({
      cleanHistory,
      schoolYear: testSchoolYear,
      isInterventionComplete: true
    });
    const thisStudentsParticipationHistory = cleanHistory.filter(
      groupHistory =>
        groupHistory.enrolledStudentIds && groupHistory.enrolledStudentIds.includes("Devin_Potter_DEMO_STUDENT")
    );
    const startWeek = await getStartWeekOfInterventions(thisStudentsParticipationHistory, testSchoolYear);

    const weeksActive = await getActiveWeeks({
      startWeek,
      endWeek,
      history: thisStudentsParticipationHistory,
      type: "individual",
      schoolYear: testSchoolYear
    });
    assert.equal(4, weeksActive.length);
  });

  it("Should not count summer break weeks if intervention is active but has no scores after school year end date", async () => {
    const history = JSON.parse(JSON.stringify(exampleClasswideHistory4));
    const cleanHistory = getCleanHistory(history, currentClasswideSkill2);
    const endWeek = await getEndWeekOfInterventions({
      cleanHistory,
      schoolYear: testSchoolYear,
      isInterventionComplete: true
    });
    const thisStudentsParticipationHistory = cleanHistory.filter(
      groupHistory =>
        groupHistory.enrolledStudentIds && groupHistory.enrolledStudentIds.includes("Devin_Potter_DEMO_STUDENT")
    );
    const startWeek = await getStartWeekOfInterventions(thisStudentsParticipationHistory, testSchoolYear);

    const weeksActive = await getActiveWeeks({
      startWeek,
      endWeek,
      history: thisStudentsParticipationHistory,
      type: "individual",
      schoolYear: testSchoolYear
    });
    assert.equal(1, weeksActive.length);
  });

  describe("should not include weeks when winter, spring or other school breaks are specified", () => {
    const startWeek = 49;
    const endWeek = 70;
    const schoolBreaks = {
      winter: { startMonth: 12, startDay: 24, endMonth: 1, endDay: 7 }, // weeks [51, 52, 53]
      spring: { startMonth: 4, startDay: 1, endMonth: 4, endDay: 8 }, // weeks [65, 66]
      other: {}
    };
    const schoolYearBoundary = {
      month: 6,
      day: 31
    };

    it("for the individual type", async () => {
      const expectedActiveWeeks = [49, 50, 51, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 67, 68, 69, 70];

      const activeWeeks = await getActiveWeeks({
        startWeek,
        endWeek,
        type: "individual",
        schoolYear: testSchoolYear,
        schoolBreaks,
        schoolYearBoundary
      });
      expect(activeWeeks).toEqual(expectedActiveWeeks);
    });

    it("for the group type", async () => {
      const expectedActiveWeeks = [49, 50, 51, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 67, 68, 69, 70];

      const activeWeeks = await getActiveWeeks({
        startWeek,
        endWeek,
        type: "group",
        schoolYear: testSchoolYear,
        schoolBreaks,
        schoolYearBoundary
      });
      expect(activeWeeks).toEqual(expectedActiveWeeks);
    });

    it("for the individualClasswide type", async () => {
      const history = JSON.parse(JSON.stringify(exampleClasswideHistory2));
      const cleanHistory = getCleanHistory(history, currentClasswideSkill2);
      const startWeekOfInterventions = await getStartWeekOfInterventions(cleanHistory, testSchoolYear);
      const endWeekOfInterventions = await getEndWeekOfInterventions({
        cleanHistory,
        schoolYear: testSchoolYear,
        isInterventionComplete: true
      });

      const expectedActiveWeeks = [54, 55, 56];

      const activeWeeks = await getActiveWeeks({
        startWeek: startWeekOfInterventions, // 53
        endWeek: endWeekOfInterventions, // 56
        type: "individualClasswide",
        history: cleanHistory,
        schoolYear: testSchoolYear,
        schoolBreaks,
        schoolYearBoundary
      });
      expect(activeWeeks).toEqual(expectedActiveWeeks);
    });
  });
});

describe("classwide-detail", () => {
  const generateConsecutive = (index, content) => {
    return Object.entries(content).reduce((a, [key, value]) => {
      const temp = a;
      if (typeof value === "string" && !["grade", "studentGroupId"].includes(key)) {
        temp[key] = value.replace(/\d+/g, index);
      } else {
        temp[key] = value;
      }
      return temp;
    }, {});
  };

  const generateStudents = (startingIndex, numberOfStudents) => {
    const elements = [];
    for (let i = startingIndex; i < numberOfStudents + startingIndex; i += 1) {
      elements.push(
        generateConsecutive(i, {
          _id: "studentId1",
          identity: {
            name: generateConsecutive(i, {
              firstName: "First1",
              lastName: "Last1"
            })
          }
        })
      );
    }
    return elements;
  };

  const genereateBasic = (startingIndex, numberOfItemsToGenerate, content) => {
    const elements = [];
    for (let i = startingIndex; i < numberOfItemsToGenerate + startingIndex; i += 1) {
      elements.push(generateConsecutive(i, content));
    }
    return elements;
  };

  describe("getIndividualStudentDetailData", () => {
    const studentGroupId = "studentGroupId";
    const schoolYear = 2020;
    const grade = "02";

    beforeEach(async () => {
      await Students.insertAsync(generateStudents(1, 5));
      await StudentGroups.insertAsync({ _id: studentGroupId, grade, schoolYear, isActive: true });
      await StudentGroupEnrollments.insertAsync(
        genereateBasic(1, 5, { _id: "sgeId1", studentId: "studentId1", schoolYear, isActive: true, studentGroupId })
      );
      await Rules.insertAsync([
        {
          grade,
          skills: genereateBasic(1, 4, { assessmentId: "assessmentId1", assessmentName: "assessment1" })
        }
      ]);
      await Assessments.insertAsync([genereateBasic(1, 4, { _id: "assessmentId1", name: "assessment1" })]);
      await AssessmentResults.insertAsync([
        {
          _id: "arId5",
          studentGroupId,
          schoolYear,
          type: "classwide",
          status: "OPEN",
          lastModified: { on: 15 },
          scores: genereateBasic(1, 5, { studentId: "studentId1", status: "STARTED" })
        },
        {
          _id: "arId4",
          studentGroupId,
          schoolYear,
          type: "classwide",
          status: "COMPLETED",
          lastModified: { on: 15 },
          scores: [
            { studentId: "studentId1", status: "COMPLETED", value: "30" },
            { studentId: "studentId2", status: "COMPLETED", value: "30" },
            { studentId: "studentId4", status: "CANCELLED", value: "N/A" },
            { studentId: "studentId5", status: "COMPLETED", value: "0" }
          ],
          measures: [
            {
              assessmentId: "assessmentId3",
              medianScore: 30,
              targetScores: [11, 23, 300],
              studentResults: [
                { studentId: "studentId1", status: "COMPLETE", individualRuleOutcome: "above" },
                { studentId: "studentId2", status: "COMPLETE", individualRuleOutcome: "above" },
                { studentId: "studentId5", status: "COMPLETE", individualRuleOutcome: "below" }
              ]
            }
          ]
        },
        {
          _id: "arId3",
          studentGroupId,
          schoolYear,
          type: "classwide",
          status: "COMPLETED",
          lastModified: { on: 10 },
          scores: [
            { studentId: "studentId1", status: "COMPLETED", value: "12" },
            { studentId: "studentId2", status: "COMPLETED", value: "12" },
            { studentId: "studentId3", status: "CANCELLED", value: "N/A" },
            { studentId: "studentId4", status: "COMPLETED", value: "12" },
            { studentId: "studentId5", status: "COMPLETED", value: "12" }
          ],
          measures: [
            {
              assessmentId: "assessmentId2",
              medianScore: 12,
              targetScores: [11, 23, 300],
              studentResults: [
                { studentId: "studentId1", status: "COMPLETE", individualRuleOutcome: "at" },
                { studentId: "studentId2", status: "COMPLETE", individualRuleOutcome: "at" },
                { studentId: "studentId4", status: "COMPLETE", individualRuleOutcome: "at" },
                { studentId: "studentId5", status: "COMPLETE", individualRuleOutcome: "at" }
              ]
            }
          ]
        },
        {
          _id: "arId2",
          studentGroupId,
          schoolYear,
          type: "classwide",
          status: "COMPLETED",
          lastModified: { on: 5 },
          scores: [
            { studentId: "studentId1", status: "COMPLETED", value: "7" },
            { studentId: "studentId2", status: "COMPLETED", value: "11" },
            { studentId: "studentId3", status: "COMPLETED", value: "8" },
            { studentId: "studentId4", status: "COMPLETED", value: "9" },
            { studentId: "studentId5", status: "COMPLETED", value: "23" }
          ],
          measures: [
            {
              assessmentId: "assessmentId2",
              medianScore: 9,
              targetScores: [11, 23, 300],
              studentResults: [
                { studentId: "studentId1", status: "COMPLETE", individualRuleOutcome: "below" },
                { studentId: "studentId2", status: "COMPLETE", individualRuleOutcome: "at" },
                { studentId: "studentId3", status: "COMPLETE", individualRuleOutcome: "below" },
                { studentId: "studentId4", status: "COMPLETE", individualRuleOutcome: "below" },
                { studentId: "studentId5", status: "COMPLETE", individualRuleOutcome: "above" }
              ]
            }
          ]
        },
        {
          _id: "arId1",
          studentGroupId,
          schoolYear,
          type: "classwide",
          status: "COMPLETED",
          lastModified: { on: 1 },
          scores: [
            { studentId: "studentId1", status: "COMPLETED", value: "30" },
            { studentId: "studentId2", status: "COMPLETED", value: "30" },
            { studentId: "studentId3", status: "COMPLETED", value: "30" },
            { studentId: "studentId4", status: "COMPLETED", value: "15" },
            { studentId: "studentId5", status: "COMPLETED", value: "9" }
          ],
          measures: [
            {
              assessmentId: "assessmentId1",
              medianScore: 30,
              targetScores: [11, 23, 300],
              studentResults: [
                { studentId: "studentId1", status: "COMPLETE", individualRuleOutcome: "above" },
                { studentId: "studentId2", status: "COMPLETE", individualRuleOutcome: "above" },
                { studentId: "studentId3", status: "COMPLETE", individualRuleOutcome: "above" },
                { studentId: "studentId4", status: "COMPLETE", individualRuleOutcome: "at" },
                { studentId: "studentId5", status: "COMPLETE", individualRuleOutcome: "below" }
              ]
            }
          ]
        }
      ]);
    });

    afterEach(async () => {
      await Students.removeAsync({});
      await StudentGroups.removeAsync({});
      await StudentGroupEnrollments.removeAsync({});
      await AssessmentResults.removeAsync({});
      await Rules.removeAsync({});
      await Assessments.removeAsync({});
    });

    it("should return empty data when there are no assessment results where group reached mastery target", async () => {
      await AssessmentResults.removeAsync({});
      await StudentGroups.removeAsync({});
      const result = await getIndividualStudentDetailData({ studentGroupId });
      expect(result.rowData).toEqual({});
      expect(result.summaryAll).toEqual({});
    });
    it("should return correct statistics", async () => {
      const result = await getIndividualStudentDetailData({ studentGroupId, shouldIncludeAllSkills: true });
      expect(result.rowData).toEqual({
        studentId1: {
          firstName: "First1",
          lastName: "Last1",
          columns: [
            { skillName: "assessment1", outcome: "above" },
            { skillName: "assessment2", outcome: "at" },
            { skillName: "assessment3", outcome: "above" },
            { skillName: "assessment4", outcome: null }
          ]
        },
        studentId2: {
          firstName: "First2",
          lastName: "Last2",
          columns: [
            { skillName: "assessment1", outcome: "above" },
            { skillName: "assessment2", outcome: "at" },
            { skillName: "assessment3", outcome: "above" },
            { skillName: "assessment4", outcome: null }
          ]
        },
        studentId3: {
          firstName: "First3",
          lastName: "Last3",
          columns: [
            { skillName: "assessment1", outcome: "above" },
            { skillName: "assessment2", outcome: "below" },
            { skillName: "assessment3", outcome: null },
            { skillName: "assessment4", outcome: null }
          ]
        },
        studentId4: {
          firstName: "First4",
          lastName: "Last4",
          columns: [
            { skillName: "assessment1", outcome: "at" },
            { skillName: "assessment2", outcome: "at" },
            { skillName: "assessment3", outcome: "absent" },
            { skillName: "assessment4", outcome: null }
          ]
        },
        studentId5: {
          firstName: "First5",
          lastName: "Last5",
          columns: [
            { skillName: "assessment1", outcome: "below" },
            { skillName: "assessment2", outcome: "at" },
            { skillName: "assessment3", outcome: "below" },
            { skillName: "assessment4", outcome: null }
          ]
        }
      });
      expect(result.summaryAll).toEqual({
        rowName: "Summary All",
        columns: [
          {
            skillName: "assessment1",
            mastery: 60,
            masteryCount: 3,
            instructional: 20,
            instructionalCount: 1,
            frustrational: 20,
            frustrationalCount: 1,
            absent: 0,
            absentCount: 0,
            numberOfStudents: 5
          },
          {
            skillName: "assessment2",
            mastery: 0,
            masteryCount: 0,
            instructional: 80,
            instructionalCount: 4,
            frustrational: 20,
            frustrationalCount: 1,
            absent: 0,
            absentCount: 0,
            numberOfStudents: 5
          },
          {
            skillName: "assessment3",
            mastery: 50,
            masteryCount: 2,
            instructional: 0,
            instructionalCount: 0,
            frustrational: 25,
            frustrationalCount: 1,
            absent: 25,
            absentCount: 1,
            numberOfStudents: 4
          },
          {
            skillName: "assessment4",
            mastery: null,
            masteryCount: undefined,
            instructional: null,
            instructionalCount: undefined,
            frustrational: null,
            frustrationalCount: undefined,
            absent: null,
            absentCount: undefined,
            numberOfStudents: undefined
          },
          {
            skillName: "All Skills",
            mastery: 36,
            masteryCount: 5,
            instructional: 36,
            instructionalCount: 5,
            frustrational: 21,
            frustrationalCount: 3,
            absent: 7,
            absentCount: 1,
            numberOfStudents: 14
          }
        ]
      });
    });
  });
  describe("getGradeDetailData", () => {
    const siteId = "siteId";
    const studentGroupId1 = "studentGroupId1";
    const studentGroupId2 = "studentGroupId2";
    const schoolYear = 2020;
    const grade = "02";

    beforeEach(async () => {
      await Students.insertAsync(generateStudents(1, 10));
      await StudentGroups.insertAsync([
        {
          _id: studentGroupId1,
          grade,
          name: "StudentGroup1",
          schoolYear,
          siteId,
          isActive: true,
          history: [{ type: "classwide" }]
        },
        {
          _id: studentGroupId2,
          grade,
          name: "StudentGroup2",
          schoolYear,
          siteId,
          isActive: true,
          history: [{ type: "classwide" }]
        }
      ]);
      await StudentGroupEnrollments.insertAsync([
        ...genereateBasic(1, 5, {
          _id: "sgeId1",
          studentId: "studentId1",
          schoolYear,
          isActive: true,
          studentGroupId: studentGroupId1
        }),
        ...genereateBasic(6, 10, {
          _id: "sgeId1",
          studentId: "studentId1",
          schoolYear,
          isActive: true,
          studentGroupId: studentGroupId2
        })
      ]);
      await Rules.insertAsync([
        {
          grade,
          skills: genereateBasic(1, 4, { assessmentId: "assessmentId1", assessmentName: "assessment1" })
        }
      ]);
      await Assessments.insertAsync([genereateBasic(1, 4, { _id: "assessmentId1", name: "assessment1" })]);
      await AssessmentResults.insertAsync([
        {
          _id: "arId5",
          studentGroupId: studentGroupId1,
          schoolYear,
          type: "classwide",
          status: "OPEN",
          lastModified: { on: 15 },
          scores: genereateBasic(1, 5, { studentId: "studentId1", status: "STARTED" })
        },
        {
          _id: "arId4",
          studentGroupId: studentGroupId1,
          schoolYear,
          type: "classwide",
          status: "COMPLETED",
          lastModified: { on: 15 },
          scores: [
            { studentId: "studentId1", status: "COMPLETED", value: "30" },
            { studentId: "studentId2", status: "COMPLETED", value: "30" },
            { studentId: "studentId4", status: "CANCELLED", value: "N/A" },
            { studentId: "studentId5", status: "COMPLETED", value: "0" }
          ],
          measures: [
            {
              assessmentId: "assessmentId3",
              medianScore: 30,
              targetScores: [11, 23, 300],
              studentResults: [
                { studentId: "studentId1", status: "COMPLETE", individualRuleOutcome: "above" },
                { studentId: "studentId2", status: "COMPLETE", individualRuleOutcome: "above" },
                { studentId: "studentId5", status: "COMPLETE", individualRuleOutcome: "below" }
              ]
            }
          ]
        },
        {
          _id: "arId3",
          studentGroupId: studentGroupId1,
          schoolYear,
          type: "classwide",
          status: "COMPLETED",
          lastModified: { on: 10 },
          scores: [
            { studentId: "studentId1", status: "COMPLETED", value: "12" },
            { studentId: "studentId2", status: "COMPLETED", value: "12" },
            { studentId: "studentId3", status: "CANCELLED", value: "N/A" },
            { studentId: "studentId4", status: "COMPLETED", value: "12" },
            { studentId: "studentId5", status: "COMPLETED", value: "12" }
          ],
          measures: [
            {
              assessmentId: "assessmentId2",
              medianScore: 12,
              targetScores: [11, 23, 300],
              studentResults: [
                { studentId: "studentId1", status: "COMPLETE", individualRuleOutcome: "at" },
                { studentId: "studentId2", status: "COMPLETE", individualRuleOutcome: "at" },
                { studentId: "studentId4", status: "COMPLETE", individualRuleOutcome: "at" },
                { studentId: "studentId5", status: "COMPLETE", individualRuleOutcome: "at" }
              ]
            }
          ]
        },
        {
          _id: "arId2",
          studentGroupId: studentGroupId1,
          schoolYear,
          type: "classwide",
          status: "COMPLETED",
          lastModified: { on: 5 },
          scores: [
            { studentId: "studentId1", status: "COMPLETED", value: "7" },
            { studentId: "studentId2", status: "COMPLETED", value: "11" },
            { studentId: "studentId3", status: "COMPLETED", value: "8" },
            { studentId: "studentId4", status: "COMPLETED", value: "9" },
            { studentId: "studentId5", status: "COMPLETED", value: "23" }
          ],
          measures: [
            {
              assessmentId: "assessmentId2",
              medianScore: 9,
              targetScores: [11, 23, 300],
              studentResults: [
                { studentId: "studentId1", status: "COMPLETE", individualRuleOutcome: "below" },
                { studentId: "studentId2", status: "COMPLETE", individualRuleOutcome: "at" },
                { studentId: "studentId3", status: "COMPLETE", individualRuleOutcome: "below" },
                { studentId: "studentId4", status: "COMPLETE", individualRuleOutcome: "below" },
                { studentId: "studentId5", status: "COMPLETE", individualRuleOutcome: "above" }
              ]
            }
          ]
        },
        {
          _id: "arId1",
          studentGroupId: studentGroupId1,
          schoolYear,
          type: "classwide",
          status: "COMPLETED",
          lastModified: { on: 1 },
          scores: [
            { studentId: "studentId1", status: "COMPLETED", value: "30" },
            { studentId: "studentId2", status: "COMPLETED", value: "30" },
            { studentId: "studentId3", status: "COMPLETED", value: "30" },
            { studentId: "studentId4", status: "COMPLETED", value: "15" },
            { studentId: "studentId5", status: "COMPLETED", value: "9" }
          ],
          measures: [
            {
              assessmentId: "assessmentId1",
              medianScore: 30,
              targetScores: [11, 23, 300],
              studentResults: [
                { studentId: "studentId1", status: "COMPLETE", individualRuleOutcome: "above" },
                { studentId: "studentId2", status: "COMPLETE", individualRuleOutcome: "above" },
                { studentId: "studentId3", status: "COMPLETE", individualRuleOutcome: "above" },
                { studentId: "studentId4", status: "COMPLETE", individualRuleOutcome: "at" },
                { studentId: "studentId5", status: "COMPLETE", individualRuleOutcome: "below" }
              ]
            }
          ]
        },

        {
          _id: "arId6",
          studentGroupId: studentGroupId2,
          schoolYear,
          type: "classwide",
          status: "OPEN",
          lastModified: { on: 15 },
          scores: [
            { studentId: "studentId6", status: "STARTED" },
            { studentId: "studentId7", status: "STARTED" },
            { studentId: "studentId8", status: "STARTED" },
            { studentId: "studentId9", status: "STARTED" },
            { studentId: "studentId10", status: "STARTED" }
          ]
        },
        {
          _id: "arId7",
          studentGroupId: studentGroupId2,
          schoolYear,
          type: "classwide",
          status: "COMPLETED",
          lastModified: { on: 15 },
          scores: [
            { studentId: "studentId6", status: "COMPLETED", value: "30" },
            { studentId: "studentId7", status: "COMPLETED", value: "30" },
            { studentId: "studentId9", status: "CANCELLED", value: "N/A" },
            { studentId: "studentId10", status: "COMPLETED", value: "0" }
          ],
          measures: [
            {
              assessmentId: "assessmentId3",
              medianScore: 30,
              targetScores: [11, 23, 300],
              studentResults: [
                { studentId: "studentId6", status: "COMPLETE", individualRuleOutcome: "above" },
                { studentId: "studentId7", status: "COMPLETE", individualRuleOutcome: "above" },
                { studentId: "studentId10", status: "COMPLETE", individualRuleOutcome: "below" }
              ]
            }
          ]
        },
        {
          _id: "arId8",
          studentGroupId: studentGroupId2,
          schoolYear,
          type: "classwide",
          status: "COMPLETED",
          lastModified: { on: 10 },
          scores: [
            { studentId: "studentId6", status: "COMPLETED", value: "12" },
            { studentId: "studentId7", status: "COMPLETED", value: "12" },
            { studentId: "studentId8", status: "CANCELLED", value: "N/A" },
            { studentId: "studentId9", status: "COMPLETED", value: "12" },
            { studentId: "studentId10", status: "COMPLETED", value: "12" }
          ],
          measures: [
            {
              assessmentId: "assessmentId2",
              medianScore: 12,
              targetScores: [11, 23, 300],
              studentResults: [
                { studentId: "studentId6", status: "COMPLETE", individualRuleOutcome: "at" },
                { studentId: "studentId7", status: "COMPLETE", individualRuleOutcome: "at" },
                { studentId: "studentId9", status: "COMPLETE", individualRuleOutcome: "at" },
                { studentId: "studentId10", status: "COMPLETE", individualRuleOutcome: "at" }
              ]
            }
          ]
        },
        {
          _id: "arId9",
          studentGroupId: studentGroupId2,
          schoolYear,
          type: "classwide",
          status: "COMPLETED",
          lastModified: { on: 5 },
          scores: [
            { studentId: "studentId6", status: "COMPLETED", value: "7" },
            { studentId: "studentId7", status: "COMPLETED", value: "11" },
            { studentId: "studentId8", status: "COMPLETED", value: "8" },
            { studentId: "studentId9", status: "COMPLETED", value: "12" },
            { studentId: "studentId10", status: "COMPLETED", value: "23" }
          ],
          measures: [
            {
              assessmentId: "assessmentId2",
              medianScore: 11,
              targetScores: [11, 23, 300],
              studentResults: [
                { studentId: "studentId6", status: "COMPLETE", individualRuleOutcome: "below" },
                { studentId: "studentId7", status: "COMPLETE", individualRuleOutcome: "at" },
                { studentId: "studentId8", status: "COMPLETE", individualRuleOutcome: "below" },
                { studentId: "studentId9", status: "COMPLETE", individualRuleOutcome: "at" },
                { studentId: "studentId10", status: "COMPLETE", individualRuleOutcome: "above" }
              ]
            }
          ]
        },
        {
          _id: "arId10",
          studentGroupId: studentGroupId2,
          schoolYear,
          type: "classwide",
          status: "COMPLETED",
          lastModified: { on: 1 },
          scores: [
            { studentId: "studentId6", status: "COMPLETED", value: "30" },
            { studentId: "studentId7", status: "COMPLETED", value: "30" },
            { studentId: "studentId8", status: "COMPLETED", value: "30" },
            { studentId: "studentId9", status: "COMPLETED", value: "9" },
            { studentId: "studentId10", status: "COMPLETED", value: "9" }
          ],
          measures: [
            {
              assessmentId: "assessmentId1",
              medianScore: 30,
              targetScores: [11, 23, 300],
              studentResults: [
                { studentId: "studentId6", status: "COMPLETE", individualRuleOutcome: "above" },
                { studentId: "studentId7", status: "COMPLETE", individualRuleOutcome: "above" },
                { studentId: "studentId8", status: "COMPLETE", individualRuleOutcome: "above" },
                { studentId: "studentId9", status: "COMPLETE", individualRuleOutcome: "below" },
                { studentId: "studentId10", status: "COMPLETE", individualRuleOutcome: "below" }
              ]
            }
          ]
        }
      ]);
    });

    afterEach(async () => {
      await Students.removeAsync({});
      await StudentGroups.removeAsync({});
      await StudentGroupEnrollments.removeAsync({});
      await AssessmentResults.removeAsync({});
      await Rules.removeAsync({});
      await Assessments.removeAsync({});
    });

    it("should return empty data when there are no assessment results where group reached mastery target", async () => {
      await AssessmentResults.removeAsync({});
      await StudentGroups.removeAsync({});
      const result = getGradeDetailData({ studentGroups: [], siteId, grade, schoolYear });
      expect(result.rowData).toEqual({});
      expect(result.summaryAll).toEqual({});
    });
    it("should return correct statistics", async () => {
      const result = getGradeDetailData({
        studentGroups: await StudentGroups.find({ siteId }).fetchAsync(),
        gradeSkills: (await Rules.findOneAsync({ grade })).skills,
        assessmentResultsByGroupId: groupBy(
          await AssessmentResults.find({ type: "classwide", status: "COMPLETED" }).fetchAsync(),
          "studentGroupId"
        ),
        assessmentNameById: getValuesByKey(await Assessments.find().fetchAsync(), "_id", "name"),
        shouldCalculateData: true
      });
      expect(result.rowData).toEqual({
        [studentGroupId1]: {
          rowName: "StudentGroup1",
          columns: [
            {
              skillName: "assessment1",
              mastery: 60,
              masteryCount: 3,
              instructional: 20,
              instructionalCount: 1,
              frustrational: 20,
              frustrationalCount: 1,
              absent: 0,
              absentCount: 0,
              numberOfStudents: 5
            },
            {
              skillName: "assessment2",
              mastery: 0,
              masteryCount: 0,
              instructional: 80,
              instructionalCount: 4,
              frustrational: 20,
              frustrationalCount: 1,
              absent: 0,
              absentCount: 0,
              numberOfStudents: 5
            },
            {
              skillName: "assessment3",
              mastery: 50,
              masteryCount: 2,
              instructional: 0,
              instructionalCount: 0,
              frustrational: 25,
              frustrationalCount: 1,
              absent: 25,
              absentCount: 1,
              numberOfStudents: 4
            },
            {
              skillName: "assessment4",
              mastery: null,
              masteryCount: undefined,
              instructional: null,
              instructionalCount: undefined,
              frustrational: null,
              frustrationalCount: undefined,
              absent: null,
              absentCount: undefined,
              numberOfStudents: undefined
            },
            {
              skillName: "All Skills",
              mastery: 36,
              masteryCount: 5,
              instructional: 36,
              instructionalCount: 5,
              frustrational: 21,
              frustrationalCount: 3,
              absent: 7,
              absentCount: 1,
              numberOfStudents: 14
            }
          ]
        },
        [studentGroupId2]: {
          rowName: "StudentGroup2",
          columns: [
            {
              skillName: "assessment1",
              mastery: 60,
              masteryCount: 3,
              instructional: 0,
              instructionalCount: 0,
              frustrational: 40,
              frustrationalCount: 2,
              absent: 0,
              absentCount: 0,
              numberOfStudents: 5
            },
            {
              skillName: "assessment2",
              mastery: 0,
              masteryCount: 0,
              instructional: 80,
              instructionalCount: 4,
              frustrational: 20,
              frustrationalCount: 1,
              absent: 0,
              absentCount: 0,
              numberOfStudents: 5
            },
            {
              skillName: "assessment3",
              mastery: 50,
              masteryCount: 2,
              instructional: 0,
              instructionalCount: 0,
              frustrational: 25,
              frustrationalCount: 1,
              absent: 25,
              absentCount: 1,
              numberOfStudents: 4
            },
            {
              skillName: "assessment4",
              mastery: null,
              instructional: null,
              frustrational: null,
              absent: null,
              masteryCount: undefined,
              instructionalCount: undefined,
              frustrationalCount: undefined,
              absentCount: undefined,
              numberOfStudents: undefined
            },
            {
              skillName: "All Skills",
              mastery: 36,
              masteryCount: 5,
              instructional: 29,
              instructionalCount: 4,
              frustrational: 29,
              frustrationalCount: 4,
              absent: 7,
              absentCount: 1,
              numberOfStudents: 14
            }
          ]
        }
      });
      expect(result.summaryAll).toEqual({
        rowName: "Summary All",
        columns: [
          {
            skillName: "assessment1",
            mastery: 60,
            masteryCount: 6,
            instructional: 10,
            instructionalCount: 1,
            frustrational: 30,
            frustrationalCount: 3,
            absent: 0,
            absentCount: 0,
            numberOfGroupsWithData: 2,
            numberOfStudents: 10
          },
          {
            skillName: "assessment2",
            mastery: 0,
            masteryCount: 0,
            instructional: 80,
            instructionalCount: 8,
            frustrational: 20,
            frustrationalCount: 2,
            absent: 0,
            absentCount: 0,
            numberOfGroupsWithData: 2,
            numberOfStudents: 10
          },
          {
            skillName: "assessment3",
            mastery: 50,
            masteryCount: 4,
            instructional: 0,
            instructionalCount: 0,
            frustrational: 25,
            frustrationalCount: 2,
            absent: 25,
            absentCount: 2,
            numberOfGroupsWithData: 2,
            numberOfStudents: 8
          },
          {
            skillName: "assessment4",
            mastery: null,
            instructional: null,
            frustrational: null,
            absent: null,
            masteryCount: null,
            instructionalCount: null,
            frustrationalCount: null,
            absentCount: null,
            numberOfGroupsWithData: null,
            numberOfStudents: 0
          },
          {
            skillName: "All Skills",
            mastery: 36,
            masteryCount: 10,
            instructional: 32,
            instructionalCount: 9,
            frustrational: 25,
            frustrationalCount: 7,
            absent: 7,
            absentCount: 2,
            numberOfGroupsWithData: 2,
            numberOfStudents: 28
          }
        ]
      });
    });
  });
});
