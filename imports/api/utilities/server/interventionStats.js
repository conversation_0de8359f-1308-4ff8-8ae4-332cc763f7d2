import { Meteor } from "meteor/meteor";
import { check, Match } from "meteor/check";
import { groupBy, uniq } from "lodash";
import { StudentGroupEnrollments } from "../../studentGroupEnrollments/studentGroupEnrollments";
import { Students } from "../../students/students";
import { StudentGroups } from "../../studentGroups/studentGroups";
import { getCurrentSchoolYear, getMeteorUser, getValuesByKey, ninjalog } from "../utilities";
import { isHighSchoolGrade } from "../../../ui/utilities";
import { AssessmentResults } from "../../assessmentResults/assessmentResults";
import { getAbsoluteWeekNumber } from "./utilities";
import { generateReport } from "../../../scripts/exportScores/lib/ReportGenerator";
import { getClasswideStatsForStudentGroup, getValidatedStatsAsOfDate } from "./getClasswideStatsForStudentGroup";
import { calculateStats } from "./calculateStats";
import { Rules } from "../../rules/rules";
import { Assessments } from "../../assessments/assessments";
import {
  getGradeDetailData,
  getSummaryAllForIndividualStudentDetail,
  processDataForDetailData
} from "../skillProgressMethods";

/** *************************************************************************
 * Get individual stats (Weeks per skill and intervention consistency)
 ************************************************************************* */
export async function getIndividualStatsForStudentGroup(studentGroup) {
  // let startWeekNumber;
  // Get data
  const studentIdsInGroup = (
    await StudentGroupEnrollments.find({
      studentGroupId: studentGroup._id,
      isActive: true
    }).fetchAsync()
  ).map(sge => sge.studentId);

  // Only students with active interventions are counted
  const studentsInActiveIndividualInterventions = await Students.find({
    _id: { $in: studentIdsInGroup },
    "currentSkill.assessmentId": { $exists: true }
  }).fetchAsync();

  // Loop through each kid and calculate stats
  const individualResults = [];
  // eslint-disable-next-line no-restricted-syntax
  for await (const stu of studentsInActiveIndividualInterventions) {
    // Make sure that they are currently or were previously scheduled for individual
    if (
      (stu.history && stu.history.some(sh => sh.assessmentId)) ||
      (stu.currentSkill && stu.currentSkill.assessmentId)
    ) {
      let customStartWeekNumber;
      if (
        stu.currentSkill &&
        stu.currentSkill.benchmarkPeriodId &&
        stu.individualStatsAsOfDate &&
        stu.individualStatsAsOfDate[stu.currentSkill.benchmarkPeriodId]
      ) {
        const validatedStatsAsOfDate = await getValidatedStatsAsOfDate(stu, "individual");
        customStartWeekNumber = await getAbsoluteWeekNumber(validatedStatsAsOfDate);
      }
      const thisStudentsStats = await calculateStats(
        stu.history,
        stu.currentSkill,
        "individual",
        null,
        customStartWeekNumber || null,
        null,
        studentGroup.schoolYear,
        studentGroup.orgid
      );
      thisStudentsStats.studentId = stu._id;

      individualResults.push({
        studentId: stu._id,
        studentFirstName: stu.identity.name.firstName,
        studentLastName: stu.identity.name.lastName,
        startWeek: thisStudentsStats.startWeek,
        endWeek: thisStudentsStats.endWeek,
        activeWeekNumbers: thisStudentsStats.activeWeekNumbers,
        numberOfSkillsPracticed: thisStudentsStats.numberOfSkillsPracticed,
        numberOfWeeksActive: thisStudentsStats.numberOfWeeksActive,
        weekNumbersWithScoresEntered: thisStudentsStats.weekNumbersWithScoresSinceStartWeek,
        numberOfWeeksWithScoresEntered: thisStudentsStats.weekNumbersWithScoresSinceStartWeek.length,
        averageWeeksPerSkill: thisStudentsStats.averageWeeksPerSkill,
        interventionConsistency: thisStudentsStats.interventionConsistency
      });
    }
  }
  // Calculate average intervention consistency and weeks perSkill for entire class
  // See JIRA - SPRIN-246 for an example of these calculations from Amanda
  let averageWeeksPerSkill = null;
  let sumTotalAverageWeeksPerSkill = 0;
  let averageWeeksPerSkillPoints = 0;
  let averageInterventionConsistency = null;
  let opportunitiesForScores = 0;
  let totalNumberofScores = 0;
  let activeWeeks = [];
  let weeksWithSomeScoresEntered = [];

  if (individualResults.length > 0) {
    individualResults.forEach(individualResultSummary => {
      opportunitiesForScores += individualResultSummary.numberOfWeeksActive;
      totalNumberofScores += individualResultSummary.numberOfWeeksWithScoresEntered;
      if (individualResultSummary.averageWeeksPerSkill !== null) {
        sumTotalAverageWeeksPerSkill += individualResultSummary.averageWeeksPerSkill;
        averageWeeksPerSkillPoints += 1;
      }
      activeWeeks = activeWeeks.concat(individualResultSummary.activeWeekNumbers);
      weeksWithSomeScoresEntered = weeksWithSomeScoresEntered.concat(
        individualResultSummary.weekNumbersWithScoresEntered
      );
    });
    // Get intervention consistency for group
    averageInterventionConsistency =
      opportunitiesForScores !== 0 ? Math.round((totalNumberofScores / opportunitiesForScores) * 100) : null;

    // Get the average weeks per skill for the group by dividing by the number of students
    if (averageWeeksPerSkillPoints > 0) {
      averageWeeksPerSkill = Math.round(sumTotalAverageWeeksPerSkill / averageWeeksPerSkillPoints);
    }

    // Clean up active and scored weeks
    activeWeeks = uniq(activeWeeks);
    weeksWithSomeScoresEntered = uniq(weeksWithSomeScoresEntered);
  }
  return {
    // startWeek: startWeekNumber,
    studentGroupId: studentGroup._id,
    studentGroupName: studentGroup.name,
    numberOfStudentsInIndividualInterventions: studentsInActiveIndividualInterventions.length,
    numberOfWeeksActive: activeWeeks.length,
    numberOfWeeksWithScoresEntered: weeksWithSomeScoresEntered.length,
    averageWeeksPerSkill,
    interventionConsistency: averageInterventionConsistency,
    individualResults
  };
}

const byDateOnMatchPattern = Match.ObjectIncluding({
  on: Number,
  date: Date
});

const studentGroupHistoryMatchPattern = {
  type: Match.Maybe(String),
  whenEnded: Match.Maybe(byDateOnMatchPattern),
  whenStarted: Match.Maybe(Match.OneOf(byDateOnMatchPattern, null)),
  enrolledStudentIds: Match.Maybe([String])
};

const studentGroupMatchPattern = {
  _id: String,
  name: String,
  grade: String,
  history: Match.Maybe(Match.OneOf([Match.ObjectIncluding(studentGroupHistoryMatchPattern)], null)),
  currentClasswideSkill: Match.Maybe(Match.ObjectIncluding(studentGroupHistoryMatchPattern))
};

Meteor.methods({
  CalculateIndividualStats(studentGroup) {
    check(studentGroup, Match.ObjectIncluding(studentGroupMatchPattern));
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    const individualResults = getIndividualStatsForStudentGroup(studentGroup);
    ninjalog.trace({
      msg: "individualResults",
      val: individualResults,
      context: "admin",
      jsonString: true,
      jsonDepth: null
    });
    return individualResults;
  },
  calculateClasswideStatsForMultipleGroups(studentGroupIds, includeIndividual) {
    ninjalog.trace({
      msg: "studentGroups",
      val: studentGroupIds,
      context: "admin",
      jsonString: true,
      jsonDepth: 10
    });
    check(studentGroupIds, [String]);
    check(includeIndividual, Boolean);
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    return calculateClasswideStatsForMultipleGroups(studentGroupIds, includeIndividual);
  },
  async CalculateClasswideStats(studentGroup, includeIndividual) {
    check(studentGroup, Match.ObjectIncluding(studentGroupMatchPattern));
    check(includeIndividual, Boolean);
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    const studentIdsInGroup = (
      await StudentGroupEnrollments.find({
        studentGroupId: studentGroup._id,
        isActive: true
      }).fetchAsync()
    ).map(sge => sge.studentId);
    const numberOfSkillsInClasswideTree = (
      await Rules.findOneAsync({
        grade: studentGroup.grade
      })
    ).skills.length;
    const classwideResults = await getClasswideStatsForStudentGroup({
      studentGroup,
      studentIdsInGroup,
      numberOfSkillsInClasswideTree
    });
    if (includeIndividual) {
      classwideResults.extendedIndividualResults = getIndividualStatsForStudentGroup(studentGroup);
    }
    ninjalog.trace({
      msg: "classwideResults",
      val: classwideResults,
      context: "admin",
      jsonString: true,
      jsonDepth: null
    });
    return classwideResults;
  },
  async generateReport({ selectedSchoolYear, selectedOrgid, selectedType, shouldGenerateInterventionNotes }) {
    check(selectedSchoolYear, Match.OneOf(String, Number));
    check(selectedOrgid, String);
    check(selectedType, String);

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    return generateReport({
      selectedSchoolYear,
      selectedOrgid,
      selectedType,
      shouldGenerateInterventionNotes
    });
  },
  getIndividualStudentDetailData({ studentGroupId, shouldIncludeAllSkills, studentId }) {
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    check(studentGroupId, String);
    check(shouldIncludeAllSkills, Boolean);
    check(studentId, Match.Maybe(studentId));

    return getIndividualStudentDetailData({ studentGroupId, shouldIncludeAllSkills, studentId });
  },
  async getGradeDetailData({ siteId, grade, schoolYear }) {
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    check(siteId, String);
    check(grade, String);
    check(schoolYear, Number);

    const studentGroups = await StudentGroups.find(
      { siteId, grade, schoolYear, isActive: true },
      { fields: { _id: 1, name: 1, history: 1 } }
    ).fetchAsync();

    const studentGroupIds = studentGroups.map(sg => sg._id);

    const shouldCalculateData =
      !!(await AssessmentResults.findOneAsync(
        {
          studentGroupId: { $in: studentGroupIds },
          schoolYear,
          status: "COMPLETED",
          type: "classwide"
        },
        { fields: { _id: 1 } }
      )) || studentGroups.length;

    const gradeSkills = (await Rules.findOneAsync({ grade })).skills;
    const assessmentNameById = getValuesByKey(
      await Assessments.find(
        { _id: { $in: gradeSkills.map(s => s.assessmentId) } },
        { fields: { name: 1 } }
      ).fetchAsync(),
      "_id",
      "name"
    );

    const assessmentResultsByGroupId =
      groupBy(
        await AssessmentResults.find(
          {
            studentGroupId: { $in: studentGroupIds },
            schoolYear,
            type: "classwide",
            status: "COMPLETED"
          },
          {
            fields: {
              "scores.studentId": 1,
              "scores.status": 1,
              "scores.value": 1,
              studentGroupId: 1,
              "measures.assessmentId": 1,
              "measures.assessmentName": 1,
              "measures.medianScore": 1,
              "measures.targetScores": 1,
              "measures.studentResults.studentId": 1,
              "measures.studentResults.status": 1,
              "measures.studentResults.individualRuleOutcome": 1
            },
            sort: { "lastModified.on": -1 }
          }
        ).fetchAsync(),
        "studentGroupId"
      ) || {};

    return getGradeDetailData({
      studentGroups,
      gradeSkills,
      assessmentResultsByGroupId,
      schoolYear,
      shouldCalculateData,
      assessmentNameById
    });
  }
});

export async function getIndividualStudentDetailData({ studentGroupId, shouldIncludeAllSkills, studentId }) {
  const { grade, schoolYear } =
    (await StudentGroups.findOneAsync({ _id: studentGroupId }, { fields: { grade: 1, schoolYear: 1 } })) || {};

  const gradeSkills = (await Rules.findOneAsync({ grade }))?.skills || [];
  const assessmentNameById = getValuesByKey(
    await Assessments.find(
      { _id: { $in: gradeSkills.map(s => s.assessmentId) } },
      { fields: { name: 1 } }
    ).fetchAsync(),
    "_id",
    "name"
  );

  const sgeStudentIds = (
    await StudentGroupEnrollments.find(
      { schoolYear, isActive: true, studentGroupId, ...(studentId ? { studentId } : {}) },
      { fields: { studentId: 1 } }
    ).fetchAsync()
  ).map(sge => sge.studentId);
  let students = (
    await Students.find({ _id: { $in: sgeStudentIds } }, { fields: { "identity.name": 1 } }).fetchAsync()
  ).map(s => ({ studentId: s._id, firstName: s.identity.name.firstName, lastName: s.identity.name.lastName }));

  if (studentId) {
    const student = students.find(s => s.studentId === studentId);
    students = student ? [student] : [];
  }

  const assessmentResults = await AssessmentResults.find(
    {
      studentGroupId,
      schoolYear,
      type: "classwide",
      status: "COMPLETED"
    },
    {
      fields: {
        "scores.studentId": 1,
        "scores.status": 1,
        "scores.value": 1,
        "measures.assessmentId": 1,
        "measures.assessmentName": 1,
        "measures.medianScore": 1,
        "measures.targetScores": 1,
        "measures.studentResults.studentId": 1,
        "measures.studentResults.status": 1,
        "measures.studentResults.individualRuleOutcome": 1,
        "lastModified.on": 1
      },
      sort: { "lastModified.on": -1 }
    }
  ).fetchAsync();

  if (!gradeSkills?.length) {
    return {
      rowData: {},
      summaryAll: {}
    };
  }
  const outcomesByAssessmentId = processDataForDetailData(assessmentResults);
  if (!outcomesByAssessmentId) {
    return {
      rowData: {},
      summaryAll: {}
    };
  }
  const summaryAll = getSummaryAllForIndividualStudentDetail({
    gradeSkills,
    outcomesByAssessmentId,
    assessmentNameById,
    shouldIncludeAllSkills
  });
  const studentRowData = {};
  students.forEach(({ studentId: sId, firstName, lastName }) => {
    studentRowData[sId] = {
      firstName,
      lastName
    };
    studentRowData[sId].columns = gradeSkills.map(skill => {
      const { assessmentId, assessmentName } = skill;
      const results = outcomesByAssessmentId[assessmentId] || {};
      const allOutcomes = [
        ...(results.above || []),
        ...(results.at || []),
        ...(results.below || []),
        ...(results.absent || [])
      ];

      return {
        skillName: results.assessmentName || assessmentName || assessmentNameById[assessmentId],
        outcome: allOutcomes.find(s => s.studentId === sId)?.outcome || null
      };
    });
  });

  return {
    rowData: studentRowData, // Data for each column for given student row
    summaryAll // Data for sum of each column
  };
}

export async function calculateClasswideStatsForMultipleGroups(studentGroupIds, includeIndividual) {
  const studentGroups = await StudentGroups.find({
    _id: { $in: studentGroupIds }
  }).fetchAsync();
  const studentGroupsWithHistory = studentGroups.filter(
    sg => (sg.history && sg.history.length) || sg.currentClasswideSkill || isHighSchoolGrade(sg.grade)
  );
  const studentGroupEnrollments = await StudentGroupEnrollments.find(
    { studentGroupId: { $in: studentGroupIds }, isActive: true },
    { fields: { studentGroupId: 1, studentId: 1, created: 1, isActive: 1 } }
  ).fetchAsync();
  const activeStudentIdsByStudentGroupId = studentGroupEnrollments.reduce((acc, sge) => {
    (acc[sge.studentGroupId] = acc[sge.studentGroupId] || []).push(sge.studentId);
    return acc;
  }, {});

  const rulesForGrades = await Rules.find(
    { grade: { $exists: true } },
    { fields: { grade: 1, skills: 1 } }
  ).fetchAsync();

  const numberOfSkillsInClasswideTreeByGrade = rulesForGrades.reduce((acc, rule) => {
    acc[rule.grade] = rule.skills?.length || 0;
    return acc;
  }, {});

  const schoolYear = await getCurrentSchoolYear(await getMeteorUser());

  const allClasswideResults = [];
  // eslint-disable-next-line no-restricted-syntax
  for await (const sg of studentGroupsWithHistory) {
    allClasswideResults.push(
      await getClasswideStatsForStudentGroup({
        studentGroup: sg,
        schoolYear,
        studentIdsInGroup: activeStudentIdsByStudentGroupId[sg._id],
        numberOfSkillsInClasswideTree: numberOfSkillsInClasswideTreeByGrade[sg.grade]
      })
    );
  }

  if (includeIndividual) {
    // eslint-disable-next-line no-restricted-syntax
    for await (const cwr of allClasswideResults) {
      cwr.extendedIndividualResults = await getIndividualStatsForStudentGroup(
        studentGroups.find(sg => sg._id === cwr.studentGroupId)
      );
    }
  }

  return { allClasswideResults, studentGroupEnrollments };
}
