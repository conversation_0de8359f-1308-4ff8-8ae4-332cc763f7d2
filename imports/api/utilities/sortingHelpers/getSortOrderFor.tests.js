import getSortOrderFor from "./getSortOrderFor";
import { ascendingOrder, descendingOrder, unsortedOrder, sampleList } from "./sortingStubs";

describe("getSortOrderFor", () => {
  it("should recognize when a list is sorted in an ascending order", () => {
    expect(getSortOrderFor(sampleList, "name")).toEqual(ascendingOrder);
  });
  it("should recognize when a list is sorted in a descending order", () => {
    expect(getSortOrderFor(sampleList, "ownerDetails.name.firstName")).toEqual(descendingOrder);
  });
  it("should recognize when a list is not sorted", () => {
    expect(getSortOrderFor(sampleList, "age")).toEqual(unsortedOrder);
  });
});
