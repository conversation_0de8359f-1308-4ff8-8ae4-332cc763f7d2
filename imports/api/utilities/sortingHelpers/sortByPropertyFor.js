import { get, sortBy, isEmpty, isString } from "lodash";
import normalizeElem from "./normalizeSortItem";

export default function sortByPropertyFor({ list, paths = [], order = 1 }) {
  if (!list) {
    throw new Error("List was not provided");
  }

  if (!paths || !paths.length) {
    throw new Error("No paths were provided");
  }

  if (isEmpty(list)) {
    throw new Error("Cannot sort an empty list");
  }

  const iteratees = paths.map(path => elem => {
    const normalizedElem = normalizeElem(elem);
    let value = get(normalizedElem, path);
    if (isString(value)) {
      value = value.toLowerCase();
    }
    return value;
  });

  const sortedList = sortBy(list, iteratees);

  if (order === -1) {
    return sortedList.reverse();
  }

  return sortedList;
}
