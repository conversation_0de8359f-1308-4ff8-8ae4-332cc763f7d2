export const sampleList = [
  {
    id: "1",
    arr: [1, 2, 3],
    name: "<PERSON>",
    grade: "03",
    age: 3,
    isOnlyPet: true,
    ownerDetails: {
      name: {
        firstName: "<PERSON><PERSON>",
        middleName: "<PERSON>",
        lastName: "<PERSON><PERSON>"
      },
      age: 23,
      isSingle: false
    }
  },
  {
    id: "2",
    arr: [1, 2, 3],
    name: "dog",
    grade: "K",
    age: 5,
    isOnlyPet: false,
    ownerDetails: {
      name: {
        firstName: "<PERSON>",
        lastName: "<PERSON>"
      },
      age: 51,
      isSingle: true
    }
  },
  {
    id: "3",
    arr: [1, 2, 3],
    name: "<PERSON>iraffe",
    grade: "08",
    age: 4,
    isOnlyPet: true,
    ownerDetails: {
      name: {
        firstName: "<PERSON>",
        middleName: "<PERSON>",
        lastName: "<PERSON>"
      },
      age: 42,
      isSingle: false
    }
  },
  {
    id: "4",
    arr: [1, 2, 3],
    name: "monkey",
    grade: "09",
    age: 7,
    isOnlyPet: false,
    ownerDetails: {
      name: {
        firstName: "<PERSON>",
        lastName: "Qson"
      },
      age: 42,
      isSingle: false
    }
  }
];

export const ascendingOrder = 1;
export const descendingOrder = -1;
export const unsortedOrder = 0;
