export const normalizeSeason = season => {
  const seasonsByLabel = {
    Fall: 0,
    Winter: 1,
    Spring: 2,
    "All Periods": 3
  };
  return seasonsByLabel[season];
};

const normalizeElem = elem => {
  const normalizedElem = { ...elem };
  if (elem.grade === "K") {
    normalizedElem.grade = "00";
  }
  if (elem.Grade === "K") {
    normalizedElem.Grade = "00";
  }
  if (elem.Season) {
    normalizedElem.Season = normalizeSeason(normalizedElem.Season);
  }
  return normalizedElem;
};

export default normalizeElem;

export const normalizeGrade = elem => {
  if (elem === "K") {
    return "00";
  }
  if (elem === "HS") {
    return "09";
  }
  return elem;
};
