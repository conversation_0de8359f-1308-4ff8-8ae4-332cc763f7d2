import { assert, expect } from "chai";
import sinon from "sinon";
import * as utils from "./utilities.js";
import { Settings } from "../settings/settings.js";
import stubUtils from "../../test-helpers/methods/stubUtils.js";
import {
  getGradeByGradeTranslation,
  getGradeFromDescriptor,
  getGradesFromOneRosterStudents,
  getGradeTranslationsByGrade,
  getSectionGradeMajority,
  getStudentHighestGrade,
  isDateInPreviousSchoolYear,
  isDateRangeInvalid
} from "./utilities.js";

describe("imports/api/utilities/utilities.js tests", () => {
  describe("getCurrentSchoolYear", () => {
    let settingsFindOneStub;
    beforeEach(() => {
      settingsFindOneStub = sinon.stub(Settings, "findOneAsync");
      settingsFindOneStub.callsFake(() => ({
        defaults: {
          schoolYearBoundary: {
            month: 6,
            day: 31 // don't care if it isn't a real date here
          }
        }
      }));
    });
    afterEach(() => {
      stubUtils.safeRestore(settingsFindOneStub);
    });
    it("Should return current year if the current date is before the school year boundary on the organization", async () => {
      settingsFindOneStub.callsFake(() => ({
        defaults: {
          schoolYearBoundary: {
            month: new Date().getMonth(),
            day: new Date().getDate() + 1 // don't care if it isn't a real date here
          }
        }
      }));
      assert.equal(await utils.getCurrentSchoolYear(), new Date().getFullYear());
    });
    it("should return current year + 1 if the current date is after the school year boundary on the organization", async () => {
      settingsFindOneStub.callsFake(() => ({
        defaults: {
          schoolYearBoundary: {
            month: new Date().getMonth(),
            day: new Date().getDate() - 1 // don't care if it isn't a real date here
          }
        }
      }));
      assert.equal(await utils.getCurrentSchoolYear(), new Date().getFullYear() + 1);
    });
  });

  describe("mapReplaceAll", () => {
    it("should return the replaced string", () => {
      const testString = " test {dog} martin";
      const map = {
        " test": "{dog}",
        "{dog}": "test",
        martin: "martin test martin"
      };

      const result = utils.mapReplaceAll(testString, map);
      const expectedResult = "{dog} test martin test martin";
      assert.equal(result, expectedResult);
    });
  });

  describe("Password regex tests", () => {
    const createTestObj = (ps, msg) => ({ ps, msg });
    describe("Valid password strings", () => {
      const passStrs = [
        createTestObj("Test.1234", "a leading capital letter"),
        createTestObj("tesT&1234", "a leading lower-case letter"),
        createTestObj("123@Test", "a leading numeric character"),
        createTestObj("$99.tEsT", "a leading special character"),
        createTestObj(" spacE.1st", "a leading space character"),
        createTestObj("Test.4444", "up to four of the same consecutive character")
      ];
      passStrs.forEach(obj => {
        it(`should allow ${obj.msg}`, () => {
          assert.isTrue(utils.passwordRegex.test(obj.ps), `did not allow ${obj.msg}`);
        });
      });
    });
    describe("Invalid password strings", () => {
      const failStrs = [
        createTestObj("2-Short", "less than 8 characters"),
        createTestObj("Te$t55555", "more than four of the same consecutive character"),
        createTestObj("testing.123", "no capital letters"),
        createTestObj("TESTING-246", "no lower-case letters"),
        createTestObj("Test-Abcd", "no numeric characters"),
        createTestObj("TestAbc123", "no special characters")
      ];
      failStrs.forEach(obj => {
        it(`have ${obj.msg}`, () => {
          assert.isTrue(!utils.passwordRegex.test(obj.ps), `did not have ${obj.msg}`);
        });
      });
    });
  });

  describe("CalculateROI tests", () => {
    const date1 = "this is not a date string"; // invalid date
    const date2 = "2017-05-05T07:00:00-05:00"; // valid date
    const date3 = "2017-06-08T07:00:00-05:00"; // valid date after date2
    const date4 = "2017-06-16T02:00:00-05:00"; // valid date after date3

    it("should return null when first date is invalid", () => {
      const dates = [date1, date2, date3];
      const scores = [1, 5, 10];
      assert.isNull(utils.calculateRoI(dates, scores), "ROI did not return null with first date invalid.");
    });
    it("should return null when any date is invalid", () => {
      const dates = [date2, date1, date3];
      const scores = [1, 5, 10];
      assert.isNull(utils.calculateRoI(dates, scores), "ROI did not return null with last date invalid.");
    });
    it("should return null when the number of scores is not the same as the number of dates", () => {
      const dates = [date2, date3];
      const scores = [1, 5, 10];
      assert.isNull(utils.calculateRoI(dates, scores), "ROI did not return null with array size mismatch.");
    });
    it("should return null when there is less than 2 points", () => {
      const dates = [date2];
      const scores = [5];
      assert.isNull(utils.calculateRoI(dates, scores), "ROI did not return null with less than 2 data points.");
    });
    it("should return null when there are two data points within the same day", () => {
      const dates = [date2, date2];
      const scores = [5, 20];
      assert.isNull(utils.calculateRoI(dates, scores), "ROI did not return null for 2 data points within one day.");
    });
    it("should return valid roi when there are two data points in two different days", () => {
      const dates = [date2, date3];
      const scores = [5, 20];
      const roi = utils.calculateRoI(dates, scores);
      assert.isAtLeast(roi, 3.1, "ROI did not return a value for 2 data points on different days.");
    });
    it("should return null if any score is not a number", () => {
      const dates = [date2, date3, date4];
      const scores = [5, 10, "badScoreType"];
      assert.isNull(utils.calculateRoI(dates, scores), "ROI did not return null with a non-numeric score.");
    });
    it("should return a positive number for ROI", () => {
      const dates = [date2, date3, date4];
      const scores = [5, 10, 15];
      const roi = utils.calculateRoI(dates, scores);
      assert.isAtLeast(roi, 1.0, `ROI value was too low: ${roi}`);
    });
    it("should return a value of zero for ROI", () => {
      const dates = [date2, date3, date4];
      const scores = [5, 5, 5];
      const roi = utils.calculateRoI(dates, scores);
      assert.equal(roi, 0.0, `ROI was non-zero: ${roi}`);
    });
    it("should return a negative number for ROI", () => {
      const dates = [date2, date3, date4];
      const scores = [15, 10, 5];
      const roi = utils.calculateRoI(dates, scores);
      assert.isAtMost(roi, -1.0, `ROI value was not negative: ${roi}`);
    });
  });

  describe("CalculateClasswideROI tests", () => {
    // create the basic studentGroup and student structures
    const addClassHistoryEntry = (studentGroup, newDate, newScore, student = null) => {
      const item = {
        type: "classwide",
        whenEnded: {
          date: new Date(newDate)
        },
        assessmentResultMeasures: [
          {
            assessmentResultType: "classwide",
            medianScore: student ? 0 : newScore,
            studentResults: []
          }
        ]
      };
      if (student) {
        const studentObj = {
          studentId: student,
          score: newScore
        };
        item.assessmentResultMeasures[0].studentResults.push(studentObj);
      }
      studentGroup.history.push(item);
    };
    let testGroup;
    beforeEach(() => {
      testGroup = {
        history: []
      };
    });
    const createStudentObj = student => ({ _id: student });

    describe("calculate the Group ROI tests", () => {
      it("should return null when no history entries are present", () => {
        assert.isNull(
          utils.calculateClasswideROI(testGroup),
          "Classwide ROI did not return null with no history entries"
        );
      });
      it("should return null when only one history entry present", () => {
        addClassHistoryEntry(testGroup, "2017-06-15T12:34:56.789+0000", 12);
        assert.isNull(
          utils.calculateClasswideROI(testGroup),
          "Classwide ROI did not return null with one history entry"
        );
      });
      it("should return null when only two history entries are present within 24 hours", () => {
        addClassHistoryEntry(testGroup, "2017-06-15T12:34:56.789+0000", 12);
        addClassHistoryEntry(testGroup, "2017-06-14T18:34:56.789+0000", 2);
        assert.isNull(
          utils.calculateClasswideROI(testGroup),
          "Classwide ROI did not return null with two history entries"
        );
      });
      it("should return null when a history entry has an invalid score", () => {
        addClassHistoryEntry(testGroup, "2017-06-15T12:34:56.789+0000", 12);
        addClassHistoryEntry(testGroup, "2017-06-08T12:34:56.789+0000", "badScoreType");
        addClassHistoryEntry(testGroup, "2017-06-01T12:34:56.789+0000", 2);
        assert.isNull(
          utils.calculateClasswideROI(testGroup),
          "Classwide ROI did not return null with a non-numeric score"
        );
      });

      it("should return an roi of at least 2.0", () => {
        addClassHistoryEntry(testGroup, "2017-06-15T12:34:56.789+0000", 11);
        addClassHistoryEntry(testGroup, "2017-06-08T12:34:56.789+0000", 5);
        addClassHistoryEntry(testGroup, "2017-06-01T12:34:56.789+0000", 1);
        const roi = utils.calculateClasswideROI(testGroup);
        assert.isAtLeast(roi, 2.0, "Classwide ROI should be at least 2.0");
      });
    });

    describe("calculate the Student ROI tests", () => {
      it("should return null when no student history entries are present", () => {
        assert.isNull(
          utils.calculateClasswideROI(testGroup, createStudentObj("test_student")),
          "Classwide ROI did not return null with no student history entries"
        );
      });

      it("should return null when the selected student history is not present", () => {
        addClassHistoryEntry(testGroup, "2017-06-15T12:34:56.789+0000", 13, "test_student");
        addClassHistoryEntry(testGroup, "2017-06-08T12:34:56.789+0000", 7, "test_student");
        addClassHistoryEntry(testGroup, "2017-06-01T12:34:56.789+0000", 3, "test_student");
        const roi = utils.calculateClasswideROI(testGroup, createStudentObj("wrong_student"));
        assert.isNull(roi, `Classwide ROI student should be null for wrong_student, got: ${roi}`);
      });

      it("should return null when the student has an invalid score", () => {
        addClassHistoryEntry(testGroup, "2017-06-15T12:34:56.789+0000", 12, "test_student");
        addClassHistoryEntry(testGroup, "2017-06-08T12:34:56.789+0000", "invalidScore", "test_student");
        addClassHistoryEntry(testGroup, "2017-06-01T12:34:56.789+0000", 2, "test_student");
        const roi = utils.calculateClasswideROI(testGroup, createStudentObj("test_student"));
        assert.isNull(roi, `Classwide ROI student should be null for invalid score: ${roi}`);
      });

      it("should return a student roi of at least 2.0", () => {
        addClassHistoryEntry(testGroup, "2017-06-15T12:34:56.789+0000", 12, "test_student");
        addClassHistoryEntry(testGroup, "2017-06-08T12:34:56.789+0000", 6, "test_student");
        addClassHistoryEntry(testGroup, "2017-06-01T12:34:56.789+0000", 2, "test_student");
        const roi = utils.calculateClasswideROI(testGroup, createStudentObj("test_student"));
        assert.isAtLeast(roi, 2.0, `Classwide ROI student should be at least 2.0, got: ${roi}`);
      });
    });
  });

  describe("uniqUnsafe tests", () => {
    it("should return the unique values in a simple value array (non-complex)", () => {
      const testValues = [1, 2, 2, 3, 1, 4, 6, -1, "a"];
      const expectation = [1, 2, 3, 4, 6, -1, "a"];
      expect(utils.uniqUnsafe(testValues).sort()).deep.equal(expectation.sort());
    });
    const testValues = [
      {
        testKey: "testDuplicateValue"
      },
      {
        testKey: "testUniqValue"
      },
      {
        testKey: "testDuplicateValue"
      },
      {
        testKey: "more stuff"
      }
    ];
    const expectation = [
      {
        testKey: "testDuplicateValue"
      },
      {
        testKey: "testUniqValue"
      },
      {
        testKey: "more stuff"
      }
    ];
    it("should return the objects with unique predicate outputs", () => {
      const testResult = utils.uniqUnsafe(testValues, x => x.testKey);
      assert.equal(testResult.length, expectation.length);
      assert.isTrue(testResult.every(x => expectation.some(y => y.testKey === x.testKey)));
    });
    it("should return the extracted values with unique predicate outputs when extract is true", () => {
      const testResult = utils.uniqUnsafe(testValues, x => x.testKey, true);
      const expectedExtraction = expectation.map(x => x.testKey);
      assert.equal(testResult.length, expectedExtraction.length);
      assert.isTrue(testResult.every(x => expectedExtraction.includes(x)));
    });
    it("should return an empty array when the list is an empty array", () => {
      const testResult = utils.uniqUnsafe([]);
      assert.isTrue(testResult.length === 0);
    });
  });

  describe("getSectionGradeMajority", () => {
    it("should return correct grade 50% grade majority", () => {
      const grades = ["K", "K", "HS", "01", "08", "K", "K"];
      expect(getSectionGradeMajority(grades)).equal("K");
    });
    it("should return lowest grade when no grade is in 50% grade majority", () => {
      const grades = ["02", "02", "HS", "01", "08", "02", "HS", "02"];
      expect(getSectionGradeMajority(grades)).equal("01");
      const gradesAlt = ["02", "02", "HS", "01", "08", "05", "HS", "03"];
      expect(getSectionGradeMajority(gradesAlt)).equal("01");
    });
    it("should return empty string when there is no grade available", () => {
      const grades = [];
      expect(getSectionGradeMajority(grades)).equal("");
    });
    it("should return lowest grade when most grades are undefined", () => {
      const grades = [undefined, undefined, undefined, "01", undefined, "02", "02"];
      expect(getSectionGradeMajority(grades)).equal("01");
    });
  });

  describe("getStudentHighestGrade", () => {
    const gradeByGradeTranslation = {
      "00": "00",
      "01": "01",
      "02": "02",
      "03": "03",
      "04": "04",
      "05": "05",
      "06": "06",
      "07": "07",
      "08": "08",
      "09": "09",
      "1": "01",
      "10": "09",
      "11": "09",
      "12": "09",
      "13": "09",
      "2": "02",
      "25": "05",
      "26": "06",
      "3": "03",
      "4": "04",
      "5": "05",
      "6": "06",
      "7": "07",
      "8": "08",
      "9": "09",
      HS: "09",
      K: "00",
      KG: "00"
    };
    it("should return the highest grade for a student", () => {
      const grades = ["05", "04"];
      expect(getStudentHighestGrade(grades, gradeByGradeTranslation)).equal("05");
    });
    it("should return the highest grade for a student when using custom grade translations", () => {
      const grades = ["25", "26"];
      expect(getStudentHighestGrade(grades, gradeByGradeTranslation)).equal("06");
    });
  });

  describe("getGradeTranslationsByGrade", () => {
    it("should return correct default grade translations by grade", () => {
      const expectedGradeTranslationsByGrade = {
        "1": ["01", "1"],
        "2": ["02", "2"],
        "3": ["03", "3"],
        "4": ["04", "4"],
        "5": ["05", "5"],
        "6": ["06", "6"],
        "7": ["07", "7"],
        "8": ["08", "8"],
        HS: ["09", "9", "10", "11", "12", "13", "HS"],
        K: ["00", "K", "KG"]
      };
      expect(getGradeTranslationsByGrade()).to.eql(expectedGradeTranslationsByGrade);
    });
    it("should return correct grade translations by grade when using custom grade translations", () => {
      const expectedGradeTranslationsByGrade = {
        "1": ["01", "1", "A"],
        "2": ["02", "2"],
        "3": ["03", "3"],
        "4": ["04", "4"],
        "5": ["05", "5"],
        "6": ["06", "6", "25", "26"],
        "7": ["07", "7"],
        "8": ["08", "8"],
        HS: ["09", "9", "10", "11", "12", "13", "HS"],
        K: ["00", "K", "KG"]
      };
      expect(getGradeTranslationsByGrade({ 1: ["A"], 6: ["25", "26"] })).to.eql(expectedGradeTranslationsByGrade);
    });
  });

  describe("getGradeByGradeTranslation", () => {
    it("should return correct default grade translations by grade", () => {
      const expectedGradeByGradeTranslation = {
        "00": "00",
        "01": "01",
        "02": "02",
        "03": "03",
        "04": "04",
        "05": "05",
        "06": "06",
        "07": "07",
        "08": "08",
        "09": "09",
        "1": "01",
        "10": "09",
        "11": "09",
        "12": "09",
        "13": "09",
        "2": "02",
        "3": "03",
        "4": "04",
        "5": "05",
        "6": "06",
        "7": "07",
        "8": "08",
        "9": "09",
        HS: "09",
        K: "00",
        KG: "00"
      };
      expect(getGradeByGradeTranslation()).to.eql(expectedGradeByGradeTranslation);
    });
    it("should return correct grade translations by grade when using custom grade translations", () => {
      const gradeTranslationsByGrade = {
        "1": ["01", "1", "A"],
        "2": ["02", "2"],
        "3": ["03", "3"],
        "4": ["04", "4"],
        "5": ["05", "5"],
        "6": ["06", "6", "25", "26"],
        "7": ["07", "7"],
        "8": ["08", "8"],
        HS: ["09", "9", "10", "11", "12", "13", "HS"],
        K: ["00", "K", "KG"]
      };
      const expectedGradeByGradeTranslation = {
        "00": "00",
        "01": "01",
        "02": "02",
        "03": "03",
        "04": "04",
        "05": "05",
        "06": "06",
        "07": "07",
        "08": "08",
        "09": "09",
        "1": "01",
        "10": "09",
        "11": "09",
        "12": "09",
        "13": "09",
        "2": "02",
        "25": "06",
        "26": "06",
        "3": "03",
        "4": "04",
        "5": "05",
        "6": "06",
        "7": "07",
        "8": "08",
        "9": "09",
        A: "01",
        HS: "09",
        K: "00",
        KG: "00"
      };
      expect(getGradeByGradeTranslation(gradeTranslationsByGrade)).to.eql(expectedGradeByGradeTranslation);
    });
  });

  describe("getGradesFromOneRosterStudents", () => {
    it("should return highest grade for students that have multiple grade levels", () => {
      const students = [{ grades: ["K", "HS"] }, { grades: ["K", "01"] }, { grades: ["02", "01"] }, { grades: "05" }];
      expect(getGradesFromOneRosterStudents(students)).to.eql(["HS", "01", "02", "05"].sort());
    });
    it("should return correct grades for students when custom grade level translations are used", () => {
      const gradeTranslationsByGrade = {
        "1": ["01", "1"],
        "2": ["02", "2"],
        "3": ["03", "3"],
        "4": ["04", "4"],
        "5": ["05", "5", "25"],
        "6": ["06", "6", "26"],
        "7": ["07", "7"],
        "8": ["08", "8"],
        HS: ["09", "9", "10", "11", "12", "13", "HS"],
        K: ["00", "K", "KG"]
      };
      const students = [
        { grades: ["25"] },
        { grades: ["26"] },
        { grades: ["25", "26"] },
        { grades: ["02", "01"] },
        { grades: "05" }
      ];
      expect(getGradesFromOneRosterStudents(students, gradeTranslationsByGrade)).to.eql(
        ["05", "06", "06", "02", "05"].sort()
      );
    });
    it("should return empty list when no students are available", () => {
      const students = [];
      expect(getGradesFromOneRosterStudents(students)).to.eql([]);
    });
  });

  describe("getGradeFromDescriptor", () => {
    it("should return correct grade for student when custom grade level translations are used", () => {
      const descriptorsMap = {
        K: "kindergarten",
        "01": "first grade",
        "02": "second grade",
        "03": "third grade",
        "04": "fourth grade",
        "05": "fifth grade",
        "06": "sixth grade",
        "07": "seventh grade",
        "08": "eighth grade",
        "09": "ninth grade",
        "10": "tenth grade",
        "11": "eleventh grade",
        "12": "twelfth grade",
        "13": "grade 13"
      };

      const gradeByGradeTranslation = {
        "25": "06",
        "26": "06",
        A: "01"
      };

      expect(getGradeFromDescriptor("descriptorURI#25", descriptorsMap, gradeByGradeTranslation)).to.eql("06");
    });
  });

  describe("isDateInPreviousSchoolYear", () => {
    const schoolYearBoundary = {
      month: 6,
      day: 5
    };

    it("should return true if month is larger than school year boundary's month", () => {
      const result = isDateInPreviousSchoolYear({ month: 9, day: 1, schoolYearBoundary });
      expect(result).to.eql(true);
    });

    it("should return false if month is smaller than school year boundary's month", () => {
      const result = isDateInPreviousSchoolYear({ month: 4, day: 1, schoolYearBoundary });
      expect(result).to.eql(false);
    });

    it("should return true if month is the same as school year boundary's month and day is larger than school year boundary's day", () => {
      const result = isDateInPreviousSchoolYear({ month: 6, day: 10, schoolYearBoundary });
      expect(result).to.eql(true);
    });

    it("should return false if month is the same as school year boundary's month and day is smaller than school year boundary's day", () => {
      const result = isDateInPreviousSchoolYear({ month: 6, day: 2, schoolYearBoundary });
      expect(result).to.eql(false);
    });
  });

  describe("isDateRangeInvalid", () => {
    test.each([
      { startDate: { month: 8, day: 1 }, endDate: { month: 8, day: 3 }, expected: true },
      { startDate: { month: 8, day: 1 }, endDate: { month: 8, day: 7 }, expected: false },
      { startDate: { month: 8, day: 30 }, endDate: { month: 9, day: 1 }, expected: true },
      { startDate: { month: 8, day: 30 }, endDate: { month: 9, day: 5 }, expected: false },
      { startDate: { month: 12, day: 30 }, endDate: { month: 1, day: 3 }, expected: true },
      { startDate: { month: 12, day: 30 }, endDate: { month: 1, day: 5 }, expected: false },
      { startDate: { month: 2, day: 29 }, endDate: { month: 3, day: 1 }, expected: true },
      { startDate: { month: 2, day: 29 }, endDate: { month: 3, day: 6 }, expected: false },
      { startDate: { month: 8, day: 1 }, endDate: { month: 3, day: 1 }, expected: false },
      { startDate: { month: 3, day: 1 }, endDate: { month: 8, day: 1 }, expected: false }
    ])(
      "should return $expected when the difference between the startDate: $startDate and the endDate: $endDate is not at least 7 days",
      ({ startDate, endDate, expected }) => {
        const result = isDateRangeInvalid(startDate, endDate);
        expect(result).to.eql(expected);
      }
    );
  });
});
