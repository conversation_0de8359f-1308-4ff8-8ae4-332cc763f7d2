import { archiveStudents, moveStudentsBetweenGroups, updateIsHighSchoolValueForSite } from "./helpers";
import { Sites } from "../sites/sites";
import { StudentGroups } from "../studentGroups/studentGroups";
import { Students } from "../students/students";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";

describe("imports/api/utilities/helpers.js tests", () => {
  const orgid = "test_orgid";
  const schoolYear = 2022;
  const mixedSiteId = "mixedSiteId";
  const highSchoolSiteId = "highSchoolSiteId";
  const studentGroupIdFrom8Grade = "studentGroupIdFrom8Grade";
  const studentGroupIdFromHSGrade = "studentGroupIdFromHSGrade";
  const studentGroupIdFromHSGrade2 = "studentGroupIdFromHSGrade2";
  const studentGroupIdFromHSGrade3 = "studentGroupIdFromHSGrade3";
  const studentIdFrom8Grade = "studentIdFrom8Grade";
  const studentIdFromHSGrade = "studentIdFromHSGrade";
  const studentIdFromHSGrade2 = "studentIdFromHSGrade2";
  const studentIdFromHSGrade3 = "studentIdFromHSGrade3";

  function generateSite(_id, name, isHighSchool = false) {
    return {
      _id,
      orgid,
      schoolYear,
      name,
      isHighSchool
    };
  }

  function generateStudentGroup(_id, siteId, grade, classSectionId, isActive = true) {
    return {
      _id,
      orgid,
      siteId,
      schoolYear,
      grade,
      name: `Class ${grade} (${classSectionId})`,
      classSectionId,
      isActive
    };
  }

  function generateStudent(_id, grade, isActive = true) {
    return {
      _id,
      orgid,
      schoolYear,
      grade,
      isActive
    };
  }

  function generateStudentGroupEnrollment(_id, siteId, studentId, studentGroupId, grade, isActive = true) {
    return {
      _id,
      orgid,
      siteId,
      schoolYear,
      studentId,
      studentGroupId,
      isActive
    };
  }

  function generateStudentGroupsWithStudentsAndEnrollments(studentGroupsWithStudentsAndEnrollments = []) {
    const studentGroups = [];
    const students = [];
    const studentGroupEnrollments = [];

    studentGroupsWithStudentsAndEnrollments.forEach(({ _id, siteId, grade, classSectionId, isActive, studentIds }) => {
      studentGroups.push(generateStudentGroup(_id, siteId, grade, classSectionId, isActive));
      studentIds.forEach(studentId => {
        students.push(generateStudent(studentId, grade));
        studentGroupEnrollments.push(
          generateStudentGroupEnrollment(`${studentId}_enrollmentId`, siteId, studentId, _id, grade)
        );
      });
    });

    return {
      studentGroups,
      students,
      studentGroupEnrollments
    };
  }

  describe("updateIsHighSchoolValueForSite", () => {
    const sites = [generateSite(mixedSiteId, "Mixed School"), generateSite(highSchoolSiteId, "High School", true)];

    const { studentGroups, students, studentGroupEnrollments } = generateStudentGroupsWithStudentsAndEnrollments([
      {
        _id: studentGroupIdFrom8Grade,
        siteId: mixedSiteId,
        grade: "08",
        classSectionId: "8.1",
        studentIds: [studentIdFrom8Grade]
      },
      {
        _id: studentGroupIdFromHSGrade,
        siteId: mixedSiteId,
        grade: "HS",
        classSectionId: "HS.1",
        studentIds: [studentIdFromHSGrade]
      },
      {
        _id: studentGroupIdFromHSGrade2,
        siteId: highSchoolSiteId,
        grade: "HS",
        classSectionId: "HS.2",
        studentIds: [studentIdFromHSGrade2]
      },
      {
        _id: studentGroupIdFromHSGrade3,
        siteId: highSchoolSiteId,
        grade: "HS",
        classSectionId: "HS.3",
        studentIds: [studentIdFromHSGrade3]
      }
    ]);

    beforeEach(async () => {
      await Sites.insertAsync(sites);
      await StudentGroups.insertAsync(studentGroups);
      await Students.insertAsync(students);
      await StudentGroupEnrollments.insertAsync(studentGroupEnrollments);
    });

    afterEach(async () => {
      await Sites.removeAsync({});
      await StudentGroups.removeAsync({});
      await Students.removeAsync({});
      await StudentGroupEnrollments.removeAsync({});
    });

    describe("if there is a mixed school", () => {
      it("should set isHighSchool value to true if there are only classes with HS grades", async () => {
        const site = await Sites.findOneAsync({ _id: mixedSiteId });
        expect(site.isHighSchool).toBe(false);

        await StudentGroups.updateAsync({ _id: studentGroupIdFrom8Grade }, { $set: { grade: "HS" } });

        await updateIsHighSchoolValueForSite(mixedSiteId);

        const updatedSite = await Sites.findOneAsync({ _id: mixedSiteId });
        expect(updatedSite.isHighSchool).toBe(true);
      });
      it("should set isHighSchool value to true after archiving students from the last non-HS grade class", async () => {
        const site = await Sites.findOneAsync({ _id: mixedSiteId });
        const studentGroup = await StudentGroups.findOneAsync({ _id: studentGroupIdFrom8Grade });
        expect(site.isHighSchool).toBe(false);

        await archiveStudents({ studentIds: [studentIdFrom8Grade], studentGroup });

        const updatedSite = await Sites.findOneAsync({ _id: mixedSiteId });
        expect(updatedSite.isHighSchool).toBe(true);
      });
      it("should set isHighSchool value to true after moving all students from the last non-HS grade class", async () => {
        const site = await Sites.findOneAsync({ _id: mixedSiteId });
        const previousGroup = await StudentGroups.findOneAsync({ _id: studentGroupIdFrom8Grade });
        const nextGroup = await StudentGroups.findOneAsync({ _id: studentGroupIdFromHSGrade });
        expect(site.isHighSchool).toBe(false);

        await moveStudentsBetweenGroups({ students: [students[0]], previousGroup, nextGroup });

        const updatedSite = await Sites.findOneAsync({ _id: mixedSiteId });
        expect(updatedSite.isHighSchool).toBe(true);
      });
      it("should keep the isHighSchool value as false if there are classes with not only HS grades", async () => {
        const site = await Sites.findOneAsync({ _id: mixedSiteId });
        expect(site.isHighSchool).toBe(false);

        await StudentGroups.updateAsync({ _id: studentGroupIdFrom8Grade }, { $set: { grade: "07" } });

        await updateIsHighSchoolValueForSite(mixedSiteId);

        const updatedSite = await Sites.findOneAsync({ _id: mixedSiteId });
        expect(updatedSite.isHighSchool).toBe(false);
      });
      it("should keep the isHighSchool value as false if there aren't any classes with HS grades", async () => {
        const site = await Sites.findOneAsync({ _id: mixedSiteId });
        expect(site.isHighSchool).toBe(false);

        await StudentGroups.updateAsync({ _id: studentGroupIdFromHSGrade }, { $set: { grade: "08" } });

        await updateIsHighSchoolValueForSite(mixedSiteId);

        const updatedSite = await Sites.findOneAsync({ _id: mixedSiteId });
        expect(updatedSite.isHighSchool).toBe(false);
      });
    });
    describe("if there is a high school", () => {
      it("should set isHighSchool value to false if there are classes with not only HS grades", async () => {
        const site = await Sites.findOneAsync({ _id: highSchoolSiteId });
        expect(site.isHighSchool).toBe(true);

        await StudentGroups.updateAsync({ _id: studentGroupIdFromHSGrade2 }, { $set: { grade: "08" } });

        await updateIsHighSchoolValueForSite(highSchoolSiteId);

        const updatedSite = await Sites.findOneAsync({ _id: highSchoolSiteId });
        expect(updatedSite.isHighSchool).toBe(false);
      });
      it("should keep the isHighSchool value as true if there are only classes with HS grades", async () => {
        const site = await Sites.findOneAsync({ _id: highSchoolSiteId });
        expect(site.isHighSchool).toBe(true);

        await StudentGroups.updateAsync({ _id: studentGroupIdFromHSGrade3 }, { $set: { grade: "HS" } });

        await updateIsHighSchoolValueForSite(highSchoolSiteId);

        const updatedSite = await Sites.findOneAsync({ _id: highSchoolSiteId });
        expect(updatedSite.isHighSchool).toBe(true);
      });
      it("should set isHighSchool value to false if there is a new non-HS grade class", async () => {
        const site = await Sites.findOneAsync({ _id: highSchoolSiteId });
        expect(site.isHighSchool).toBe(true);

        await StudentGroups.insertAsync({
          _id: "studentGroupIdFrom8Grade2",
          orgid,
          siteId: highSchoolSiteId,
          schoolYear,
          grade: "08",
          name: "Class 8",
          classSectionID: "888",
          isActive: true
        });

        await updateIsHighSchoolValueForSite(highSchoolSiteId);

        const updatedSite = await Sites.findOneAsync({ _id: highSchoolSiteId });
        expect(updatedSite.isHighSchool).toBe(false);
      });
    });
  });
});
