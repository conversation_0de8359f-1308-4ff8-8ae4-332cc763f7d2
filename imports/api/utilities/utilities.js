import util from "util";
import { check, Match } from "meteor/check";
import moment from "moment";
import { Meteor } from "meteor/meteor";
import { extend, find, get, groupBy, includes, invert, isNumber, max, min, range as lodashRange, round } from "lodash";
import CryptoJS from "crypto-js";
import { useEffect } from "react";
import { Organizations } from "../organizations/organizations";
import { Settings } from "../settings/settings";
import { StudentGroups } from "../studentGroups/studentGroups";
import getUnitedStatesNames from "../helpers/getUnitedStatesNames";
import { getCurrentDate } from "../helpers/getCurrentDate";
import { normalizeGrade } from "./sortingHelpers/normalizeSortItem";

const passphrase = Meteor.settings.public.PASSPHRASE || "";

export function encrypt(text) {
  if (!text) {
    return "";
  }
  return CryptoJS.AES.encrypt(text, passphrase).toString();
}

export function decrypt(text = "") {
  const bytes = CryptoJS.AES.decrypt(text, passphrase);
  return bytes.toString(CryptoJS.enc.Utf8);
}

export function decryptRosteringSettings(rosteringSettings) {
  check(rosteringSettings.apiUrl, String);
  check(rosteringSettings.authUrl, Match.Maybe(String));
  check(rosteringSettings.clientId, String);
  check(rosteringSettings.clientSecret, String);
  const {
    apiUrl: encApiUrl,
    authUrl: encAuthUrl,
    clientId: encClientId,
    clientSecret: encClientSecret
  } = rosteringSettings;
  const apiUrl = decrypt(encApiUrl);
  const authUrl = decrypt(encAuthUrl);
  const clientId = decrypt(encClientId);
  const clientSecret = decrypt(encClientSecret);
  return { apiUrl, authUrl, clientId, clientSecret };
}

export function rosteringSettingsReplacer(rosteringSettings, replacementStrings = ["<old>", "<new>"], previousValue) {
  const {
    apiUrl: encApiUrl,
    authUrl: encAuthUrl,
    clientId: encClientId,
    clientSecret: encClientSecret
  } = rosteringSettings;
  const apiUrl = encApiUrl && decrypt(encApiUrl);
  const authUrl = encAuthUrl && decrypt(encAuthUrl);
  const clientId = encClientId && decrypt(encClientId);
  const isInitialClientSecretSetup = !previousValue && previousValue !== encClientSecret;
  const hasClientSecretChanged =
    encClientSecret && previousValue && decrypt(previousValue) !== decrypt(encClientSecret);
  const clientSecret =
    isInitialClientSecretSetup || hasClientSecretChanged ? replacementStrings[1] : replacementStrings[0];

  return { ...rosteringSettings, apiUrl, authUrl, clientId, clientSecret };
}

export async function getOrganizationIdsForState(state = "") {
  const unitedStatesNames = getUnitedStatesNames(); // { "NY": "New York", ... }
  const areInternationalOrganizations = state === "international";
  const organizationsQuery = {};
  if (areInternationalOrganizations) {
    const allUnitedStatesNames = [...Object.keys(unitedStatesNames), ...Object.values(unitedStatesNames)];
    organizationsQuery["details.state"] = { $nin: allUnitedStatesNames };
  } else if (state) {
    const stateNames = [state, unitedStatesNames[state]]; // e.g. ["NY", "New York"]
    organizationsQuery["details.state"] = { $in: stateNames };
  }
  return (await Organizations.find(organizationsQuery, { fields: { _id: 1, "details.state": 1 } }).fetchAsync()).map(
    org => org._id
  );
}

export async function getOrganizationIdsWithStates(organizationIds = []) {
  const unitedStatesNames = getUnitedStatesNames(); // { "NY": "New York", ... }
  const invertedUnitedStatesNames = invert(unitedStatesNames); // {"New York": "NY", ... }
  return (
    await Organizations.find({ _id: { $in: organizationIds } }, { fields: { _id: 1, "details.state": 1 } }).fetchAsync()
  ).map(org => ({ _id: org._id, state: invertedUnitedStatesNames[org.details.state] || org.details.state }));
}

export async function getCurrentSchoolYear(user, orgid) {
  if (!user?.profile?.customDate?.length && user?.profile?.selectedSchoolYear) {
    return user.profile.selectedSchoolYear;
  }

  return getLatestAvailableSchoolYear(user, orgid);
}

export async function getLatestAvailableSchoolYear(user, orgid) {
  const settings = await Settings.findOneAsync();
  let schoolYearBoundary = (settings && settings.defaults && settings.defaults.schoolYearBoundary) || {
    month: 6,
    day: 31
  };
  if (orgid || user?.profile?.orgid) {
    const org = await Organizations.findOneAsync({ _id: orgid || user.profile.orgid });
    schoolYearBoundary = (org && org.schoolYearBoundary) || schoolYearBoundary;
  }
  const customDate = get(user, "profile.customDate");
  const currentDate = await getCurrentDate(customDate, orgid || get(user, "profile.orgid"));
  const centralTimeOffset = -(currentDate.getTimezoneOffset() / 60);
  const nowCentral = new Date(
    new Date(currentDate.getTime() + centralTimeOffset * 3600 * 1000).toUTCString().replace(/ GMT$/, "")
  );
  if (nowCentral.getMonth() < schoolYearBoundary.month) {
    return nowCentral.getFullYear();
  }
  if (nowCentral.getMonth() > schoolYearBoundary.month) {
    return nowCentral.getFullYear() + 1;
  }
  return nowCentral.getDate() <= schoolYearBoundary.day ? nowCentral.getFullYear() : nowCentral.getFullYear() + 1;
}

export async function getInActiveSchoolYear(schoolYear, user, orgid) {
  if (user?.profile?.customDate?.length) {
    return true;
  }

  const latest = await getLatestAvailableSchoolYear(user, orgid);
  return !schoolYear || schoolYear === latest;
}

export async function getAllAvailableSchoolYears(startYear = 2017) {
  return lodashRange(startYear, (await getLatestAvailableSchoolYear()) + 1);
}

// str = string to operate on
// map = [{key, value}]
export function mapReplaceAll(str, map) {
  const re = new RegExp(Object.keys(map).join("|"), "gi");
  return str.replace(re, matched => map[matched]);
}

export function getMedianNumber(values, roundResult) {
  // NOTE: 'roundResult' is optional and should be 'roundDown' or 'roundUp' if used.
  check(values, Array);
  values.sort((a, b) => a - b);

  let medianValue = 0;
  const half = Math.floor(values.length / 2);

  if (values.length % 2) {
    medianValue = values[half];
  } else {
    switch (roundResult) {
      case "roundDown":
        medianValue = Math.floor((values[half - 1] + values[half]) / 2.0);
        break;
      case "roundUp":
        medianValue = Math.ceil((values[half - 1] + values[half]) / 2.0);
        break;
      default:
        medianValue = (values[half - 1] + values[half]) / 2.0;
        break;
    }
  }

  return medianValue;
}

export function getFormattedSchoolYear(schoolYear) {
  return `${(schoolYear - 1).toString()}-${schoolYear.toString().slice(2)}`;
}

export function getResultsMessageFromCode(code, studentName, season) {
  const firstName = studentName && studentName.firstName;
  const messages = {
    // Classwide Messages
    1: "Your class has been scheduled for a classwide intervention.",
    2: {
      text:
        "It appears your class needs to better learn this skill. We recommend providing an acquisition intervention this week.",
      type: "warning"
    },
    3: "Your class is doing well but still needs more practice with this intervention skill.",
    4: "Great work, your class is ready to start working on a new intervention skill!",
    5: "Congratulations, your class has completed all classwide interventions!",
    6: "",
    7: "Your class has completed screening. We recommend that you continue classwide interventions.",
    // Individual Messages
    51: `We need to administer a quick assessment before starting interventions with ${firstName}.`,
    52: `${firstName} needs practice acquiring this intervention skill. Keep up the hard work!`,
    53: `${firstName} is doing well but still needs more practice with this intervention skill.`,
    54: `Great work, ${firstName} is ready to start working on a new intervention skill!`,
    55: `Great work, ${firstName} just passed a goal skill and is ready to start on a new one!`,
    56: `Congratulations, ${firstName} has completed all goal skills for ${season}!`,
    57: `We need to administer a quick assessment to determine the best intervention for ${firstName}.`,
    58: `Individual interventions have been started for ${firstName}`,
    59: `We have found an appropriate intervention for ${firstName} to practice.`,
    60: `${firstName} is doing great but needs more practice on this goal skill.`,
    61: `We need to administer another quick assessment to determine the best intervention for ${firstName}.`,
    100: ""
  };
  if (!messages[code]) {
    return "";
  }
  return messages[code];
}

/** ****************************************************************
 * Translate Benchmark Period
 ***************************************************************** */
export function translateBenchmarkPeriod(benchmarkPeriodIdNameOrLabel) {
  const transBM = {};

  switch (benchmarkPeriodIdNameOrLabel) {
    case "8S52Gz5o85hRkECgq":
      transBM.title = "Fall";
      transBM.name = "fall";
      transBM.label = "fall-period";
      transBM.color = "#f9bf68";
      break;
    case "nEsbWokBWutTZFkTh":
      transBM.title = "Winter";
      transBM.name = "winter";
      transBM.label = "winter-period";
      transBM.color = "#70a1d9";
      break;
    case "cjCMnZKARBJmG8suT":
      transBM.title = "Spring";
      transBM.name = "spring";
      transBM.label = "spring-period";
      transBM.color = "#aee57c";
      break;
    case "allPeriods":
      transBM.title = "All";
      transBM.name = "all";
      transBM.label = "all-periods";
      transBM.color = "#ff7079";
      break;
    case "FALL":
    case "Fall":
    case "fall":
      transBM.id = "8S52Gz5o85hRkECgq";
      transBM.label = "fall-period";
      transBM.color = "#f9bf68";
      break;
    case "WINTER":
    case "Winter":
    case "winter":
      transBM.id = "nEsbWokBWutTZFkTh";
      transBM.label = "winter-period";
      transBM.color = "#70a1d9";
      break;
    case "SPRING":
    case "Spring":
    case "spring":
      transBM.id = "cjCMnZKARBJmG8suT";
      transBM.label = "spring-period";
      transBM.color = "#aee57c";
      break;
    case "ALL":
    case "All":
    case "all":
      transBM.id = "allPeriods";
      transBM.label = "all-periods";
      transBM.color = "#ff7079";
      break;
    case "fall-period":
      transBM.id = "8S52Gz5o85hRkECgq";
      transBM.title = "Fall";
      transBM.name = "fall";
      transBM.color = "#f9bf68";
      break;
    case "winter-period":
      transBM.id = "nEsbWokBWutTZFkTh";
      transBM.title = "Winter";
      transBM.name = "winter";
      transBM.color = "#70a1d9";
      break;
    case "spring-period":
      transBM.id = "cjCMnZKARBJmG8suT";
      transBM.title = "Spring";
      transBM.name = "spring";
      transBM.color = "#aee57c";
      break;
    case "all-periods":
      transBM.id = "allPeriods";
      transBM.title = "All";
      transBM.name = "all";
      transBM.color = "#ff7079";
      break;
    default:
      break;
  }
  return transBM;
}

export function getGender(genderCode) {
  if (genderCode === "F") {
    return "Female";
  }
  if (genderCode === "M") {
    return "Male";
  }
  return "";
}

/** ****************************************************************
 * Capitalize First Letter
 ***************************************************************** */
export function capitalizeFirstLetter(string = "") {
  if (!string.length) {
    return "";
  }
  return string.charAt(0).toUpperCase() + string.slice(1);
}

function displayNinjalog(opts) {
  // example call:  ninjalog.log({msg: 'Example Message', val: this.props, context: 'dashboard', jsonString: true})
  // [msg] - the message to be displayed
  // [val] - the value to be logged
  // [context] - the functional piece of the app that is being logged
  // [jsonString] - set to true if you would like to print a json object on the server side
  // [jsonDepth] - set to number to indicate the depth you would like jsonString to go to
  const logOpts = extend(
    {
      msg: "",
      val: "",
      context: "",
      jsonString: false,
      jsonDepth: 2
    },
    opts
  );
  console.log("=========================================================");
  console.log("   ");
  if (logOpts.jsonString) {
    console.log(
      `${logOpts.logLevel} (${logOpts.context}) ${logOpts.msg.toString()} = ${util.inspect(logOpts.val, {
        depth: logOpts.jsonDepth
      })}`
    );
  } else {
    console.log(`${logOpts.logLevel} (${logOpts.context}) ${logOpts.msg.toString()} = `, logOpts.val);
  }
  console.log("   ");
  console.log("=========================================================");
}

const logSettings = Meteor && Meteor.settings && Meteor.settings.public && Meteor.settings.public.LOG_SETTINGS;
const logLevelSettings = (logSettings && logSettings.LOG_LEVEL) || "ERROR";
const context = (logSettings && logSettings.CONTEXT) || [];

export const ninjalog = {
  trace(opts) {
    if (includes(["TRACE"], logLevelSettings) && (context.length === 0 || includes(context, opts.context))) {
      displayNinjalog(extend({}, opts, { logLevel: "TRACE" }));
    }
  },
  log(opts) {
    if (includes(["LOG", "TRACE"], logLevelSettings) && (context.length === 0 || includes(context, opts.context))) {
      displayNinjalog(extend({}, opts, { logLevel: "LOG" }));
    }
  },
  warning(opts) {
    if (
      includes(["WARNING", "LOG", "TRACE"], logLevelSettings) &&
      (context.length === 0 || includes(context, opts.context))
    ) {
      displayNinjalog(extend({}, opts, { logLevel: "WARNING" }));
    }
  },
  error(opts) {
    if (
      includes(["WARNING", "ERROR", "LOG", "TRACE"], logLevelSettings) &&
      (context.length === 0 || includes(context, opts.context))
    ) {
      displayNinjalog(extend({}, opts, { logLevel: "ERROR" }));
    }
  }
};

// flags because they might be in many situations at once
// and we need to avoid conditional-hell
export const situations = {
  screening: 1, // currently active screening
  classwide: 2, // currently active classwide intervention
  individual: 4, // currently active individual intervention
  needsScreening: 8 // currently needs a screening
};

export const dashboardNavs = {
  classwide: "classwide",
  individual: "individual",
  students: "students",
  screening: "screening",
  growth: "growth",
  interventionWithoutScreening: "interventionWithoutScreening"
};

export const studentsInMultipleClassesInfo =
  "Students are only allowed to be rostered into one class in Spring Math. To resolve the errors related to students in multiple classes, remove one of the classes from the Filters tab by moving a class from the right side to the left side of the page";

export const passwordRegex = /(?!.*(.)\1{4})(?=.*[a-z])(?=.*[A-Z])(?=.*[\d])(?=.*[^a-zA-Z\d]).{8,}$/;
export const passwordSpecMsg =
  "The new password must meet the following requirements:" +
  "<ul>" +
  "<li>It must be at least 8 characters in length.</li>" +
  "<li>It must contain at least 1 lower-case alpha character. (a...z)</li>" +
  "<li>It must contain at least 1 upper-case alpha character. (A...Z)</li>" +
  "<li>It must contain at least 1 numeric digit. (0...9)</li>" +
  "<li>It must contain at least 1 special character or space. (~,!,@,#,$...)</li>" +
  "<li>It cannot contain more than 4 of the same character in a row.</li>" +
  "</ul>";

export function calculateClasswideROI(studentGroup, student = null, assessmentId = null) {
  // Gathers scores and dates for classwide ROI for either entire group or one student.
  const classHistory = studentGroup.history || [];
  const studentId = student ? student._id : null;
  const dates = [];
  const scores = [];
  classHistory.forEach(h => {
    const currentAssessmentId = assessmentId || get(studentGroup, "currentClasswideSkill.assessmentId", h.assessmentId);
    if (h.type === "classwide" && h.assessmentId === currentAssessmentId) {
      const whenEndedDate = h.whenEnded.date;
      let score = -1;
      h.assessmentResultMeasures.forEach(assRM => {
        if (assRM.assessmentResultType === "classwide") {
          if (student) {
            const studentObj = assRM.studentResults.find(stu => stu.studentId === studentId);
            // eslint-disable-next-line no-restricted-globals
            score = studentObj && !isNaN(parseInt(studentObj.score)) ? parseInt(studentObj.score) : null;
          } else {
            score = assRM.medianScore;
          }
        }
      });
      scores.push(score);
      dates.push(whenEndedDate);
    }
  });
  scores.reverse();
  dates.reverse();

  return calculateRoI(dates, scores);
}

export function calculateRoI(arrDates, arrScores) {
  // This calculation uses the 'Ordinary Least Squares Regression' formula for the ROI
  if (arrDates.length !== arrScores.length) {
    // should never happen with valid data
    return null;
  }
  const numItems = arrDates.length;

  if (numItems < 2) {
    // don't calculate with 2 or fewer data points
    return null;
  }

  if (numItems === 2) {
    // if there are only two data points calculate ROI only if the scores were taken on different days
    const firstDate = moment(arrDates[0]);
    const minHourDifferenceToDisplayRoiForTwoDataPoints = 24;
    const hourDifferenceBetweenTwoDates = Math.abs(firstDate.diff(moment(arrDates[1]), "hours"));
    if (hourDifferenceBetweenTwoDates < minHourDifferenceToDisplayRoiForTwoDataPoints) {
      return null;
    }
  }

  const expectedFormats = ["ddd MMM DD YYYY HH:mm:ss", "YYYY-MM-DDTHH:mm:ssZ"];
  const allValidDates = arrDates.every(d => moment(d, expectedFormats).isValid());
  if (!allValidDates) {
    // abort if any of the dates are invalid
    return null;
  }
  const allValidScores = arrScores.every(s => typeof s === "number");
  if (!allValidScores) {
    // abort if any of the scores are non-numeric
    return null;
  }
  const firstDateMoment = moment(arrDates[0], expectedFormats);
  const msPerWeek = 1000 * 60 * 60 * 24 * 7;
  const dateOffset = firstDateMoment.valueOf();
  const unixDateOffsets = arrDates.map(dt => (moment(dt, expectedFormats).valueOf() - dateOffset) / msPerWeek);

  const scoresSum = arrScores.reduce((acc, val) => acc + parseInt(val), 0);
  const datesSum = unixDateOffsets.reduce((acc, val) => acc + val, 0);
  const datesSumSquare = datesSum * datesSum;
  const datesSquareSum = unixDateOffsets.reduce((acc, val) => acc + val * val, 0);

  const datesScoreSumProduct = unixDateOffsets.reduce((acc, val, index) => acc + val * parseInt(arrScores[index]), 0);

  const numerator = datesScoreSumProduct - (scoresSum * datesSum) / numItems;
  const denominator = datesSquareSum - datesSumSquare / numItems;
  const olsRoi = numerator / denominator;

  return olsRoi.toFixed(1);
}

export function uniqUnsafe(list, func, doExtract) {
  if (func) {
    const uniqueMap = list.reduceRight((a, c) => ({ ...a, [func(c)]: c }), {});
    return Object.keys(uniqueMap).map(k => (doExtract ? func(uniqueMap[k]) : uniqueMap[k]));
  }
  const uniqList = list.reduceRight((a, c) => ({ ...a, [c]: c }), {});
  return Object.keys(uniqList).map(k => uniqList[k]);
}

export function range(start, end) {
  let endToUse = end;
  let startToUse = start;
  if (!endToUse) {
    endToUse = startToUse;
    startToUse = 0;
  }
  const count = endToUse - startToUse;
  return Array.from({ length: count }, (v, k) => k + startToUse);
}

export function validateSchemaProperty(regex, errorName) {
  return function() {
    const reg = new RegExp(regex);
    if (!reg.test(this.value)) {
      return errorName;
    }
    return undefined;
  };
}

const idRegex = new RegExp(/^([a-zA-Z0-9]+)$/);
const userIdRegex = new RegExp(/(^.+$)/);
export const userIdInvalidErrorMessage = "You must provide a valid Teacher/Local Id";

export function trimValuesInObject(object) {
  return Object.entries(object).reduce((a, [key, value]) => {
    // eslint-disable-next-line no-param-reassign
    a[key] = value.trim();
    return a;
  }, {});
}

export const idValidation = {
  regex: idRegex,
  regexWithEmptyString: new RegExp(/(^([a-zA-Z0-9]*)$)/),
  description: "should only contain letters and digits"
};

export function isIdValid(id, isUserId = false) {
  return (isUserId ? userIdRegex : idRegex).test(id);
}

export function isSML(orgid) {
  if (Meteor.isServer) {
    throw new Error("Use isSMLAsync() on the server side");
  }
  const org = Organizations.findOne({ _id: orgid });
  return org && org.isSelfEnrollee;
}

export async function isSMLAsync(orgid) {
  const org = await Organizations.findOneAsync({ _id: orgid });
  return org && org.isSelfEnrollee;
}

export async function fetchRosteringType(orgid) {
  const org = await Organizations.findOneAsync({ _id: orgid }, { fields: { rostering: 1 } });
  return get(org, "rostering");
}

export function isEmailValid(email = "") {
  return /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(email.trim());
}

export function makeCancellableMeteorCall(...args) {
  if (!Meteor.isClient) throw new Error("Use synchronous style in the server, this is client only");
  if (!args || args.length < 2) throw new Error("Must at least provide name and callback");

  const cb = args.pop();

  if (typeof args[0] !== "string") throw new Error("First argument must be method name");
  if (typeof cb !== "function") throw new Error("Callback must be a function");

  let isCancelled = false;

  Meteor.call(...args, (err, res) => {
    if (!isCancelled) cb(err, res);
  });

  return function() {
    isCancelled = true;
  };
}

export async function isUserAssignedToAnyGroup({ siteId, schoolYear, userId }) {
  return (
    (await StudentGroups.find(
      {
        siteId,
        schoolYear,
        $or: [{ ownerIds: userId }, { secondaryTeachers: userId }]
      },
      {
        fields: { _id: 1 }
      }
    ).countAsync()) > 0
  );
}

export const findTwoMostRecentEntriesInSameIntervention = ({ group, type }) => {
  const historyItems = group.history.filter(result => {
    if (type === "individual") {
      return result.type === type && result.interventions?.length > 0;
    }
    return result.type === type;
  });

  let latestHistoryEntry;
  let previousHistoryEntry;

  if (type === "individual") {
    [latestHistoryEntry, previousHistoryEntry] = historyItems;
  } else {
    const classwideInterventionResultsByAssessment = groupBy(historyItems, "assessmentId");
    [latestHistoryEntry, previousHistoryEntry] =
      find(classwideInterventionResultsByAssessment, interventionResults => interventionResults.length >= 2) || [];
  }

  return { latestHistoryEntry, previousHistoryEntry };
};

export const getScoringTrend = ({ type, group, numStudents, studentGroupEnrollments }) => {
  if (!group.history || group.history.length < 2) {
    return undefined;
  }

  const { latestHistoryEntry, previousHistoryEntry } = findTwoMostRecentEntriesInSameIntervention({ group, type });
  if (
    latestHistoryEntry &&
    previousHistoryEntry &&
    latestHistoryEntry.assessmentId === previousHistoryEntry.assessmentId
  ) {
    let lastHistoryStudentResults;
    let previousHistoryStudentResults;
    if (type === "individual") {
      const lastAssessmentId = latestHistoryEntry.assessmentId;
      const previousAssessmentId = previousHistoryEntry.assessmentId;
      lastHistoryStudentResults =
        latestHistoryEntry.assessmentResultMeasures.find(arm => arm.assessmentId === lastAssessmentId)
          ?.studentResults || [];
      previousHistoryStudentResults =
        previousHistoryEntry.assessmentResultMeasures.find(arm => arm.assessmentId === previousAssessmentId)
          ?.studentResults || [];
    } else {
      lastHistoryStudentResults = latestHistoryEntry.assessmentResultMeasures[0].studentResults;
      previousHistoryStudentResults = previousHistoryEntry.assessmentResultMeasures[0].studentResults;
    }
    const proficiencyThreshold = latestHistoryEntry?.targets?.[1] * 1.2 || null;
    const improvingStudents = lastHistoryStudentResults.filter(sr => {
      if (
        studentGroupEnrollments &&
        !studentGroupEnrollments.filter(e => e.studentGroupId === group._id).some(e => e.studentId === sr.studentId)
      ) {
        return false;
      }
      const previousResult = previousHistoryStudentResults.find(result => result.studentId === sr.studentId);
      if (!previousResult) {
        return false;
      }
      const lastScore = parseInt(sr.score);
      const prevScore = parseInt(previousResult.score);
      // SPRIN-2446 - Include student that has same/better/worse score in proficiency range
      if (proficiencyThreshold && lastScore >= proficiencyThreshold && prevScore >= proficiencyThreshold) {
        return true;
      }
      return lastScore > prevScore;
    });
    return ((improvingStudents.length / numStudents) * 100).toFixed(0);
  }

  return undefined;
};

function addHtmlScript(src, elementId) {
  if (!document.getElementById(elementId)) {
    const scriptElement = document.createElement("script");
    scriptElement.src = src;
    scriptElement.id = elementId;
    document.body.appendChild(scriptElement);
  }
}

export function removeHtmlScript(elementId) {
  const elementToRemove = document.getElementById(elementId);
  if (elementToRemove) {
    elementToRemove.remove();
  }
}

export function renderZendeskWidget(organizationName = "", siteName = "") {
  window.zESettings = {
    webWidget: {
      contactOptions: {
        enabled: true
      },
      launcher: {
        label: {
          "*": "Reach out"
        }
      },
      position: {
        horizontal: "left"
      },
      chat: {
        prechatForm: {
          greeting: {
            "*": "Thank you for chatting with SpringMath! Please let us know how we can help."
          }
        },
        departments: {
          enabled: ["SpringMath"],
          select: "SpringMath"
        },
        title: {
          "*": "Chat with SpringMath"
        }
      },
      helpCenter: {
        title: {
          "*": "SpringMath Support"
        }
      },
      contactForm: {
        ticketForms: [{ id: 568628 }],
        fields: [
          { id: 360040407654, prefill: { "*": organizationName } },
          { id: 44424648, prefill: { "*": siteName } }
        ]
      },
      authenticate: {
        chat: {
          jwtFn(callback) {
            Meteor.call("zendesk:getJWTTokenForWidget", (err, resp) => {
              if (!err) {
                callback(resp);
              }
            });
          }
        }
      }
    }
  };
  const scriptSrc = "https://static.zdassets.com/ekr/snippet.js?key=466cac4f-38e1-4135-a6fb-79da2516a800";
  addHtmlScript(scriptSrc, "ze-snippet");
}

export function displayStudentData(data, messagePrefix = "Please see student with:") {
  const {
    studentLastName,
    studentFirstName,
    className,
    classSectionID,
    teacherLastName,
    teacherFirstName,
    studentLocalID,
    studentStateID
  } = Object.entries(data).reduce((a, [key, value]) => {
    const temp = a;
    temp[key] = value || "N/A";
    return temp;
  }, {});
  return `${messagePrefix}\n\tFirst Name: ${studentFirstName}\n\tLast Name: ${studentLastName}\n\tLocalID: ${studentLocalID}\n\tStateID: ${studentStateID}\n\tClass: ${className} (${classSectionID})\n\tTeacher: ${teacherLastName}, ${teacherFirstName}`;
}

export function getLastDayOfMonth(date) {
  const dateString = new Date(date);
  return new Date(dateString.getFullYear(), dateString.getMonth() + 1, 0).getDate();
}

export function getMeteorUserId(customUserId = "") {
  if (customUserId) {
    return customUserId;
  }
  let userId;
  try {
    userId = Meteor.userId();
  } catch {
    userId = "";
  }
  return userId;
}

export async function getMeteorUser() {
  let user;
  try {
    user = await Meteor.userAsync();
  } catch {
    user = null;
  }
  return user;
}

export function getMeteorUserSync() {
  let user;
  try {
    user = Meteor.user();
  } catch {
    user = null;
  }
  return user;
}

// TODO(fmazur) - rewrite props
export async function getOrgId(props, user) {
  if (!user) {
    // eslint-disable-next-line no-param-reassign
    user = await getMeteorUser();
  }
  return (
    props.orgid || get(props, "match.params.orgid", "") || localStorage.getItem("orgid") || get(user, "profile.orgid")
  );
}

export function getOrgIdSync(props, user) {
  if (!user) {
    // eslint-disable-next-line no-param-reassign
    user = getMeteorUserSync();
  }
  return (
    props.orgid || get(props, "match.params.orgid", "") || localStorage.getItem("orgid") || get(user, "profile.orgid")
  );
}

export function isUserLoggedOut(publicationContext) {
  if (!publicationContext.userId) {
    ninjalog.log({
      // eslint-disable-next-line no-underscore-dangle
      msg: `Publication Access Denied: ${publicationContext._name} - user is not logged in`
    });
    return true;
  }
  return false;
}

export const DEFAULT_USER_IDENTIFIERS = [{ role: "teacher", identifiers: ["teacher"] }];
export const SELECTABLE_USER_IDENTIFIERS = ["administrator", "aide"];

export const DEFAULT_GRADE_TRANSLATIONS = [
  { grade: "K", name: "Kindergarten", translations: ["00", "K", "KG"] },
  { grade: "1", name: "First Grade", translations: ["01", "1"] },
  { grade: "2", name: "Second Grade", translations: ["02", "2"] },
  { grade: "3", name: "Third Grade", translations: ["03", "3"] },
  { grade: "4", name: "Fourth Grade", translations: ["04", "4"] },
  { grade: "5", name: "Fifth Grade", translations: ["05", "5"] },
  { grade: "6", name: "Sixth Grade", translations: ["06", "6"] },
  { grade: "7", name: "Seventh Grade", translations: ["07", "7"] },
  { grade: "8", name: "Eighth Grade", translations: ["08", "8"] },
  { grade: "HS", name: "High School Grade", translations: ["09", "9", "10", "11", "12", "13", "HS"] }
];

export const MAX_SKILLS_FROM_NEXT_GRADE = 5;

export function getHistoryFieldName(isAdditionalSkill = false) {
  return isAdditionalSkill ? "additionalHistory" : "history";
}

export const ALLOWED_DEFAULT_GRADES = DEFAULT_GRADE_TRANSLATIONS.map(({ translations }) => translations).flat(1);

export function getGradeTranslationsByGrade(customGradeTranslations = {}) {
  return DEFAULT_GRADE_TRANSLATIONS.reduce((acc, { grade, translations }) => {
    acc[grade] = [...translations];
    if (customGradeTranslations[grade]) {
      acc[grade].push(...customGradeTranslations[grade]);
    }
    return acc;
  }, {});
}

export function getFilteredGrades(grades, allowedGrades = ALLOWED_DEFAULT_GRADES) {
  return grades.filter(g => allowedGrades.includes(g.toUpperCase()));
}

export function getGradeByGradeTranslation(gradeTranslationsByGrade = getGradeTranslationsByGrade()) {
  return Object.values(gradeTranslationsByGrade).reduce((acc, translations) => {
    translations.forEach(translation => {
      // eslint-disable-next-line prefer-destructuring
      acc[translation] = translations[0];
    });
    return acc;
  }, {});
}

export const getGradesFromOneRosterStudents = (
  students = [],
  gradeTranslationsByGrade = getGradeTranslationsByGrade()
) => {
  const allowedGrades = Object.values(gradeTranslationsByGrade).flat(1);
  const orderedGradeByGradeTranslation = getGradeByGradeTranslation(gradeTranslationsByGrade);
  return getFilteredGrades(
    students
      .map(s => {
        const studentGrades = Array.isArray(s.grades) ? s.grades : [s.grades];
        return getStudentHighestGrade(studentGrades, orderedGradeByGradeTranslation);
      })
      .flat(2),
    allowedGrades
  ).sort();
};

export function getGradeFromDescriptor(
  descriptorURI = "",
  descriptorsMap,
  gradeByGradeTranslation = getGradeByGradeTranslation()
) {
  if (descriptorURI === "") {
    return "";
  }
  const allowedGradesByLabel = {
    kindergarten: "K",
    "first grade": "01",
    "second grade": "02",
    "third grade": "03",
    "fourth grade": "04",
    "fifth grade": "05",
    "sixth grade": "06",
    "seventh grade": "07",
    "eighth grade": "08",
    "ninth grade": "09",
    "tenth grade": "10",
    "eleventh grade": "11",
    "twelfth grade": "12",
    "grade 13": "13"
  };
  const splitGrade = descriptorURI.split("#")[1];
  const gradeName = descriptorsMap[splitGrade] || descriptorsMap[gradeByGradeTranslation[splitGrade]];
  const gradeLabel = Object.keys(allowedGradesByLabel).includes(gradeName) ? gradeName : "";
  return allowedGradesByLabel[gradeLabel];
}

export function normalizeExternalRosteringId(id) {
  return id.replace(/-/g, "");
}

export function getGradeLabel(grade) {
  if (["09", "10", "11", "12", "13"].includes(grade)) {
    return "HS";
  }
  if (grade === "00") {
    return "K";
  }
  return "";
}

export function getStudentHighestGrade(studentGrades, orderedGradeByGradeTranslation = {}) {
  if (!studentGrades.length) {
    return "";
  }
  const highestNormalizedGrade = max(
    studentGrades.map(g => {
      return orderedGradeByGradeTranslation[g.toUpperCase()];
    })
  );
  return getGradeLabel(highestNormalizedGrade) || highestNormalizedGrade || "";
}

export function getSectionGradeMajority(studentGrades = []) {
  if (!studentGrades.length) {
    return "";
  }
  let majorityGrade = "";
  let majorityGradeCount = 0;
  const normalizedGrades = mapExternalRosteringStudentGrades(studentGrades).sort();
  const gradeTotalCount = normalizedGrades.reduce((gradeCount, grade) => {
    if (!gradeCount[grade]) {
      // eslint-disable-next-line no-param-reassign
      gradeCount[grade] = 0;
    }
    // eslint-disable-next-line no-param-reassign
    const currentValue = ++gradeCount[grade];
    if (grade && currentValue >= majorityGradeCount) {
      majorityGradeCount = currentValue;
      majorityGrade = getLabelFromNormalizedGrade(grade);
    }
    return gradeCount;
  }, {});
  if (gradeTotalCount[normalizeGrade(majorityGrade)] / studentGrades.length > 0.5) {
    return majorityGrade;
  }
  return getLabelFromNormalizedGrade(min(normalizedGrades));
}

function getLabelFromNormalizedGrade(grade) {
  if (grade === "00") {
    return "K";
  }
  if (grade === "09") {
    return "HS";
  }
  return grade;
}

export function mapExternalRosteringStudentGrades(grades = []) {
  return grades.map(g => {
    if (["KG", "K"].includes(g)) {
      return "00";
    }
    if (["HS", "10", "11", "12", "13"].includes(g)) {
      return "09";
    }
    return g;
  });
}

export function getValuesByKey(
  data,
  keyPath,
  valuePath,
  group = false,
  shouldAvoidDuplicates = false,
  optionalValuePath = ""
) {
  return data.reduce((a, c) => {
    if (group) {
      if (!a[get(c, keyPath, "")]) {
        // eslint-disable-next-line no-param-reassign
        a[get(c, keyPath, "")] = [];
      }
      if (shouldAvoidDuplicates) {
        if (!a[get(c, keyPath, "")].includes(get(c, valuePath, ""))) {
          // eslint-disable-next-line no-param-reassign
          a[get(c, keyPath, "")].push(get(c, valuePath, ""));
        }
      } else {
        // eslint-disable-next-line no-param-reassign
        a[get(c, keyPath, "")].push(get(c, valuePath, ""));
      }
    } else {
      const suffix = optionalValuePath.length ? ` (${get(c, optionalValuePath, "")})` : "";
      // eslint-disable-next-line no-param-reassign
      a[get(c, keyPath, "")] = get(c, valuePath, "").concat(suffix);
    }
    return a;
  }, {});
}

export function initializeEmptyRosterStats() {
  return {
    students: {
      added: 0,
      updated: 0,
      removed: 0
    },
    teachers: {
      added: 0,
      updated: 0,
      removed: 0
    },
    sites: {
      added: 0,
      updated: 0,
      removed: 0
    },
    studentGroups: {
      added: 0,
      updated: 0,
      removed: 0
    }
  };
}

export function isSmallGroupPassingClasswideIntervention({
  studentScores,
  instructionalTarget,
  totalStudentsAssessed
}) {
  return totalStudentsAssessed
    ? Number(
        (
          (studentScores.filter(score => Number(score) >= instructionalTarget).length / totalStudentsAssessed) *
          100
        ).toFixed(2)
      ) >= 80
    : false;
}

export function hasGroupPassedClasswideIntervention({
  medianScore,
  totalStudentsAssessed,
  targetScores,
  studentScores,
  numberOfEnrolledStudents
}) {
  const [instructionalTarget, masteryTarget] = targetScores;
  return numberOfEnrolledStudents <= 10
    ? isSmallGroupPassingClasswideIntervention({ studentScores, instructionalTarget, totalStudentsAssessed }) ||
        medianScore >= masteryTarget
    : medianScore >= masteryTarget;
}

export function sortByGradeAndName(a, b) {
  return normalizeGrade(a.grade) - normalizeGrade(b.grade) || a.name.localeCompare(b.name);
}

export function getQueryString(name) {
  return new URLSearchParams(window.location.search).get(name);
}

export function parseBoolean(value = "") {
  const valueString = String(value).toLowerCase();
  return !["", "0", "false"].includes(valueString);
}

export function getClassSectionIdForOneRoster({ classId, school, course, classCode, grades }) {
  return (
    classId.replace(`${school?.sourcedId || ""}_${course?.sourcedId || ""}_`, "") ||
    `${classCode}${grades[0] ? `_${grades[0]}` : ""}`
  );
}

export const useDebouncedEffect = (effect, deps, delay) => {
  useEffect(() => {
    const handler = setTimeout(() => effect(), delay);

    return () => clearTimeout(handler);
  }, [...(deps || []), delay]);
};

export const getPageBreakClassForEverySecondElementByIndex = (index, startFromFirstIndex = false) => {
  if (startFromFirstIndex) {
    return index % 2 === 1 ? " page-break-before" : "";
  }
  return index !== 0 && index % 2 === 0 ? " page-break-before" : "";
};

export function roundTo(number, precision = 3) {
  if (!isNumber(number)) {
    return number;
  }
  return round(number, precision);
}

export function getPreviousDayObject(month, day) {
  const date = moment.utc(`2000-${month}-${day}`, "YYYY-MM-DD");
  const previousDay = date.subtract(1, "day");
  return { month: previousDay.month() + 1, day: previousDay.date() };
}

export function getDefaultSchoolBreakValues() {
  return {
    winter: { startMonth: 12, startDay: 20, endMonth: 1, endDay: 2 },
    spring: { startMonth: 4, startDay: 1, endMonth: 4, endDay: 7 },
    other: {}
  };
}

export function isDateInPreviousSchoolYear({ month, day, schoolYearBoundary }) {
  if (month > schoolYearBoundary.month) {
    return true;
  }
  if (month < schoolYearBoundary.month) {
    return false;
  }
  return day > schoolYearBoundary.day;
}

export function isDateRangeInvalid(startDateObject, endDateObject, minDaysDiff = 7) {
  const { month: startMonth, day: startDay } = startDateObject;
  const { month: endMonth, day: endDay } = endDateObject;
  const startDateFullObject = {
    y: 2000,
    M: startMonth - 1,
    d: startDay
  };
  const endDateFullObject = {
    y: endMonth >= startMonth ? 2000 : 2001,
    M: endMonth - 1,
    d: endDay
  };
  const startDate = moment.utc(startDateFullObject);
  const endDate = moment.utc(endDateFullObject);
  const dayDiff = endDate.diff(startDate, "days") + 1;

  return dayDiff < minDaysDiff;
}

export function shouldUseDevMode(CI, devModeEnvironments = ["LOCAL", "DEV"]) {
  return devModeEnvironments.includes(Meteor?.settings?.public?.ENVIRONMENT) && !CI;
}

export function isOnExposedRoute(path) {
  return (
    path.includes("/login") ||
    path.includes("/resetPassword") ||
    path.includes("/self-enrollment") ||
    path.includes("/information-statement") ||
    path.includes("/onboarding")
  );
}
