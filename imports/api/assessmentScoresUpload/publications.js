import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";
import uniq from "lodash/uniq";
import { AssessmentScoresUpload } from "./assessmentScoresUpload";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";
import { isUserLoggedOut } from "../utilities/utilities";

Meteor.publish("AssessmentScoresUpload:ForSite", async function getAssessmentScoresUploadForSite(orgid, siteId) {
  if (isUserLoggedOut(this)) {
    return this.ready();
  }
  check(orgid, String);
  check(siteId, String);

  const schoolYearsToFind = uniq(
    (await AssessmentScoresUpload.find({}, { fields: { _id: 0, schoolYear: 1 } }).fetchAsync()).map(
      asu => asu.schoolYear
    )
  );
  const activeStudentsInSite = (
    await StudentGroupEnrollments.find(
      { schoolYear: { $in: schoolYearsToFind }, siteId },
      { fields: { _id: 0, studentId: 1 } }
    ).fetchAsync()
  ).map(sge => sge.studentId);

  return AssessmentScoresUpload.find(
    { orgid, schoolYear: { $in: schoolYearsToFind }, studentId: { $in: activeStudentsInSite } },
    {
      fields: { data: 1, schoolYear: 1, grade: 1, studentId: 1 }
    }
  );
});
