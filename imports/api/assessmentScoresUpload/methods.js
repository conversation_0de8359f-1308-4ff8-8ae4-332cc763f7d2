import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";
import * as auth from "../authorization/server/methods";
import { Students } from "../students/students";
import * as assessmentScoresInsertMethods from "./insertMethods";
import { AssessmentScoresUpload } from "./assessmentScoresUpload";

export const checkIfLocalAndStateIdsExist = async (data, orgid, schoolYear) => {
  const studentsLocalIds = data.map(studentData => studentData.localId);
  const studentsStateIds = data.map(studentData => studentData.stateId);

  const errors = [];

  if (!data.length) {
    return ["Error fetching data from server, please refresh the page."];
  }

  const students = await Students.find(
    {
      $or: [
        {
          "identity.identification.localId": { $in: studentsLocalIds },
          orgid,
          schoolYear
        },
        {
          "identity.identification.stateId": { $in: studentsStateIds },
          orgid,
          schoolYear
        }
      ]
    },
    { fields: { identity: 1 } }
  ).fetchAsync();

  data.forEach((item, index) => {
    const correspondingStudent = students.find(
      student =>
        (!item.localId || item.localId === student.identity.identification.localId) &&
        item.stateId === student.identity.identification.stateId
    );
    if (!correspondingStudent) {
      if ((item.firstName || item.lastName) && (item.localId || item.stateId)) {
        errors.push(
          `Student: ${item.firstName} ${item.lastName} with ${
            item.localId ? `localID ${item.localId} and ` : ""
          }stateID ${item.stateId} doesn't exist in ${schoolYear} year. Please see row: ${index + 2}.`
        );
      } else {
        errors.push(`Student doesn't exist in ${schoolYear} year. Please see row: ${index + 2}.`);
      }
    } else if (
      correspondingStudent &&
      (correspondingStudent.identity.name.firstName.toUpperCase() !== item.firstName.toUpperCase() ||
        correspondingStudent.identity.name.lastName.toUpperCase() !== item.lastName.toUpperCase())
    ) {
      errors.push(
        `Student with localID ${item.localId} corresponds to ${correspondingStudent.identity.name.firstName} ${
          correspondingStudent.identity.name.lastName
        } rather than ${item.firstName} ${item.lastName}. Please see row: ${index + 2}.`
      );
    }
  });

  return errors;
};

const insertUploadedAssessmentScores = async (dataPackage, orgid) => {
  const { data, shouldAddToExistingData } = dataPackage;

  try {
    await assessmentScoresInsertMethods.bulkInsert(data, orgid, shouldAddToExistingData);
  } catch (error) {
    throw new Meteor.Error(`Error while inserting external assessment scores: ${error.message || error.toString()}`);
  }
};

async function getSchoolYears(orgid) {
  return AssessmentScoresUpload.rawCollection().distinct("schoolYear", { orgid });
}

function transformOptionalBoolValue(value) {
  if (value === "") {
    return "";
  }
  return value ? "Yes" : "No";
}

async function exportExternalAssessmentScores({ orgid, schoolYear }) {
  const assessmentScores = await AssessmentScoresUpload.find({
    "data.assessmentYear": schoolYear,
    orgid
  }).fetchAsync();

  return assessmentScores.map(({ data }) => ({
    StudentLocalID: data.studentLocalID,
    StudentStateID: data.studentStateID,
    StudentLastName: data.studentLastName,
    StudentFirstName: data.studentFirstName,
    AssessmentYear: data.assessmentYear,
    StateAssessmentName: data.stateAssessmentName,
    StateAssessmentScaleScore: data.stateAssessmentScaleScore,
    StateAssessmentProficient: transformOptionalBoolValue(data.stateAssessmentProficient),
    StateAssessmentPercentileScore: data.stateAssessmentPercentileScore,
    DistrictAssessmentName: data.districtAssessmentName,
    DistrictAssessmentFallScaleScore: data.districtAssessmentFallScaleScore,
    DistrictAssessmentFallProficient: transformOptionalBoolValue(data.districtAssessmentFallProficient),
    DistrictAssessmentSpringScaleScore: data.districtAssessmentSpringScaleScore,
    DistrictAssessmentSpringProficient: transformOptionalBoolValue(data.districtAssessmentSpringProficient)
  }));
}

if (Meteor.isServer) {
  Meteor.methods({
    async "AssessmentScoresUpload:CheckIfLocalAndStateIdsExist"(studentDetails, orgid, schoolYear) {
      check(studentDetails, Array);
      check(orgid, String);
      check(schoolYear, Number);
      if (!this.userId) {
        throw new Meteor.Error(403, "No logged in user found!");
      }
      if (
        !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid
        }))
      ) {
        throw new Meteor.Error(
          403,
          "User is not authorized to use AssessmentScoresUpload:CheckIfLocalAndStateIdsExist for this organization"
        );
      }
      return checkIfLocalAndStateIdsExist(studentDetails, orgid, schoolYear);
    },
    async "AssessmentScoresUpload:insert"({ data, orgid }) {
      check(data, Object);
      check(orgid, String);
      if (!this.userId) {
        throw new Meteor.Error(403, "No logged in user found!");
      }
      if (
        !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid
        }))
      ) {
        throw new Meteor.Error(
          403,
          "User is not authorized to use AssessmentScoresUpload:insert for this organization"
        );
      }
      await insertUploadedAssessmentScores(data, orgid);
    },
    async "AssessmentScoresUpload:getSchoolYears"(orgid) {
      check(orgid, String);

      if (!this.userId) {
        throw new Meteor.Error(403, "No logged in user found!");
      }
      if (
        !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid
        }))
      ) {
        throw new Meteor.Error(
          403,
          "User is not authorized to use AssessmentScoresUpload:getSchoolYears for this organization"
        );
      }
      return getSchoolYears(orgid);
    },
    async "AssessmentScoresUpload:export"({ orgid, schoolYear }) {
      check(orgid, String);
      check(schoolYear, Number);

      if (!this.userId) {
        throw new Meteor.Error(403, "No logged in user found!");
      }
      if (
        !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid
        }))
      ) {
        throw new Meteor.Error(
          403,
          "User is not authorized to use AssessmentScoresUpload:export for this organization"
        );
      }
      return exportExternalAssessmentScores({ orgid, schoolYear });
    }
  });
}
