export const logTypes = [
  { value: "all", label: "All" },
  { value: "rosteringFilter", label: "Rostering Filter" },
  { value: "rosteringSchedule", label: "Rostering Schedule" },
  { value: "rosteringSetting", label: "Rostering Setting" },
  { value: "districtSettings", label: "District Settings" }
];

export const getTypeLabel = type => logTypes.find(logType => logType.value === type)?.label ?? "Unknown";
