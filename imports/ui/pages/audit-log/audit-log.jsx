import React, { useEffect, useState } from "react";
import ReactPaginate from "react-paginate";
import Select from "react-select";
import { Meteor } from "meteor/meteor";
import Alert from "react-s-alert";
import PropTypes from "prop-types";
import { Button } from "react-bootstrap";
import { getTypeLabel, logTypes } from "./utils/get-type-label";
import PageHeader from "../../components/page-header";
import Loading from "../../components/loading";
import AuditLogModal from "./audit-log-modal";
import { formatProfileName } from "./utils/format-profile-name";

const AuditLog = ({ orgid }) => {
  const itemsPerPage = 10;
  const [selectedLogType, setSelectedLogType] = useState(logTypes[0]);
  const [data, setData] = useState([]);
  const [selectedLog, setSelectedLog] = useState(null);
  const [isModalOpen, setModalOpen] = useState(false);
  const [numberOfPages, setNumberOfPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [isError, setIsError] = useState(false);

  const getPageData = (type, page, limit) => {
    setIsLoading(true);
    setIsError(false);
    Meteor.call("AuditLogs:getLogs", orgid, type, page, limit, (err, result) => {
      if (!err) {
        setData(result.data);
        setNumberOfPages(Math.ceil(result.totalCount / itemsPerPage));
        setIsLoading(false);
        setIsError(false);
      } else {
        setIsLoading(false);
        setIsError(true);
        Alert.error("There was a problem while fetching audit logs", { timeout: 5000 });
      }
    });
  };

  const getPageDataWrapper = () =>
    getPageData(selectedLogType.value === "all" ? null : selectedLogType.value, currentPage, itemsPerPage);

  useEffect(() => {
    getPageDataWrapper();
  }, [selectedLogType, currentPage]);

  const closeModal = () => {
    return setModalOpen(false);
  };

  const selectLogToShowInModal = log => {
    setSelectedLog(log);
    setModalOpen(true);
  };

  const getDataIndicator = (loading, error) => {
    if (loading) {
      return <Loading />;
    }
    if (error) {
      return (
        <div>
          An error occurred while fetching data
          <br />
          <Button variant="default" onClick={getPageDataWrapper}>
            Retry
          </Button>
        </div>
      );
    }
    return <span />;
  };

  return (
    <div className="conFullScreen">
      <PageHeader title="Audit Log" />
      <div className="animated fadeIn container">
        <form className="form-horizontal">
          <div className="form-group m-t-10 row align-items-center">
            <label className="col-3 text-end">Log Type</label>
            <Select
              onChange={itemToSelect => {
                setSelectedLogType(itemToSelect);
                setCurrentPage(1);
              }}
              isSearchable={true}
              value={selectedLogType}
              options={logTypes}
              placeholder={"Log Type"}
              classNamePrefix="react-select"
              className="col-7"
            />
          </div>
        </form>
        <div className="p-3">
          {isLoading || isError ? (
            getDataIndicator(isLoading, isError)
          ) : (
            <>
              {numberOfPages > 1 && (
                <ReactPaginate
                  forcePage={currentPage - 1}
                  previousLabel={"Prev"}
                  nextLabel={"Next"}
                  breakLabel={"..."}
                  pageCount={numberOfPages}
                  marginPagesDisplayed={1}
                  pageRangeDisplayed={5}
                  onPageChange={pageObject => {
                    setCurrentPage(pageObject.selected + 1);
                  }}
                  containerClassName={"pagination custom-pagination pagination-sm"}
                  pageClassName="page-item"
                  previousClassName="page-item"
                  nextClassName="page-item"
                  breakClassName="page-item"
                  pageLinkClassName="page-link"
                  previousLinkClassName="page-link"
                  nextLinkClassName="page-link"
                  breakLinkClassName="page-link"
                  activeClassName={"active"}
                  disableInitialCallback
                />
              )}
              <div className="table-responsive m-t-70">
                <table className="table table-condensed table-striped table-bordered table-hover table-row-centered">
                  <thead>
                    <tr>
                      <td>Date</td>
                      <td>Type</td>
                      <td>User</td>
                    </tr>
                  </thead>
                  <tbody>
                    {data.map(logItem => (
                      <tr key={logItem._id} onClick={() => selectLogToShowInModal(logItem)}>
                        <td>{logItem.created.date.toLocaleString()}</td>
                        <td>{getTypeLabel(logItem.type)}</td>
                        <td>
                          {formatProfileName(logItem.userProfileName)} ({logItem.created.by})
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </>
          )}
        </div>
        <AuditLogModal log={selectedLog} showModal={isModalOpen} onHide={closeModal} />
      </div>
    </div>
  );
};

AuditLog.propTypes = {
  orgid: PropTypes.string
};

export default AuditLog;
