/* eslint-disable react/no-string-refs */
import React from "react";
import { with<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { withTracker } from "meteor/react-meteor-data";
import PropTypes from "prop-types";
import Loading from "../../components/loading";
import LoginWithSSOTracker from "./login-with-sso-tracker";

const LoginWithSSO = ({ loggingIn, login, error }) => {
  let errorDetailsObj = {};
  if (error?.details?.includes("{")) {
    try {
      errorDetailsObj = JSON.parse(error?.details);
      // eslint-disable-next-line no-empty
    } catch (e) {}
  }

  const renderErrorDetails = (details, e) => {
    if (details?.userDetailsString) {
      return (
        <div className="text-muted">
          <small>
            {errorDetailsObj?.userDetailsString.split("\n").map((line, i) => (
              <div key={i}>{line}</div>
            ))}
          </small>
        </div>
      );
    }
    if (e?.details) {
      return (
        <div className="text-muted">
          <small>{e.details}</small>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="conLogin">
      <h3 className="text-center w7">
        <Link to="/" className="logo logo-lg">
          <i className="md md-equalizer" /> <span>SpringMath</span>{" "}
        </Link>
      </h3>
      <div className="text-center">
        <button className="btn btn-success btn-custom w-md waves-effect waves-light" onClick={() => login()}>
          Log In with Single Sign On Provider
        </button>
        {error && (
          <div className="m-t-10">
            <i className="fa fa-exclamation-triangle fa-2x text-danger"> Sign On error</i>
            <div className="text-muted">
              <small>{error?.reason || error?.message}</small>
            </div>
            {renderErrorDetails(errorDetailsObj, error)}
            <div className="m-t-10">
              Please contact your Data Admin for assistance.
              {errorDetailsObj?.dataAdmins?.length ? (
                <table className="table">
                  {errorDetailsObj.dataAdmins.map(dataAdmin => (
                    <tr key={dataAdmin.email}>
                      <td className="text-center">
                        {dataAdmin.fullName} -{" "}
                        <a className="link-primary p-0" href={`mailto:${dataAdmin.email}`}>
                          {dataAdmin.email}
                        </a>
                      </td>
                    </tr>
                  ))}
                </table>
              ) : null}
            </div>
          </div>
        )}
        {loggingIn && <Loading message="Loading Single Sign On" />}
      </div>
    </div>
  );
};

LoginWithSSO.propTypes = {
  history: PropTypes.object,
  error: PropTypes.object,
  loggingIn: PropTypes.bool,
  login: PropTypes.func
};

export default withRouter(withTracker(LoginWithSSOTracker)(LoginWithSSO));
