import React, { useEffect } from "react";
import { useH<PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { getMeteorUserSync, parseBoolean } from "/imports/api/utilities/utilities";

const Logout = () => {
  const history = useHistory();

  useEffect(() => {
    let redirect;
    const ssoLogIn = parseBoolean(localStorage.getItem("ssoLogIn"));

    if (ssoLogIn) {
      const user = getMeteorUserSync();

      if (user?.profile?.ssoIssuerLogoutRedirect) {
        redirect = user.profile.ssoIssuerLogoutRedirect;
      }
    }
    Meteor.logoutOtherClients(() => {
      Meteor.logout(() => {
        localStorage.removeItem("lastSiteIdWithGrades");
        localStorage.removeItem("lastRouteWithGrades");
        localStorage.removeItem("orgid");
        localStorage.removeItem("ssoLogIn");
        localStorage.removeItem("rosterSorting");

        localStorage.removeItem("Meteor.userId");
        localStorage.removeItem("Meteor.loginToken");
        localStorage.removeItem("Meteor.loginTokenExpires");

        if (redirect) {
          window.location = redirect;
        } else {
          history.push("/");
        }
      });
    });
  }, []);

  return (
    <div className="conLogin">
      <h3 className="text-center w7">
        <Link to="#" className="logo logo-lg">
          <i className="md md-equalizer" /> <span>SpringMath</span>{" "}
        </Link>
      </h3>
      <h4 className="text-center w7">Logging out...</h4>
    </div>
  );
};

export default Logout;
