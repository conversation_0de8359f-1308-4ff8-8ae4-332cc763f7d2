import React from "react";
import MockDate from "mockdate";
import { assert } from "chai";

import ResetPassword from "./reset-password.jsx";
import { renderWithRouter } from "../../../../tests/helpers/testUtils";

jest.mock("../../../api/utilities/utilities", () => ({
  ...jest.requireActual("../../../api/utilities/utilities"),
  getCurrentSchoolYear: jest.fn(() => 2019)
}));

describe("ResetPassword UI", () => {
  beforeAll(() => {
    MockDate.set("2018-12-20");
  });
  afterAll(() => {
    jest.restoreAllMocks();
    MockDate.reset();
  });
  describe("Render", () => {
    let resetPasswordComponent;
    beforeEach(() => {
      resetPasswordComponent = renderWithRouter(<ResetPassword />);
    });
    it("render", () => {
      // Verify that the method does what we expected
      assert.isDefined(resetPasswordComponent, "passwordResetComponent did not render");
    });
  });
});
