import { <PERSON><PERSON><PERSON> } from "buffer";
import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { Accounts } from "meteor/accounts-base";
import { <PERSON><PERSON>, <PERSON><PERSON>ead<PERSON>, <PERSON><PERSON><PERSON>, ModalFooter } from "react-bootstrap";

function MfaModal(props) {
  const [MFAuri, setMFAuri] = useState("");
  const [MFACode, setMFACode] = useState("");
  const [qrCode, setQrCode] = useState("");

  const generateQRCode = () => {
    Accounts.generate2faActivationQrCode("SpringMath", (codeError, result) => {
      if (codeError) {
        Alert.error(codeError.message || "Error while generating activation code");
        return;
      }
      const { svg, uri } = result;
      setQrCode(Buffer.from(svg).toString("base64"));
      setMFAuri(uri);
    });
  };

  const onMFACodeChange = e => {
    const code = e.target.value;
    setMFACode(code);
  };

  const enableMFA = e => {
    e.preventDefault();
    Accounts.enableUser2fa(MFACode, error => {
      if (error) {
        Alert.error(error.reason || "Error while enabling MFA");
      } else {
        if (props.handleMFALogin) {
          props.handleMFALogin(MFACode);
        } else {
          Alert.success("Successfully linked MFA");
        }
        props.closeModal();
      }
    });
  };

  const handleSubmit = e => {
    if (props.isMFAEnabled) {
      props.handleMFALogin(MFACode);
    } else {
      enableMFA(e);
    }
  };

  const handleCodeInputKey = e => {
    if (!MFACode.length || e.key !== "Enter") {
      return;
    }
    enableMFA(e);
  };

  const handleEnterKeySubmit = e => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  useEffect(() => {
    if (!props.isMFAEnabled) {
      generateQRCode();
    }
  }, []);

  const renderContent = () => {
    return (
      <div className="text-center">
        {qrCode && MFAuri && (
          <div>
            <p>
              Scan the QR code in the authenticator app
              <br />
              or use the link to enable MFA
            </p>
            <img width="200" src={`data:image/svg+xml;base64,${qrCode}`} />
            <br />
            <a href={MFAuri} className="btn btn-outline-primary">
              Enable MFA
            </a>
          </div>
        )}
        <input
          className="mt-3 form-control font-18 w-50 d-inline-block"
          type="text"
          placeholder="MFA Code (e.g. 123456)"
          onChange={onMFACodeChange}
          onKeyUp={handleCodeInputKey}
          onKeyDown={handleEnterKeySubmit}
          autoFocus={true}
        />
      </div>
    );
  };

  return (
    <Modal show={props.showModal} onHide={props.closeModal} backdrop="static">
      <ModalHeader>
        <div className="d-flex justify-content-between align-items-center w-100">
          <div className="flex-grow-1 text-center">
            <h3 className="m-0">{props.isMFAEnabled ? "Enter MFA Code" : "Setup MFA"}</h3>
          </div>
        </div>
        <button className="btn btn-close-custom red-hover" onClick={props.closeModal}>
          &times;
        </button>
      </ModalHeader>
      <ModalBody>{renderContent()}</ModalBody>
      <ModalFooter>
        <div className="text-center w-100">
          <button className="btn btn-success" disabled={!MFACode.trim().length} onClick={handleSubmit}>
            Submit
          </button>
        </div>
      </ModalFooter>
    </Modal>
  );
}

MfaModal.propTypes = {
  handleMFALogin: PropTypes.func,
  showModal: PropTypes.func.required,
  closeModal: PropTypes.func.required,
  isMFAEnabled: PropTypes.bool
};

export default MfaModal;
