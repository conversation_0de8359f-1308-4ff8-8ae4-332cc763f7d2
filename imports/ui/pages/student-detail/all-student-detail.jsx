// eslint-disable-next-line max-classes-per-file
import React, { Component } from "react";
import PropTypes from "prop-types";
import html2pdf from "html2pdf.js";
import { saveAs } from "file-saver";
import JSZip from "jszip";
import { get, slice } from "lodash";

import moment from "moment";
import { PureStudentDetail as StudentDetail } from "./student-detail";
import Loading from "/imports/ui/components/loading";
import allStudentDetailContainerWithContext from "./all-student-detail-context";

class AllStudentDetail extends Component {
  state = {
    timestamp: new Date().getTime(),
    queueProgress: 0,
    tempPdfQueue: [],
    isGenerating: true
  };

  componentDidMount() {
    document.getElementById("filepicker_comm_iframe")?.remove();
    document.getElementById("fpapi_comm_iframe")?.remove();
    this.waitForLoadingAndGenerateDocumentQueue();
  }

  componentDidUpdate(prevProps, prevState) {
    const { studentIdsInGroup } = this.props;
    const { tempPdfQueue, isGenerating } = this.state;
    // queue finished generating as contains same number of documents as students in group
    if (
      studentIdsInGroup.length === tempPdfQueue.length &&
      prevState.tempPdfQueue.length !== tempPdfQueue.length &&
      isGenerating
    ) {
      this.zipPrintQueue(tempPdfQueue);
    }
    // queue filling up
    else if (
      prevState.tempPdfQueue.length < tempPdfQueue.length &&
      studentIdsInGroup.length &&
      tempPdfQueue !== prevState.tempPdfQueue &&
      isGenerating
    ) {
      this.waitForLoadingAndGenerateDocumentQueue();
      window.parent.postMessage(`generatingProgress=${tempPdfQueue.length}=${studentIdsInGroup.length}`, "*");
    }
    // update generating progress
    if (tempPdfQueue.length < studentIdsInGroup.length && isGenerating) {
      window.parent.postMessage(`generatingProgress=${tempPdfQueue.length}=${studentIdsInGroup.length}`, "*");
    }
  }

  waitForLoadingAndGenerateDocumentQueue = () => {
    this.printInterval = setInterval(async () => {
      if (document.getElementsByClassName("rect5").length === 0) {
        clearInterval(this.printInterval);
        await this.generateNextNStudentsInQueue();
      }
    }, 1000);
  };

  getQueueScope = () => {
    if (!this.state.isGenerating) {
      return [];
    }
    const { studentIdsInGroup } = this.props;
    const { queueProgress } = this.state;
    return slice(studentIdsInGroup, queueProgress, queueProgress + 1);
  };

  generateNextNStudentsInQueue = async () => {
    const pdfQueue = [];
    const queueScope = this.getQueueScope();
    queueScope.forEach(studentId => {
      pdfQueue.push(this.generateCurrentPagePdfBlob(studentId));
    });
    Promise.all(pdfQueue).then(queue => {
      this.setState(state => ({
        ...state,
        tempPdfQueue: [...state.tempPdfQueue, ...queue],
        queueProgress: state.queueProgress + 1
      }));
    });
  };

  zipPrintQueue = pdfList => {
    window.parent.postMessage("preparingZip", "*");
    const zip = new JSZip();
    pdfList.forEach(({ pdf, pdfName }) => {
      zip.file(pdfName, pdf);
    });
    zip
      .generateAsync({
        type: "blob",
        compression: "DEFLATE",
        compressionOptions: {
          level: 6
        }
      })
      .then(content => {
        saveAs(content, this.getZipFileName());
        window.parent.postMessage("generatingFinished", "*");
        this.setState({ queueProgress: 0, isGenerating: false });
        setTimeout(() => {
          window.parent.document.getElementById("print-iframe")?.remove();
          window.document.title = "SpringMath";
        }, 60000);
      });
  };

  generateCurrentPagePdfBlob = studentId => {
    const element = document.getElementById(`print_${studentId}`);
    element.classList.add("custom-forced-print");
    const opt = {
      margin: [8, 8, 8, 0],
      enableLinks: false,
      jsPDF: { putOnlyUsedFonts: true }
    };
    return {
      pdf: html2pdf()
        .set(opt)
        .from(element)
        .output("blob", null, "pdf"),
      pdfName: this.getStudentPdfName(studentId)
    };
  };

  getStudentPdfName = studentId => {
    const student = get(
      this.props.studentsInGroup.find(s => s._id === studentId),
      "identity.name",
      {}
    );
    return `${student.lastName} ${student.firstName}.pdf`;
  };

  getZipFileName = () => {
    const { studentGroup, printOption } = this.props;
    const { name } = studentGroup;
    const currentDateString = moment().format("DD-MM-YY");
    const documentName = printOption ? printOption.replace("Print ", "") : "All Student Detail";

    return `${documentName} - ${name.match(/\((.*)\)/).pop()} ${currentDateString}.zip`;
  };

  render() {
    if (this.props.loading) {
      return <Loading />;
    }
    const {
      allClasswideScores,
      benchmarkWindows,
      classwideBenchmarkScores,
      classwideInterventionScores,
      individualInterventionScoresByStudentId,
      loading,
      otherResults,
      otherStudentGroupsByStudentId,
      printCurrentClasswideInterventionGraph,
      printAllClasswideInterventionGraphs,
      printAllIndividualInterventionGraphs,
      printCurrentStudentProfilePage,
      printAllStudentDetailsIncludingAllGraphs,
      printStudentClasswideProgressAndScreeningResults,
      schoolYear,
      siteId,
      studentEnrollmentsByStudentId,
      studentGroup,
      studentGroupId,
      studentsInGroup = []
    } = this.props;

    return (
      <div id="allStudentDetail">
        {this.getQueueScope().map(studentId => {
          const studentDetailTrackerData = {
            allClasswideScores,
            benchmarkWindows,
            classwideBenchmarkScores,
            classwideInterventionScores,
            individualInterventionScores: individualInterventionScoresByStudentId[studentId],
            loading,
            otherResults,
            otherStudentGroups: otherStudentGroupsByStudentId[studentId],
            printCurrentClasswideInterventionGraph,
            printAllClasswideInterventionGraphs,
            printAllIndividualInterventionGraphs,
            printCurrentStudentProfilePage,
            printAllStudentDetailsIncludingAllGraphs,
            printStudentClasswideProgressAndScreeningResults,
            student: studentsInGroup.find(s => s._id === studentId),
            studentEnrollments: studentEnrollmentsByStudentId[studentId],
            studentGroup,
            students: studentsInGroup,
            studentsInGroup
          };
          return (
            <div key={studentId} id={`print_${studentId}`} className="printContainer">
              <StudentDetail
                siteId={siteId}
                studentGroupId={studentGroupId}
                studentId={studentId}
                activeNavName={null}
                schoolYear={schoolYear}
                {...studentDetailTrackerData}
              />
            </div>
          );
        })}
      </div>
    );
  }
}

AllStudentDetail.propTypes = {
  allClasswideScores: PropTypes.array,
  benchmarkWindows: PropTypes.array,
  classwideBenchmarkScores: PropTypes.array,
  classwideInterventionScores: PropTypes.array,
  individualInterventionScoresByStudentId: PropTypes.object,
  loading: PropTypes.bool,
  otherResults: PropTypes.array,
  otherStudentGroupsByStudentId: PropTypes.object,
  printOption: PropTypes.string,
  printCurrentClasswideInterventionGraph: PropTypes.bool,
  printAllClasswideInterventionGraphs: PropTypes.bool,
  printAllIndividualInterventionGraphs: PropTypes.bool,
  printCurrentStudentProfilePage: PropTypes.bool,
  printStudentClasswideProgressAndScreeningResults: PropTypes.bool,
  printAllStudentDetailsIncludingAllGraphs: PropTypes.bool,
  schoolYear: PropTypes.number,
  siteId: PropTypes.string,
  studentEnrollmentsByStudentId: PropTypes.object,
  studentGroup: PropTypes.object,
  studentGroupId: PropTypes.string,
  studentIdsInGroup: PropTypes.array,
  studentsInGroup: PropTypes.array
};

export default allStudentDetailContainerWithContext(<AllStudentDetail />);
