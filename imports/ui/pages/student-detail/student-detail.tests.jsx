import { assert } from "chai";
import React from "react";
import td from "testdouble";
import { Meteor } from "meteor/meteor";
import { cleanup, waitFor } from "@testing-library/react";
import { PureStudentDetail } from "./student-detail.jsx";
import { ClassContext } from "../classContext";
import { StudentContext } from "../../../contexts/StudentContext";
import { SiteContext } from "../../../contexts/SiteContext";
import { SchoolYearContext } from "../../../contexts/SchoolYearContext";
import { StudentGroupContext } from "../../../contexts/StudentGroupContext";
import { UserContext } from "../../../contexts/UserContext";
import { renderWithRouter } from "../../../../tests/helpers/testUtils";
import "@testing-library/jest-dom";

// Mock getCurrentSchoolYear to return a resolved value
jest.mock("/imports/api/utilities/utilities", () => ({
  ...jest.requireActual("/imports/api/utilities/utilities"),
  getCurrentSchoolYear: jest.fn(() => Promise.resolve(2019)),
  getMeteorUser: jest.fn(() => ({ profile: { orgid: "test_organization_id" } })),
  getMeteorUserId: jest.fn(() => "test_user_id")
}));

const basicStudent = {
  _id: "studentId",
  history: [],
  identity: { name: { firstName: "Test", lastName: "Student" } }
};

const basicStudentGroup = {
  _id: "test_student_group_id",
  name: "Test Group",
  grade: "04",
  history: [],
  additionalHistory: []
};

const defaultStudentContext = {
  student: basicStudent,
  studentId: basicStudent._id,
  studentsInGroup: [basicStudent]
};

const defaultSiteContext = {
  siteId: "test_site_id",
  benchmarkWindows: [],
  studentGroupsInSite: [basicStudentGroup]
};

const defaultSchoolYearContext = {
  schoolYear: 2019
};

const defaultStudentGroupContext = {
  studentGroup: basicStudentGroup,
  studentGroupId: basicStudentGroup._id,
  studentsInStudentGroup: [basicStudent]
};

const defaultUserContext = {
  user: { _id: "test_user_id", profile: { orgid: "test_organization_id" } },
  userId: "test_user_id"
};

const defaultClassContext = {
  student: basicStudent,
  studentGroup: basicStudentGroup,
  setStats: () => {}
};

// eslint-disable-next-line react/prop-types
function renderStudentDetailWith({
  studentContext = defaultStudentContext,
  siteContext = defaultSiteContext,
  schoolYearContext = defaultSchoolYearContext,
  studentGroupContext = defaultStudentGroupContext,
  userContext = defaultUserContext,
  classContext = defaultClassContext,
  props = {}
} = {}) {
  const componentProps = {
    classwideBenchmarkScores: [],
    IndividualInterventionScores: [],
    allClasswideScores: [],
    studentEnrollments: [],
    otherResults: [],
    student: basicStudent,
    studentGroup: basicStudentGroup,
    benchmarkWindows: [],
    loading: false,
    studentId: "studentId",
    studentGroupId: "test_student_group_id",
    ...props
  };

  return (
    <UserContext.Provider value={userContext}>
      <SchoolYearContext.Provider value={schoolYearContext}>
        <SiteContext.Provider value={siteContext}>
          <StudentGroupContext.Provider value={studentGroupContext}>
            <StudentContext.Provider value={studentContext}>
              <ClassContext.Provider value={classContext}>
                <PureStudentDetail {...componentProps} />
              </ClassContext.Provider>
            </StudentContext.Provider>
          </StudentGroupContext.Provider>
        </SiteContext.Provider>
      </SchoolYearContext.Provider>
    </UserContext.Provider>
  );
}

jest.mock("../../../api/utilities/utilities", () => ({
  ...jest.requireActual("../../../api/utilities/utilities"),
  getCurrentSchoolYear: jest.fn(() => 2018),
  getFormattedSchoolYear: jest.fn(() => "2017-18"),
  translateBenchmarkPeriod: jest.fn(() => ({ title: "Winter" }))
}));

describe("Student-Detail UI", () => {
  afterEach(() => {
    cleanup();
  });
  afterAll(() => {
    jest.restoreAllMocks();
  });
  describe("Render", () => {
    const studentGroup = { history: [] };
    beforeEach(async () => {
      td.replace(Meteor, "call");
      td.when(Meteor.call("CalculateIndividualStats", studentGroup)).thenCallback(null, {});
      td.when(Meteor.call("getIndividualStudentDetailData", td.matchers.anything())).thenCallback(null, {});
      // Mock Meteor methods and subscriptions
      Meteor.user = jest.fn(() => ({ profile: { orgid: "test_organization_id" } }));
      Meteor.subscribe = jest.fn(() => ({ ready: () => true }));
    });
    afterEach(() => {
      td.reset();
    });
    it("renders the primary component", () => {
      // Verify that the method does what we expected
      const PureStudentDetailComponent = renderWithRouter(renderStudentDetailWith());
      assert.isDefined(PureStudentDetailComponent, "StudentDetailComponent did not render");
    });

    it("renders the StudentLog", async () => {
      const { getByTestId } = renderWithRouter(renderStudentDetailWith());
      await waitFor(() => {
        const studentLogComponent = getByTestId("conStudentActivityLog");
        expect(studentLogComponent).toBeVisible();
      });
    });

    it("renders the individual intervention progress when the student has history interventions", async () => {
      const assessmentId = "assessmentId";
      const studentWithHistory = {
        _id: "studentId",
        currentSkill: {},
        identity: { name: { firstName: "Test", lastName: "Student" } },
        history: [
          {
            benchmarkAssessmentId: "benchmarkAssessmentId",
            assessmentId,
            type: "individual",
            assessmentResultMeasures: [{ assessmentId, studentScores: [] }]
          }
        ]
      };

      const studentContext = {
        ...defaultStudentContext,
        student: studentWithHistory
      };

      const classContext = {
        ...defaultClassContext,
        student: studentWithHistory
      };

      const individualProgressProps = {
        printAllIndividualInterventionGoalSkills: false,
        printAllIndividualInterventionSkills: false,
        student: studentWithHistory
      };

      const { getByTestId } = renderWithRouter(
        renderStudentDetailWith({
          studentContext,
          classContext,
          props: individualProgressProps
        })
      );

      await waitFor(() => {
        const individualInterventionProgressComponent = getByTestId("student-detail-intervention-progress");
        expect(individualInterventionProgressComponent).toBeVisible();
      });
    });

    describe("ScreeningResultsChart", () => {
      afterEach(cleanup);
      const assessmentResultId = "assessmentResultId";
      const classwideBMScores = [
        {
          assessmentId: "assessmentId",
          assessmentResultId,
          assessmentResultMeasures: [
            {
              assessmentId: "assessmentId",
              assessmentName: "Assessment Name",
              targetScores: [3, 4, 5],
              studentResults: [
                {
                  studentId: basicStudent._id,
                  score: "5"
                }
              ]
            }
          ],
          type: "benchmark",
          enrolledStudentIds: [basicStudent._id],
          benchmarkPeriodId: "winter",
          whenEnded: {
            date: new Date(),
            on: 1234567899
          }
        }
      ];
      const allClasswideScores = [
        {
          assessmentResultId,
          benchmarkPeriodId: "winter",
          enrolledStudentIds: [basicStudent._id],
          assessmentResultMeasures: [
            {
              assessmentId: "assessmentId",
              assessmentName: "Assessment Name",
              targetScores: [3, 4, 5],
              studentResults: [
                {
                  studentId: basicStudent._id,
                  score: "5"
                }
              ]
            }
          ],
          whenEnded: {
            date: new Date(),
            on: 1234567890
          }
        }
      ];

      it("renders the screening results when student scores in classwideBenchmark scores are present", async () => {
        const siteContext = {
          ...defaultSiteContext,
          benchmarkWindows: [{ benchmarkPeriodId: "winter" }]
        };

        const { getByTestId } = renderWithRouter(
          renderStudentDetailWith({
            siteContext,
            props: {
              classwideBenchmarkScores: classwideBMScores,
              allClasswideScores,
              student: basicStudent
            }
          })
        );
        await waitFor(() => {
          const individualScreeningChartComponent = getByTestId(`${assessmentResultId}_${basicStudent._id}`);
          expect(individualScreeningChartComponent).toBeVisible();
        });
      });

      it('renders "student was absent or unavailable" text message if there aren\'t any valid student scores', async () => {
        const bmScoresWithNullStudentScore = JSON.parse(JSON.stringify(allClasswideScores));
        bmScoresWithNullStudentScore[0].assessmentResultMeasures[0].studentResults[0].score = null;

        const siteContext = {
          ...defaultSiteContext,
          benchmarkWindows: [{ benchmarkPeriodId: "winter" }]
        };

        const { getByTestId } = renderWithRouter(
          renderStudentDetailWith({
            siteContext,
            props: {
              classwideBenchmarkScores: bmScoresWithNullStudentScore,
              allClasswideScores,
              student: basicStudent
            }
          })
        );

        await waitFor(() => {
          const chartResults = getByTestId("screening-results-charts");
          expect(chartResults.textContent).toContain("Test was absent or unavailable during screening");
        });
      });

      it('renders "student did not participate in this screening assessment" text if there aren\'t any screening scores for this student', async () => {
        const bmScoresWithoutStudentScores = getBmScoresWithoutStudentScores(allClasswideScores);

        const siteContext = {
          ...defaultSiteContext,
          benchmarkWindows: [{ benchmarkPeriodId: "winter" }]
        };

        const { getByTestId } = renderWithRouter(
          renderStudentDetailWith({
            siteContext,
            props: {
              classwideBenchmarkScores: bmScoresWithoutStudentScores,
              allClasswideScores,
              student: basicStudent
            }
          })
        );

        await waitFor(() => {
          const screeningResultsCharts = getByTestId("screening-results-charts");
          expect(screeningResultsCharts.textContent).toContain("Test did not participate in this screening assessment");
        });
      });

      it("renders information about the enrollment in which the student took the screening assessment if assessments from other groups are available", async () => {
        const otherGroupId = "otherGroupId";
        const bmScoresWithoutStudentScores = getBmScoresWithoutStudentScores(allClasswideScores);
        const otherGroupBMResults = [{ ...classwideBMScores[0], studentGroupId: otherGroupId }];
        const otherGroups = [
          {
            _id: otherGroupId,
            name: "Other Group Name"
          }
        ];

        const siteContext = {
          ...defaultSiteContext,
          benchmarkWindows: [{ benchmarkPeriodId: "winter" }]
        };

        const { getByTestId } = renderWithRouter(
          renderStudentDetailWith({
            siteContext,
            props: {
              classwideBenchmarkScores: bmScoresWithoutStudentScores,
              allClasswideScores,
              otherResults: otherGroupBMResults,
              otherStudentGroups: otherGroups,
              student: basicStudent
            }
          })
        );

        await waitFor(() => {
          const screeningResultsCharts = getByTestId("screening-results-charts");
          expect(screeningResultsCharts.textContent).toContain(
            `Test was enrolled in class ${otherGroups[0].name} when they took the screening assessment`
          );
        });
      });
    });
  });
});

function getBmScoresWithoutStudentScores(bmScores) {
  const bmScoresWithoutStudentScores = JSON.parse(JSON.stringify(bmScores));
  bmScoresWithoutStudentScores[0].assessmentResultMeasures[0].studentResults = [];
  bmScoresWithoutStudentScores[0].enrolledStudentIds = [];
  return bmScoresWithoutStudentScores;
}
