import React from "react";
import { sortBy } from "lodash";
import PropTypes from "prop-types";
import { isOnPrintPage } from "../../utilities";

export default function DetailTable(props) {
  const outcomeColors = {
    above: "#54a600",
    at: "#F1C40F", // s-alert-warning
    below: "#E74C3C", // s-alert-error
    absent: "grey"
  };

  const isPrinting = isOnPrintPage();

  const renderDot = (outcome, shouldRenderAbsent, alignmentClassName) => {
    if (!shouldRenderAbsent && outcome === "absent") {
      return null;
    }
    return (
      <span
        className={`${isPrinting ? "dot-small" : "dot"}${alignmentClassName ? ` ${alignmentClassName}` : ""}`}
        style={{ background: outcomeColors[outcome] }}
      ></span>
    );
  };

  const renderDotLegend = (renderAsRow = false, useSmallIcons = false) => {
    const dotClassName = useSmallIcons ? "dot-small" : "dot";
    return (
      <div className={`d-flex${renderAsRow ? " flex-row gap-3 col-12 justify-content-center" : " flex-column gap-1"}`}>
        <div className="d-flex align-items-center gap-1">
          <span className={dotClassName} style={{ background: outcomeColors.above }} /> Mastery
        </div>
        <div className="d-flex align-items-center gap-1">
          <span className={dotClassName} style={{ background: outcomeColors.at }} /> Instructional
        </div>
        <div className="d-flex align-items-center gap-1">
          <span className={dotClassName} style={{ background: outcomeColors.below }} /> Frustrational
        </div>
        <div className="d-flex align-items-center gap-1">
          <span className={dotClassName} style={{ background: outcomeColors.absent }} /> Absent
        </div>
      </div>
    );
  };

  function renderTableRow(columns, rowName) {
    return (
      <tr key={rowName} className="text-center">
        <td className={`vertical-align-middle text-nowrap text-left-forced${rowName === "Summary All" ? " w6" : ""}`}>
          {rowName}
        </td>
        {columns?.map((content, index) => {
          if (Object.keys(content || {})?.length <= 1 || content.outcome === null) {
            return <td key={`${rowName}_${index}`}></td>;
          }
          return (
            <td key={`${rowName}_${index}`} className={rowName === "Summary All" ? "w6" : ""}>
              {content.outcome ? (
                renderDot(content.outcome, true)
              ) : (
                <React.Fragment>
                  <div style={{ color: outcomeColors.above }}>
                    {!Number.isInteger(content.mastery) ? "" : `${content.mastery}%`}
                  </div>
                  <div style={{ color: outcomeColors.at }}>
                    {!Number.isInteger(content.instructional) ? "" : `${content.instructional}%`}
                  </div>
                  <div style={{ color: outcomeColors.below }}>
                    {!Number.isInteger(content.frustrational) ? "" : `${content.frustrational}%`}
                  </div>
                  <div style={{ color: outcomeColors.absent }}>
                    {!Number.isInteger(content.absent) ? "" : `${content.absent}%`}
                  </div>
                  {content.numberOfStudents && content.skillName !== "All Skills" ? (
                    <span className="text-black font-13">(n={content.numberOfStudents})</span>
                  ) : null}
                </React.Fragment>
              )}
            </td>
          );
        })}
      </tr>
    );
  }

  const renderCompactSkillProgressTable = () => {
    const colorsByIndex = ["#000", "grey", "#E74C3C", "#F1C40F", "#54a600"];

    if (!props.summaryAll?.columns?.length && props.componentContext !== "compactTable") {
      return <div className="alert alert-info text-center">No data available</div>;
    }
    return (
      <table className="table table-bordered table-compact overflow-auto progress-table">
        <thead>
          <tr>
            <th className="col-7">Skill Name</th>
            <th className="col-1 text-center">N</th>
            <th className="col-1 text-center">Absent</th>
            <th className="col-1 text-center">Frustrational</th>
            <th className="col-1 text-center">Instructional</th>
            <th className="col-1 text-center">Mastery</th>
          </tr>
        </thead>
        <tbody>
          {props.rowData.map((data, index) => (
            <tr key={`skillProgress_${index}`}>
              <td className={isPrinting ? "text-black" : ""}>{data.rowName}</td>
              {data.columns.map((c, i) => (
                <td key={`skillProgress_${index}_${i}`} className="text-center vertical-align-middle">
                  {/* // NOTE(fmazur) - Do not display N for All Skills Row */}
                  {data.rowName === "All Skills" && i === 0 ? null : (
                    <div style={{ color: colorsByIndex[i] }}>
                      {!Number.isInteger(c) ? "" : `${c}${i !== 0 ? "%" : ""}`}
                    </div>
                  )}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    );
  };

  const renderSkillProgressTable = () => {
    if (!props.summaryAll?.columns?.length && props.componentContext !== "compactTable") {
      return <div className="alert alert-info text-center">No data available</div>;
    }
    // NOTE(fmazur) - fill columns with empty data for groups without any classwide score
    const columnsShell = (
      Object.values(props.rowData || {}).find(r => r.columns?.length) || props.summaryAll
    )?.columns?.map(() => ({}));
    return (
      <table className="table table-condensed overflow-auto">
        <thead>
          <tr style={{ verticalAlign: "middle" }}>
            <th>{renderDotLegend()}</th>
            {(props.componentContext !== "compactTable"
              ? props.summaryAll?.columns
              : Object.values(props.rowData)[0].columns
            ).map((colContent, index) => (
              <th className="sideways text-break" key={`${colContent.skillName}_${index}`}>
                {colContent.skillName}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {sortBy(
            Object.values(props.rowData),
            props.componentContext === "groupDetail" ? ["lastName", "firstName"] : ["rowName"]
          ).map(row => {
            let rowName = row.rowName || "";
            if (row.firstName) {
              rowName = `${row.lastName}, ${row.firstName}`;
            }
            return renderTableRow(row.columns?.length ? row.columns : columnsShell, rowName);
          })}
          {props.componentContext !== "compactTable" ? renderTableRow(props.summaryAll.columns, "Summary All") : null}
        </tbody>
      </table>
    );
  };

  const renderIndividualStudentTable = () => {
    if (!props.rowData?.columns?.find(c => c.outcome !== null)) {
      return null;
    }
    return (
      <table className={`table overflow-auto progress-table${isPrinting ? " table-compact-2" : " table-compact"}`}>
        <thead>
          <tr className="align-middle">
            <th colSpan="3" className="border-0">
              {renderDotLegend(true, true)}
            </th>
          </tr>
          <tr className="align-middle">
            <th></th>
            <th className="col-10">Skill Name</th>
            <th className="col-2 text-center">Progress</th>
          </tr>
        </thead>
        <tbody>
          {props.rowData.columns.map((content, index) => (
            <tr key={`skillProgress_${index}`} className="align-middle">
              <td className={`text-end ${isPrinting ? " font-13" : ""}`}>{index + 1}.</td>
              <td className={isPrinting ? "text-black font-13" : ""}>{content.skillName}</td>
              <td key={`skillProgress_${index}`} className="text-center">
                {renderDot(content.outcome, true, "align-middle")}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    );
  };

  const renderedElementClassName = "main-content overflow-auto";

  if (props.componentContext === "compactTable") {
    return <div className={renderedElementClassName}>{renderCompactSkillProgressTable()}</div>;
  }

  if (props.componentContext === "studentDetail") {
    return <div className={renderedElementClassName}>{renderIndividualStudentTable()}</div>;
  }

  return <div className={renderedElementClassName}>{renderSkillProgressTable()}</div>;
}

DetailTable.propTypes = {
  rowData: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
  summaryAll: PropTypes.object,
  componentContext: PropTypes.string,
  studentId: PropTypes.string
};
