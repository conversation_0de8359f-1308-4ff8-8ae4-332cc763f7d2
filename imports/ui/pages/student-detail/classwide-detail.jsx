import React, { useState, useContext } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";

import { useTracker } from "meteor/react-meteor-data";
import { Students } from "/imports/api/students/students";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { Loading } from "/imports/ui/components/loading";
import ClasswideInterventionProgress from "../../components/student-detail/classwide-intervention-progress.jsx";
import { StudentGroupEnrollments } from "/imports/api/studentGroupEnrollments/studentGroupEnrollments";
import InterventionMessage from "../../components/dashboard/intervention-message";
import { SchoolYearContext } from "/imports/contexts/SchoolYearContext";
import { isOnPrintPage } from "/imports/ui/utilities";

function ClasswideDetail(props) {
  const [, setScrollState] = useState({
    scroll: {
      lastReload: new Date().valueOf()
    }
  });

  const isPrinting = isOnPrintPage();

  const refreshScroll = () => {
    setScrollState(prev => ({
      ...prev,
      scroll: {
        lastReload: new Date().valueOf()
      }
    }));
  };

  const renderClasswideProgress = () => {
    return (
      <div>
        <ClasswideInterventionProgress
          student={null}
          studentGroup={props.studentGroup}
          students={props.students}
          refreshScroll={refreshScroll}
          shouldShowStudentsScores={isPrinting}
        />
      </div>
    );
  };

  if (!props.loading) {
    const { studentGroup } = props;
    const isClasswideCompleteWithActiveMessage =
      studentGroup?.currentClasswideSkill?.message?.messageCode === "5" &&
      !studentGroup?.currentClasswideSkill?.message?.dismissed;
    return (
      <div>
        {isClasswideCompleteWithActiveMessage ? (
          <div>
            <InterventionMessage
              entityType="StudentGroup"
              entityId={studentGroup._id}
              message={studentGroup.currentClasswideSkill.message}
            />
            <small>
              We recommend that you visit the students page to schedule a couple students for individual interventions.
            </small>
          </div>
        ) : null}
        <div
          id="studentDetailContainer"
          className={`studentDetailContent ${!isClasswideCompleteWithActiveMessage ? "classroomDetail" : ""}`}
        >
          <section id="student-detail-intervention-progress" data-testid="student-detail-intervention-progress">
            {studentGroup.history && studentGroup.history.some(sgh => sgh.type === "classwide")
              ? renderClasswideProgress()
              : null}
          </section>
        </div>
      </div>
    );
  }
  return <Loading />;
}

ClasswideDetail.propTypes = {
  students: PropTypes.array,
  studentGroup: PropTypes.object, // TODO SHAPE
  loading: PropTypes.bool,
  situation: PropTypes.number
};

// Data Container
function ClasswideDetailWithContainer({ studentGroupId, siteId }) {
  const { schoolYear } = useContext(SchoolYearContext);

  const trackerData = useTracker(() => {
    if (!schoolYear) {
      return { loading: true };
    }

    let students;
    const studentsSub = Meteor.subscribe("StudentsInStudentGroup", studentGroupId);
    const studentGroupEnrollmentsSub = Meteor.subscribe("StudentGroupEnrollmentsInStudentGroup", studentGroupId);
    const studentGroupsSub = Meteor.subscribe("StudentGroupsAssociatedWithUser", schoolYear, siteId);

    const loading = !studentsSub.ready() || !studentGroupEnrollmentsSub.ready() || !studentGroupsSub.ready();
    let studentGroup = {};
    if (!loading) {
      studentGroup = StudentGroups.findOne({ _id: studentGroupId });
      const studentIds = StudentGroupEnrollments.find({}, { fields: { studentId: 1 } })
        .fetch()
        .map(studentGroupEnrollment => studentGroupEnrollment.studentId);
      students = Students.find({ _id: { $in: studentIds } }).fetch();
    }
    return {
      loading,
      studentGroup,
      students
    };
  }, [schoolYear, studentGroupId, siteId]);

  return <ClasswideDetail {...trackerData} />;
}

ClasswideDetailWithContainer.propTypes = {
  studentGroupId: PropTypes.string.isRequired,
  siteId: PropTypes.string.isRequired,
  schoolYear: PropTypes.number
};

const ClasswideDetailContainerWithContext = props => {
  const { schoolYear } = useContext(SchoolYearContext) || {};
  return <ClasswideDetailWithContainer {...props} schoolYear={schoolYear} />;
};

ClasswideDetailContainerWithContext.propTypes = {
  studentGroupId: PropTypes.string.isRequired,
  siteId: PropTypes.string.isRequired,
  schoolYear: PropTypes.number
};

export default ClasswideDetailContainerWithContext;

export { ClasswideDetail as PureClasswideDetail };
