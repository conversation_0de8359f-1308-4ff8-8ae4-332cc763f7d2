import React, { use<PERSON><PERSON>back, useContext, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { useTracker } from "meteor/react-meteor-data";
import Highcharts from "highcharts/highstock";
import { <PERSON> } from "react-router-dom";

import moment from "moment";
import { PRINT_OPTIONS } from "/imports/api/constants";
import { AssessmentResults } from "/imports/api/assessmentResults/assessmentResults";
import { StudentGroupEnrollments } from "/imports/api/studentGroupEnrollments/studentGroupEnrollments";

import { Loading } from "../../components/loading.jsx";
import IndividualInterventionProgress from "../../components/student-detail/individual-intervention-progress.jsx";
import ClasswideInterventionProgress from "../../components/student-detail/classwide-intervention-progress.jsx";
import ClasswideInterventionGraphHistory from "../../components/student-detail/classwide-intervention-graph-history";
import StudentLog from "../../components/student-detail/student-log.jsx";
import ScreeningResultsCharts from "../../components/student-detail/screening-results-charts";
import { sortByGradeAndName } from "/imports/api/utilities/utilities";
import { reformatAssessmentResults } from "/imports/api/assessmentResults/helpers";
import DetailHeaderText from "../../layouts/detail-header-text";
import ScrollIndicator from "../../components/scrollIndicator";
import ScrollIndicatorView from "../../components/scrollIndicatorView";
import { areSubscriptionsLoading, isHighSchoolGrade, isOnPrintPage } from "../../utilities";
import DetailTable from "./skillProgress";
import { StudentContext } from "../../../contexts/StudentContext";
import { StudentGroupContext } from "../../../contexts/StudentGroupContext";
import { SchoolYearContext } from "../../../contexts/SchoolYearContext";
import { SiteContext } from "../../../contexts/SiteContext";
import { UserContext } from "../../../contexts/UserContext";

function scroll(idToScrollTo) {
  document.getElementById(idToScrollTo).scrollIntoView({ behavior: "smooth" });
}

function disableChartAnimations() {
  Highcharts.setOptions({
    chart: {
      animation: false
    },
    series: {
      column: {
        animation: false
      },
      line: {
        animation: false
      }
    },
    plotOptions: {
      series: {
        animation: false
      }
    }
  });
}

function StudentDetail(params) {
  const { student: contextStudent, studentId: contextStudentId } = useContext(StudentContext);
  const { studentGroup, studentGroupId, studentsInStudentGroup } = useContext(StudentGroupContext);
  const { schoolYear } = useContext(SchoolYearContext);

  const studentId = params.studentId || contextStudentId;
  const student = params.studentId ? studentsInStudentGroup.find(s => s._id === studentId) : contextStudent;
  // const [groupStats, setGroupStats] = useState({});

  const [scrollLastReload, setScrollLastReload] = useState(new Date().valueOf());
  const [fetchingData, setFetchingData] = useState(false);
  const [individualStudentProgressData, setIndividualStudentProgressData] = useState({});
  const isPrinting = isOnPrintPage();

  const refreshScroll = useCallback(() => {
    setScrollLastReload(new Date().valueOf());
  }, []);

  const getIndividualProgress = useCallback((sgId, sId) => {
    if (sgId && sId) {
      setFetchingData(true);
      Meteor.call(
        "getIndividualStudentDetailData",
        { studentGroupId: sgId, shouldIncludeAllSkills: false, studentId: sId },
        (err, r) => {
          if (!err) {
            setIndividualStudentProgressData(r?.rowData?.[sId] || {});
          }
          setFetchingData(false);
        }
      );
    }
  }, []);

  useEffect(() => {
    if (isPrinting) {
      disableChartAnimations();
    }
    getIndividualProgress(studentGroupId, studentId);
  }, []);

  useEffect(() => {
    getIndividualProgress(studentGroupId, studentId);
  }, [studentId]);

  const shouldRenderClasswideInterventionProgress = useCallback(() => {
    const studentGroupHistory = [...(studentGroup.additionalHistory || []), ...(studentGroup.history || [])];
    return studentGroupHistory?.some(sgh => sgh.type === "classwide");
  }, [studentGroup]);

  const shouldRenderIndividualProgress = useCallback(() => {
    return (
      !params.printCurrentClasswideInterventionGraph &&
      !params.printAllClasswideInterventionGraphs &&
      !params.printOnlyClasswideInterventionSkillGraphs &&
      !params.printStudentClasswideProgressAndScreeningResults
    );
  }, [
    params.printCurrentClasswideInterventionGraph,
    params.printAllClasswideInterventionGraphs,
    params.printOnlyClasswideInterventionSkillGraphs,
    params.printStudentClasswideProgressAndScreeningResults
  ]);

  const printIndividualProgress = hasStudentHistory => {
    if (!shouldRenderIndividualProgress()) {
      return null;
    }

    if (!hasStudentHistory) {
      return (
        <div className={`${params.printAllIndividualInterventionGraphs ? "" : "page-break-before"}`}>
          <h3>Individual Intervention Progress</h3>
          <div className="alert alert-info text-center">No data found for this student</div>
        </div>
      );
    }

    return (
      <IndividualInterventionProgress
        printAllIndividualInterventionGraphs={
          params.printAllIndividualInterventionGraphs || params.printAllStudentDetailsIncludingAllGraphs
        }
        isFirstSection={params.printAllIndividualInterventionGraphs}
        studentGroup={studentGroup}
        student={student}
      />
    );
  };

  const renderScreeningResults = isHS => {
    if (isHS) {
      return null;
    }

    const studentGroupHistory = [...(studentGroup.additionalHistory || []), ...(studentGroup.history || [])];

    return (
      <section
        id="student-detail-screening"
        className={params.printStudentClasswideProgressAndScreeningResults ? "" : "page-break-before"}
      >
        <ScreeningResultsCharts
          classwideBenchmarkScores={params.classwideBenchmarkScores}
          otherScreeningResults={params.otherResults.filter(result => result.type === "benchmark")}
          otherStudentGroups={params.otherStudentGroups}
          studentGroupHistory={studentGroupHistory}
          student={student}
        />
      </section>
    );
  };

  const renderIndividualStudentProgressSection = useCallback(
    isHS => {
      if (!individualStudentProgressData?.columns?.find(c => c.outcome !== null)) {
        return (
          <React.Fragment>
            <h3>Student Skill Progress </h3>
            <div className="alert alert-info text-center">No skill progress data found for this student</div>
          </React.Fragment>
        );
      }

      return (
        <section id="student-detail-individual-progress" className={isHS ? "" : "page-break-before"}>
          <h3>Student Skill Progress </h3>
          <DetailTable
            rowData={individualStudentProgressData}
            componentContext={"studentDetail"}
            studentId={student._id}
          />
        </section>
      );
    },
    [individualStudentProgressData, student]
  );

  const printIndividualStudentProgressAndScreeningResults = isHS => {
    if (!params.printStudentClasswideProgressAndScreeningResults) {
      return null;
    }
    return (
      <React.Fragment>
        {renderScreeningResults(isHS)}
        {renderIndividualStudentProgressSection(isHS)}
      </React.Fragment>
    );
  };

  const printClasswideInterventionProgress = () => {
    if (params.printAllIndividualInterventionGraphs || params.printStudentClasswideProgressAndScreeningResults) {
      return null;
    }

    if (!shouldRenderClasswideInterventionProgress()) {
      return (
        <div data-testid="classwide-intervention-progress-section">
          <h3>Classwide Intervention Progress</h3>
          <div className="alert alert-info text-center">No data found for this student</div>
        </div>
      );
    }

    const studentsInGroup = student ? [student] : [];
    const shouldShowStudentsScores = true;

    return (
      <div>
        {params.printAllClasswideInterventionGraphs || params.printAllStudentDetailsIncludingAllGraphs ? (
          <ClasswideInterventionGraphHistory />
        ) : (
          // TODO get rid of passing student group and student to Classwide Intervention Progress once we move group context to the top (where SG is chosen)
          <ClasswideInterventionProgress
            student={student}
            students={studentsInGroup}
            studentGroup={studentGroup}
            refreshScroll={refreshScroll}
            shouldShowSkillTreeProgress={!isPrinting || params.printCurrentStudentProfilePage}
            shouldShowStudentsScores={shouldShowStudentsScores || isPrinting}
            selectedSkillAssessmentId={params.selectedSkillAssessmentId}
          />
        )}
      </div>
    );
  };

  const renderStudentLog = () => {
    const {
      otherStudentGroups,
      allClasswideScores,
      otherResults,
      studentEnrollments,
      individualInterventionScores
    } = params;
    return (
      <section id="conStudentActivityLog" data-testid="conStudentActivityLog" className="page-break-before">
        <h3>Student Activity Log</h3>
        <StudentLog
          individualInterventionScores={individualInterventionScores}
          allClasswideScores={allClasswideScores}
          studentEnrollments={studentEnrollments}
          otherStudentGroups={otherStudentGroups}
          otherResults={otherResults}
          schoolYear={schoolYear}
          student={student}
        />
      </section>
    );
  };

  if (params.loading || fetchingData || !studentId || !studentGroupId) {
    return <Loading />;
  }

  const isHS = isHighSchoolGrade(studentGroup.grade);
  const containerClass = isPrinting ? "container" : "main-content p-2";
  const { printOption, selectedSkillAssessmentId } = params;

  const studentGroupHistory = [...(studentGroup.additionalHistory || []), ...(studentGroup.history || [])];

  if (isPrinting) {
    const currentDateString = moment().format("DD-MM-YY");
    if (params.printOnlyClasswideInterventionSkillGraphs) {
      document.title = `${studentGroup.name.match(/\((.*)\)/).pop()}_${studentGroupHistory.find(
        classwideInterventionScore => classwideInterventionScore.assessmentId === selectedSkillAssessmentId
      )?.assessmentName ||
        studentGroup.currentClasswideSkill?.assessmentName ||
        studentGroupHistory[studentGroupHistory.length - 1]?.assessmentName}_${currentDateString}`;
    } else {
      const studentName = student ? `${student.identity.name.lastName} ${student.identity.name.firstName}` : "Student";
      const documentName = printOption ? printOption.replace("Print ", "") : "Detail Page";
      const pdfTitle = `${studentName} - ${documentName} ${currentDateString}`;
      document.title = pdfTitle;
      // Set title for iframe parent page
      window.parent.document.querySelector("title").innerHTML = pdfTitle;
      // Set title for iframe page
      window.document.querySelector("title").innerHTML = pdfTitle;
    }
  }

  const hasStudentHistory = student?.history?.length > 0;

  return (
    <div className={containerClass}>
      {isPrinting ? (
        <div className="profile-view m-t-10">
          <DetailHeaderText
            student={student}
            studentGroup={studentGroup}
            displayStats={hasStudentHistory && shouldRenderIndividualProgress()}
          />
        </div>
      ) : null}
      <ul className="nav nav-pills middle-sub-nav">
        {shouldRenderClasswideInterventionProgress() || student.history ? (
          <li>
            <Link to={"#"} onClick={() => scroll("student-detail-intervention-progress")}>
              Intervention Progress
            </Link>
          </li>
        ) : null}
        {!isHS ? (
          <li>
            <Link to={"#"} onClick={() => scroll("student-detail-screening")}>
              Screening Results
            </Link>
          </li>
        ) : null}
        <li>
          <Link to={"#"} onClick={() => scroll("student-detail-individual-progress")}>
            Individual Progress
          </Link>
        </li>
        <li>
          <Link to={"#"} onClick={() => scroll("conStudentActivityLog")}>
            Activity Log
          </Link>
        </li>
      </ul>
      <ScrollIndicator
        container={null}
        targetSelector={".studentDetailContent"}
        indicatorComponent={<ScrollIndicatorView />}
        uniqKey={scrollLastReload}
      >
        <div id="studentDetailContainer" className="studentDetailContent">
          <section id="student-detail-intervention-progress" data-testid="student-detail-intervention-progress">
            {printClasswideInterventionProgress()}
            {printIndividualProgress(hasStudentHistory)}
            {printIndividualStudentProgressAndScreeningResults(isHS)}
          </section>
          {!params.printCurrentClasswideInterventionGraph &&
            !params.printAllClasswideInterventionGraphs &&
            !params.printAllIndividualInterventionGraphs &&
            !params.printOnlyClasswideInterventionSkillGraphs &&
            !params.printStudentClasswideProgressAndScreeningResults && (
              <React.Fragment>
                {renderScreeningResults(isHS)}
                {renderIndividualStudentProgressSection()}
                {renderStudentLog()}
              </React.Fragment>
            )}
        </div>
      </ScrollIndicator>
    </div>
  );
}

function StudentDetailTracker(params) {
  const { student: contextStudent, studentId: contextStudentId } = useContext(StudentContext);
  const { studentGroup, studentGroupId, studentsInStudentGroup } = useContext(StudentGroupContext);
  const { siteId, studentGroupsInSite } = useContext(SiteContext);
  const { schoolYear } = useContext(SchoolYearContext);
  const { user } = useContext(UserContext);

  const studentId = params.studentId || contextStudentId;
  const student = params.studentId ? studentsInStudentGroup.find(s => s._id === params.studentId) : contextStudent;

  const trackerData = useTracker(() => {
    if (!schoolYear || !studentGroupId || !studentId || !student || !user || !studentGroup) {
      return { loading: true };
    }

    let allClasswideScores = [];
    let classwideBenchmarkScores = [];
    let classwideInterventionScores = [];
    let individualInterventionScores = [];
    const assessmentsResultsHandle = Meteor.subscribe("AssessmentResults:FindStudentResultsFromOtherGroups", {
      studentGroupId,
      studentIds: [studentId],
      schoolYear
    });
    const enrollmentsHandle = Meteor.subscribe("StudentGroupEnrollmentsAssociatedWithUser", {
      studentId,
      schoolYear,
      user
    });

    const { grade } = studentGroup;
    const additionalGradeForRules = grade === "K" ? "01" : null;
    const rulesHandle = Meteor.subscribe("GradeLevelRulesByStudentGroup", studentGroupId, additionalGradeForRules);
    const assessmentsHandle = Meteor.subscribe("AssessmentsForGrade", grade);
    const individualRulesHandle = Meteor.subscribe("Rules:IndividualRootRulesByGrade", grade);
    const screeningAssignmentsHandle = Meteor.subscribe("ScreeningAssignmentsByGrade", grade);

    let studentEnrollments = [];
    let otherStudentAssessmentResults = [];
    let otherStudentGroups = [];
    const loading = areSubscriptionsLoading(
      assessmentsResultsHandle,
      enrollmentsHandle,
      rulesHandle,
      assessmentsHandle,
      individualRulesHandle,
      screeningAssignmentsHandle
    );
    if (!loading) {
      const studentGroupHistory = [...(studentGroup.additionalHistory || []), ...(studentGroup.history || [])];

      allClasswideScores = studentGroupHistory.filter(sgh => !!sgh.whenEnded);

      const assessmentResultIdsOfAllClasswideScores = allClasswideScores.map(
        ({ assessmentResultId }) => assessmentResultId
      );
      const assessmentResults = AssessmentResults.find({
        _id: { $nin: assessmentResultIdsOfAllClasswideScores },
        status: "COMPLETED",
        type: { $in: ["classwide", "benchmark"] }
      }).fetch();
      otherStudentAssessmentResults = reformatAssessmentResults(assessmentResults);
      studentEnrollments = StudentGroupEnrollments.find({ studentId }).fetch();
      classwideBenchmarkScores = studentGroupHistory.filter(sgh => sgh.type === "benchmark" && sgh.whenEnded);
      classwideInterventionScores = studentGroupHistory.filter(sgh => sgh.type === "classwide" && sgh.whenEnded);
      individualInterventionScores = (student?.history || []).filter(sh => !!sh.whenEnded);

      const sgIds = studentEnrollments.map(enr => enr.studentGroupId);
      otherStudentGroups = studentGroupsInSite.filter(sg => sgIds.includes(sg._id)).sort(sortByGradeAndName);
    }
    const printOptions = getPrintOptionsFrom(params);

    return {
      allClasswideScores,
      individualInterventionScores,
      classwideBenchmarkScores,
      classwideInterventionScores,
      otherResults: otherStudentAssessmentResults,
      studentEnrollments,
      loading,
      otherStudentGroups,
      ...printOptions
    };
  }, [schoolYear, studentGroup, student, studentGroupId, studentId, siteId, user]);

  return <StudentDetail {...params} {...trackerData} />;
}

StudentDetail.propTypes = {
  allClasswideScores: PropTypes.array, // TODO OF
  classwideBenchmarkScores: PropTypes.array, // TODO OF
  individualInterventionScores: PropTypes.array, // TODO OF
  loading: PropTypes.bool,
  student: PropTypes.object, // TODO SHAPE
  studentGroup: PropTypes.object, // TODO SHAPE
  schoolYear: PropTypes.number,
  otherResults: PropTypes.array,
  otherStudentGroups: PropTypes.array,
  studentEnrollments: PropTypes.array,
  benchmarkWindows: PropTypes.array,
  classwideInterventionScores: PropTypes.array,
  studentsInGroup: PropTypes.array,
  shouldShowStudentsScores: PropTypes.bool,
  printOption: PropTypes.string,
  printCurrentClasswideInterventionGraph: PropTypes.bool,
  printAllClasswideInterventionGraphs: PropTypes.bool,
  printAllIndividualInterventionGraphs: PropTypes.bool,
  printCurrentStudentProfilePage: PropTypes.bool,
  printAllStudentDetailsIncludingAllGraphs: PropTypes.bool,
  printStudentClasswideProgressAndScreeningResults: PropTypes.bool,
  printOnlyClasswideInterventionSkillGraphs: PropTypes.bool,
  selectedSkillAssessmentId: PropTypes.string
};

export default StudentDetailTracker;

export { StudentDetail as PureStudentDetail };

export function getPrintOptionsFrom(props) {
  let printCurrentClasswideInterventionGraph = false;
  let printAllClasswideInterventionGraphs = false;
  let printAllIndividualInterventionGraphs = false;
  let printCurrentStudentProfilePage = false;
  let printAllStudentDetailsIncludingAllGraphs = false;
  let printStudentClasswideProgressAndScreeningResults = false;

  switch (props.printOption) {
    case PRINT_OPTIONS.CURRENT_CLASSWIDE_INTERVENTION:
      printCurrentClasswideInterventionGraph = true;
      break;
    case PRINT_OPTIONS.ALL_CLASSWIDE_INTERVENTION_GRAPHS:
      printAllClasswideInterventionGraphs = true;
      break;
    case PRINT_OPTIONS.ALL_INDIVIDUAL_INTERVENTION_GRAPHS:
      printAllIndividualInterventionGraphs = true;
      break;
    case PRINT_OPTIONS.CURRENT_STUDENT_PROFILE:
      printCurrentStudentProfilePage = true;
      break;
    case PRINT_OPTIONS.ALL_STUDENT_DETAILS:
      printAllStudentDetailsIncludingAllGraphs = true;
      break;
    case PRINT_OPTIONS.STUDENT_CLASSWIDE_PROGRESS_AND_SCREENING_RESULTS:
      printStudentClasswideProgressAndScreeningResults = true;
      break;
    default:
  }
  return {
    printCurrentClasswideInterventionGraph,
    printAllClasswideInterventionGraphs,
    printAllIndividualInterventionGraphs,
    printCurrentStudentProfilePage,
    printAllStudentDetailsIncludingAllGraphs,
    printStudentClasswideProgressAndScreeningResults,
    printOnlyClasswideInterventionSkillGraphs:
      props.printOnlyClasswideInterventionSkillGraphs === "true" || props.printOnlyClasswideInterventionSkillGraphs
  };
}
