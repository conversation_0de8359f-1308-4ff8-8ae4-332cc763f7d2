import React, { useState, useContext } from "react";
import PropTypes from "prop-types";
import { useHistory, Link } from "react-router-dom";
import { Meteor } from "meteor/meteor";
import { useTracker } from "meteor/react-meteor-data";
import { every } from "lodash";
import Alert from "react-s-alert";

import { isExternalRostering } from "/imports/ui/utilities";
import Header from "/imports/ui/components/admin-view/admin-header";

import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { Organizations } from "/imports/api/organizations/organizations";
import { trimValuesInObject } from "/imports/api/utilities/utilities";

import AddTeacherForm from "./add-teacher-form";
import SelectClassForm from "./select-class-form";
import ConfirmModal from "./confirm-modal";
import { isSingleSchoolDataAdmin } from "./utilities";
import Loading from "../../components/loading";
import { SchoolYearContext } from "/imports/contexts/SchoolYearContext";

const defaultTeacher = { lastName: "", firstName: "", localId: "", email: "" };

const AddTeacherToClass = ({ orgid, siteId }) => {
  const history = useHistory();
  const { schoolYear } = useContext(SchoolYearContext);

  const [newTeacher, setNewTeacher] = useState(defaultTeacher);
  const [currentStudentGroup, setCurrentStudentGroup] = useState({
    name: "Select a class",
    sectionId: "",
    grade: ""
  });
  const [teacherType, setTeacherType] = useState("");
  const [isSending, setIsSending] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isTeacherFormValid, setIsTeacherFormValid] = useState(false);

  const backToManageTeachers = () => {
    history.push(`/data-admin/manage-accounts/${orgid}`);
  };

  const updateNewTeacherData = (updatedData, isTeacherFormValidValue = false) => {
    setNewTeacher(prevTeacher => ({ ...prevTeacher, ...updatedData }));
    setIsTeacherFormValid(isTeacherFormValidValue);
  };

  const updateCurrentStudentGroup = updatedData => {
    setCurrentStudentGroup(prevGroup => ({ ...prevGroup, ...updatedData }));
  };

  const updateTeacherType = updatedTeacherType => {
    setTeacherType(updatedTeacherType);
  };

  const isFormValid = () => {
    const isValidTeacher = every(newTeacher);
    const isValidClass =
      currentStudentGroup.name && currentStudentGroup.sectionId && currentStudentGroup.grade && teacherType;
    return isValidTeacher && isValidClass;
  };

  const addTeacher = () => {
    if (isFormValid()) {
      const teacher = trimValuesInObject(newTeacher);
      Meteor.call(
        "addTeacher",
        {
          teacher,
          orgid: currentStudentGroup.orgid,
          siteId: currentStudentGroup.siteId,
          schoolYear: currentStudentGroup.schoolYear
        },
        (addTeacherErr, userId) => {
          if (!addTeacherErr) {
            const currentSecondaryTeachers = currentStudentGroup.secondaryTeachers || [];
            if (teacherType === "Primary") {
              currentSecondaryTeachers.push(currentStudentGroup.ownerIds[0]);
            } else if (teacherType === "Secondary") {
              currentSecondaryTeachers.push(userId);
            }

            Meteor.call(
              "saveGroupData",
              {
                studentGroupId: currentStudentGroup._id,
                newTeacherId: userId,
                orgid: currentStudentGroup.orgid,
                siteId: currentStudentGroup.siteId,
                studentGroupName: currentStudentGroup.name,
                secondaryTeachers: currentSecondaryTeachers.filter(t => t), // filter out falsy values
                grade: currentStudentGroup.grade,
                hasGradeChanged: false,
                hasPrimaryTeacherChanged: teacherType === "Primary"
              },
              saveGroupDataErr => {
                if (!saveGroupDataErr) {
                  Alert.success(
                    `New ${teacherType.toLowerCase()} teacher successfully added to ${
                      currentStudentGroup.name
                    } student group`,
                    { timeout: 5000 }
                  );
                  backToManageTeachers();
                } else {
                  Alert.error(saveGroupDataErr.message, { timeout: 5000 });
                }
              }
            );
          } else {
            Alert.error(addTeacherErr.message, { timeout: 5000 });
          }
          setIsSending(false);
        }
      );
      setIsSending(true);
    }
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const openModal = () => {
    setIsModalOpen(true);
  };

  const onAddTeacher = () => {
    if (!isTeacherFormValid) {
      Alert.error("Please fix teacher form before proceeding", { timeout: 3000 });
    } else if (isFormValid()) {
      if (teacherType === "Primary") {
        openModal();
      } else {
        addTeacher();
      }
    }
  };

  // Data subscription using useTracker
  const { loading, studentGroups, org } = useTracker(() => {
    const sitesHandler = Meteor.subscribe("Sites", orgid);
    const studentGroupsHandler = Meteor.subscribe("StudentGroups:PerOrg", orgid, schoolYear);
    const organization = Organizations.findOne({ _id: orgid }, { fields: { rostering: 1 } });

    const isLoading = !sitesHandler.ready() || !studentGroupsHandler.ready();

    let studentGroupsData = [];

    if (!isLoading) {
      studentGroupsData = StudentGroups.find({ siteId }, { fields: { history: 0 } }).fetch();
    }

    return {
      loading: isLoading,
      org: organization,
      studentGroups: studentGroupsData
    };
  }, [orgid, siteId, schoolYear]);

  if (loading) {
    return <Loading />;
  }

  return (
    <div className="workspace-container">
      <div className="d-flex mt-1">
        {!isSingleSchoolDataAdmin() ? (
          <Link className="btn btn-outline-blue btn-xs me-2" to={`/data-admin/dashboard/${orgid}`}>
            <i className="fa fa-caret-left" /> Back to All Schools
          </Link>
        ) : null}
        {siteId && siteId !== "undefined" ? (
          <Link
            className="btn btn-outline-blue btn-xs me-2"
            to={`/data-admin/manage-group/students/${orgid}/site/${siteId}`}
          >
            <i className="fa fa-caret-left" /> Back to School
          </Link>
        ) : null}
        <Link className="btn btn-outline-blue btn-xs me-2" to={`/data-admin/manage-accounts/${orgid}`}>
          <i className="fa fa-caret-left" /> Back to Manage Accounts
        </Link>
      </div>

      <Header keyLabel="schoolOverview" headerTitle="Add Teacher" />

      <div className="card-box-wrapper mt-2">
        <div className="p-3">
          <div className="card-box">
            {isExternalRostering(org?.rostering) && (
              <div className="alert alert-warning">
                Auto-rostering is currently enabled. The teacher being added may be removed during the next roster sync.
              </div>
            )}
            <div className="addTeacherBackground">
              <AddTeacherForm teacher={newTeacher} onChange={updateNewTeacherData} orgid={orgid} />
              <SelectClassForm
                currentStudentGroup={currentStudentGroup}
                studentGroups={studentGroups}
                teacherType={teacherType}
                onSelectTeacherType={updateTeacherType}
                onSelectCurrentStudentGroup={updateCurrentStudentGroup}
              />
            </div>
            {isSending ? (
              <React.Fragment>
                <Loading inline={true} />
              </React.Fragment>
            ) : (
              <React.Fragment>
                <button
                  className="btn btn-outline-blue btn-wide btn-xs me-2"
                  onClick={onAddTeacher}
                  disabled={!isFormValid()}
                >
                  Add teacher to the selected student group
                </button>
                {isModalOpen ? (
                  <ConfirmModal
                    showModal={isModalOpen}
                    onCloseModal={closeModal}
                    confirmAction={addTeacher}
                    headerText={`Are you sure you want to add a new ${teacherType.toLowerCase()} to ${
                      currentStudentGroup.name
                    } student group?`}
                    bodyQuestion={
                      "By making a new primary teacher, the current primary teacher will become a secondary teacher."
                    }
                    confirmText="Yes, add new teacher"
                  />
                ) : null}
              </React.Fragment>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

AddTeacherToClass.propTypes = {
  orgid: PropTypes.string.isRequired,
  siteId: PropTypes.string.isRequired
};

export default AddTeacherToClass;
