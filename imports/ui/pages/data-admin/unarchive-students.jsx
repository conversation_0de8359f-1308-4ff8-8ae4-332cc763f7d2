import React, { useContext } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { useTracker } from "meteor/react-meteor-data";
import { Link } from "react-router-dom";

import Header from "/imports/ui/components/admin-view/admin-header";
import Loading from "/imports/ui/components/loading";
import { isExternalRostering } from "/imports/ui/utilities";
import { Students } from "/imports/api/students/students";
import { StudentGroupEnrollments } from "/imports/api/studentGroupEnrollments/studentGroupEnrollments";
import { OrganizationContext } from "/imports/contexts/OrganizationContext";
import { SchoolYearContext } from "/imports/contexts/SchoolYearContext";

import ManageStudents from "./manage-students";
import { isSingleSchoolDataAdmin } from "./utilities";

const UnarchiveStudents = ({ orgid, siteId, students, rostering, loading }) => {
  if (loading) {
    return <Loading />;
  }

  return (
    <div className="workspace-container">
      <div className="d-flex mt-1">
        {!isSingleSchoolDataAdmin() ? (
          <Link className="btn btn-outline-blue btn-xs me-2" to={`/data-admin/dashboard/${orgid}`}>
            <i className="fa fa-caret-left" /> Back to All Schools
          </Link>
        ) : null}
        <Link className="btn btn-outline-blue btn-xs" to={`/data-admin/manage-group/students/${orgid}/site/${siteId}`}>
          <i className="fa fa-caret-left" /> Back to School
        </Link>
      </div>

      <Header keyLabel="schoolOverview" headerTitle="Unarchive Students" />

      <div className="card-box-wrapper mt-2">
        <ManageStudents
          students={students}
          siteId={siteId}
          orgid={orgid}
          manageView="unarchive"
          isExternalRostering={isExternalRostering(rostering)}
        />
      </div>
    </div>
  );
};

UnarchiveStudents.propTypes = {
  loading: PropTypes.bool,
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  students: PropTypes.array,
  rostering: PropTypes.string
};

const UnarchiveStudentsWithData = ({ orgid = "", siteId }) => {
  const { orgId, org } = useContext(OrganizationContext);
  const { rostering } = org || {};
  const { schoolYear } = useContext(SchoolYearContext);

  // Use orgid from props, fallback to context
  const resolvedOrgId = orgid || orgId;

  const { loading, students } = useTracker(() => {
    if (!schoolYear) {
      return { loading: true, rostering: "", students: [] };
    }

    const studentGroupEnrollmentsHandler = Meteor.subscribe("StudentGroupEnrollments:PerOrg:Archived", resolvedOrgId);
    const studentsHandler = Meteor.subscribe("Students:PerSiteInYear", {
      orgid: resolvedOrgId,
      siteId,
      schoolYear
    });

    const isLoading = !studentGroupEnrollmentsHandler.ready() || !studentsHandler.ready();

    let studentsData = [];

    if (!isLoading) {
      let studentEnrollments = StudentGroupEnrollments.find({}).fetch();
      studentEnrollments = studentEnrollments.filter(studentEnrollment => {
        const thisStudentIsActive = studentEnrollments.find(
          se => studentEnrollment.studentId === se.studentId && se.isActive
        );
        return !thisStudentIsActive;
      });

      studentEnrollments = studentEnrollments.filter(studentEnrollment => studentEnrollment.isActive === false);

      const studentIds = studentEnrollments.map(enrollment => enrollment.studentId);
      studentsData = Students.find(
        { _id: { $in: studentIds } },
        { sort: { "identity.name.lastName": 1, "identity.name.firstName": 1 } }
      ).fetch();
    }

    return {
      loading: isLoading,
      rostering,
      students: studentsData
    };
  }, [resolvedOrgId, siteId, schoolYear]);

  return (
    <UnarchiveStudents
      orgid={resolvedOrgId}
      siteId={siteId}
      students={students}
      rostering={rostering}
      loading={loading}
    />
  );
};

UnarchiveStudentsWithData.propTypes = {
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  schoolYear: PropTypes.number
};

export default UnarchiveStudentsWithData;
