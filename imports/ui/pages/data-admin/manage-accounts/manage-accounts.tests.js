import { Meteor } from "meteor/meteor";
import React from "react";
import MockDate from "mockdate";
import { cleanup, fireEvent, within, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import td from "testdouble";
import { Router } from "react-router-dom";
import { PureManageTeachersView as ManageTeachersView } from "./manage-accounts-view.jsx";
import getAllUsersForManageTeachers from "../../../../test-helpers/data/manage-accounts";
import { renderWithRouter } from "../../../../../tests/helpers/testUtils";
import { AppDataProvider } from "../../../routing/AppDataContext";
import { user } from "../../../../test-helpers/data/users";

// Mock getCurrentSchoolYear to return a resolved value
jest.mock("/imports/api/utilities/utilities", () => ({
  ...jest.requireActual("/imports/api/utilities/utilities"),
  getCurrentSchoolYear: jest.fn(() => Promise.resolve(2019)),
  getMeteorUser: jest.fn(() => ({ profile: { orgid: "test_organization_id" } })),
  getMeteorUserSync: jest.fn(() => ({ profile: { orgid: "test_organization_id" } })),
  getMeteorUserId: jest.fn(() => "test_user_id")
}));

function hoverOver(element) {
  fireEvent(
    element,
    new MouseEvent("focus", {
      bubbles: true,
      cancelable: true
    })
  );
}

function getTeacherIds(usersData) {
  return usersData.teachers.map(t => t._id);
}

function getPropsWithAddedSite(defaultManageViewProps, orgid) {
  const otherSiteName = "Other Site";
  const otherSchoolId = "someOtherSchool";
  const propsWithAddedSite = {
    ...defaultManageViewProps,
    schools: [
      defaultManageViewProps.schools[0],
      {
        _id: otherSchoolId,
        grades: ["01", "02"],
        isVisible: true,
        name: otherSiteName,
        orgid,
        schoolYear: 2019
      }
    ]
  };
  return { otherSiteName, otherSchoolId, propsWithAddedSite };
}

describe("Manage Accounts", () => {
  const schoolYear = 2019;
  jest.mock("../../../../api/utilities/utilities", () => ({
    ...jest.requireActual("../../../../api/utilities/utilities"),
    getCurrentSchoolYear: jest.fn(() => Promise.resolve(schoolYear))
  }));
  beforeAll(() => {
    MockDate.set("2018-12-20");
  });
  afterAll(() => {
    jest.restoreAllMocks();
    MockDate.reset();
  });
  const selectedSchoolId = "test_elementary_site_id";
  const orgid = "test_organization_id";
  const teacherRole = "arbitraryIdteacher";
  const adminRole = "arbitraryIdadmin";
  const dataAdminRole = "arbitraryIddataAdmin";
  const users = getAllUsersForManageTeachers(selectedSchoolId);
  const siteName = "Test Elementary Site";
  const schools = [
    {
      _id: selectedSchoolId,
      grades: ["01", "02"],
      isVisible: true,
      name: siteName,
      orgid,
      schoolYear: 2019
    }
  ];
  const defaultManageViewProps = {
    selectedUserIds: [],
    schools,
    selectedSchoolId,
    orgid,
    loading: false,
    history: { location: {} }
  };
  const responseWithNoUsers = {
    teachers: [],
    admins: [],
    dataAdmins: [],
    archivedUserIdsByRoleBySiteId: {
      arbitraryIdteacher: { [selectedSchoolId]: [] },
      arbitraryIdadmin: { [selectedSchoolId]: [] },
      arbitraryIddataAdmin: { allSites: [] }
    },
    activeUserIdsByRoleBySiteId: {
      arbitraryIdteacher: { [selectedSchoolId]: [] },
      arbitraryIdadmin: { [selectedSchoolId]: [] },
      arbitraryIddataAdmin: { allSites: [] }
    }
  };
  const responseWithTeachers = {
    ...responseWithNoUsers,
    teachers: users.filter(u => u.profile.siteAccess.find(sa => sa.role === teacherRole)),
    archivedUserIdsByRoleBySiteId: {
      arbitraryIdteacher: { [selectedSchoolId]: [] },
      arbitraryIdadmin: { [selectedSchoolId]: [] },
      arbitraryIddataAdmin: { allSites: [] }
    },
    activeUserIdsByRoleBySiteId: {
      arbitraryIdteacher: { [selectedSchoolId]: [users[0]._id, users[1]._id] },
      arbitraryIdadmin: { [selectedSchoolId]: [] },
      arbitraryIddataAdmin: { allSites: [] }
    }
  };
  const responseWithArchivedTeacher = {
    ...responseWithTeachers,
    archivedUserIdsByRoleBySiteId: {
      arbitraryIdteacher: { [selectedSchoolId]: [users[0]._id] },
      arbitraryIdadmin: { [selectedSchoolId]: [] },
      arbitraryIddataAdmin: { allSites: [] }
    },
    activeUserIdsByRoleBySiteId: {
      arbitraryIdteacher: { [selectedSchoolId]: [users[1]._id] },
      arbitraryIdadmin: { [selectedSchoolId]: [] },
      arbitraryIddataAdmin: { allSites: [] }
    }
  };
  const responseWithAllUsers = {
    ...responseWithTeachers,
    admins: users.filter(u => u.profile.siteAccess.find(sa => sa.role === adminRole)),
    dataAdmins: users.filter(u => u.profile.siteAccess.find(sa => sa.role === dataAdminRole)),
    archivedUserIdsByRoleBySiteId: {
      arbitraryIdteacher: { [selectedSchoolId]: [] },
      arbitraryIdadmin: { [selectedSchoolId]: [] },
      arbitraryIddataAdmin: { allSites: [] }
    },
    activeUserIdsByRoleBySiteId: {
      arbitraryIdteacher: { [selectedSchoolId]: [users[0]._id, users[1]._id, users[2]._id] },
      arbitraryIdadmin: { [selectedSchoolId]: [users[3]._id] },
      arbitraryIddataAdmin: { allSites: [users[4]._id] }
    }
  };
  let meteorCallSpy;
  describe("component", () => {
    beforeEach(() => {
      meteorCallSpy = td.replace(Meteor, "call");
      // Mock Meteor methods and subscriptions
      Meteor.user = jest.fn(() => ({ profile: { orgid: "test_organization_id" } }));
      Meteor.subscribe = jest.fn(() => ({ ready: () => true }));
    });
    afterEach(() => {
      cleanup();
      td.reset();
    });

    it("should show loader when getting data", () => {
      const { getByTestId } = renderWithRouter(<ManageTeachersView {...defaultManageViewProps} />);

      expect(getByTestId("loading-icon")).toBeVisible();
    });

    it("should display message for no users in Manage Accounts table", async () => {
      td.when(Meteor.call("users:getManageTeacherData", orgid, td.matchers.isA(Function))).thenDo((...args) => {
        const callback = args[args.length - 1];
        callback(null, responseWithNoUsers);
      });
      const { getByText } = renderWithRouter(
        <ManageTeachersView
          {...defaultManageViewProps}
          activeUserIdsByRoleId={{
            arbitraryIdteacher: [],
            arbitraryIdadmin: [],
            arbitraryIddataAdmin: []
          }}
        />
      );

      await waitFor(() => {
        expect(getByText("No active teachers found in the selected site")).toBeVisible();
      });
      getByText("Coaches").click();
      expect(getByText("No active coaches found in the selected site")).toBeVisible();
      getByText("Data Admins").click();
      expect(getByText("No active data admins found in the selected site")).toBeVisible();
    });
    it("should display proper headers for Manage Accounts table", async () => {
      td.when(Meteor.call("users:getManageTeacherData", orgid, td.matchers.isA(Function))).thenDo((...args) => {
        const callback = args[args.length - 1];
        callback(null, responseWithTeachers);
      });
      const { getByText } = renderWithRouter(<ManageTeachersView {...defaultManageViewProps} />);

      await waitFor(() => {
        expect(getByText("Teacher ID")).toBeVisible();
      });
      expect(getByText("Last Name")).toBeVisible();
      expect(getByText("First Name")).toBeVisible();
      expect(getByText("Email")).toBeVisible();
      expect(getByText("Invite Email Sent")).toBeVisible();
      expect(getByText("Account Activated")).toBeVisible();
    });

    it("should display proper headers for manage coaches table", async () => {
      td.when(Meteor.call("users:getManageTeacherData", orgid, td.matchers.isA(Function))).thenDo((...args) => {
        const callback = args[args.length - 1];
        callback(null, responseWithAllUsers);
      });
      const { getByText } = renderWithRouter(<ManageTeachersView {...defaultManageViewProps} />);

      await waitFor(() => {
        expect(getByText("Coaches")).toBeVisible();
      });
      getByText("Coaches").click();

      expect(getByText("Teacher ID")).toBeVisible();
      expect(getByText("Last Name")).toBeVisible();
      expect(getByText("First Name")).toBeVisible();
      expect(getByText("Email")).toBeVisible();
      expect(getByText("Invite Email Sent")).toBeVisible();
      expect(getByText("Account Activated")).toBeVisible();
    });

    it("should display proper headers for manage data admins table", async () => {
      td.when(Meteor.call("users:getManageTeacherData", orgid, td.matchers.isA(Function))).thenDo((...args) => {
        const callback = args[args.length - 1];
        callback(null, responseWithAllUsers);
      });
      const { getByText } = renderWithRouter(<ManageTeachersView {...defaultManageViewProps} />);

      await waitFor(() => {
        expect(getByText("Data Admins")).toBeVisible();
      });
      getByText("Data Admins").click();

      expect(getByText("Teacher ID")).toBeVisible();
      expect(getByText("Last Name")).toBeVisible();
      expect(getByText("First Name")).toBeVisible();
      expect(getByText("Email")).toBeVisible();
      expect(getByText("Invite Email Sent")).toBeVisible();
      expect(getByText("Account Activated")).toBeVisible();
    });

    it("should only display teachers by default", async () => {
      const firstTeacherName = responseWithAllUsers.teachers[0].profile.name.first;
      const secondTeacherName = responseWithAllUsers.teachers[1].profile.name.first;
      const adminName = responseWithAllUsers.admins[0].profile.name.first;

      td.when(Meteor.call("users:getManageTeacherData", orgid, td.matchers.isA(Function))).thenDo((...args) => {
        const callback = args[args.length - 1];
        callback(null, responseWithAllUsers);
      });

      const { getByTestId: getByTestIdParent, getByDisplayValue, queryByDisplayValue } = renderWithRouter(
        <ManageTeachersView {...defaultManageViewProps} />
      );

      await waitFor(() => {
        expect(getByDisplayValue(firstTeacherName)).toBeVisible();
      });
      expect(getByDisplayValue(secondTeacherName)).toBeVisible();
      expect(queryByDisplayValue(adminName)).toBeNull();
      expect(getByTestIdParent("role-selector-id_arbitraryIdteacher")).toHaveClass("active");
      expect(getByTestIdParent("role-selector-id_arbitraryIdadmin")).not.toHaveClass("active");
      expect(getByTestIdParent("role-selector-id_arbitraryIddataAdmin")).not.toHaveClass("active");
    });

    it("should make impossible removing group ownership when no teacher is selected", async () => {
      td.when(Meteor.call("users:getManageTeacherData", orgid, td.matchers.isA(Function))).thenDo((...args) => {
        const callback = args[args.length - 1];
        callback(null, responseWithTeachers);
      });
      const { getByText, queryByText } = renderWithRouter(<ManageTeachersView {...defaultManageViewProps} />);

      await waitFor(() => {
        const manageButton = getByText("Manage");
        expect(manageButton).toHaveAttribute("disabled");
      });

      const manageButton = getByText("Manage");
      hoverOver(manageButton);
      expect(queryByText("Archive Selected Teachers")).toBeNull();
      expect(queryByText("Send Invites to Selected")).toBeNull();
    });

    it("should not display Archived Teachers table if no archived teachers are available", async () => {
      td.when(Meteor.call("users:getManageTeacherData", orgid, td.matchers.isA(Function))).thenDo((...args) => {
        const callback = args[args.length - 1];
        callback(null, responseWithTeachers);
      });

      const { queryByText } = renderWithRouter(<ManageTeachersView {...defaultManageViewProps} />);

      await waitFor(() => {
        expect(queryByText("Archived Teachers")).toBeNull();
      });
    });

    it("should display Archived Teachers table when archived teachers are available", async () => {
      const [archivedTeacherId, activeTeacherId] = getTeacherIds(responseWithTeachers);
      td.when(Meteor.call("users:getManageTeacherData", orgid, td.matchers.isA(Function))).thenDo((...args) => {
        const callback = args[args.length - 1];
        callback(null, responseWithArchivedTeacher);
      });

      const { getByTestId, queryByTestId } = renderWithRouter(<ManageTeachersView {...defaultManageViewProps} />);

      await waitFor(() => {
        expect(getByTestId(`${archivedTeacherId}_Archived`)).toBeVisible();
      });
      expect(queryByTestId(`${archivedTeacherId}_Active`)).toBeNull();
      expect(getByTestId(`${activeTeacherId}_Active`)).toBeVisible();
      expect(queryByTestId(`${activeTeacherId}_Archived`)).toBeNull();
    });

    it("should only display admins when switching to Coaches tab", async () => {
      const teacherName = responseWithAllUsers.teachers[0].profile.name.first;
      td.when(Meteor.call("users:getManageTeacherData", orgid, td.matchers.isA(Function))).thenDo((...args) => {
        const callback = args[args.length - 1];
        callback(null, responseWithAllUsers);
      });
      const { getByTestId: getByTestIdParent, getByTestId, queryByDisplayValue } = renderWithRouter(
        <ManageTeachersView {...defaultManageViewProps} />
      );

      await waitFor(() => {
        expect(getByTestIdParent("manage-accounts-btn-list")).toBeVisible();
      });

      const { getByText } = within(getByTestIdParent("manage-accounts-btn-list"));
      getByText("Coaches").click();

      expect(getByTestId("lastName_admin_user")).toBeVisible();
      expect(queryByDisplayValue(teacherName)).toBeNull();
    });

    it("should only display Data Admins when switching to Data Admins tab", async () => {
      const adminName = responseWithAllUsers.admins[0].profile.name.first;
      td.when(Meteor.call("users:getManageTeacherData", orgid, td.matchers.isA(Function))).thenDo((...args) => {
        const callback = args[args.length - 1];
        callback(null, responseWithAllUsers);
      });
      const { getByTestId: getByTestIdParent, queryByDisplayValue } = renderWithRouter(
        <ManageTeachersView {...defaultManageViewProps} />
      );

      await waitFor(() => {
        expect(getByTestIdParent("manage-accounts-btn-list")).toBeVisible();
      });

      const { getByText } = within(getByTestIdParent("manage-accounts-btn-list"));
      getByText("Data Admins").click();

      expect(getByTestIdParent("lastName_data_admin_user_id")).toBeVisible();
      expect(queryByDisplayValue(adminName)).toBeNull();
    });

    it("should clear selected users when changing active role", async () => {
      const [firstTeacherId] = getTeacherIds(responseWithTeachers);
      td.when(Meteor.call("users:getManageTeacherData", orgid, td.matchers.isA(Function))).thenDo((...args) => {
        const callback = args[args.length - 1];
        callback(null, responseWithTeachers);
      });
      const { getByTestId, queryByTestId, getByText } = renderWithRouter(
        <ManageTeachersView {...defaultManageViewProps} />
      );

      await waitFor(() => {
        expect(getByTestId(`unchecked_${firstTeacherId}`)).toBeVisible();
      });

      getByTestId(`unchecked_${firstTeacherId}`).click();
      expect(getByTestId(`checked_${firstTeacherId}`)).toBeVisible();
      getByText("Coaches").click();
      expect(getByTestId("role-selector-id_arbitraryIdadmin")).toHaveClass("active");

      getByText("Teachers").click();

      expect(getByTestId(`unchecked_${firstTeacherId}`)).toBeVisible();
      expect(queryByTestId(`checked_${firstTeacherId}`)).toBeNull();
    });

    it("should select / deselect all users using select all checkbox", async () => {
      const [firstTeacherId, secondTeacherId] = getTeacherIds(responseWithTeachers);
      td.when(Meteor.call("users:getManageTeacherData", orgid, td.matchers.isA(Function))).thenDo((...args) => {
        const callback = args[args.length - 1];
        callback(null, responseWithTeachers);
      });
      const { getByTestId } = renderWithRouter(<ManageTeachersView {...defaultManageViewProps} />);

      await waitFor(() => {
        expect(getByTestId("selectAll")).toBeVisible();
      });

      getByTestId("selectAll").click();
      expect(getByTestId(`checked_${firstTeacherId}`)).toBeVisible();
      expect(getByTestId(`checked_${secondTeacherId}`)).toBeVisible();

      getByTestId("selectAll").click();
      expect(getByTestId(`unchecked_${firstTeacherId}`)).toBeVisible();
      expect(getByTestId(`unchecked_${secondTeacherId}`)).toBeVisible();
    });

    it("should clear all selected users when changing the selected role to 'Coaches'", async () => {
      const [firstTeacherId, secondTeacherId] = getTeacherIds(responseWithTeachers);
      td.when(Meteor.call("users:getManageTeacherData", orgid, td.matchers.isA(Function))).thenDo((...args) => {
        const callback = args[args.length - 1];
        callback(null, responseWithTeachers);
      });
      const { getByTestId, getByText, queryByTestId } = renderWithRouter(
        <ManageTeachersView {...defaultManageViewProps} />
      );

      await waitFor(() => {
        expect(getByTestId("selectAll")).toBeVisible();
      });

      getByTestId("selectAll").click();
      expect(getByTestId(`checked_${firstTeacherId}`)).toBeVisible();
      expect(getByTestId(`checked_${secondTeacherId}`)).toBeVisible();
      getByText("Coaches").click();
      expect(getByTestId("role-selector-id_arbitraryIdadmin")).toHaveClass("active");
      getByText("Teachers").click();
      expect(getByTestId(`unchecked_${firstTeacherId}`)).toBeVisible();
      expect(queryByTestId(`checked_${firstTeacherId}`)).toBeNull();
      expect(getByTestId(`unchecked_${secondTeacherId}`)).toBeVisible();
      expect(queryByTestId(`checked_${secondTeacherId}`)).toBeNull();
    });

    it("should clear all selected users when changing the selected role to 'Data Admins'", async () => {
      const [firstTeacherId, secondTeacherId] = getTeacherIds(responseWithTeachers);
      td.when(Meteor.call("users:getManageTeacherData", orgid, td.matchers.isA(Function))).thenDo((...args) => {
        const callback = args[args.length - 1];
        callback(null, responseWithTeachers);
      });
      const { getByTestId, getByText, queryByTestId } = renderWithRouter(
        <ManageTeachersView {...defaultManageViewProps} />
      );

      await waitFor(() => {
        expect(getByTestId("selectAll")).toBeVisible();
      });

      getByTestId("selectAll").click();
      expect(getByTestId(`checked_${firstTeacherId}`)).toBeVisible();
      expect(getByTestId(`checked_${secondTeacherId}`)).toBeVisible();
      getByText("Data Admins").click();
      expect(getByTestId("role-selector-id_arbitraryIddataAdmin")).toHaveClass("active");
      getByText("Teachers").click();
      expect(getByTestId(`unchecked_${firstTeacherId}`)).toBeVisible();
      expect(queryByTestId(`checked_${firstTeacherId}`)).toBeNull();
      expect(getByTestId(`unchecked_${secondTeacherId}`)).toBeVisible();
      expect(queryByTestId(`checked_${secondTeacherId}`)).toBeNull();
    });

    it("should clear selected users when changing active site", async () => {
      const [firstTeacherId] = getTeacherIds(responseWithTeachers);
      const { otherSiteName, otherSchoolId, propsWithAddedSite } = getPropsWithAddedSite(defaultManageViewProps, orgid);
      td.when(Meteor.call("users:getManageTeacherData", orgid, td.matchers.isA(Function))).thenDo((...args) => {
        const callback = args[args.length - 1];
        callback(null, responseWithTeachers);
      });
      const { getByText, getByTestId, queryByTestId, rerender, history } = renderWithRouter(
        <ManageTeachersView {...propsWithAddedSite} />
      );

      await waitFor(() => {
        expect(getByTestId(`unchecked_${firstTeacherId}`)).toBeVisible();
      });

      getByTestId(`unchecked_${firstTeacherId}`).click();
      expect(getByTestId(`checked_${firstTeacherId}`)).toBeVisible();
      getByText(otherSiteName).click();

      const propsWithChangedActiveSchool = {
        ...propsWithAddedSite,
        selectedSchoolId: otherSchoolId
      };

      // Test data for rerender
      const testUser = user("Test", "User", "teacher");
      const testEnv = { METEOR_ENVIRONMENT: "test" };
      const testSchoolYear = 2019;

      rerender(
        <Router history={history}>
          <AppDataProvider user={testUser} env={testEnv} schoolYear={testSchoolYear} orgid="test_organization_id">
            <ManageTeachersView {...propsWithChangedActiveSchool} />
          </AppDataProvider>
        </Router>
      );

      rerender(
        <Router history={history}>
          <AppDataProvider user={testUser} env={testEnv} schoolYear={testSchoolYear} orgid="test_organization_id">
            <ManageTeachersView {...propsWithAddedSite} />
          </AppDataProvider>
        </Router>
      );
      expect(queryByTestId(`checked_${firstTeacherId}`)).toBeNull();
      expect(getByTestId(`unchecked_${firstTeacherId}`)).toBeVisible();
    });

    it("should show a confirmation modal when none of the removed users is a group owner", async () => {
      const [firstTeacherId, secondTeacherId] = getTeacherIds(responseWithTeachers);
      td.when(Meteor.call("users:getManageTeacherData", orgid, td.matchers.isA(Function))).thenDo((...args) => {
        const callback = args[args.length - 1];
        callback(null, responseWithTeachers);
      });
      td.when(
        Meteor.call(
          "StudentGroups:getGroupsOwnedByUsers",
          [firstTeacherId, secondTeacherId],
          orgid,
          selectedSchoolId,
          td.matchers.isA(Function)
        )
      ).thenDo((...args) => {
        const callback = args[args.length - 1];
        callback(null, []);
      });
      const { getByText, getByTestId: getByTestIdParent, queryByTestId } = renderWithRouter(
        <ManageTeachersView {...defaultManageViewProps} />
      );

      await waitFor(() => {
        expect(getByTestIdParent(`unchecked_${firstTeacherId}`)).toBeVisible();
      });

      getByTestIdParent(`unchecked_${firstTeacherId}`).click();
      getByTestIdParent(`unchecked_${secondTeacherId}`).click();
      hoverOver(getByText("Manage"));

      getByText("Archive Selected Teachers").click();

      expect(getByTestIdParent("confirmModalDialog")).toBeVisible();
      getByText("Confirm").click();
      td.verify(
        meteorCallSpy(
          "users:removeGroupOwnershipAndDeactivateSiteAccess",
          [firstTeacherId, secondTeacherId],
          orgid,
          selectedSchoolId,
          td.matchers.isA(Function)
        )
      );
      expect(queryByTestId("confirmModalDialog")).toBeNull();
    });

    describe("assignUserModal", () => {
      const {
        teachers: [
          {
            _id: firstTeacherId,
            profile: {
              name: { first: firstTeacherName, last: firstTeacherLastName }
            }
          },
          {
            _id: secondTeacherId,
            profile: {
              name: { first: secondTeacherName, last: secondTeacherLastName }
            }
          },
          {
            _id: thirdTeacherId,
            profile: {
              name: { first: thirdTeacherName, last: thirdTeacherLastName }
            }
          }
        ],
        admins: [
          {
            _id: adminId,
            profile: {
              name: { first: adminFirstName }
            }
          }
        ]
      } = responseWithAllUsers;
      const firstGroupId = "firstGroupId";
      const firstGroupName = "Math (21)";
      const secondGroupId = "secondGroupId";
      const secondGroupName = "Math (ATM)";
      const firstGroup = {
        _id: firstGroupId,
        ownerIds: [firstTeacherId],
        name: firstGroupName,
        sectionId: "21",
        grade: "01",
        secondaryTeachers: [secondTeacherId, thirdTeacherId]
      };
      const secondGroup = {
        _id: secondGroupId,
        ownerIds: [firstTeacherId],
        name: secondGroupName,
        sectionId: "ATM",
        grade: "K",
        secondaryTeachers: [secondTeacherId, thirdTeacherId]
      };
      const studentGroups = [firstGroup, secondGroup];

      let getByText;
      let getByTestId;
      let queryByTestId;
      let getByDisplayValue;
      beforeEach(async () => {
        td.when(Meteor.call("users:getManageTeacherData", orgid, td.matchers.isA(Function))).thenDo((...args) => {
          const callback = args[args.length - 1];
          callback(null, responseWithAllUsers);
        });
        td.when(
          Meteor.call(
            "StudentGroups:getGroupsOwnedByUsers",
            [firstTeacherId, secondTeacherId, thirdTeacherId],
            orgid,
            selectedSchoolId,
            td.matchers.isA(Function)
          )
        ).thenDo((...args) => {
          const callback = args[args.length - 1];
          callback(null, studentGroups);
        });

        ({ getByText, getByTestId, queryByTestId, getByDisplayValue } = renderWithRouter(
          <ManageTeachersView {...defaultManageViewProps} />
        ));

        await waitFor(() => {
          expect(getByTestId(`unchecked_${firstTeacherId}`)).toBeVisible();
        });

        getByTestId(`unchecked_${firstTeacherId}`).click();
        getByTestId(`unchecked_${secondTeacherId}`).click();
        getByTestId(`unchecked_${thirdTeacherId}`).click();
        hoverOver(getByText("Manage"));

        getByText("Archive Selected Teachers").click();
      });

      it("should be displayed when at least one of the removed users is a group owner", () => {
        expect(getByTestId("assignUsersModalDialog")).toBeVisible();
      });

      it("should display a confirmation text for users that are not owners of any group", () => {
        const { getByText: getByTextForNoOwners } = within(getByTestId("non_owners_section"));
        expect(getByTextForNoOwners(`${secondTeacherName} ${secondTeacherLastName},`, { exact: false })).toBeVisible();
        expect(getByTextForNoOwners(`${thirdTeacherName} ${thirdTeacherLastName}`, { exact: false })).toBeVisible();
      });

      it("should display a list of groups for reassigning new owners based on removed users that are group owners", () => {
        const { getByText: getByTextForOwners } = within(getByTestId("owners_section"));
        expect(getByTextForOwners(`${firstTeacherName} ${firstTeacherLastName}`, { exact: false })).toBeVisible();
      });

      it("should make impossible confirming new group assignments when all new owners are not set", () => {
        expect(getByTestId("submitNewAssignments")).toBeDisabled();
      });

      it("should close the modal when all new owners are selected and the action is confirmed", () => {
        studentGroups.forEach(group => {
          expect(getByDisplayValue(group.name)).toBeVisible();
          const reactSelectInput = document.querySelector(`#${group._id}_select input`);
          fireEvent.change(reactSelectInput, { target: { value: adminFirstName } });
          fireEvent.keyDown(reactSelectInput, { key: "Enter", code: 13 });
          fireEvent.change(getByTestId(`${group._id}_input`), { target: { value: group._id } }); // change group name
        });
        expect(getByTestId("submitNewAssignments")).not.toBeDisabled();

        getByTestId("submitNewAssignments").click();

        td.verify(
          meteorCallSpy(
            "StudentGroups:updateGroupsData",
            {
              updatedGroups: [
                {
                  _id: firstGroupId,
                  ownerIds: [adminId],
                  name: `${firstGroupId} (${firstGroup.sectionId})`
                },
                {
                  _id: secondGroupId,
                  ownerIds: [adminId],
                  name: `${secondGroupId} (${secondGroup.sectionId})`
                }
              ],
              selectedUserIds: ["teacher_user_id1", "teacher_user_id2", "teacher_user_id3"],
              orgid,
              siteId: selectedSchoolId
            },
            td.matchers.isA(Function)
          )
        );
        td.verify(
          meteorCallSpy(
            "users:removeGroupOwnershipAndDeactivateSiteAccess",
            [firstTeacherId, secondTeacherId, thirdTeacherId],
            orgid,
            selectedSchoolId,
            td.matchers.isA(Function)
          )
        );
        expect(queryByTestId("assignUsersModalDialog")).toBeNull();
      });
    });
  });
});
