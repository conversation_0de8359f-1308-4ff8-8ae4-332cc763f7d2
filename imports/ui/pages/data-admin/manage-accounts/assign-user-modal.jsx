import React, { Component } from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import AssignUserTableRow from "./assign-user-table-row";
import { getClassNameFromStudentGroupName, getCourseNameFromStudentGroupName } from "../utilities";

export default class AssignUserModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      groupsNeedingNewOwner: this.props.groupsToUpdate
        .filter(g => this.props.selectedUserIds.includes(g.ownerIds[0]))
        .map(groupData => ({
          ...groupData,
          shortClassName: getClassNameFromStudentGroupName(groupData.name),
          courseName: getCourseNameFromStudentGroupName(groupData.name)
        }))
    };
    this.usersThatAreGroupOwners = this.props.selectedUsers.filter(user =>
      this.props.groupsToUpdate.find(groupData => groupData.ownerIds.includes(user._id))
    );
    this.usersThatAreSecondaryTeacher = this.props.selectedUsers.filter(
      user =>
        !!this.props.groupsToUpdate.find(group => group.secondaryTeachers && group.secondaryTeachers.includes(user._id))
    );
    // Users that can be selected when assigning new owners to groups
    this.availableOwners = this.props.potentialGroupOwners
      .filter(owner => !this.props.selectedUsers.find(user => user._id === owner._id))
      .map(owner => ({
        label: `${owner.profile.name.first} ${owner.profile.name.last} ${
          owner.profile.siteAccess
            ? `(last active in ${Math.max(...owner.profile.siteAccess.map(sa => sa.schoolYear))})`
            : "(No Site Access)"
        } (${owner.emails[0].address})`,
        value: owner._id
      }));
  }

  close = () => {
    this.props.onCloseModal();
  };

  confirmAction = () => {
    if (this.props.confirmAction) {
      const updatedGroupsData = this.state.groupsNeedingNewOwner.map(group => {
        return {
          _id: group._id,
          name: `${group.shortClassName} ${group.courseName}`,
          ownerIds: [group.selectedUser]
        };
      });
      this.props.confirmAction(updatedGroupsData, this.props.selectedUserIds);
    }
    this.close();
  };

  updateData = ({ groupId, ...rowData }) => {
    this.setState(prevState => ({
      ...prevState,
      groupsNeedingNewOwner: prevState.groupsNeedingNewOwner.map(group => {
        if (group._id === groupId) {
          return {
            ...group,
            ...rowData
          };
        }
        return group;
      })
    }));
  };

  getPreviousOwnerNames() {
    return this.usersThatAreGroupOwners.map(user => `${user.profile.name.first} ${user.profile.name.last}`).join(", ");
  }

  getNonGroupOwnerNames() {
    return this.usersThatAreSecondaryTeacher
      .map(user => `${user.profile.name.first} ${user.profile.name.last}`)
      .join(", ");
  }

  render() {
    return (
      <Modal
        show={this.props.showModal}
        onHide={this.close}
        dialogClassName="confirm-modal"
        backdrop="static"
        size="lg"
        data-testid="assignUsersModalDialog"
      >
        <ModalHeader>
          <h2 className="w9">
            <i className="fa fa-2x fa-warning text-warning pull-left" />
            <div>Are you sure you want to replace the current group owners?</div>
          </h2>
        </ModalHeader>

        <ModalBody>
          <React.Fragment>
            <section data-testid="owners_section">
              <label>
                <p>
                  You have selected <strong>{this.getPreviousOwnerNames()}</strong> who{" "}
                  {`${this.usersThatAreGroupOwners.length > 1 ? "are" : "is"}`} assigned to the following classes.
                  Please select a new user for each class below:
                </p>
              </label>
              <table className="table table-condensed">
                <thead>
                  <tr style={{ textAlign: "center" }}>
                    <td className="col-md-3">Class Name</td>
                    <td className="col-md-4">Full Class Name</td>
                    <td className="col-md-5">Teacher</td>
                  </tr>
                </thead>
                <tbody>
                  {this.state.groupsNeedingNewOwner.map((studentGroup, index) => {
                    return (
                      <AssignUserTableRow
                        key={studentGroup._id}
                        studentGroup={studentGroup}
                        availableOwners={this.availableOwners}
                        updateData={this.updateData}
                        index={index}
                        shortClassName={studentGroup.shortClassName}
                        courseName={studentGroup.courseName}
                      />
                    );
                  })}
                </tbody>
              </table>
            </section>
            {this.usersThatAreSecondaryTeacher.length ? (
              <React.Fragment>
                <hr />
                <section data-testid="non_owners_section">
                  <label>
                    <p>
                      You have selected <strong>{this.getNonGroupOwnerNames()}</strong> who will no longer be secondary
                      teacher(s) in the selected site.
                    </p>
                  </label>
                </section>
              </React.Fragment>
            ) : null}
          </React.Fragment>
        </ModalBody>

        <ModalFooter className="d-flex justify-content-center">
          <Button
            variant="success"
            onClick={this.confirmAction}
            data-testid={"submitNewAssignments"}
            disabled={this.state.groupsNeedingNewOwner.some(group => !group.selectedUser)}
          >
            {this.props.confirmText}
          </Button>
          <Button variant="default" onClick={this.close}>
            {this.props.cancelText}
          </Button>
        </ModalFooter>
      </Modal>
    );
  }
}

AssignUserModal.propTypes = {
  showModal: PropTypes.bool.isRequired,
  confirmAction: PropTypes.func.isRequired,
  onCloseModal: PropTypes.func.isRequired,
  confirmText: PropTypes.string,
  cancelText: PropTypes.string,
  selectedUserIds: PropTypes.array,
  selectedUsers: PropTypes.array,
  groupsToUpdate: PropTypes.array,
  potentialGroupOwners: PropTypes.array
};

AssignUserModal.defaultProps = {
  confirmText: "Submit",
  cancelText: "Cancel"
};
