import React, { Component } from "react";
import PropTypes from "prop-types";
import _ from "lodash";

import Alert from "react-s-alert";
import { Meteor } from "meteor/meteor";
import { with<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { Row, Col, ListGroup } from "react-bootstrap";
import ManageTeachersRow from "./manage-accounts-row";
import ConfirmModal from "../confirm-modal";
import AssignUserModal from "./assign-user-modal";
import sortByPropertyFor from "/imports/api/utilities/sortingHelpers/sortByPropertyFor";
import { getCurrentSchoolYear, getMeteorUserSync } from "/imports/api/utilities/utilities";
import { AppDataContext } from "../../../routing/AppDataContext";
import { isExternalRostering } from "../../../utilities";

class ManageAccounts extends Component {
  static contextType = AppDataContext;

  constructor(props) {
    super(props);

    this.state = {
      isSelected: false,
      sendingList: [],
      isSending: false,
      inviteSentStatusByUserId: {},
      displayAssignUserModal: false,
      filteredGroupData: [],
      areThereGroupOwners: false,
      shouldShowModal: false,
      groupsToUpdate: [],
      sortBy: "profile.localId",
      sortOrder: 1,
      isSSOOnlyOrg: false
    };
  }

  componentDidMount() {
    if (this.props.users.length) {
      this.updateSentInviteStateForUsers();
    }
    if (this.props.selectedTeacherId) {
      const user = this.props.users.find(currentUser => currentUser._id === this.props.selectedTeacherId);
      this.props.toggleSelectedUser(user._id);
    }
    Meteor.call("Organizations:getOrganizationFieldValues", this.props.orgid, ["useSSOOnly"], (err, resp) => {
      if (!err && resp) {
        const { useSSOOnly = false } = resp;
        this.setState({
          isSSOOnlyOrg: useSSOOnly
        });
      }
    });
  }

  componentDidUpdate(prevProps) {
    if (this.props.users.length !== prevProps.users.length || !_.isEqual(this.props.users, prevProps.users)) {
      this.updateSentInviteStateForUsers();
    }
  }

  sendEmailToSelected = () => {
    const userIds = this.props.selectedUserIds;
    this.queueForSending(userIds);
    userIds.forEach(userId => this.sendEmail(userId));
  };

  handleEmailSend = id => {
    this.queueForSending([id]);
    this.sendEmail(id);
  };

  removeFromQueue = id => {
    const sendingList = [...this.state.sendingList];
    if (sendingList.includes(id)) {
      sendingList.splice(sendingList.indexOf(id), 1);
    }
    this.setState({ sendingList, isSending: false });
  };

  queueForSending = userIds => {
    const sendingList = [...this.state.sendingList, ...userIds];
    this.setState({ sendingList, isSending: true });
  };

  sendEmail = id => {
    this.setState({ isSending: true });
    Meteor.call("users:sendEnrollmentEmail", id, this.props.orgid, (err, res) => {
      if (!err) {
        this.removeFromQueue(res);
        this.updateSentInviteStateForUsers();
        this.props.toggleSelectedUser(id);
      }
    });
  };

  updateSentInviteStateForUsers = () => {
    Meteor.call(
      "users:getUsersInviteEmailStatus",
      this.props.users.map(user => user._id),
      this.props.orgid,
      (err, res) => {
        if (err) {
          Alert.error(err.message, { timeout: 5000 });
        } else {
          this.setState({ inviteSentStatusByUserId: res });
        }
      }
    );
  };

  removeDataAdministrator = () => {
    Meteor.call("users:removeDataAdminUsers", this.props.orgid, this.props.selectedUserIds, err => {
      if (err) {
        this.setState({ showModal: false });
        Alert.error(err.message, { timeout: 3000 });
      } else {
        this.setState({ shouldShowModal: false });
        Alert.success("Selected Data administrator(s) removed", { timeout: 3000 });
        this.props.fetchUsers();
      }
    });
  };

  closeModal = () => {
    this.setState({ shouldShowModal: false });
  };

  renderSecondaryAccessConfirmModal = () => {
    const headerText =
      "Are you sure you want to remove secondary access to groups in the selected site for the selected users?";
    const bodyText = "The users will no longer appear as secondary teachers for groups in the selected site";
    const confirmText = "Confirm";

    return (
      <ConfirmModal
        showModal={this.state.shouldShowModal}
        onCloseModal={this.closeModal}
        confirmAction={this.removeUserSiteAccess}
        headerText={headerText}
        bodyText={bodyText}
        confirmText={confirmText}
      />
    );
  };

  renderAssignUserModal = () => {
    return (
      <AssignUserModal
        showModal={this.state.shouldShowModal}
        onCloseModal={this.closeModal}
        confirmAction={this.updateGroupsData}
        selectedUserIds={this.props.selectedUserIds}
        groupsToUpdate={this.state.groupsToUpdate}
        selectedUsers={this.props.users.filter(user => this.props.selectedUserIds.includes(user._id))}
        potentialGroupOwners={this.props.potentialGroupOwners}
      />
    );
  };

  renderRemoveDataAdminModal = () => {
    return (
      <ConfirmModal
        showModal={this.state.shouldShowModal}
        confirmAction={this.removeDataAdministrator}
        onCloseModal={this.closeModal}
        bodyQuestion=""
        bodyText="You are about to remove a data admin account or data admin role for this user, do you wish to continue?"
      />
    );
  };

  updateGroupsData = (updatedGroups, selectedUserIds = []) => {
    Meteor.call(
      "StudentGroups:updateGroupsData",
      { updatedGroups, selectedUserIds, orgid: this.props.orgid, siteId: this.props.selectedSchoolId },
      err => {
        if (err) {
          Alert.error(`Error updating - ${err.reason || err.message}`);
        } else if (!selectedUserIds.length) {
          this.showSuccessAlertAndRefetchUsers();
        }
      }
    );
    if (selectedUserIds.length) {
      this.removeUserSiteAccess(selectedUserIds);
    }
  };

  removeUserSiteAccess = (userIdsToRemoveStudentGroupAccess = this.props.selectedUserIds) => {
    Meteor.call(
      "users:removeGroupOwnershipAndDeactivateSiteAccess",
      userIdsToRemoveStudentGroupAccess,
      this.props.orgid,
      this.props.selectedSchoolId,
      err => {
        if (err) {
          return Alert.error(`Error updating - ${err.reason || err.message}`);
        }
        return this.showSuccessAlertAndRefetchUsers();
      }
    );
  };

  showSuccessAlertAndRefetchUsers() {
    Alert.success("Groups and users successfully updated", { timeout: 3000 });
    this.props.fetchUsers();
  }

  showOwnerModal = () => {
    if (this.props.selectedRole === "arbitraryIddataAdmin") {
      return this.renderRemoveDataAdminModal();
    }
    if (this.state.areThereGroupOwners) {
      return this.renderAssignUserModal();
    }
    return this.renderSecondaryAccessConfirmModal();
  };

  isRoleActive = role => (role === this.props.selectedRole ? "active" : "");

  renderSelectorButton = (buttonText, role) => (
    <div className="col-sm-4">
      <li
        className={`manage-accounts-list-element ${this.isRoleActive(role)}`}
        data-testid={`role-selector-id_${role}`}
        onClick={() => this.props.selectActiveRole(role)}
      >
        <a>{buttonText}</a>
      </li>
    </div>
  );

  addTeacher = () => {
    this.props.history.push(
      `/data-admin/manage-accounts/${this.props.orgid}/site/${this.props.selectedSchoolId}/add-teacher`
    );
  };

  addCoach = () => {
    this.props.history.push(`/coach-account-setup/${this.props.orgid}/site/${this.props.selectedSchoolId}`);
  };

  renderTeacherRow = (user, isArchived) => {
    const dbUser = this.props.dbUsers.find(u => u._id === user._id);
    return (
      user && (
        <ManageTeachersRow
          user={dbUser ? { ...user, ...dbUser } : user}
          selectedUsers={this.props.selectedUserIds}
          setSelectedUser={this.props.toggleSelectedUser}
          selectedRole={this.props.selectedRole}
          sendEmail={this.handleEmailSend}
          isInSendingList={this.state.sendingList.includes(user._id)}
          isSending={this.state.isSending}
          inviteSent={this.state.inviteSentStatusByUserId[user._id]}
          key={user._id}
          isArchived={isArchived}
          isSSOOnlyOrg={this.state.isSSOOnlyOrg}
        />
      )
    );
  };

  getGroupsOwnedBySelectedUsers = () => {
    Meteor.call(
      "StudentGroups:getGroupsOwnedByUsers",
      this.props.selectedUserIds,
      this.props.orgid,
      this.props.selectedSchoolId,
      (err, res) => {
        if (err) {
          Alert.error(err.message, { timeout: 5000 });
        } else {
          const areThereGroupOwners = this.props.selectedUserIds.length
            ? !!this.props.selectedUserIds.filter(userId => res.find(r => r.ownerIds.includes(userId))).length
            : false;
          this.setState({ areThereGroupOwners, shouldShowModal: true, groupsToUpdate: res });
        }
      }
    );
  };

  sortBy = property => () => {
    const sortOrder = this.state.sortBy === property ? -this.state.sortOrder : 1;
    this.setState({ sortBy: property, sortOrder });
  };

  renderSortOrderIndicator = sortBy => {
    if (sortBy !== this.state.sortBy) return null;
    return this.state.sortOrder === 1 ? (
      <span className="fa fa-chevron-up" aria-hidden="true" />
    ) : (
      <span className="fa fa-chevron-down" aria-hidden="true" />
    );
  };

  renderSideSiteList = () => {
    return (
      <ListGroup className="student-group-list">
        {this.props.schools.map(school => (
          <Link
            to={`/data-admin/manage-accounts/${this.props.orgid}/site/${school._id}`}
            key={school._id}
            className={`list-group-item ${this.props.selectedSchoolId === school._id ? "active" : ""}`}
          >
            {school.name}
          </Link>
        ))}
      </ListGroup>
    );
  };

  renderAddTeacherButton = () => {
    return (
      <button className="btn btn-primary" onClick={this.addTeacher} disabled={!this.props.selectedSchoolId}>
        Add Teacher
      </button>
    );
  };

  renderAddCoachButton = () => {
    return (
      <button className="btn btn-primary" onClick={this.addCoach} disabled={!this.props.selectedSchoolId}>
        Add Coach
      </button>
    );
  };

  renderRoleHeaders = () => {
    const selectedSchool = this.props.schools.find(s => s._id === this.props.selectedSchoolId);
    return (
      <div className="card-box" key={this.props.selectedSchoolId}>
        <h3 className="w9">
          {selectedSchool ? selectedSchool.name : "Unknown School"}
          <div className="pull-right d-flex gap-1">
            {this.renderAddTeacherButton()}
            {this.renderAddCoachButton()}
          </div>
        </h3>
        <div className="manage-accounts-btn-list" data-testid="manage-accounts-btn-list">
          <ul id="teacher-role-selector" className="nav nav-pills nav-justified">
            {this.renderSelectorButton("Teachers", "arbitraryIdteacher")}
            {this.renderSelectorButton("Coaches", "arbitraryIdadmin")}
            {this.renderSelectorButton("Data Admins", "arbitraryIddataAdmin")}
          </ul>
        </div>
      </div>
    );
  };

  renderArchiveSelectedButton() {
    const archiveLabelByRole = {
      arbitraryIdteacher: "Archive Selected Teachers",
      arbitraryIdadmin: "Archive Selected Coaches",
      arbitraryIddataAdmin: "Remove Selected Data Admins"
    };
    const currentRole = this.props.selectedRole;
    if (currentRole !== "arbitraryIdteacher" || !isExternalRostering(this.context.rostering)) {
      return currentRole === "arbitraryIddataAdmin" ? (
        <button
          type="button"
          disabled={!this.props.selectedUserIds.length}
          className="btn btn-success"
          onClick={() => this.setState({ shouldShowModal: true })}
        >
          {archiveLabelByRole[this.props.selectedRole]}
        </button>
      ) : (
        <button
          type="button"
          disabled={!this.props.selectedUserIds.length}
          className="btn btn-success"
          onClick={this.getGroupsOwnedBySelectedUsers}
        >
          {archiveLabelByRole[this.props.selectedRole]}
        </button>
      );
    }
    return null;
  }

  renderManageButton = () => {
    return (
      <div className="dropdown-hover">
        <button disabled={!this.props.selectedUserIds.length} className="btn btn-primary">
          Manage
        </button>
        {this.props.selectedUserIds.length ? (
          <div className="dropdown-hover-content btn-group-vertical">
            {this.renderArchiveSelectedButton()}
            <button
              type="button"
              disabled={!this.props.selectedUserIds.length}
              className="btn btn-success"
              onClick={this.sendEmailToSelected}
            >
              Send Invites to Selected
            </button>
          </div>
        ) : null}
      </div>
    );
  };

  renderArchivedUsersTable = () => {
    const { users, archivedUserIdsByRoleId, selectedRole, orgid } = this.props;
    const schoolYear = getCurrentSchoolYear(getMeteorUserSync(), orgid);
    const archiveHeaderByRole = {
      arbitraryIdteacher: "Archived Teachers",
      arbitraryIdadmin: "Archived Coaches",
      arbitraryIddataAdmin: "Archived Data Admins"
    };
    const archivedUsers = users
      .filter(user => archivedUserIdsByRoleId[selectedRole].includes(user._id))
      .map(user => ({
        ...user,
        schoolYear: user.profile.siteAccess.length
          ? Math.max(...user.profile.siteAccess.map(sa => (sa.schoolYear === 0 ? schoolYear : sa.schoolYear)))
          : "No Site Access"
      }));

    return archivedUsers.length ? (
      <div>
        <h4 className="w7">{archiveHeaderByRole[this.props.selectedRole]}</h4>
        <table className="table table-compact table-striped vertical-align-middle">
          <thead>
            <tr>
              <th width="15%" className="text-center">
                Teacher ID
              </th>
              <th width="20%" className="text-center">
                Last Name
              </th>
              <th width="20%" className="text-center">
                First Name
              </th>
              <th width="25%" className="text-center">
                Email
              </th>
              {this.props.selectedRole !== "arbitraryIdteacher" || !isExternalRostering(this.context.rostering) ? (
                <th className="text-end">&nbsp;</th>
              ) : null}
              <th></th>
              <th width="10%" className="text-center">
                Last active in
              </th>
              <th width="10%" className="text-center">
                Last log in
              </th>
            </tr>
          </thead>
          <tbody data-testid="manageTeachersTable">
            {archivedUsers.map(user => this.renderTeacherRow(user, true))}
          </tbody>
        </table>
      </div>
    ) : null;
  };

  renderUserTable = () => {
    const { users, activeUserIdsByRoleId, selectedRole, selectedTeacherId, selectedSchoolId } = this.props;
    const activeHeaderByRole = {
      arbitraryIdteacher: "Active Teachers",
      arbitraryIdadmin: "Active Coaches",
      arbitraryIddataAdmin: "Active Data Admins"
    };
    const activeUsers = users.filter(user => {
      return (
        activeUserIdsByRoleId[selectedRole].includes(user._id) &&
        user.profile.siteAccess.find(
          sa => sa.role === selectedRole && (["none", "allSites"].includes(sa.siteId) || sa.siteId === selectedSchoolId)
        )
      );
    });
    let activeUsersWithSorting = activeUsers.map(currentUser => {
      const userRoles = _.uniq(currentUser.profile.siteAccess.map(sa => sa.role));
      return {
        ...currentUser,
        isMultiRoleTeacher: userRoles.length > 1 && userRoles.includes("arbitraryIdteacher"),
        inviteSentStatus: this.state.inviteSentStatusByUserId[currentUser._id]
      };
    });
    activeUsersWithSorting = activeUsersWithSorting.length
      ? sortByPropertyFor({
          list: activeUsersWithSorting,
          paths: [this.state.sortBy],
          order: this.state.sortOrder
        })
      : [];
    if (selectedTeacherId) {
      activeUsersWithSorting.unshift(
        activeUsersWithSorting.splice(
          activeUsersWithSorting.findIndex(currentElement => currentElement._id === selectedTeacherId),
          1
        )[0]
      );
    }
    return activeUsers.length ? (
      <div>
        <h5 className="w7">{activeHeaderByRole[this.props.selectedRole]}</h5>
        <table className="table table-compact table-striped vertical-align-middle">
          <thead>
            <tr>
              <th className="font-18">
                <span
                  className="fa-stack"
                  data-testid="selectAll"
                  onClick={this.props.toggleSelectAllTeachers(activeUsers)}
                >
                  <i className="fa fa-square-o fa-stack-2x" />
                  {activeUsers.length === this.props.selectedUserIds.length && (
                    <i className="text-success fa fa-check fa-stack-1x" />
                  )}
                </span>
              </th>
              <th className="text-center" onClick={this.sortBy("profile.localId")}>
                Teacher ID {this.renderSortOrderIndicator("profile.localId")}
              </th>
              <th width="10%" className="text-center" onClick={this.sortBy("profile.name.last")}>
                Last Name {this.renderSortOrderIndicator("profile.name.last")}
              </th>
              <th width="10%" className="text-center" onClick={this.sortBy("profile.name.first")}>
                First Name {this.renderSortOrderIndicator("profile.name.first")}
              </th>
              <th width="20%" className="text-center" onClick={this.sortBy("emails[0].address")}>
                Email {this.renderSortOrderIndicator("emails[0].address")}
              </th>
              <th className="text-end">&nbsp;</th>
              <th></th>
              <th className="text-center" onClick={this.sortBy("inviteSentStatus")}>
                Invite Email Sent
                <i className="fa fa-paper-plane-o" /> {this.renderSortOrderIndicator("inviteSentStatus")}
              </th>
              <th className="text-center" onClick={this.sortBy("profile.onboarded")}>
                Account Activated <i className="fa fa-bolt text-primary" />{" "}
                {this.renderSortOrderIndicator("profile.onboarded")}
              </th>
              <th className="text-end">&nbsp;</th>
              <th width="10%" className="text-center">
                Last log in
              </th>
            </tr>
          </thead>
          <tbody data-testid="manageTeachersTable">
            {activeUsersWithSorting.map(user => this.renderTeacherRow(user, false))}
          </tbody>
        </table>
      </div>
    ) : (
      <div className="alert alert-info text-center m-t-10">
        No {activeHeaderByRole[this.props.selectedRole].toLowerCase()} found in the selected site
      </div>
    );
  };

  render() {
    return (
      <div className="conFullScreen">
        <div className="page-header">
          <h3 className="text-center">Manage Accounts</h3>
          <div className="header-action-button-container">
            <Link to={`/data-admin/manage-school/search-teachers/${this.props.orgid}`}>
              <button type="button" className="btn btn-primary">
                Search Teacher
              </button>
            </Link>
            <Link to={`/data-admin/manage-school/search-coaches/${this.props.orgid}`}>
              <button type="button" className="btn btn-primary m-l-10">
                Search Coach
              </button>
            </Link>
          </div>
        </div>
        <div className="container">
          <Row>
            <Col sm={2}>{this.renderSideSiteList()}</Col>
            <Col sm={10}>
              {this.renderRoleHeaders()}
              {this.state.shouldShowModal ? this.showOwnerModal() : null}
              {this.renderManageButton()}
              {this.renderUserTable()}
              {this.renderArchivedUsersTable()}
            </Col>
          </Row>
        </div>
      </div>
    );
  }
}

ManageAccounts.propTypes = {
  activeUserIdsByRoleId: PropTypes.object.isRequired,
  archivedUserIdsByRoleId: PropTypes.object.isRequired,
  dbUsers: PropTypes.array,
  fetchUsers: PropTypes.func.isRequired,
  history: PropTypes.object.isRequired,
  orgid: PropTypes.string.isRequired,
  potentialGroupOwners: PropTypes.array,
  schools: PropTypes.array.isRequired,
  selectActiveRole: PropTypes.func,
  selectedRole: PropTypes.string.isRequired,
  selectedSchoolId: PropTypes.string,
  selectedTeacherId: PropTypes.string,
  selectedUserIds: PropTypes.array.isRequired,
  toggleSelectAllTeachers: PropTypes.func,
  toggleSelectedUser: PropTypes.func,
  users: PropTypes.array.isRequired
};

export default withRouter(ManageAccounts);
