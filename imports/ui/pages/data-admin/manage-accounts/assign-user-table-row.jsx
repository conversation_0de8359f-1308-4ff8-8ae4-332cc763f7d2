import React, { Component } from "react";
import PropTypes from "prop-types";
import Select from "react-select";

export default class AssignUserTableRow extends Component {
  onChangeSelect = e => {
    this.props.updateData({
      groupId: this.props.studentGroup._id,
      selectedUser: e.value
    });
  };

  onNameChange = e => {
    this.props.updateData({
      groupId: this.props.studentGroup._id,
      shortClassName: e.target.value
    });
  };

  render() {
    return (
      <React.Fragment>
        <tr key={this.props.studentGroup._id}>
          <td>
            <input
              data-testid={`${this.props.studentGroup._id}_input`}
              type="text"
              className="form-control form-control-lg"
              value={this.props.shortClassName}
              onChange={this.onNameChange}
            />
          </td>
          <td>
            <input
              className="form-control form-control-lg"
              type="text"
              value={`${this.props.shortClassName} ${this.props.courseName}`}
              disabled
            />
          </td>
          <td>
            <Select
              id={`${this.props.studentGroup._id}_select`}
              name={"reassignOwnerSelect"}
              onChange={this.onChangeSelect}
              isSearchable={true}
              value={this.props.updateData.selectedUser}
              options={this.props.availableOwners}
              placeholder={"Type or select user name"}
              className="react-select-container"
              classNamePrefix="react-select"
            />
          </td>
        </tr>
      </React.Fragment>
    );
  }
}

AssignUserTableRow.propTypes = {
  availableOwners: PropTypes.array,
  studentGroup: PropTypes.object,
  updateData: PropTypes.func,
  shortClassName: PropTypes.string,
  courseName: PropTypes.string,
  selectedUser: PropTypes.string,
  index: PropTypes.number
};
