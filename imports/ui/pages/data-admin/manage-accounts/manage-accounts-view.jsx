import React, { Component } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { Meteor } from "meteor/meteor";
import { withTracker } from "meteor/react-meteor-data";
import { withRouter } from "react-router-dom";
import queryString from "query-string";
import { Loading } from "../../../components/loading.jsx";
import { Sites } from "/imports/api/sites/sites";
import ManageTeachers from "./manage-accounts";
import { Users } from "/imports/api/users/users";
import { isSingleSchoolDataAdmin } from "../utilities";
import { getMeteorUserSync } from "../../../../api/utilities/utilities";

const roleIdsToNames = {
  arbitraryIdteacher: "teachers",
  arbitraryIdadmin: "admins",
  arbitraryIddataAdmin: "dataAdmins"
};

class ManageAccountsView extends Component {
  state = {
    selectedRole: "arbitraryIdteacher",
    loading: true,
    teachers: [],
    admins: [],
    dataAdmins: [],
    selectedUserIds: [],
    archivedUserIdsByRoleBySiteId: {},
    activeUserIdsByRoleBySiteId: {},
    selectedTeacherId: ""
  };

  componentDidMount() {
    this.fetchUsers();
    const { id, role } = queryString.parse(this.props.history.location.search);
    if (id) {
      this.setState({
        selectedTeacherId: id
      });
    }
    if (role) {
      this.setState({
        selectedRole: role
      });
    }
  }

  componentDidUpdate(prevProps) {
    if (this.props.selectedSchoolId !== prevProps.selectedSchoolId) {
      this.setState({ selectedUserIds: [] });
    }
    return this.props.dbUsers !== prevProps.dbUsers;
  }

  fetchUsers = () => {
    Meteor.call("users:getManageTeacherData", this.props.orgid, (err, res) => {
      if (err) {
        Alert.error(err.message, { timeout: 5000 });
      } else {
        const { teachers, admins, dataAdmins, archivedUserIdsByRoleBySiteId, activeUserIdsByRoleBySiteId } = res;
        this.setState({
          loading: false,
          teachers,
          admins,
          dataAdmins,
          archivedUserIdsByRoleBySiteId,
          activeUserIdsByRoleBySiteId,
          selectedUserIds: []
        });
      }
    });
  };

  toggleSelectedUser = userId => {
    const selectedUserIds = [...this.state.selectedUserIds];
    if (selectedUserIds.includes(userId)) {
      selectedUserIds.splice(selectedUserIds.indexOf(userId), 1);
    } else {
      selectedUserIds.push(userId);
    }
    this.setState({ selectedUserIds });
  };

  getUsersByRole = () => {
    if (Object.keys(roleIdsToNames).includes(this.state.selectedRole)) {
      const currentRole = roleIdsToNames[this.state.selectedRole];
      if (currentRole !== "dataAdmins") {
        return this.state[currentRole].filter(
          user =>
            user.profile.siteAccess.find(access => access.siteId === this.props.selectedSchoolId) ||
            !user.profile.siteAccess.length
        );
      }
      return this.state[currentRole];
    }
    return [];
  };

  selectActiveRole = role => {
    this.setState({ selectedRole: role, selectedUserIds: [] });
  };

  toggleSelectAllTeachers = userArray => () => {
    if (this.state.selectedUserIds.length === userArray.length) {
      this.setState({ selectedUserIds: [] });
    } else {
      this.setState({ selectedUserIds: userArray.map(currentUser => currentUser._id) });
    }
  };

  filterUsersBySiteId = (siteId, users) => {
    const userIdsByRoleIdBySiteId = {
      arbitraryIdadmin: [],
      arbitraryIddataAdmin: [],
      arbitraryIdteacher: []
    };
    if (!users) {
      return userIdsByRoleIdBySiteId;
    }
    Object.keys(userIdsByRoleIdBySiteId).forEach(roleId => {
      userIdsByRoleIdBySiteId[roleId] = users[roleId][roleId === "arbitraryIddataAdmin" ? "allSites" : siteId] || [];
    });
    return userIdsByRoleIdBySiteId;
  };

  render() {
    if (this.state.loading || this.props.loading) return <Loading />;
    const { schools, selectedSchoolId, orgid, dbUsers } = this.props;
    const potentialGroupOwners = this.state.teachers.concat(this.state.admins);
    return (
      <ManageTeachers
        schools={schools}
        selectedSchoolId={selectedSchoolId}
        users={this.getUsersByRole()}
        dbUsers={dbUsers || []}
        archivedUserIdsByRoleId={this.filterUsersBySiteId(selectedSchoolId, this.state.archivedUserIdsByRoleBySiteId)}
        activeUserIdsByRoleId={this.filterUsersBySiteId(selectedSchoolId, this.state.activeUserIdsByRoleBySiteId)}
        orgid={orgid}
        selectActiveRole={this.selectActiveRole}
        selectedRole={this.state.selectedRole}
        toggleSelectedUser={this.toggleSelectedUser}
        selectedUserIds={this.state.selectedUserIds}
        fetchUsers={this.fetchUsers}
        potentialGroupOwners={potentialGroupOwners}
        toggleSelectAllTeachers={this.toggleSelectAllTeachers}
        selectedTeacherId={this.state.selectedTeacherId}
      />
    );
  }
}

ManageAccountsView.propTypes = {
  dbUsers: PropTypes.array,
  history: PropTypes.object,
  loading: PropTypes.bool.isRequired,
  orgid: PropTypes.string.isRequired,
  schools: PropTypes.array.isRequired,
  selectedSchoolId: PropTypes.string,
  siteId: PropTypes.string
};

export default withRouter(
  withTracker(props => {
    const { orgid } = props;
    let { siteId: selectedSchoolId } = props.params;
    const sitesHandler = Meteor.subscribe("Sites", orgid);
    const userSub = Meteor.subscribe("Users", { orgid });

    const loading = !sitesHandler.ready() && !userSub.ready();

    let schools = [];
    let dbUsers = [];
    if (!loading) {
      let sitesQuery = { orgid };
      if (isSingleSchoolDataAdmin()) {
        const { siteId } = getMeteorUserSync()?.profile?.siteAccess?.[0] || {};
        if (siteId) {
          sitesQuery = { _id: siteId };
        }
      }

      schools = Sites.find(sitesQuery, { sort: { "lastModified.on": -1, name: 1 } }).fetch();
      if (schools.length) {
        selectedSchoolId = selectedSchoolId || schools[0]._id;
      }
      dbUsers = Users.find().fetch();
    }
    return { loading, orgid, selectedSchoolId, schools, dbUsers };
  })(ManageAccountsView)
);

export { ManageAccountsView as PureManageTeachersView };
