import React, { useMemo, useState, useEffect } from "react";
import { get, sortBy } from "lodash";
import { Meteor } from "meteor/meteor";
import PropTypes from "prop-types";
import moment from "moment-timezone";
import Select from "react-select";
import Alert from "react-s-alert";

import { getCurrentDate } from "/imports/api/helpers/getCurrentDate";
import { getCurrentSchoolYear, getMeteorUserSync } from "/imports/api/utilities/utilities";
import { getSelectCustomStyles } from "/imports/ui/utilities";

function getTimeZoneObject(timeZoneName, currentDate) {
  const offsetInMinutes = -moment.tz.zone(timeZoneName).parse(currentDate);
  const sign = offsetInMinutes < 0 ? "-" : "+";
  const date = moment().startOf("day");
  const formattedOffset = date.add(Math.abs(offsetInMinutes), "minutes").format("HH:mm");
  return {
    value: timeZoneName,
    label: `(GMT ${sign}${formattedOffset}) ${timeZoneName}`,
    offset: offsetInMinutes
  };
}

function calculateEndDate(orgid, schoolYearBoundary) {
  return moment(schoolYearBoundary)
    .set("year", getCurrentSchoolYear(undefined, orgid))
    .format()
    .slice(0, 10);
}

function calculateMinimumDate(endDate) {
  return moment(endDate)
    .clone()
    .subtract(1, "year")
    .add(1, "day")
    .format()
    .slice(0, 10);
}

export function RosterSyncSchedule({ orgid, rosteringSettings, schoolYearBoundary }) {
  const customDate = get(getMeteorUserSync(), "profile.customDate");
  const [currentDate, setCurrentDate] = useState(new Date());
  const hasSyncScheduleSaved = !!get(rosteringSettings, "syncSchedule.frequency");

  useEffect(() => {
    const fetchCurrentDate = async () => {
      try {
        const date = await getCurrentDate(customDate, orgid);
        setCurrentDate(date);
      } catch (error) {
        console.error("Error fetching current date:", error);
        setCurrentDate(new Date()); // fallback to current date
      }
    };
    fetchCurrentDate();
  }, [customDate, orgid]);

  const [startDate, setStartDate] = useState(() => {
    const savedStartDate = get(rosteringSettings, "syncSchedule.startDate");
    return savedStartDate || new Date().toISOString().slice(0, 10);
  });

  // Update startDate when currentDate is loaded and there's no saved start date
  useEffect(() => {
    const savedStartDate = get(rosteringSettings, "syncSchedule.startDate");
    if (!savedStartDate && currentDate) {
      setStartDate(currentDate.toISOString().slice(0, 10));
    }
  }, [currentDate, rosteringSettings]);

  const endDate = useMemo(
    () => get(rosteringSettings, "syncSchedule.endDate", calculateEndDate(orgid, schoolYearBoundary)),
    [schoolYearBoundary]
  );
  const minimumDate = useMemo(() => calculateMinimumDate(endDate), [endDate]);
  const [isLastDayOfMonth, setIsLastDayOfMonth] = useState(new Date(startDate).getDate() >= 28);
  const [time, setTime] = useState(get(rosteringSettings, "syncSchedule.time", "02:00"));
  const [timeZone, setTimeZone] = useState(() => {
    const savedTimeZone = get(rosteringSettings, "syncSchedule.timeZone", moment.tz.guess());
    return getTimeZoneObject(savedTimeZone, new Date());
  });
  const [frequency, setFrequency] = useState(get(rosteringSettings, "syncSchedule.frequency", "weekly"));
  const [isSaving, setIsSaving] = useState(false);
  const [endDateValue, setEndDateValue] = useState(endDate);

  // Update timeZone when currentDate is loaded
  useEffect(() => {
    if (currentDate) {
      const savedTimeZone = get(rosteringSettings, "syncSchedule.timeZone", moment.tz.guess());
      setTimeZone(getTimeZoneObject(savedTimeZone, currentDate));
    }
  }, [currentDate, rosteringSettings]);

  const availableZones = useMemo(() => {
    if (!currentDate) return [];
    return sortBy(
      moment.tz.names().map(name => getTimeZoneObject(name, currentDate)),
      ["offset", "label"]
    );
  }, [currentDate]);

  const updateStartDate = date => {
    const dayOfMonth = new Date(date).getDate();
    setIsLastDayOfMonth(dayOfMonth >= 28);
    setStartDate(date);
  };

  const validateStartDate = event => {
    const date = event.target.value;
    if (date < minimumDate) {
      updateStartDate(minimumDate);
    } else if (date > endDate) {
      updateStartDate(endDate);
    }
  };

  const onCheckboxToggle = isChecked => {
    const startDateObject = new Date(startDate);
    const lastDayOfMonth = new Date(Date.UTC(startDateObject.getFullYear(), startDateObject.getMonth() + 1, 0));
    const newStartDate = (isChecked ? lastDayOfMonth : new Date()).toISOString().split("T")[0];
    setStartDate(newStartDate);
    setIsLastDayOfMonth(isChecked);
  };

  const onInputChange = (setFunction, dependentSetFunction = () => {}) => e => {
    setFunction(e.target.value);
    dependentSetFunction(e.target.value);
  };

  const saveSyncSchedule = e => {
    e.preventDefault();
    const syncSchedule = {
      startDate,
      endDate: endDateValue,
      time,
      timeZone: timeZone.value,
      frequency
    };
    setIsSaving(true);
    Meteor.call(
      "Organizations:saveRosterSyncSchedule",
      {
        orgid,
        syncSchedule
      },
      err => {
        setIsSaving(false);
        if (err) {
          Alert.error(err.reason || "There was an error while saving rostering sync settings", {
            timeout: 3000
          });
        } else {
          Alert.success("Rostering sync settings saved successfully.", {
            timeout: 3000
          });
        }
      }
    );
  };

  const removeSyncSchedule = e => {
    e.preventDefault();
    setIsSaving(true);
    Meteor.call("Organizations:removeRosterSyncSchedule", orgid, err => {
      setIsSaving(false);
      if (err) {
        Alert.error(err.reason || "There was an error while removing rostering sync settings", {
          timeout: 3000
        });
      } else {
        Alert.success("Rostering sync settings removed successfully.", {
          timeout: 3000
        });
      }
    });
  };

  const isMonthly = frequency === "monthly";

  return (
    <React.Fragment>
      {!hasSyncScheduleSaved && (
        <div className="alert alert-warning text-center">
          No Sync Schedule settings saved yet. Set and save the settings to start using the feature.
        </div>
      )}
      <form className="form-horizontal">
        <div className="align-items-center d-flex form-group">
          <label className="col-sm-3 text-end">Frequency:</label>
          <div className={`col-sm-${isMonthly ? 6 : 9}`}>
            <select className="form-select" name="frequency" onChange={onInputChange(setFrequency)} value={frequency}>
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
            </select>
          </div>
          {isMonthly && (
            <div className="col-sm-3 d-block">
              <label className="control-label">Use Last Day of Month:</label>
              <input
                className="checkbox-md f-right"
                type="checkbox"
                onChange={e => onCheckboxToggle(e.target.checked)}
                checked={isLastDayOfMonth}
                name="lastDayOfMonth"
              />
            </div>
          )}
        </div>
        <div className="align-items-center d-flex form-group">
          <label className="col-sm-3 text-end">Start Date:</label>
          <div className="col-sm-9">
            <input
              className="form-control"
              type="date"
              onChange={onInputChange(updateStartDate)}
              onBlur={validateStartDate}
              value={startDate}
              min={minimumDate}
              max={endDate}
              name="startDate"
              disabled={isMonthly && isLastDayOfMonth}
            />
          </div>
        </div>
        <div className="align-items-center d-flex form-group">
          <label className="col-sm-3 text-end">End Date:</label>
          <div className="col-sm-9">
            <input
              className="form-control"
              type="date"
              value={endDateValue}
              name="endDate"
              min={minimumDate}
              max={endDate}
              onChange={onInputChange(setEndDateValue)}
            />
          </div>
        </div>
        <div className="align-items-center d-flex form-group">
          <label className="col-sm-3 text-end">Time:</label>
          <div className="col-sm-9">
            <input className="form-control" type="time" onChange={onInputChange(setTime)} value={time} name="time" />
          </div>
        </div>
        <div className="align-items-center d-flex form-group">
          <label className="col-sm-3 text-end">Time Zone:</label>
          <div className="col-sm-9">
            <Select
              id="selectTimeZone"
              value={timeZone}
              isSearchable={true}
              name="timeZone"
              options={availableZones}
              className="basic-multi-select"
              classNamePrefix="select"
              styles={getSelectCustomStyles()}
              onChange={setTimeZone}
            />
          </div>
        </div>

        <div className="form-group d-grid">
          <div className="row">
            <div className="col-sm-6 d-grid">
              <button className="btn btn-primary" onClick={saveSyncSchedule} disabled={isSaving}>
                Save
              </button>
            </div>

            <div className="col-sm-6 d-grid">
              <button
                type="button"
                className={`btn btn-danger ${hasSyncScheduleSaved ? "" : "disabled"}`}
                name="buttonDeleteRosterSyncSchedule"
                data-testid="deleteRosterSyncSchedule"
                onClick={removeSyncSchedule}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      </form>
    </React.Fragment>
  );
}

RosterSyncSchedule.defaultProps = {
  schoolYearBoundary: { month: 6, day: 31 }
};

RosterSyncSchedule.propTypes = {
  orgid: PropTypes.string,
  rosteringSettings: PropTypes.object,
  schoolYearBoundary: PropTypes.shape({
    month: PropTypes.number,
    day: PropTypes.number
  })
};
