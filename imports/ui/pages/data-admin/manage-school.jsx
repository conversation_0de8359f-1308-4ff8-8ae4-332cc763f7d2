import React, { Component, useContext } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { useTracker } from "meteor/react-meteor-data";
import { Link } from "react-router-dom";
import Alert from "react-s-alert";

import Header from "/imports/ui/components/admin-view/admin-header";

import AddClassForm from "./add-class-form";
import { getUsersThatCanBeGroupOwners, isSingleSchoolDataAdmin } from "./utilities";
import Loading from "../../components/loading";
import { StaticDataContext } from "../../../contexts/StaticDataContext";

class ManageSchool extends Component {
  state = {
    studentGroupCount: 0,
    isFetchingStudentGroupsCount: true
  };

  componentDidMount() {
    Meteor.call("StudentGroups:getActiveStudentGroupsCountInSites", this.props.orgid, (err, resp) => {
      if (!err) {
        const studentGroupsInSite = resp.find(item => item._id === this.props.siteId);
        const studentGroupCount = studentGroupsInSite ? studentGroupsInSite.activeGroupsCount : 0;
        this.setState({ studentGroupCount });
      } else {
        Alert.error("Error getting number of active student groups");
      }
      this.setState({ isFetchingStudentGroupsCount: false });
    });
  }

  render() {
    const { orgid, siteId, teachers, grades, loading } = this.props;
    if (loading || this.state.isFetchingStudentGroupsCount) {
      return <Loading />;
    }

    return (
      <div className="workspace-container">
        <div className="d-flex mt-1">
          {!isSingleSchoolDataAdmin() ? (
            <Link className="btn btn-outline-blue btn-xs me-2" to={`/data-admin/dashboard/${this.props.orgid}`}>
              <i className="fa fa-caret-left" /> Back to All Schools
            </Link>
          ) : null}
          {this.state.studentGroupCount ? (
            <Link
              className="btn btn-outline-blue btn-xs"
              to={`/data-admin/manage-group/students/${this.props.orgid}/site/${this.props.siteId}`}
            >
              <i className="fa fa-caret-left" /> Back to School
            </Link>
          ) : null}
        </div>

        <Header keyLabel="schoolOverview" headerTitle="Add Class & Teacher" />

        <div className="card-box-wrapper mt-2">
          <div className="main-content">
            <AddClassForm orgid={orgid} siteId={siteId} teachers={teachers} grades={grades} />
          </div>
        </div>
      </div>
    );
  }
}

ManageSchool.propTypes = {
  loading: PropTypes.bool,
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  teachers: PropTypes.array,
  grades: PropTypes.array,
  history: PropTypes.object
};

export default function ManageSchoolTracker(props) {
  const { orgid, siteId } = props;
  const { grades } = useContext(StaticDataContext);
  const trackerData = useTracker(() => {
    const usersHandler = Meteor.subscribe("Users", { orgid, siteId });

    const loading = !usersHandler.ready();

    let teachers = [];

    if (!loading) {
      teachers = getUsersThatCanBeGroupOwners(orgid);
    }

    return {
      loading,
      teachers
    };
  }, [orgid, siteId]);

  return <ManageSchool {...trackerData} orgid={orgid} siteId={siteId} grades={grades} />;
}

ManageSchoolTracker.propTypes = {
  orgid: PropTypes.string,
  siteId: PropTypes.string
};
