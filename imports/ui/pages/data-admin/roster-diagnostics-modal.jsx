import React from "react";
import PropTypes from "prop-types";
import { Button, Modal } from "react-bootstrap";

import { DiagnosticsEnrollments } from "./diagnostics-enrollments";

const RosterDiagnosticsModal = ({ showModal, onHide, item, itemName, fetchFunction }) => {
  if (!item || !showModal) return null;

  return (
    <Modal show={showModal} onHide={onHide} size="lg" backdrop="static">
      <Modal.Header>
        <div>
          <strong>{itemName}</strong>
        </div>
      </Modal.Header>
      <Modal.Body>
        <div>
          <h5>API Data:</h5>
          <textarea disabled className="form-control mt-1" rows={20} value={JSON.stringify(item, null, 4)} />
        </div>
        <br />
        <DiagnosticsEnrollments fetchFunction={fetchFunction} id={item.sourcedId} />
      </Modal.Body>
      <Modal.Footer>
        <Button variant="default" onClick={onHide}>
          OK
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

RosterDiagnosticsModal.defaultProps = {
  item: {},
  fetchFunction: () => {}
};

RosterDiagnosticsModal.propTypes = {
  showModal: PropTypes.bool,
  onHide: PropTypes.func,
  item: PropTypes.object,
  itemName: PropTypes.string,
  fetchFunction: PropTypes.func
};

export default RosterDiagnosticsModal;
