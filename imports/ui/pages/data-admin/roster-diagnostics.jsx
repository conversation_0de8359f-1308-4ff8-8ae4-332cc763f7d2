import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";

import { DataProviderForRosteringFilters } from "./roster-filtering/dataFetching";
import { Loading } from "../../components/loading";
import { TabContent } from "./tab-content";
import RosterDiagnosticsModal from "./roster-diagnostics-modal";

// eslint-disable-next-line react/display-name
export const RosterDiagnostics = React.memo(({ rosteringType, rosteringSettings, orgid, orgName }) => {
  const [dataProviderForRostering, setDataProviderForRostering] = useState(null);
  const [activeViewName, setActiveViewName] = useState("staffAndStudents");

  const [searchUsersText, setSearchUsersText] = useState("");
  const [isSearchingUsers, setIsSearchingUsers] = useState(false);
  const [userDiagnosticsData, setUserDiagnosticsData] = useState(null);
  const [selectedUser, setSelectedUser] = useState(null);
  const [isUserModalOpen, setUserModalOpen] = useState(false);

  const [searchClassesText, setSearchClassesText] = useState("");
  const [isSearchingClasses, setIsSearchingClasses] = useState(false);
  const [classDiagnosticsData, setClassDiagnosticsData] = useState(null);
  const [selectedClass, setSelectedClass] = useState(null);
  const [isClassModalOpen, setClassModalOpen] = useState(false);

  const [searchSchoolsText, setSearchSchoolsText] = useState("");
  const [isSearchingSchools, setIsSearchingSchools] = useState(false);
  const [schoolDiagnosticsData, setSchoolDiagnosticsData] = useState(null);
  const [selectedSchool, setSelectedSchool] = useState(null);
  const [isSchoolModalOpen, setSchoolModalOpen] = useState(false);

  useEffect(() => {
    const dataProvider = new DataProviderForRosteringFilters({ rosteringSettings, rosteringType, orgid, orgName });
    setDataProviderForRostering(dataProvider);
  }, []);

  const getTabClassName = viewName => (activeViewName === viewName ? "active" : "");
  const setActiveView = viewName => () => setActiveViewName(viewName);

  const onSearchUsersTextChange = event => {
    setSearchUsersText(event.target.value);
  };

  const onUserSearchSubmit = () => {
    setIsSearchingUsers(true);
    dataProviderForRostering.searchUsers(searchUsersText, (data = []) => {
      setIsSearchingUsers(false);
      setUserDiagnosticsData(data);
    });
  };

  const selectUserToShowInModal = user => () => {
    setSelectedUser(user);
    setUserModalOpen(true);
  };

  const closeUserModal = () => {
    return setUserModalOpen(false);
  };

  const getUserName = userItem => {
    const { givenName, middleName, familyName } = userItem;
    return [givenName, middleName, familyName].filter(f => f).join(" ");
  };

  const onSearchClassesTextChange = event => {
    setSearchClassesText(event.target.value);
  };

  const onClassSearchSubmit = () => {
    setIsSearchingClasses(true);
    dataProviderForRostering.searchClasses(searchClassesText, (data = []) => {
      setIsSearchingClasses(false);
      setClassDiagnosticsData(data);
    });
  };

  const selectClassToShowInModal = classItem => () => {
    setSelectedClass(classItem);
    setClassModalOpen(true);
  };

  const closeClassModal = () => {
    return setClassModalOpen(false);
  };

  const onSearchSchoolsTextChange = event => {
    setSearchSchoolsText(event.target.value);
  };

  const onSchoolSearchSubmit = () => {
    setIsSearchingSchools(true);
    dataProviderForRostering.searchSchools(searchSchoolsText, (data = []) => {
      setIsSearchingSchools(false);
      setSchoolDiagnosticsData(data);
    });
  };

  const selectSchoolToShowInModal = user => () => {
    setSelectedSchool(user);
    setSchoolModalOpen(true);
  };

  const closeSchoolModal = () => {
    return setSchoolModalOpen(false);
  };

  const renderUsersTable = () => {
    if (!userDiagnosticsData) {
      return null;
    }
    if (userDiagnosticsData.length) {
      return (
        <div className="table-responsive mt-1">
          <table className="table table-compact table-striped table-bordered table-hover table-row-centered">
            <thead>
              <tr>
                <td>Sourced ID</td>
                <td>Identifier</td>
                <td>Name</td>
                <td>Role</td>
                <td>Email</td>
                <td>Status</td>
              </tr>
            </thead>
            <tbody>
              {userDiagnosticsData.map(userItem => (
                <tr key={userItem.sourcedId} onClick={selectUserToShowInModal(userItem)}>
                  <td>{userItem.sourcedId}</td>
                  <td>{userItem.identifier}</td>
                  <td>{getUserName(userItem)}</td>
                  <td>{userItem.role}</td>
                  <td>{userItem.email}</td>
                  <td>{userItem.status}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      );
    }
    return <div className="alert alert-info mt-1 text-center">No Users found</div>;
  };

  const renderClassesTable = () => {
    if (!classDiagnosticsData) {
      return null;
    }
    if (classDiagnosticsData.length) {
      return (
        <div className="table-responsive mt-1">
          <table className="table table-compact table-striped table-bordered table-hover table-row-centered">
            <thead>
              <tr>
                <td>Sourced ID</td>
                <td>Title</td>
                <td>School</td>
                <td>Class Code</td>
                <td>Status</td>
              </tr>
            </thead>
            <tbody>
              {classDiagnosticsData.map(classItem => (
                <tr key={classItem.sourcedId} onClick={selectClassToShowInModal(classItem)}>
                  <td>{classItem.sourcedId}</td>
                  <td>{classItem.title}</td>
                  <td>{classItem.schoolName || "N/A"}</td>
                  <td>{classItem.classCode}</td>
                  <td>{classItem.status}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      );
    }
    return <div className="alert alert-info mt-1 text-center">No Classes found</div>;
  };

  const renderSchoolsTable = () => {
    if (!schoolDiagnosticsData) {
      return null;
    }
    if (schoolDiagnosticsData.length) {
      return (
        <div className="table-responsive mt-1">
          <table className="table table-compact table-striped table-bordered table-hover table-row-centered">
            <thead>
              <tr>
                <td>Sourced ID</td>
                <td>Name</td>
                <td>Identifier</td>
                <td>Status</td>
              </tr>
            </thead>
            <tbody>
              {schoolDiagnosticsData.map(school => (
                <tr key={school.sourcedId} onClick={selectSchoolToShowInModal(school)}>
                  <td>{school.sourcedId}</td>
                  <td>{school.name}</td>
                  <td>{school.identifier}</td>
                  <td>{school.status}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      );
    }
    return <div className="alert alert-info mt-1 text-center">No Schools found</div>;
  };

  return (
    <div>
      <ul className="nav nav-pills middle-sub-nav middle-sub-nav-compact">
        <li>
          <a className={getTabClassName("staffAndStudents")} onClick={setActiveView("staffAndStudents")}>
            Staff & Students
          </a>
        </li>
        <li>
          <a className={getTabClassName("classes")} onClick={setActiveView("classes")}>
            Classes
          </a>
        </li>
        <li>
          <a className={getTabClassName("schools")} onClick={setActiveView("schools")}>
            Schools
          </a>
        </li>
      </ul>

      <TabContent name="staffAndStudents" activeViewName={activeViewName}>
        <h5>Staff & Students</h5>
        <div className="d-grid gap-1">
          <input
            onChange={onSearchUsersTextChange}
            type="text"
            required="required"
            name="searchUsersText"
            value={searchUsersText}
            placeholder="Search Staff & Students..."
            className="form-control"
          />
          <button
            className="btn btn-success"
            onClick={onUserSearchSubmit}
            disabled={!searchUsersText || isSearchingUsers}
          >
            Search
          </button>
        </div>
        {isSearchingUsers ? <Loading /> : renderUsersTable()}
        <RosterDiagnosticsModal
          item={selectedUser}
          showModal={isUserModalOpen}
          onHide={closeUserModal}
          itemName={getUserName(selectedUser || {})}
          fetchFunction={dataProviderForRostering?.getEnrollmentsByUserId}
        />
      </TabContent>

      <TabContent name="classes" activeViewName={activeViewName}>
        <h5>Classes</h5>
        <div className="d-grid gap-1">
          <input
            onChange={onSearchClassesTextChange}
            type="text"
            required="required"
            name="searchClassesText"
            value={searchClassesText}
            placeholder="Search Classes..."
            className="form-control"
          />
          <button
            className="btn btn-success"
            onClick={onClassSearchSubmit}
            disabled={!searchClassesText || isSearchingClasses}
          >
            Search
          </button>
        </div>
        {isSearchingClasses ? <Loading /> : renderClassesTable()}
        <RosterDiagnosticsModal
          item={selectedClass}
          showModal={isClassModalOpen}
          onHide={closeClassModal}
          itemName={selectedClass?.title}
          fetchFunction={dataProviderForRostering?.getEnrollmentsByClassId}
        />
      </TabContent>

      <TabContent name="schools" activeViewName={activeViewName}>
        <h5>Schools</h5>
        <div className="d-grid gap-1">
          <input
            onChange={onSearchSchoolsTextChange}
            type="text"
            required="required"
            name="searchSchoolsText"
            value={searchSchoolsText}
            placeholder="Search Schools..."
            className="form-control"
          />
          <button
            className="btn btn-success"
            onClick={onSchoolSearchSubmit}
            disabled={!searchSchoolsText || isSearchingSchools}
          >
            Search
          </button>
        </div>
        {isSearchingSchools ? <Loading /> : renderSchoolsTable()}
        <RosterDiagnosticsModal
          item={selectedSchool}
          showModal={isSchoolModalOpen}
          onHide={closeSchoolModal}
          itemName={selectedSchool?.name}
          fetchFunction={dataProviderForRostering?.getEnrollmentsBySchoolId}
        />
      </TabContent>
    </div>
  );
});

RosterDiagnostics.propTypes = {
  orgid: PropTypes.string,
  orgName: PropTypes.string,
  rosteringSettings: PropTypes.object,
  rosteringType: PropTypes.string
};
