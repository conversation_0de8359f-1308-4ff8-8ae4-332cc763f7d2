import React, { Component } from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import { withRouter } from "react-router-dom";

class ManageSSOOrgModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showModal: false,
      ssoIssuerOrgId: this.props.ssoIssuerOrgId
    };
  }

  // eslint-disable-next-line camelcase
  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.showModal) {
      this.open();
    }
    if (nextProps.ssoIssuerOrgId !== this.props.ssoIssuerOrgId) {
      this.setState({
        ssoIssuerOrgId: nextProps.ssoIssuerOrgId
      });
    }
  }

  close = () => {
    this.setState({ showModal: false, ssoIssuerOrgId: this.props.ssoIssuerOrgId });
    this.props.onCloseModal();
  };

  open = () => {
    this.setState({ showModal: true });
  };

  saveSSOOrgId = () => {
    this.props.onSave(this.state.ssoIssuerOrgId);
    this.close();
  };

  onChange = event => {
    const ssoIssuerOrgId = event.target.value;
    this.setState({
      ssoIssuerOrgId
    });
  };

  render() {
    return (
      <Modal
        show={this.state.showModal}
        size="lg"
        onHide={this.close}
        dialogClassName="manage-sso-org"
        data-testid="ssoOrgModal"
        backdrop="static"
      >
        <ModalHeader>
          <h4 className="w9">{this.props.orgName} SSO Setup</h4>
        </ModalHeader>

        <ModalBody>
          <p className=" text-center">Enter the District ID of the Single Sign on Provider</p>
          <div className="form-group">
            <label>District SSO ID</label>
            <input
              onChange={this.onChange}
              type="text"
              name="ssoIssuerOrgId"
              value={this.state.ssoIssuerOrgId}
              className="form-control"
            />
          </div>
        </ModalBody>

        <ModalFooter className="d-flex justify-content-center">
          <Button variant="success" onClick={this.saveSSOOrgId} data-testid="submitSSOOrg">
            Save
          </Button>
          <Button variant="default" onClick={this.close}>
            Cancel
          </Button>
        </ModalFooter>
      </Modal>
    );
  }
}

ManageSSOOrgModal.propTypes = {
  showModal: PropTypes.bool,
  orgName: PropTypes.string,
  ssoIssuerOrgId: PropTypes.string,
  onCloseModal: PropTypes.func,
  onSave: PropTypes.func
};

ManageSSOOrgModal.defaultProps = {
  onCloseModal: () => {},
  onSave: () => {}
};

export default withRouter(ManageSSOOrgModal);
