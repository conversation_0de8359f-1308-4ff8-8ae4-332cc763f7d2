import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { withTracker } from "meteor/react-meteor-data";

import { Organizations } from "/imports/api/organizations/organizations";
import { decrypt } from "/imports/api/utilities/utilities";
import { areSubscriptionsLoading } from "/imports/ui/utilities";
import { get } from "lodash";
import PageHeader from "/imports/ui/components/page-header";
import Loading from "/imports/ui/components/loading";

import { RosterFiltering } from "./roster-filtering/roster-filtering";
import { RosterSettings } from "./roster-settings";
import { RosterDiagnostics } from "./roster-diagnostics";
import { RosterSyncSchedule } from "./roster-sync-schedule";
import { TabContent } from "./tab-content";

export function Rostering({ loading, organization, rosteringSettings, filters, rosteringType, orgid }) {
  const hasSavedCredentials = !!(
    rosteringSettings?.apiUrl.length &&
    rosteringSettings?.clientId.length &&
    rosteringSettings?.clientSecret.length
  );
  const [activeViewName, setActiveViewName] = useState("settings");
  const [hasRosterFilters, setHasRosterFilters] = useState(false);
  const [rosterFilteringForcedRerenderKey, setRosterFilteringForcedRerenderKey] = useState(1);

  const getTabClassName = viewName => (activeViewName === viewName ? "active" : "");
  const setActiveView = viewName => () => setActiveViewName(viewName);

  useEffect(() => {
    const filterValues = Object.values(filters);
    setHasRosterFilters(filterValues.length > 0 && filterValues.every(f => f.length));
  }, [filters]);

  useEffect(() => {
    setActiveViewName(hasSavedCredentials ? "filters" : "settings");
  }, [hasSavedCredentials]);

  useEffect(() => {
    if (rosteringSettings) {
      setRosterFilteringForcedRerenderKey(rosterFilteringForcedRerenderKey === 1 ? 2 : 1);
    }
  }, [rosteringSettings?.apiUrl, rosteringSettings?.clientId, rosteringSettings?.clientSecret]);

  if (loading) {
    return <Loading />;
  }

  const shouldShowDiagnostics = rosteringType === "rosterOR";

  return (
    <div className="conFullScreen">
      <PageHeader title="Rostering" description={organization.name} />
      <div className="container">
        <ul className="nav nav-pills middle-sub-nav middle-sub-nav-compact">
          <li>
            <a className={getTabClassName("settings")} onClick={setActiveView("settings")}>
              Settings
            </a>
          </li>
          {hasSavedCredentials && shouldShowDiagnostics && (
            <li>
              <a className={getTabClassName("diagnostics")} onClick={setActiveView("diagnostics")}>
                Diagnostics
              </a>
            </li>
          )}
          {hasSavedCredentials && (
            <li>
              <a className={getTabClassName("filters")} onClick={setActiveView("filters")}>
                Filters
              </a>
            </li>
          )}
          {hasRosterFilters && (
            <li>
              <a className={getTabClassName("schedule")} onClick={setActiveView("schedule")}>
                Sync Schedule
              </a>
            </li>
          )}
        </ul>
        <div className="row">
          <div className="col-sm-10 offset-sm-1">
            <TabContent name="settings" activeViewName={activeViewName}>
              <RosterSettings rosteringType={rosteringType} rosteringSettings={rosteringSettings} orgid={orgid} />
            </TabContent>
            {hasSavedCredentials && (
              <TabContent name="filters" activeViewName={activeViewName}>
                <RosterFiltering
                  key={rosterFilteringForcedRerenderKey}
                  rosteringType={rosteringType}
                  rosteringSettings={rosteringSettings}
                  orgid={orgid}
                  orgName={organization.name}
                />
              </TabContent>
            )}
            {hasSavedCredentials && shouldShowDiagnostics && (
              <TabContent name="diagnostics" activeViewName={activeViewName}>
                <RosterDiagnostics
                  key={rosterFilteringForcedRerenderKey}
                  rosteringType={rosteringType}
                  rosteringSettings={rosteringSettings}
                  orgid={orgid}
                  orgName={organization.name}
                />
              </TabContent>
            )}
            {hasRosterFilters && (
              <TabContent name="schedule" activeViewName={activeViewName}>
                <RosterSyncSchedule
                  rosteringSettings={rosteringSettings}
                  orgid={orgid}
                  schoolYearBoundary={organization.schoolYearBoundary}
                />
              </TabContent>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

Rostering.propTypes = {
  loading: PropTypes.bool,
  orgid: PropTypes.string,
  organization: PropTypes.object,
  filters: PropTypes.object,
  rosteringType: PropTypes.string,
  rosteringSettings: PropTypes.object
};

export default withTracker(({ orgid }) => {
  const orgSub = Meteor.subscribe("Organizations", orgid);
  const usersSub = Meteor.subscribe("Users", { orgid });

  let rosteringSettings = null;
  let organization = {};
  let rosteringType = null;

  const loading = areSubscriptionsLoading(orgSub, usersSub);

  if (!loading) {
    organization =
      Organizations.findOne(orgid, {
        fields: { name: 1, rosteringSettings: 1, rostering: 1, schoolYearBoundary: 1 }
      }) || {};
    if (organization.rostering) {
      rosteringType = organization.rostering;
    }

    if (organization.rosteringSettings) {
      rosteringSettings = {
        apiUrl: decrypt(organization.rosteringSettings.apiUrl),
        authUrl: decrypt(organization.rosteringSettings.authUrl),
        clientId: decrypt(organization.rosteringSettings.clientId),
        clientSecret: decrypt(organization.rosteringSettings.clientSecret),
        shouldUseScopes: organization.rosteringSettings.shouldUseScopes,
        shouldUseSequentialRequests: organization.rosteringSettings.shouldUseSequentialRequests,
        limit: organization.rosteringSettings.limit,
        schoolYear: organization.rosteringSettings.schoolYear,
        translations: organization.rosteringSettings.translations,
        userIdentifiers: organization.rosteringSettings.userIdentifiers,
        filters: organization.rosteringSettings.filters,
        shouldIgnoreEnrollmentStartDate: organization.rosteringSettings.shouldIgnoreEnrollmentStartDate,
        shouldIgnoreEnrollmentEndDate: organization.rosteringSettings.shouldIgnoreEnrollmentEndDate,
        syncSchedule: organization.rosteringSettings.syncSchedule
      };
    }
  }

  return {
    loading,
    organization,
    rosteringSettings,
    filters: get(rosteringSettings, "filters", {}),
    rosteringType
  };
})(Rostering);
