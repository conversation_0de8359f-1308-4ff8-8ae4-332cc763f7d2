import React, { useCallback } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { Meteor } from "meteor/meteor";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>dal<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Form, Row, Col } from "react-bootstrap";
import { Formik } from "formik";
import * as yup from "yup";
import { debounce } from "lodash";
import { FieldError } from "./field-error";

export default function AddSchoolModal(props) {
  const existingSchoolNumbers = props.existingSchools.map(s => s.stateInformation.schoolNumber);
  const existingSchoolNames = props.existingSchools.map(s => s.name);

  const close = () => {
    props.onCloseModal();
  };

  const submitAction = ({ schoolName, schoolNumber }) => {
    Meteor.call("Sites:addSchool", { orgid: props.orgid, schoolName, schoolNumber }, err => {
      if (err) {
        Alert.error("There was a problem adding School");
      } else {
        Alert.success("Successfully added School");
        props.confirmAction();
      }
      close();
    });
  };

  const handleDebouncedTouch = useCallback(
    debounce((setFieldTouched, fieldName) => {
      setFieldTouched(fieldName, true);
    }, 1000),
    []
  );

  const validationSchema = yup.object().shape({
    schoolName: yup
      .string()
      .trim()
      .label("School Name")
      .notOneOf(existingSchoolNames, "Already in use")
      .required("Cannot be empty"),
    schoolNumber: yup
      .string()
      .trim()
      .label("School Number/ID")
      .notOneOf(existingSchoolNumbers, "Already in use")
      .required("Cannot be empty")
  });
  return (
    <Modal
      show={props.showModal}
      onHide={close}
      dialogClassName="confirm-modal"
      backdrop="static"
      data-testid="addSiteModalDialog"
    >
      <Formik
        validationSchema={validationSchema}
        onSubmit={values => submitAction(values)}
        initialValues={{
          schoolName: "",
          schoolNumber: ""
        }}
      >
        {formProps => {
          const { values, handleChange, errors, touched, handleBlur, setFieldTouched, handleSubmit } = formProps;

          const handleChangeWithDebounce = e => {
            const { name } = e.target;
            handleChange(e);
            handleDebouncedTouch(setFieldTouched, name);
          };

          return (
            <Form onSubmit={handleSubmit}>
              <ModalHeader>
                <h2 className="w9">
                  <div className="d-flex align-items-center gap-2">
                    <i className="fa fa-2x fa-warning text-warning pull-left" />
                    <div className="text-nowrap">Add new school</div>
                  </div>
                </h2>
              </ModalHeader>

              <ModalBody>
                <Row className="m-b-10">
                  <Form.Group as={Col} md="6" controlId="name" className="position-relative">
                    <Form.Label>School Name</Form.Label>
                    <Form.Control
                      type="text"
                      required={true}
                      placeholder="School Name"
                      name="schoolName"
                      data-testid="school-name-input"
                      onChange={handleChangeWithDebounce}
                      onBlur={handleBlur}
                      value={values.schoolName}
                      isInvalid={!!errors.schoolName && touched.schoolName}
                    />
                    {touched.schoolName && <FieldError>{errors.schoolName}</FieldError>}
                  </Form.Group>
                  <Form.Group as={Col} md="6" controlId="schoolNumber" className="position-relative">
                    <Form.Label>School Number/ID</Form.Label>
                    <Form.Control
                      type="text"
                      required={true}
                      placeholder="School Number"
                      name="schoolNumber"
                      data-testid="school-number-input"
                      onChange={handleChangeWithDebounce}
                      onBlur={handleBlur}
                      value={values.schoolNumber}
                      isInvalid={!!errors.schoolNumber && touched.schoolNumber}
                    />
                    {touched.schoolNumber && <FieldError>{errors.schoolNumber}</FieldError>}
                  </Form.Group>
                </Row>
              </ModalBody>

              <ModalFooter className="justify-content-center">
                <Button className="btn btn-default" type="submit" data-testid="submitNewSite">
                  {props.confirmText}
                </Button>
                <Button className="btn btn-danger" onClick={close}>
                  {props.cancelText}
                </Button>
              </ModalFooter>
            </Form>
          );
        }}
      </Formik>
    </Modal>
  );
}

AddSchoolModal.propTypes = {
  showModal: PropTypes.bool.isRequired,
  confirmAction: PropTypes.func.isRequired,
  onCloseModal: PropTypes.func.isRequired,
  confirmText: PropTypes.string,
  cancelText: PropTypes.string,
  existingSchools: PropTypes.array,
  orgid: PropTypes.string
};

AddSchoolModal.defaultProps = {
  confirmText: "Add School",
  cancelText: "Cancel",
  schoolNumbersInUse: []
};
