import React, { Component } from "react";
import PropTypes from "prop-types";
import { Form } from "react-bootstrap";

import { getPreviousDayObject } from "/imports/api/utilities/utilities";
import { FieldError } from "./field-error";

export class BenchmarkPeriodsTable extends Component {
  renderBenchmarkPeriodRow = ({ benchmarkPeriodsGroupId, benchmarkPeriod, index, benchmarkPeriods, formProps }) => {
    const benchmarkPeriodStart =
      benchmarkPeriod.startDate[benchmarkPeriodsGroupId] || benchmarkPeriod.startDate.default;
    const benchmarkPeriodEnd = benchmarkPeriod.endDate[benchmarkPeriodsGroupId] || benchmarkPeriod.endDate.default;

    return (
      <tr key={benchmarkPeriod.name}>
        <td>{benchmarkPeriod.name}</td>
        {formProps ? (
          this.renderCustomBenchmarkPeriodRanges({ benchmarkPeriod, index, benchmarkPeriods, formProps })
        ) : (
          <React.Fragment>
            <td>
              {benchmarkPeriodStart.month} / {benchmarkPeriodStart.day}
            </td>
            <td>
              {benchmarkPeriodEnd.month} / {benchmarkPeriodEnd.day}
            </td>
          </React.Fragment>
        )}
      </tr>
    );
  };

  renderCustomBenchmarkPeriodRanges = ({ benchmarkPeriod, index, benchmarkPeriods, formProps }) => {
    const { values, handleChange, errors } = formProps;

    const startMonthFieldName = `${benchmarkPeriod.name.toLowerCase()}StartMonth`;
    const startDayFieldName = `${benchmarkPeriod.name.toLowerCase()}StartDay`;
    const nextBenchmarkPeriodIndex = (index + 1) % benchmarkPeriods.length;
    const nextBenchmarkPeriod = benchmarkPeriods[nextBenchmarkPeriodIndex];
    const nextSeasonStartMonthFieldName = `${nextBenchmarkPeriod.name.toLowerCase()}StartMonth`;
    const nextSeasonStartDayFieldName = `${nextBenchmarkPeriod.name.toLowerCase()}StartDay`;
    let endMonth;
    let endDay;
    if (values[nextSeasonStartMonthFieldName] && values[nextSeasonStartDayFieldName]) {
      ({ month: endMonth, day: endDay } = getPreviousDayObject(
        values[nextSeasonStartMonthFieldName],
        values[nextSeasonStartDayFieldName]
      ));
    }

    return (
      <React.Fragment>
        <td>
          <Form.Control
            type="number"
            size="sm"
            className="d-inline w-25"
            placeholder="Month"
            min="1"
            max="12"
            name={startMonthFieldName}
            value={values[startMonthFieldName]}
            onChange={handleChange}
            isInvalid={!!errors[startMonthFieldName]}
          />
          <FieldError>{errors[startMonthFieldName]}</FieldError>
          {" / "}
          <Form.Control
            type="number"
            size="sm"
            className="d-inline w-25"
            placeholder="Day"
            min="1"
            max="31"
            name={startDayFieldName}
            value={values[startDayFieldName]}
            onChange={handleChange}
            isInvalid={!!errors[startDayFieldName]}
          />
          <FieldError>{errors[startDayFieldName]}</FieldError>
        </td>
        <td>
          {endMonth && endDay ? (
            <>
              {endMonth} / {endDay}
            </>
          ) : (
            `Incorrect ${nextBenchmarkPeriod.name} start date`
          )}
        </td>
      </React.Fragment>
    );
  };

  render() {
    const { formProps, benchmarkPeriodsGroupId } = this.props;

    if (!this.props.benchmarkPeriods?.length) {
      return null;
    }

    return (
      <div>
        <table className="table table-condensed">
          <thead>
            <tr>
              <th className="col-4">Season</th>
              <th className="col-4">Start</th>
              <th className="col-4">End</th>
            </tr>
          </thead>
          <tbody className="l-h-34">
            {this.props.benchmarkPeriods.map((benchmarkPeriod, index, benchmarkPeriods) =>
              this.renderBenchmarkPeriodRow({
                benchmarkPeriodsGroupId,
                benchmarkPeriod,
                index,
                benchmarkPeriods,
                formProps
              })
            )}
          </tbody>
        </table>
      </div>
    );
  }
}

BenchmarkPeriodsTable.propTypes = {
  formProps: PropTypes.object,
  benchmarkPeriods: PropTypes.array,
  benchmarkPeriodsGroupId: PropTypes.string
};
