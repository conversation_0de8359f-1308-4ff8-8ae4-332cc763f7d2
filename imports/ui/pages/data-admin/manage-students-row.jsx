import React, { Component } from "react";
import PropTypes from "prop-types";
import { Link } from "react-router-dom";
import Alert from "react-s-alert";

import { isIdValid, idValidation } from "/imports/api/utilities/utilities";
import { getMinMaxBirthDate } from "./utilities";

export default class ManageStudentsRow extends Component {
  state = {
    studentFirstName: this.props.student.identity.name.firstName,
    studentLastName: this.props.student.identity.name.lastName,
    studentBirthDate: this.props.student.demographic.birthDate,
    studentLocalId: this.props.student.identity.identification.localId,
    studentStateId: this.props.student.identity.identification.stateId,
    isEditing: false
  };

  goToStudentGroup = () => {
    return `/data-admin/manage-group/students/${this.props.orgid}/site/${this.props.siteId}/${this.props.studentGroup._id}`;
  };

  goToUnarchivePage = () => {
    return `/data-admin/manage-school/unarchive/${this.props.orgid}/site/${this.props.siteId}`;
  };

  handleChange = e => {
    this.setState({ [e.target.name]: e.target.value, isEditing: true });
  };

  areInputFieldsValid = () => {
    const { studentBirthDate, studentFirstName, studentLastName, studentLocalId, studentStateId } = this.state;
    if (!studentFirstName.length) {
      Alert.error("Invalid first name");
      return false;
    }
    if (!studentLastName.length) {
      Alert.error("Invalid last name");
      return false;
    }
    if (
      studentBirthDate?.length &&
      (studentBirthDate.length < 8 || studentBirthDate.length > 10 || !Date.parse(studentBirthDate))
    ) {
      Alert.error("Invalid birth date");
      return false;
    }
    if (!studentLocalId.length || !isIdValid(studentLocalId)) {
      Alert.error(`Invalid local ID. It ${idValidation.description}`);
      return false;
    }
    if (!studentStateId.length || !isIdValid(studentStateId)) {
      Alert.error(`Invalid state ID. It ${idValidation.description}`);
      return false;
    }
    return true;
  };

  saveStudentData = () => {
    const newBirthDate = this.state.studentBirthDate;
    const newBirthDateTimestamp = newBirthDate?.length ? new Date(newBirthDate).getTime() : undefined;
    if (this.areInputFieldsValid()) {
      this.setState({ isEditing: false });
      this.props.updateStudentData({
        _id: this.props.student._id,
        firstName: this.state.studentFirstName,
        lastName: this.state.studentLastName,
        birthDate: this.state.studentBirthDate,
        birthDateTimeStamp: newBirthDateTimestamp,
        localId: this.state.studentLocalId,
        stateId: this.state.studentStateId
      });
    }
  };

  isInEditMode(prevProps) {
    return this.props.isEditingStudents !== prevProps.isEditingStudents && this.props.isEditingStudents;
  }

  hasFinishedEditing(prevProps) {
    return this.props.isEditingStudents !== prevProps.isEditingStudents && prevProps.isEditingStudents;
  }

  componentDidUpdate(prevProps) {
    if (this.isInEditMode(prevProps)) {
      this.setState({ isEditing: false });
    } else if (this.hasFinishedEditing(prevProps)) {
      this.setState({
        isEditing: false,
        studentFirstName: this.props.student.identity.name.firstName,
        studentLastName: this.props.student.identity.name.lastName,
        studentBirthDate: this.props.student.demographic.birthDate,
        studentLocalId: this.props.student.identity.identification.localId,
        studentStateId: this.props.student.identity.identification.stateId
      });
    }
  }

  render() {
    const {
      student,
      isEditingStudents,
      siteName,
      showCheckbox,
      selected,
      selectStudent,
      isActive,
      studentGroup,
      isStudentSearch
    } = this.props;
    const studentLastName = student.identity.name.lastName;
    const studentFirstName = student.identity.name.firstName;
    const { birthDate } = student.demographic;

    const { minBirthDate, maxBirthDate } = getMinMaxBirthDate();

    return (
      <tr data-testid={`${student.identity.name.lastName}_row`}>
        <td>
          <label>
            {showCheckbox ? (
              <input
                className="me-2"
                type="checkbox"
                data-testid={`${student.identity.name.lastName}_checkbox`}
                checked={selected}
                onChange={selectStudent}
              />
            ) : null}
            {isEditingStudents ? (
              <input
                className="me-2 form-control input-sm manage-students-row-input"
                type="text"
                onChange={this.handleChange}
                value={this.state.studentLastName}
                name="studentLastName"
                data-testid={`studentLastName_${student._id}`}
              />
            ) : (
              `${studentLastName}, `
            )}
            {isEditingStudents ? (
              <input
                className="me-2 form-control input-sm manage-students-row-input"
                type="text"
                onChange={this.handleChange}
                value={this.state.studentFirstName}
                name="studentFirstName"
                data-testid={`studentFirstName_${student._id}`}
              />
            ) : (
              studentFirstName
            )}
            {!isActive && isStudentSearch ? (
              <Link to={this.goToUnarchivePage()} data-testid="unarchiveLink">
                {" "}
                (Archived)
              </Link>
            ) : (
              ""
            )}
          </label>
        </td>
        {studentGroup && isStudentSearch ? (
          <td>
            {isActive ? (
              <Link to={this.goToStudentGroup()} data-testid="studentGroupLink">
                {studentGroup.name}
              </Link>
            ) : null}
          </td>
        ) : null}
        {siteName ? <td>{siteName}</td> : null}
        {this.props.isUnarchivePage ? <td>{student.grade || "N/A"}</td> : <td>{student.studentGrade || "N/A"}</td>}
        <td>
          {isEditingStudents ? (
            <input
              className="me-2 form-control input-sm manage-students-row-input"
              type="date"
              onChange={this.handleChange}
              value={this.state.studentBirthDate}
              min={minBirthDate}
              max={maxBirthDate}
              name="studentBirthDate"
              data-testid={`studentBirthDate_${student._id}`}
            />
          ) : (
            birthDate || "N/A"
          )}
        </td>
        <td>
          {isEditingStudents ? (
            <input
              className="me-2 form-control input-sm manage-students-row-input"
              type="text"
              onChange={this.handleChange}
              value={this.state.studentLocalId}
              name="studentLocalId"
              data-testid={`studentLocalId_${student._id}`}
            />
          ) : (
            student.identity.identification.localId
          )}
        </td>
        <td>
          {isEditingStudents ? (
            <input
              className="me-2 form-control input-sm manage-students-row-input"
              type="text"
              onChange={this.handleChange}
              value={this.state.studentStateId}
              name="studentStateId"
              data-testid={`studentStateId_${student._id}`}
            />
          ) : (
            student.identity.identification.stateId
          )}
        </td>
        {isEditingStudents ? (
          <td>
            <button
              className={`btn ${this.state.isEditing && "btn-success"}`}
              disabled={!this.state.isEditing}
              onClick={this.saveStudentData}
              data-testid={`saveStudent_${student._id}`}
            >
              Save
            </button>
          </td>
        ) : null}
        <td />
      </tr>
    );
  }
}

ManageStudentsRow.defaultProps = {
  isStudentSearch: false
};

ManageStudentsRow.propTypes = {
  student: PropTypes.object,
  selected: PropTypes.bool,
  studentGroup: PropTypes.object,
  showCheckbox: PropTypes.bool,
  siteId: PropTypes.string,
  siteName: PropTypes.string,
  orgid: PropTypes.string,
  selectStudent: PropTypes.func,
  isStudentSearch: PropTypes.bool,
  isActive: PropTypes.bool,
  isUnarchivePage: PropTypes.bool,
  history: PropTypes.object,
  isEditingStudents: PropTypes.bool,
  updateStudentData: PropTypes.func
};
