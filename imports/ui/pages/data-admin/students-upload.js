import { Meteor } from "meteor/meteor";
import React, { useState, useContext, useCallback } from "react";
import Dropzone from "react-dropzone";
import { difference, union, flatten, each } from "lodash";
import Papa from "papaparse";
import PropTypes from "prop-types";
import { useHistory, Link } from "react-router-dom";
import { useTracker } from "meteor/react-meteor-data";
import { getCSV } from "./upload/file-upload-utils";
import { decWaitingOn, incWaitingOn } from "/imports/api/loadingCounter/methods";
import { requiredStudentUploadFields, optionalStudentUploadFields, download } from "../../utilities";
import { RosterImportItems } from "/imports/api/rosterImportItems/rosterImportItems";
import RosterImportItemsHelpers from "/imports/api/rosterImportItems/methods";
import RosterImportItemsValidator from "/imports/api/rosterImportItems/rosterImportItemsValidator";
import { Users } from "/imports/api/users/users";
import { SiteContext } from "/imports/contexts/SiteContext";
import { OrganizationContext } from "/imports/contexts/OrganizationContext";
import { Loading } from "/imports/ui/components/loading";

const requiredFieldsExample = {
  StudentLocalID: "685123",
  StudentStateID: "3999000685555",
  StudentLastName: "Anderson",
  StudentFirstName: "Gail",
  StudentBirthDate: "2005-01-03"
};

function StudentsUpload({ uploadStudents, isNewGroup, studentGroup }) {
  const [data, setData] = useState([]);
  const [parseMeta, setParseMeta] = useState({});
  const [fileName, setFileName] = useState("");
  const [errors, setErrors] = useState([]);
  const [fileDropped, setFileDropped] = useState(false);

  const history = useHistory();
  const { site } = useContext(SiteContext);
  const { org } = useContext(OrganizationContext);

  const { teacher, loading } = useTracker(() => {
    if (!studentGroup?.orgid || !studentGroup?.siteId) {
      return { teacher: {}, loading: true };
    }

    const { orgid, siteId } = studentGroup;
    const usersHandler = Meteor.subscribe("Users", { orgid });
    const sitesHandler = Meteor.subscribe("Sites", orgid, siteId);
    const orgHandler = Meteor.subscribe("Organizations", orgid);
    const isLoading = !usersHandler.ready() || !sitesHandler.ready() || !orgHandler.ready();

    let teacherData = {};
    if (!isLoading && studentGroup?.ownerIds?.[0]) {
      teacherData = Users.findOne(studentGroup.ownerIds[0]) || {};
    }

    return { teacher: teacherData, loading: isLoading };
  }, [studentGroup?.orgid, studentGroup?.siteId, studentGroup?.ownerIds]);

  const setInvalidFieldsError = useCallback((invalidFields, currentFileName, unsupportedFields = false) => {
    setFileName(currentFileName);
    const errorText = unsupportedFields
      ? "Unsupported fields found:\n"
      : "Your CSV file is missing the following fields:\n";
    setErrors([
      `${errorText}${invalidFields.join(
        "\n"
      )}\n\nPlease make sure you are using the latest CSV Template for File Uploads.`
    ]);
    setFileDropped(true);
  }, []);

  const validateAndSetData = useCallback(
    (dataToValidate, cb) => {
      const validationErrors = [];
      const students = new Map();
      const fileUploadLineItems = [];
      const processFileUploadLineItemFunctions = [];
      const schema = RosterImportItems.schemaStudentData;
      incWaitingOn(1, "Validating!");

      function processFileUploadLineItem(datum, index) {
        return new Promise(resolve => {
          const fuli = RosterImportItemsHelpers.createFromCSVDataRow(datum);
          fileUploadLineItems.push(fuli);
          try {
            schema.validate(fuli.data);
          } catch (e) {
            e.details.forEach(error => {
              const message = schema.messageForError(error);
              validationErrors.push(`${message}. Please see row: ${index + 2}`);
            });

            resolve(false);
          }
          students.set(
            `${fuli.data.studentStateID}` +
              `${fuli.data.studentFirstName}` +
              `${fuli.data.studentLastName}` +
              `${fuli.data.studentLocalID}` +
              `${fuli.data.studentBirthDate}`,
            true
          );

          resolve(true);
        });
      }

      each(dataToValidate, (datum, index) => {
        processFileUploadLineItemFunctions.push(processFileUploadLineItem(datum, index));
      });

      Promise.all(processFileUploadLineItemFunctions).then(async () => {
        const result = await new RosterImportItemsValidator(fileUploadLineItems).validate();

        if (!result.success) {
          const extraErrors = flatten(Object.values(result.errors));
          validationErrors.push(...extraErrors);
        }
        const studentLocalStateIds = dataToValidate.map(student => ({
          localId: student.StudentLocalID,
          stateId: student.StudentStateID
        }));

        Meteor.call("RosterImports:CheckIfLocalAndStateIdsAreUnique", studentLocalStateIds, site?.orgid, (err, res) => {
          if (!err) {
            res.forEach(duplicatedIdError => {
              validationErrors.push(duplicatedIdError);
            });
            decWaitingOn();
            cb(validationErrors);
          }
        });
      });
    },
    [site, incWaitingOn, decWaitingOn, each, flatten]
  );

  const goBackToSchool = useCallback(() => {
    history.push(
      `/data-admin/manage-group/students/${studentGroup?.orgid}/site/${studentGroup?.siteId}/${studentGroup?._id}`
    );
  }, [history, studentGroup]);

  const handleCancel = useCallback(() => {
    setData([]);
    setFileName("");
    setFileDropped(false);
    if (isNewGroup) {
      goBackToSchool();
    }
  }, [isNewGroup, goBackToSchool]);

  const handleSubmit = useCallback(
    event => {
      event.preventDefault();
      uploadStudents({
        parseMeta,
        data
      });
    },
    [uploadStudents, parseMeta, data]
  );

  const parsingComplete = useCallback(
    (results, currentFileName) => {
      const resultsWithTeacherData = { ...results };

      let { grade } = studentGroup;
      if (grade?.includes("0") && grade !== "10") {
        [, grade] = studentGroup.grade;
      }

      resultsWithTeacherData.data = results.data.map(student => ({
        ...student,
        DistrictID: site?.stateInformation?.districtNumber,
        DistrictName: org?.name,
        SchoolID: site?.stateInformation?.schoolNumber,
        SchoolName: site?.name,
        TeacherID: teacher?.profile?.localId,
        TeacherLastName: teacher?.profile?.name?.last,
        TeacherFirstName: teacher?.profile?.name?.first,
        TeacherEmail: teacher?.emails?.[0]?.address,
        ClassName: studentGroup?.name,
        ClassSectionID: studentGroup?.sectionId,
        SpringMathGrade: grade
      }));
      decWaitingOn();
      if (!(results.data && results.data.length > 0)) {
        setFileDropped(true);
        return;
      }
      const missingFields = difference(requiredStudentUploadFields, results.meta.fields);
      const unsupportedFields = difference(results.meta.fields, [
        ...requiredStudentUploadFields,
        ...optionalStudentUploadFields
      ]);
      if (missingFields.length) {
        setInvalidFieldsError(missingFields, currentFileName);
        return;
      }
      if (unsupportedFields.length) {
        setInvalidFieldsError(unsupportedFields, currentFileName, true);
        return;
      }

      let validationErrors = results.errors ? results.errors : [];

      validateAndSetData(resultsWithTeacherData.data, validationErrorsFromCallback => {
        setParseMeta(results.meta);
        setData(resultsWithTeacherData.data);
        setFileName(currentFileName);
        validationErrors = union(results.errors, validationErrorsFromCallback);
        setErrors(validationErrors);
        setFileDropped(true);
      });
    },
    [
      site,
      org,
      studentGroup,
      teacher,
      setInvalidFieldsError,
      validateAndSetData,
      decWaitingOn,
      difference,
      requiredStudentUploadFields,
      optionalStudentUploadFields,
      union
    ]
  );

  const onDrop = useCallback(
    files => {
      const currentFileName = files[0] ? files[0]?.name : "No Name Found";
      incWaitingOn(1, "Parsing CSV!");
      Papa.parse(files[0], {
        header: true,
        skipEmptyLines: true,
        complete: results => parsingComplete(results, currentFileName)
      });
    },
    [parsingComplete, incWaitingOn, Papa]
  );

  const renderUploadGuidelines = () => (
    <div id="resultsArea" className="col-12">
      <div className="animated fadeIn">
        <h3 className="w7">Student file guidelines:</h3>
        <ol className="file-upload-guidelines">
          <li>Student files must be in a .CSV (comma separated value text file) format.</li>
          <li>
            <strong>Upload will allow you to add new students, without changing the current list.</strong>
          </li>
          <li>
            The file must contain values in every record for the mandatory fields. See the{" "}
            <Link to="/assets/Description of Variables_Manage Upload Fields.pdf" target="_blank">
              Description of variables
            </Link>{" "}
            for more information.
          </li>
        </ol>
      </div>
      {isNewGroup ? (
        <fieldset className="form-group">
          <div className="row">
            <div className="col-4 offset-8 pull-right">
              <button type="submit" className="btn btn-primary form-control" onClick={goBackToSchool}>
                Manually Add Students
              </button>
            </div>
          </div>
        </fieldset>
      ) : null}
    </div>
  );

  const saveErrorsToFile = useCallback(() => {
    const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf(".")) || fileName;
    const filename = `${fileNameWithoutExtension}_ERRORS.txt`;
    const errorText = errors.map(error => error).join("\n\n");
    const hrefData = `data:text/plain;charset=utf-8,${encodeURIComponent(errorText)}`;
    download({ filename, hrefData });
  }, [fileName, errors, download]);

  const canFinalizeUpload = !!(studentGroup && site && org && teacher && !loading);

  return (
    <div>
      <div className="row">
        <div className="col-6 offset-3">
          <div className="card-box">
            <div className="row">
              <div className="col-12">
                {canFinalizeUpload ? (
                  <Dropzone id="dz1" className="alert text-xs-center alert-success" onDrop={onDrop} disablePreview>
                    <div className="text-center">
                      {fileName ? (
                        <h3 className="animated fadeIn drop-zone-file-name">{fileName}</h3>
                      ) : (
                        <div className="animated fadeIn">
                          <span>
                            Try dropping your file here,
                            <br />
                            or click to select a file to upload.
                          </span>
                        </div>
                      )}
                    </div>
                  </Dropzone>
                ) : (
                  <Loading inline={true} />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="row">
        <div className="col-12">
          <p htmlFor="exampleSelect1">Would you like a helper file to know how to layout your data?</p>
          <a download="CSVHelperFile.csv" href={`data:application/octet-stream,${getCSV([requiredFieldsExample])}`}>
            Download CSV Template for File Uploads
          </a>
          <br />
          <Link to="/assets/Description of Variables_Manage Upload Fields.pdf" target="_blank">
            Description of variables
          </Link>
        </div>
        {!fileDropped
          ? renderUploadGuidelines()
          : (() => {
              if (data.length < 1 || errors.length > 0) {
                if (errors.length > 0) {
                  const errorText = errors.map(error => error).join("\n\n");
                  const newLinesCount = errorText.match(/\n/g) ? errorText.match(/\n/g).length : 0;
                  const rows = Math.min(newLinesCount + 1, 40);
                  return (
                    <div id="resultsArea" className="col-12">
                      <div className="animated fadeIn">
                        <div className="pull-right">
                          <button className="btn btn-primary" onClick={saveErrorsToFile}>
                            Save errors to file
                          </button>
                        </div>
                        <h2 className="w7">Sorry</h2>
                        <p> The upload contained errors: </p>
                        <textarea disabled className="form-control alert alert-danger" rows={rows} value={errorText} />
                      </div>
                    </div>
                  );
                }
                return (
                  <div id="resultsArea" className="col-9">
                    <div className="">
                      <h2 className="w7">Sorry</h2>
                      <p> The upload tool found no parsable data. </p>
                    </div>
                  </div>
                );
              }
              return (
                <div id="resultsArea" className="col-12">
                  <div className="animated fadeIn">
                    <h3 id="congratulationsHeader" className="w7">
                      Congratulations!
                    </h3>
                    <p>
                      {" "}
                      Nice job on the upload. The data looks good to us! Below is a list of students who we are going to
                      insert into the app.
                    </p>
                    <div className="row">
                      <table className="table table-condensed">
                        <thead>
                          <tr>
                            <th>Name</th>
                            <th>Date of Birth</th>
                            <th>Local ID</th>
                            <th>State ID</th>
                            <th />
                          </tr>
                        </thead>
                        <tbody>
                          {data.map(student => (
                            <tr key={student.StudentLocalID} data-testid="studentUploadRow">
                              <td>
                                {student.StudentFirstName} {student.StudentLastName}
                              </td>
                              <td>{student.StudentBirthDate}</td>
                              <td>{student.StudentLocalID}</td>
                              <td>{student.StudentStateID}</td>
                              <td />
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>

                    <fieldset className="form-group">
                      <div className="row justify-content-between">
                        <div className="col-4">
                          <button type="button" className="btn btn-danger form-control" onClick={handleCancel}>
                            Cancel
                          </button>
                        </div>
                        <div className="col-4">
                          <button
                            type="submit"
                            className="btn btn-primary form-control"
                            onClick={handleSubmit}
                            disabled={!canFinalizeUpload}
                            title={"Finalize the student upload"}
                          >
                            Finalize Upload
                          </button>
                        </div>
                      </div>
                    </fieldset>
                  </div>
                </div>
              );
            })()}
      </div>
    </div>
  );
}

StudentsUpload.propTypes = {
  isNewGroup: PropTypes.bool,
  uploadStudents: PropTypes.func,
  studentGroup: PropTypes.object
};

export default StudentsUpload;
