import React from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>oot<PERSON>, <PERSON><PERSON> } from "react-bootstrap";

const ConfirmModal = ({
  showModal,
  onCloseModal,
  confirmAction,
  headerText,
  bodyText,
  bodyQuestion,
  confirmText,
  cancelText,
  isWarning,
  customProps,
  cancelButtonClassName,
  confirmButtonClassName,
  size
}) => {
  const close = () => {
    onCloseModal();
  };

  const handleConfirmAction = () => {
    if (confirmAction) {
      confirmAction();
    }
    close();
  };

  const renderHeader = () => {
    const { useCustomHeader } = customProps;

    if (useCustomHeader) {
      return headerText;
    }

    return (
      <ModalHeader>
        <h3 className="w9 d-flex">
          <i className="fa fa-2x fa-warning text-warning pull-left" />
          <div>{headerText}</div>
        </h3>
      </ModalHeader>
    );
  };

  const renderBodyText = () => {
    if (
      (typeof bodyText === "string" && !bodyText.length) ||
      (typeof bodyText === "object" && !Object.keys(bodyText))
    ) {
      return null;
    }
    return <div className={isWarning ? "alert-error text-center" : ""}>{bodyText}</div>;
  };

  const renderBodyQuestion = () => {
    if (!bodyQuestion.length) {
      return null;
    }
    return (
      <p>
        <strong className={isWarning ? "alert-error" : ""}>{bodyQuestion}</strong>
      </p>
    );
  };

  const renderBody = () => {
    return (
      <ModalBody>
        {renderBodyText()}
        {renderBodyQuestion()}
      </ModalBody>
    );
  };

  const renderConfirmButton = () => {
    if (!confirmText.length) {
      return null;
    }

    const buttonProps = {};

    if (confirmButtonClassName) {
      buttonProps.className = confirmButtonClassName;
    } else {
      buttonProps.variant = "success";
    }

    return (
      <Button {...buttonProps} onClick={handleConfirmAction} data-testid="confirm-modal-btn">
        {confirmText}
      </Button>
    );
  };

  const renderCancelButton = () => {
    if (!cancelText.length) {
      return null;
    }

    const buttonProps = {};

    if (cancelButtonClassName) {
      buttonProps.className = cancelButtonClassName;
    } else {
      buttonProps.variant = "default";
    }

    return (
      <Button {...buttonProps} onClick={close} data-testid="cancel-modal-btn">
        {cancelText}
      </Button>
    );
  };

  const renderFooter = () => {
    return (
      <ModalFooter className="d-flex justify-content-center">
        {renderConfirmButton()}
        {renderCancelButton()}
      </ModalFooter>
    );
  };

  const getModalClassName = () => {
    const { shouldUseCustomShadow, customModalClassName } = customProps;
    let modalClassName = `confirm-modal`;
    if (shouldUseCustomShadow) {
      modalClassName += " modal-shadow m-t-50";
    }
    if (customModalClassName) {
      modalClassName += ` ${customModalClassName}`;
    }
    return modalClassName;
  };

  const { canCloseUsingBackdrop } = customProps;
  const modalClassName = getModalClassName();

  return (
    <Modal
      show={showModal}
      onHide={close}
      dialogClassName={modalClassName}
      backdrop={!confirmText.length || canCloseUsingBackdrop ? true : "static"}
      data-testid="confirmModalDialog"
      size={size}
    >
      {renderHeader()}
      {renderBody()}
      {renderFooter()}
    </Modal>
  );
};

ConfirmModal.propTypes = {
  showModal: PropTypes.bool.isRequired,
  confirmAction: PropTypes.func,
  onCloseModal: PropTypes.func.isRequired,
  headerText: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  bodyText: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  bodyQuestion: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  confirmText: PropTypes.string,
  cancelText: PropTypes.string,
  cancelButtonClassName: PropTypes.string,
  confirmButtonClassName: PropTypes.string,
  isWarning: PropTypes.bool,
  size: PropTypes.string,
  customProps: PropTypes.object
};

ConfirmModal.defaultProps = {
  headerText: "Are you sure?",
  bodyText: "",
  bodyQuestion: "Do you wish to continue?",
  confirmText: "Yes",
  cancelText: "No, Cancel",
  isWarning: false,
  customProps: {},
  cancelButtonClassName: "",
  confirmButtonClassName: "",
  confirmAction: () => {},
  onCloseModal: () => {}
};

export default ConfirmModal;
