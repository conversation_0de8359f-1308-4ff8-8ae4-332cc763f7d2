import React, { Component } from "react";
import PropTypes from "prop-types";
import { with<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { Meteor } from "meteor/meteor";
import { withTracker } from "meteor/react-meteor-data";
import <PERSON> from "papa<PERSON><PERSON>";
import <PERSON><PERSON> from "react-s-alert";
import { last, keyBy, startCase } from "lodash";
import { Button, ButtonGroup, Dropdown, DropdownButton } from "react-bootstrap";

import SchoolItem from "../../components/data-admin/school-item/school-item.jsx";
import { RosterImports } from "/imports/api/rosterImports/rosterImports";
import { Organizations } from "/imports/api/organizations/organizations";
import { Users } from "/imports/api/users/users";
import LastImport from "../../components/data-admin/upload/last-import.jsx";
import { Loading } from "../../components/loading.jsx";
import PageHeader from "../../components/page-header.jsx";
import ConfirmModal from "./confirm-modal";
import ManageSSOOrgModal from "./manage-sso-org-modal";
import { areSubscriptionsLoading, download, isExternalRostering } from "../../utilities";
import NewsBanner from "../../components/dashboard/news-banner";
import { getUserRoles } from "./utilities";
// TODO(fmazur) - move to more appropriate place
import { ROLE_IDS } from "../../../../tests/cypress/support/common/constants";
import { getMeteorUserId, getMeteorUserSync } from "/imports/api/utilities/utilities";
import DistrictSettingsModal from "./district-settings-modal";
import TooltipWrapper from "../../components/tooltip-wrapper";
import AddSchoolModal from "./add-school-modal";
import UserProfile from "../../components/user/user-profile";

function getCSV(data) {
  return encodeURIComponent(Papa.unparse(data, { delimiter: ",", newline: "\r\n" }));
}

export function exportRoster(orgid) {
  Meteor.call("RosterImports:exportRoster", orgid, (err, response) => {
    if (err) {
      console.log("RosterImports:exportRoster error", err);
    } else {
      const hrefData = `data:application/octet-stream,${getCSV(response)}`;
      download({ filename: "export.csv", hrefData });
    }
  });
}

function renderLastImport({ loading, lastRosterImport, orgid, rostering }, fileUploadLink) {
  if (!rostering || isExternalRostering(rostering)) {
    return null;
  }

  let content;

  if (loading) {
    content = <Loading inline message="Fetching import information" />;
  } else if (lastRosterImport) {
    content = <LastImport lastRosterImport={lastRosterImport} orgid={orgid} />;
  } else {
    content = (
      <div className="text-center">
        <ul>
          <li>{"Your organization doesn't have any data uploaded yet."}</li>
          <li>
            <Link to={fileUploadLink} className="btn btn-success">
              UPLOAD DATA <i className="fa fa-upload" />
            </Link>
          </li>
        </ul>
      </div>
    );
  }

  return (
    <React.Fragment>
      <h6>Last Import</h6>
      {content}
    </React.Fragment>
  );
}

class DataAdminDashboard extends Component {
  state = {
    activelyEnrolledStudentsInSites: [],
    activeStudentGroupsCountInSites: [],
    schoolItemData: [],
    isFetchingStudentsCount: false,
    isFetchingStudentGroupsCount: false,
    isFetchingSchoolItemData: false,
    showRemoveModal: false,
    showEmailModal: false,
    showSSOOrgModal: false,
    showDistrictSettingsModal: false,
    isMFARequired: false,
    useSSOOnly: false,
    dataAdminId: null,
    message: null,
    rosteringMessage: null,
    externalAssessmentScoresSchoolYears: [],
    externalAssessmentScoresSchoolYear: undefined,
    rostering: "",
    allowClasswideWithoutScreening: false,
    isModalOpen: false,
    canOrgStartClasswidePriorToScreening: false,
    showClasswidePriorToScreeningModal: false,
    isExternalRosteringNotificationOpen: true,
    ssoIssuerOrgId: "",
    isAddSchoolModalOpen: false,
    isProfileModalOpen: false,
    selectedUserId: null
  };

  componentDidMount() {
    this.loadData();
  }

  loadData = () => {
    this.setState({
      isFetchingStudentsCount: true,
      isFetchingStudentGroupsCount: true
    });
    Meteor.call("Students:getNumberOfActiveStudents", this.props.orgid, (err, resp) => {
      if (!err) {
        this.setState({ activelyEnrolledStudentsInSites: resp });
      } else {
        Alert.error("Error getting number of active students");
      }
      this.setState({ isFetchingStudentsCount: false });
    });

    Meteor.call("StudentGroups:getActiveStudentGroupsCountInSites", this.props.orgid, (err, resp) => {
      if (!err) {
        this.setState({ activeStudentGroupsCountInSites: resp });
      } else {
        Alert.error("Error getting number of active student groups");
      }
      this.setState({ isFetchingStudentGroupsCount: false });
    });

    const isSupportUser = this.props.userRole === "support";
    if (!isSupportUser) {
      Meteor.call("AssessmentScoresUpload:getSchoolYears", this.props.orgid, (err, schoolYears) => {
        if (!err) {
          this.setState({
            externalAssessmentScoresSchoolYears: schoolYears,
            externalAssessmentScoresSchoolYear: last(schoolYears)
          });
        } else {
          Alert.error("Error getting school years for the External Assessment Scores");
        }
      });
    }

    this.getSchoolItemData();
    this.getActiveMessages();
    this.getOrganizationFieldValues();
  };

  componentDidUpdate(prevProps) {
    if (prevProps.schoolYear && this.props.schoolYear && prevProps.schoolYear !== this.props.schoolYear) {
      this.loadData();
    }
  }

  getOrganizationFieldValues = () => {
    const userRole = getUserRoles();
    const isSuperAdminOrUniversalDataAdmin = userRole.includes("universalDataAdmin") || userRole.includes("superAdmin");
    Meteor.call(
      "Organizations:getOrganizationFieldValues",
      this.props.orgid,
      [
        "allowClasswideWithoutScreening",
        "canOrgStartClasswidePriorToScreening",
        "rostering",
        "ssoIssuerOrgId",
        "useSSOOnly",
        "isMFARequired"
      ],
      (err, resp) => {
        if (!err && resp) {
          const {
            allowClasswideWithoutScreening = false,
            rostering,
            canOrgStartClasswidePriorToScreening,
            ssoIssuerOrgId,
            useSSOOnly = false,
            isMFARequired = false
          } = resp;
          let parsedRostering = rostering;
          if (resp.rostering === "rosterUpload" && isSuperAdminOrUniversalDataAdmin) {
            parsedRostering = "rosterImport";
          }
          this.setState({
            rostering: parsedRostering,
            allowClasswideWithoutScreening,
            canOrgStartClasswidePriorToScreening: canOrgStartClasswidePriorToScreening || false,
            ssoIssuerOrgId,
            useSSOOnly,
            isMFARequired
          });
        }
      }
    );
  };

  shouldRenderAllowClasswideWithoutScreeningModal = () => {
    if (!this.state.canOrgStartClasswidePriorToScreening) {
      this.setState({ showClasswidePriorToScreeningModal: true });
    } else {
      this.updateOrganizationFieldValue(
        "canOrgStartClasswidePriorToScreening",
        !this.state.canOrgStartClasswidePriorToScreening
      );
    }
  };

  updateDistrictSSOId = districtSSOId => {
    this.updateOrganizationFieldValue("ssoIssuerOrgId", districtSSOId);
    this.updateOrganizationFieldValue("useSSOOnly", false);
  };

  updateUseSSOOnly = () => {
    this.updateOrganizationFieldValue("useSSOOnly", !this.state.useSSOOnly);
  };

  updateIsMFARequired = () => {
    this.updateOrganizationFieldValue("isMFARequired", !this.state.isMFARequired);
  };

  updateOrganizationFieldValue = (keyToSet, value) => {
    const orgIdsWithMFADisabled = Meteor.settings.public.ORG_IDS_WITH_MFA_DISABLED || [];
    if (keyToSet === "isMFARequired" && orgIdsWithMFADisabled.includes(this.props.orgid)) {
      Alert.error("Organization has the MFA feature disabled");
      return;
    }
    Meteor.call("Organizations:updateOrganizationFieldValue", this.props.orgid, keyToSet, value, err => {
      if (err) {
        Alert.error(err.reason || err.message || "There was a problem updating organization");
      } else {
        Alert.success(`Successfully updated ${startCase(keyToSet)}`);
        const newState = {
          [keyToSet]: value
        };
        if (keyToSet === "useSSOOnly" && value === true) {
          newState.isMFARequired = true;
        }
        this.setState(newState);
      }
    });
  };

  getActiveMessages = () => {
    const { orgid } = this.props;
    Meteor.call("News:getActiveMessage", { orgid, multi: true }, (err, resp) => {
      if (err) {
        Alert.error("There was a problem getting active message.", err.reason || err.message);
      } else {
        const messagesByType = keyBy(resp, ({ type }) => type || "default");
        this.setState({ message: messagesByType.default, rosteringMessage: messagesByType.rostering });
      }
    });
  };

  getSchoolItemData = () => {
    this.setState({
      isFetchingSchoolItemData: true
    });
    Meteor.call("Sites:getSchoolItemData", this.props.orgid, (err, resp) => {
      if (!err) {
        this.setState({ schoolItemData: resp });
      } else {
        Alert.error("Error getting school items data");
      }
      this.setState({ isFetchingSchoolItemData: false });
    });
  };

  isFetchingData = () =>
    [
      this.props.loading,
      this.state.isFetchingStudentsCount,
      this.state.isFetchingStudentGroupsCount,
      this.state.isFetchingSchoolItemData
    ].some(handler => handler);

  openRemoveModal = dataAdminId => {
    this.setState({ dataAdminId, showRemoveModal: true });
  };

  openEmailModal = dataAdminId => {
    this.setState({ dataAdminId, showEmailModal: true });
  };

  openProfileModal = userId => {
    this.setState({ isProfileModalOpen: true, selectedUserId: userId });
  };

  closeProfileModal = () => {
    this.setState({ isProfileModalOpen: false, selectedUserId: null });
  };

  removeDataAdministrator = () => {
    if (this.state.dataAdminId) {
      Meteor.call("users:removeDataAdminUsers", this.props.orgid, [this.state.dataAdminId], err => {
        if (err) {
          this.setState({ showModal: false });
          Alert.error(err.message, {
            timeout: 3000
          });
        } else {
          this.setState({ dataAdminId: null, showRemoveModal: false });
          Alert.success("Data administrator removed", {
            timeout: 3000
          });
        }
      });
    }
  };

  resendInvitationEmail = () => {
    if (this.state.dataAdminId) {
      Meteor.call("users:sendEnrollmentEmail", this.state.dataAdminId, "", err => {
        if (err) {
          this.setState({ showEmailModal: false });
          Alert.error(err.message, {
            timeout: 3000
          });
        } else {
          this.setState({ dataAdminId: null, showEmailModal: false });
          Alert.success("Invitation email successfully resend.", {
            timeout: 3000
          });
        }
      });
    }
  };

  closeModal = () => {
    this.setState({
      showRemoveModal: false,
      showEmailModal: false,
      showSSOOrgModal: false,
      dataAdminId: null
    });
  };

  exportPreviousRoster = () => {
    Meteor.call("RosterImports:exportPreviousRoster", this.props.orgid, (err, response) => {
      if (err) {
        console.log("RosterImports:exportPreviousRoster error", err);
      } else {
        const hrefData = `data:application/octet-stream,${getCSV(response)}`;
        download({ filename: "export.csv", hrefData });
      }
    });
  };

  exportExternalAssessmentScores = () => {
    const { orgid, org } = this.props;
    const { externalAssessmentScoresSchoolYear: schoolYear } = this.state;
    Meteor.call("AssessmentScoresUpload:export", { orgid, schoolYear }, (err, response) => {
      if (err) {
        Alert.error(err.message || "Error while exporting External Assessment Scores");
      } else if (response.length) {
        const hrefData = `data:application/octet-stream,${getCSV(response)}`;
        download({ filename: `External Assessment Scores ${org.name} ${schoolYear}.csv`, hrefData });
      } else {
        Alert.info(`There are no External Assessment Scores in ${schoolYear}`);
      }
    });
  };

  setExternalAssessmentScoresSchoolYear = schoolYear => e => {
    e.preventDefault();
    this.setState({ externalAssessmentScoresSchoolYear: schoolYear });
  };

  getRosteringLink = () => {
    const { orgid = "" } = this.props;
    const { rostering } = this.state;
    const routePath = {
      rosterUpload: `/data-admin/upload/${orgid}`,
      rosterImport: `/data-admin/upload/${orgid}`,
      rosterEdFi: `/data-admin/rostering/${orgid}`,
      rosterOR: `/data-admin/rostering/${orgid}`
    };
    const rosteringHeader = {
      rosterUpload: "Upload Roster file to SpringMath Support",
      rosterImport: "Import Records",
      rosterEdFi: "Manage Ed-Fi Rostering",
      rosterOR: "Manage OneRoster Rostering"
    };

    return rostering ? (
      <Link to={routePath[rostering]} className="list-action" data-testid="import-records-testid">
        {rosteringHeader[rostering]} <i className="fa fa-upload" />
      </Link>
    ) : (
      <Loading inline={true} />
    );
  };

  changeDataAdminSiteId = user => e => {
    const siteId = e.target.value;
    const siteAccess = [{ role: ROLE_IDS.dataAdmin, siteId, isActive: true }];
    Meteor.call("users:modifyAccess", user._id, siteAccess, ROLE_IDS.dataAdmin, err => {
      if (err) {
        Alert.error("There was a problem while updating user site access", {
          timeout: 5000
        });
      } else {
        Alert.success("Site access updated successfully", {
          timeout: 5000
        });
      }
    });
  };

  showAddSchoolModal = () => {
    this.setState({ isAddSchoolModalOpen: true });
  };

  hideAddSchoolModal = () => {
    this.setState({ isAddSchoolModalOpen: false });
  };

  render() {
    const { props, state } = this;
    const { loading, lastRosterImport, orgid } = props;
    const isFetchingData = this.isFetchingData();

    const userRole = getUserRoles();
    const myUserId = getMeteorUserId();
    const isSuperAdminOrUniversalDataAdmin = userRole.includes("universalDataAdmin") || userRole.includes("superAdmin");
    const orgIdsWithMFADisabled = Meteor.settings.public.ORG_IDS_WITH_MFA_DISABLED || [];
    const hasMFADisabled = orgIdsWithMFADisabled.includes(orgid);
    const canManageOrganization = isSuperAdminOrUniversalDataAdmin || userRole.includes("dataAdmin");
    const fileUploadLink = !props.loading ? `/data-admin/upload/${props.orgid || ""}` : "";
    const externalAssessmentScoresUploadLink = !props.loading
      ? `/data-admin/upload-assessment-scores/${props.orgid || ""}`
      : "";
    const addCoachLink = `/coach-account-setup/${props.orgid}`;
    const addDataAdminLink = `/data-admin-account-setup/${props.orgid}`;
    let totalNumberOfStudentsInOrg = 0;
    if (state.activelyEnrolledStudentsInSites.length) {
      totalNumberOfStudentsInOrg = state.activelyEnrolledStudentsInSites.reduce((acc, cv) => acc + cv.totalStudents, 0);
    }
    const isSupportUser = this.props.userRole === "support";
    const supportViewClassName = isSupportUser ? " support-view" : "";
    const totalNumberOfTeachersInOrg = state.schoolItemData.reduce(
      (acc, cv) => acc + cv.teachersOnBoardedCount + cv.teachersYetToOnBoardCount,
      0
    );

    const isExportExternalAssessmentScoresButtonEnabled = !!state.externalAssessmentScoresSchoolYears.length;
    const schoolNumbers = state.schoolItemData?.map(s => s.stateInformation.schoolNumber) || [];
    const schoolNames = state.schoolItemData?.map(s => s.name) || [];

    const renderSchoolItems = () => {
      if (isFetchingData) {
        return <Loading />;
      }

      return state.schoolItemData.length === 0 ? (
        <h1 className="stamped">No Sites Uploaded</h1>
      ) : (
        state.schoolItemData.map((site, index) => {
          const schoolData = state.activelyEnrolledStudentsInSites.find(school => school._id === site._id);
          const studentsCount = schoolData && schoolData.totalStudents ? schoolData.totalStudents : 0;
          const studentGroupsInSite = state.activeStudentGroupsCountInSites.find(item => item._id === site._id);
          const studentGroupsCount = studentGroupsInSite ? studentGroupsInSite.activeGroupsCount : 0;
          return (
            <SchoolItem
              ref={index}
              key={index}
              site={site}
              teachersYetToOnboardCount={site.teachersYetToOnBoardCount}
              teachersCount={site.teachersOnBoardedCount}
              admins={site.siteAdmins}
              studentGroupsCount={studentGroupsCount}
              studentsCount={studentsCount}
              loading={state.isFetchingStudentsCount}
              userRole={this.props.userRole}
              orgid={this.props.orgid}
              getSchoolItemData={this.getSchoolItemData}
              otherSchoolNumbers={schoolNumbers.filter(s => s !== site.stateInformation.schoolNumber)}
              otherSchoolNames={schoolNames.filter(s => s !== site.name)}
            />
          );
        })
      );
    };

    const shouldDisplayAddSchoolButton = ["rosterImport", "rosterUpload"].includes(state.rostering);
    return (
      <div className="conFullScreen">
        <PageHeader title={props.org?.name || ""} />
        {!isSupportUser ? (
          <div className="header-action-button-container d-flex gap-1">
            <button
              type="button"
              className="btn btn-primary"
              onClick={() => this.setState({ showDistrictSettingsModal: true })}
            >
              <i className="fa fa-gear" /> District Settings
            </button>
            <Link to={`/data-admin/manage-school/search-students/${this.props.orgid}`}>
              <button type="button" className="btn btn-primary">
                <i className="fa fa-search" /> Student Search
              </button>
            </Link>
          </div>
        ) : null}
        <div className="news-data-admin-dashboard-offset">
          {state.message ? (
            <div data-testid="global-news-banner">
              <NewsBanner message={state.message} />
            </div>
          ) : null}
        </div>
        <div className="container">
          <div className="row">
            <div className="col-sm-12">
              <div className="entity-header">
                <div className="row">
                  <div className="col-md-12">
                    <ul className="nav nav-pills gap-4">
                      <li className="small">
                        Schools <span className="badge rounded-pill bg-secondary">{state.schoolItemData.length}</span>
                      </li>
                      <li className="small">
                        Students <span className="badge rounded-pill bg-secondary">{totalNumberOfStudentsInOrg}</span>
                      </li>
                      <li className="small">
                        Teachers <span className="badge rounded-pill bg-secondary">{totalNumberOfTeachersInOrg}</span>
                      </li>
                      <li className="small">
                        District SSO ID{" "}
                        <span className="badge rounded-pill bg-secondary">
                          {state.ssoIssuerOrgId || "N/A"}{" "}
                          {!isSupportUser && (
                            <i
                              onClick={() => this.setState({ showSSOOrgModal: true })}
                              className="fa fa-edit red-hover"
                              title="Edit"
                            />
                          )}
                        </span>
                      </li>
                      {state.ssoIssuerOrgId && (
                        <li className="small">
                          <label>
                            <input type="checkbox" onChange={this.updateUseSSOOnly} checked={state.useSSOOnly} /> Use
                            SSO Only
                          </label>
                        </li>
                      )}
                      {canManageOrganization && !hasMFADisabled && (
                        <li className="small">
                          <label>
                            <input
                              type="checkbox"
                              className="m-r-5"
                              onChange={this.updateIsMFARequired}
                              checked={state.isMFARequired}
                              disabled={state.useSSOOnly}
                            />
                            <TooltipWrapper
                              text="Require MFA"
                              tooltipText="Multi Factor Authentication"
                              customClassName="d-inline"
                              isClickTriggerEnabled={false}
                            />
                          </label>
                        </li>
                      )}
                    </ul>
                  </div>
                </div>
              </div>
              {!isSupportUser && (
                <div className="entity-header">
                  <div className="row">
                    <div className="col-md-6 d-grid gap-1">
                      <button
                        className="btn btn-success text-left-forced"
                        onClick={() => exportRoster(this.props.orgid)}
                        disabled={isFetchingData}
                      >
                        <i className="fa fa-download m-r-10" /> Export current roster
                      </button>
                      <button
                        className="btn btn-success text-left-forced"
                        onClick={this.exportPreviousRoster}
                        disabled={isFetchingData}
                      >
                        <i className="fa fa-download m-r-10" /> Export end-of-year roster from the previous year
                      </button>
                    </div>
                    <div className="col-md-6 d-grid gap-1">
                      <Link
                        to={externalAssessmentScoresUploadLink}
                        className="btn btn-purple text-left-forced"
                        data-testid="import-external-assessment-scores"
                      >
                        <i className="fa fa-upload m-r-10" /> Import External Assessment Scores
                      </Link>
                      <ButtonGroup className="btn-group d-flex" role="group">
                        <Button
                          className="btn btn-purple text-left-forced flex-grow-1"
                          disabled={!isExportExternalAssessmentScoresButtonEnabled}
                          onClick={this.exportExternalAssessmentScores}
                        >
                          <i className="fa fa-download m-r-10" /> Export External Assessment Scores
                        </Button>
                        {isExportExternalAssessmentScoresButtonEnabled && (
                          <DropdownButton
                            as={ButtonGroup}
                            variant="inverse"
                            title={state.externalAssessmentScoresSchoolYear}
                          >
                            {state.externalAssessmentScoresSchoolYears.map(schoolYear => (
                              <Dropdown.Item
                                key={schoolYear}
                                href="#"
                                onClick={this.setExternalAssessmentScoresSchoolYear(schoolYear)}
                              >
                                {schoolYear}
                              </Dropdown.Item>
                            ))}
                          </DropdownButton>
                        )}
                      </ButtonGroup>
                    </div>
                  </div>
                </div>
              )}
            </div>
            {state.allowClasswideWithoutScreening ? (
              <div className="col-sm-12">
                <div className="entity-header d-grid">
                  <button
                    className="btn btn-outline-blue"
                    disabled={isFetchingData}
                    onClick={this.shouldRenderAllowClasswideWithoutScreeningModal}
                  >
                    <i className="fa fa-bullhorn m-r-10" />
                    {state.canOrgStartClasswidePriorToScreening
                      ? "Require Screening prior to beginning Classwide Intervention"
                      : "Allow starting Classwide Intervention without Screening"}
                  </button>
                </div>
              </div>
            ) : null}
          </div>
          <div className="row">
            <div className="col-sm-12">
              <div className="entity-container">
                {!isSupportUser && (
                  <div className="district-details" data-testid="district-details-testid">
                    <section className="m-b-15">
                      {state.rosteringMessage && this.props.org ? (
                        <div className="text-center m-b-15" data-testid="compact-news-banner">
                          <NewsBanner dense message={state.rosteringMessage} />
                        </div>
                      ) : null}
                      <div className="text-center m-b-15">{this.getRosteringLink()}</div>
                      <div className="text-center m-b-15">
                        <Link
                          to={`/data-admin/roster-import-history/${orgid}`}
                          className="list-action"
                          data-testid="import-history-link"
                        >
                          Import History <i className="fa fa-list-alt" aria-hidden="true" />
                        </Link>
                      </div>
                      {renderLastImport(
                        { loading, lastRosterImport, orgid, rostering: state.rostering },
                        fileUploadLink
                      )}
                    </section>
                    <section
                      className={`d-flex justify-content-${
                        shouldDisplayAddSchoolButton ? "between" : "end"
                      } align-items-center m-b-15`}
                    >
                      {shouldDisplayAddSchoolButton ? (
                        <Button
                          className="btn btn-success"
                          data-testid="add-school-button"
                          onClick={this.showAddSchoolModal}
                        >
                          Add School
                        </Button>
                      ) : null}
                      <Link to={addCoachLink} className="lnkAddCoachUser text-success" data-testid="addCoachUserMain">
                        Add Coach User <i className="fa fa-plus" />
                      </Link>
                    </section>
                    <div className="conDataAdminsList">
                      {isSuperAdminOrUniversalDataAdmin && (
                        <Link
                          to={addDataAdminLink}
                          className="lnkAddDataAdminUser text-success pull-right"
                          data-testid="addDataAdminUserMain"
                        >
                          Add Data Administrator <i className="fa fa-plus" />
                        </Link>
                      )}
                      <h6 className="pull-left">Data Administrators</h6>
                      <ul className="vertical-centered d-flex flex-column">
                        {(props.dataAdmins || []).length === 0 ? (
                          <li>
                            <em>No Existing Data Administrators</em>
                          </li>
                        ) : (
                          (props.dataAdmins || []).map((a, index) => {
                            const dataAdminName = `${a.profile.name.first} ${a.profile.name.last}`;
                            const dataAdminEmail = a.emails?.[0]?.address || "";
                            const siteId = a.profile.siteAccess.find(sa => sa.role === "arbitraryIddataAdmin")?.siteId;
                            return (
                              <li ref={index} key={a._id} className="d-flex flex-column">
                                <div className="d-flex flex-row align-items-center w-100 gap-1">
                                  {dataAdminEmail ? (
                                    <TooltipWrapper
                                      text={dataAdminName}
                                      tooltipText={dataAdminEmail}
                                      customClassName="school-item-user flex-grow-1 text-overflow"
                                      placement="top"
                                    />
                                  ) : (
                                    <span className="text-overflow">{dataAdminName}</span>
                                  )}
                                  {!isSupportUser && (
                                    <span className="d-flex flex-row gap-1">
                                      <i
                                        onClick={() => this.openProfileModal(a._id)}
                                        className="fa fa-user blue-hover"
                                        title="Profile Page"
                                      />
                                      <i
                                        onClick={() => this.openEmailModal(a._id)}
                                        className="fa fa-envelope-o red-hover"
                                      />
                                      {myUserId !== a._id && (
                                        <i
                                          onClick={() => this.openRemoveModal(a._id)}
                                          className="fa fa-trash red-hover"
                                          data-testid="removeDataAdminUsers"
                                        />
                                      )}
                                    </span>
                                  )}
                                </div>
                                {!isSupportUser && (
                                  <span>
                                    {isSuperAdminOrUniversalDataAdmin ? (
                                      <select
                                        className="form-select mb-3"
                                        name="dataAdminSiteId"
                                        onChange={this.changeDataAdminSiteId(a)}
                                        value={siteId}
                                      >
                                        <option value="allSites">All sites</option>
                                        {state.schoolItemData.map(site => (
                                          <option key={site._id} value={site._id}>
                                            {site.name}
                                          </option>
                                        ))}
                                      </select>
                                    ) : (
                                      <div className="small mt-0 mb-3">
                                        Access:{" "}
                                        <strong>
                                          {state.schoolItemData.find(site => site._id === siteId)?.name ?? "All sites"}
                                        </strong>
                                      </div>
                                    )}
                                  </span>
                                )}
                              </li>
                            );
                          })
                        )}
                      </ul>
                    </div>
                  </div>
                )}
                <div className={`school-list${supportViewClassName}`}>{renderSchoolItems()}</div>
              </div>
            </div>
          </div>
        </div>
        <ConfirmModal
          showModal={state.showRemoveModal}
          confirmAction={this.removeDataAdministrator}
          onCloseModal={this.closeModal}
          bodyQuestion=""
          bodyText="You are about to remove a data admin account or data admin role for this user, do you wish to continue?"
        />
        <ConfirmModal
          showModal={state.showEmailModal}
          confirmAction={this.resendInvitationEmail}
          onCloseModal={this.closeModal}
          bodyQuestion=""
          bodyText="Do you want to resend invitation email?"
        />
        {!state.canOrgStartClasswidePriorToScreening ? (
          <ConfirmModal
            showModal={state.showClasswidePriorToScreeningModal}
            onCloseModal={() => this.setState({ showClasswidePriorToScreeningModal: false })}
            confirmAction={() =>
              this.updateOrganizationFieldValue(
                "canOrgStartClasswidePriorToScreening",
                !state.canOrgStartClasswidePriorToScreening
              )
            }
            headerText="Are you sure you want to allow users to start Classwide Interventions prior to Screening?"
            bodyQuestion="If your students are working remotely you may need to begin classwide interventions prior to screening. When your students return to school please return to this page and disable this option. Click the button below to allow."
            confirmText="Allow starting Classwide Intervention without Screening"
            cancelText="Cancel"
          />
        ) : null}

        <ManageSSOOrgModal
          showModal={state.showSSOOrgModal}
          onCloseModal={() => this.setState({ showSSOOrgModal: false })}
          ssoIssuerOrgId={state.ssoIssuerOrgId}
          orgName={this.props.org?.name || ""}
          onSave={this.updateDistrictSSOId}
        />

        {state.showDistrictSettingsModal && (
          <DistrictSettingsModal
            showModal={state.showDistrictSettingsModal}
            onCloseModal={() => this.setState({ showDistrictSettingsModal: false })}
            org={props.org}
            isSuperAdminOrUniversalDataAdmin={props.isSuperAdminOrUniversalDataAdmin}
          />
        )}
        {state.isAddSchoolModalOpen && (
          <AddSchoolModal
            showModal={state.isAddSchoolModalOpen}
            onCloseModal={this.hideAddSchoolModal}
            confirmAction={this.getSchoolItemData}
            existingSchools={state.schoolItemData}
            orgid={this.props.orgid}
          />
        )}
        {state.isProfileModalOpen ? (
          <ConfirmModal
            showModal={state.isProfileModalOpen}
            confirmText=""
            cancelText="Close"
            customProps={{ useCustomHeader: true }}
            headerText=""
            onCloseModal={this.closeProfileModal}
            bodyQuestion=""
            bodyText={<UserProfile userId={state.selectedUserId} />}
            size="lg"
          />
        ) : null}
      </div>
    );
  }
}

renderLastImport.propTypes = {
  lastRosterImport: PropTypes.object,
  loading: PropTypes.bool,
  orgid: PropTypes.string
};

DataAdminDashboard.propTypes = {
  admins: PropTypes.array,
  dataAdmins: PropTypes.array,
  lastRosterImport: PropTypes.object,
  loading: PropTypes.bool,
  org: PropTypes.object,
  orgid: PropTypes.string,
  schoolYear: PropTypes.number,
  students: PropTypes.array,
  teachers: PropTypes.array,
  userRole: PropTypes.string,
  isSuperAdminOrUniversalDataAdmin: PropTypes.bool
};

export default withRouter(
  withTracker(props => {
    const currentUserId = getMeteorUserId();
    if (!currentUserId) {
      props.history.push("/login");
      return { loading: true };
    }
    const curUser = getMeteorUserSync();
    if (!curUser) {
      return {
        loading: true,
        lastRosterImport: {},
        org: { _id: "" },
        dataAdmins: []
      };
    }
    const orgid = props.orgid || curUser?.profile?.orgid || "";
    if (!orgid) {
      return {
        loading: true,
        lastRosterImport: {},
        org: { _id: "" },
        dataAdmins: []
      };
    }
    let lastImportsSub;
    if (
      curUser.profile.siteAccess.find(sa =>
        [ROLE_IDS.superAdmin, ROLE_IDS.dataAdmin, ROLE_IDS.universalDataAdmin].includes(sa.role)
      )
    ) {
      lastImportsSub = Meteor.subscribe("RosterImports:lastImport", orgid);
    } else {
      lastImportsSub = {
        ready: () => true
      };
    }

    const organizationsHandler = Meteor.subscribe("Organizations", orgid);
    const usersHandler = Meteor.subscribe("Users:DataAdminsInOrganization", orgid);
    const loading = areSubscriptionsLoading(lastImportsSub, organizationsHandler, usersHandler);

    let lastRosterImport = {};
    let org = {};
    let dataAdmins = [];
    let isSuperAdminOrUniversalDataAdmin = false;
    let userRoles = [];

    if (!loading) {
      lastRosterImport = RosterImports.findOne({});
      org = Organizations.findOne(orgid) || {};
      dataAdmins = Users.find({
        "profile.orgid": orgid,
        "profile.siteAccess.role": ROLE_IDS.dataAdmin
      }).fetch();

      userRoles = getUserRoles();
      isSuperAdminOrUniversalDataAdmin = userRoles.includes("universalDataAdmin") || userRoles.includes("superAdmin");
    }

    return {
      loading,
      lastRosterImport,
      org,
      orgid,
      dataAdmins,
      userRole: props.userRole || userRoles[0],
      isSuperAdminOrUniversalDataAdmin
    };
  })(DataAdminDashboard)
);

export { DataAdminDashboard as PureDataAdminDashboard };
