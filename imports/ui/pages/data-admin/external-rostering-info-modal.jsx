import React, { Component } from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";

export default class ExternalRosteringInfoModal extends Component {
  close = () => {
    this.props.onCloseModal();
  };

  render() {
    return (
      <Modal show={true} size="lg" onHide={this.close} backdrop="static">
        <ModalHeader>
          <h2 className="w9 w-100 text-center">
            <i className="fa fa-info-circle text-info" />
            <span className="m-l-10">External Rostering Info</span>
          </h2>
        </ModalHeader>

        <ModalBody>
          <p>
            As a suggestion, any time there is a question about a class/teacher/student that appears to be missing,{" "}
            <strong>run an import first.</strong>
          </p>
          <p>
            If something is still missing, make sure that the{" "}
            <strong>class has been moved to the right side of the Class filter and the filters have been saved.</strong>
          </p>
          <p>
            If the class was not on the right and it was then moved to the right, an import would need to be{" "}
            <strong>run after moving the class.</strong>
          </p>
          <p>Another situation that causes questions deals with enrollment dates.</p>
          <p>
            For example, if a student is not showing up in a class it may be due to their enrollment start date being in
            the future. Teachers also have enrollment dates.{" "}
            <strong>
              If the teacher enrollment date is in the future the entire class would not show up in SpringMath.
            </strong>
          </p>
          <p>
            There are options at the top of the Filters tab to{" "}
            <strong>ignore the enrollment dates if this becomes an issue.</strong>
          </p>
        </ModalBody>

        <ModalFooter className="d-flex justify-content-center">
          <Button variant="default" onClick={this.close}>
            Close
          </Button>
        </ModalFooter>
      </Modal>
    );
  }
}

ExternalRosteringInfoModal.propTypes = {
  onCloseModal: PropTypes.func
};

ExternalRosteringInfoModal.defaultProps = {
  onCloseModal: () => {}
};
