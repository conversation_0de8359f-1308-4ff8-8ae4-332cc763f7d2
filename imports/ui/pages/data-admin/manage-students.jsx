import React, { Component } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import _ from "lodash";
import Alert from "react-s-alert";
import { withRouter } from "react-router-dom";

import { Loading } from "/imports/ui/components/loading";

import MoveStudentsModal from "./move-students-modal";
import ConfirmModal from "./confirm-modal";
import AddStudentForm from "./add-student-form";
import StudentsUpload from "./students-upload";
import { decWaitingOn, incWaitingOn } from "/imports/api/loadingCounter/methods";
import ManageStudentsRow from "./manage-students-row";
import ManageStudentsButtons from "./manage-students-buttons";
import { AppDataContext } from "../../routing/AppDataContext";
import { getUserRoles } from "./utilities";
import { isExternalRostering } from "../../utilities";

class ManageStudents extends Component {
  state = {
    selectedStudents: [],
    sortBy: "lastFirst",
    isMoveStudentsModalOpen: false,
    isArchiveStudentsModalOpen: false,
    isAddStudentsFormOpen: !this.props.students.length,
    isEditingStudents: false,
    rostering: "rosterUpload"
  };

  componentDidMount() {
    const userRole = getUserRoles();
    const isSuperAdminOrUniversalDataAdmin = userRole.includes("universalDataAdmin") || userRole.includes("superAdmin");
    Meteor.call("Organizations:getOrganizationFieldValues", this.props.orgid, ["rostering"], (err, resp) => {
      if (!err) {
        if (resp.rostering === "rosterUpload" && isSuperAdminOrUniversalDataAdmin) {
          // SuperAdmin or UniversalDataAdmin needs access to import when organization has blocked roster imports
          this.setState({ rostering: "rosterImport" });
        } else {
          this.setState({ rostering: resp.rostering });
        }
      }
    });
  }

  componentDidUpdate(prevProps, prevState) {
    if (this.shouldHideAddStudentsForm(prevProps, prevState)) {
      this.hideAddStudentForm();
    }
    if (this.shouldShowAddStudentForm(prevProps)) {
      this.showAddStudentForm();
    }
  }

  shouldHideAddStudentsForm = (prevProps, prevState) => {
    return (
      prevState.isAddStudentsFormOpen &&
      (!prevProps.studentGroup ||
        !this.props.studentGroup ||
        prevProps.studentGroup._id !== this.props.studentGroup._id) &&
      this.props.students.length
    );
  };

  shouldShowAddStudentForm = prevProps => {
    return prevProps.students.length !== this.props.students.length && this.props.students.length === 0;
  };

  moveStudents = () => {
    this.setState({ isMoveStudentsModalOpen: true });
  };

  setEditingStudents = () => {
    this.setState({ isEditingStudents: true });
  };

  cancelEditingStudents = () => {
    this.setState({ isEditingStudents: false });
  };

  closeMoveStudentsModal = () => {
    this.setState({ isMoveStudentsModalOpen: false });
  };

  clearSelectedStudents = () => {
    this.setState({ selectedStudents: [] });
  };

  archiveStudents = () => {
    Meteor.call(
      "archiveStudents",
      {
        studentIds: this.state.selectedStudents,
        studentGroupId: this.props.studentGroup._id
      },
      (error, { wasGroupDeactivated }) => {
        if (error) {
          console.log(`archiveStudents error: ${error}`);
          Alert.error("There was a problem while archiving students", {
            timeout: 3000
          });
        } else {
          Alert.closeAll();
          Alert.success("Students have been archived successfully", {
            timeout: 3000
          });
        }
        this.clearSelectedStudents();
        this.closeArchiveStudentsModal();
        if (wasGroupDeactivated) {
          this.props.history.push(`/data-admin/manage-group/students/${this.props.orgid}/site/${this.props.siteId}`);
        }
      }
    );
  };

  openArchiveStudentsModal = () => {
    this.setState({ isArchiveStudentsModalOpen: true });
  };

  closeArchiveStudentsModal = () => {
    this.setState({ isArchiveStudentsModalOpen: false });
  };

  showAddStudentForm = () => {
    this.setState({ isAddStudentsFormOpen: true });
  };

  hideAddStudentForm = () => {
    this.setState({ isAddStudentsFormOpen: false });
  };

  showUploadStudents = () => {
    this.setState({ isUploadStudentsOpen: true });
  };

  hideUploadStudents = () => {
    this.setState({ isUploadStudentsOpen: false });
  };

  toggleSelectedStudents = studentId => e => {
    const { selectedStudents } = this.state;
    if (e.currentTarget.checked) {
      if (!selectedStudents.includes(studentId)) {
        selectedStudents.push(studentId);
      }
    } else {
      _.pull(selectedStudents, studentId);
    }

    this.setState({ selectedStudents });
  };

  toggleSelectAllStudents = () => {
    if (this.state.selectedStudents.length === this.props.students.length) {
      this.setState({ selectedStudents: [] });
    } else {
      const allStudentsIds = this.props.students.map(student => student._id);
      this.setState({ selectedStudents: allStudentsIds });
    }
  };

  get sortByString() {
    return this.state.sortBy === "lastFirst" ? "Last, First" : "First, Last";
  }

  toggleSorting = () => {
    this.setState(prevState => ({
      sortBy: prevState.sortBy === "lastFirst" ? "firstLast" : "lastFirst"
    }));
  };

  getSortingValue = student =>
    this.state.sortBy === "lastFirst" ? student.identity.name.lastName : student.identity.name.firstName;

  handleStudentUploadSubmit = data => {
    Meteor.call(
      "RosterImports:insertStudentRoster",
      {
        data: { ...data, source: "CSV-Students" },
        orgid: this.props.orgid,
        siteId: this.props.siteId,
        studentGroupId: this.props.studentGroup._id
      },
      err => {
        if (err) {
          decWaitingOn();
        } else {
          decWaitingOn();
          this.hideUploadStudents();
        }
      }
    );
    incWaitingOn(1, "Inserting into the db!");
  };

  renderHeader = () => (
    <thead>
      <tr>
        <th>
          {this.props.isExternalRostering ? null : (
            <input
              className="me-2"
              type="checkbox"
              checked={this.state.selectedStudents.length === this.props.students.length}
              onChange={this.toggleSelectAllStudents}
              data-testid="select-all-students-checkbox"
            />
          )}
          Sort by:{" "}
          <u role="button" onClick={this.toggleSorting}>
            {this.sortByString}
          </u>
        </th>
        {this.isUnarchivePage() ? <th>Most Recent Grade</th> : <th>Student Grade</th>}
        <th>Date of Birth</th>
        <th>Local ID</th>
        <th>State ID</th>
        <th />
      </tr>
    </thead>
  );

  updateStudentData = studentData => {
    Meteor.call("updateStudentData", this.props.studentGroup._id, studentData, (err, res) => {
      if (res) {
        Alert.success("Student has been updated successfully", {
          timeout: 3000
        });
      }
      if (err) {
        Alert.error(err.reason || "Error updating the Student", {
          timeout: 3000
        });
      }
    });
  };

  renderStudents = () =>
    _.sortBy(this.props.students, this.getSortingValue).map(student => (
      <ManageStudentsRow
        key={student._id}
        student={student}
        orgid={this.props.orgid}
        studentGroup={this.props.studentGroup}
        selected={this.state.selectedStudents.includes(student._id)}
        selectStudent={this.toggleSelectedStudents(student._id)}
        showCheckbox={!this.props.isExternalRostering}
        isEditingStudents={this.state.isEditingStudents}
        updateStudentData={this.updateStudentData}
        isUnarchivePage={this.isUnarchivePage()}
      />
    ));

  isUnarchivePage = () => this.props.manageView === "unarchive";

  render() {
    const { loading } = this.props;
    if (loading) return <Loading />;
    const { students, manageView, studentGroup, grades, orgid, siteId } = this.props;
    const {
      selectedStudents,
      isUploadStudentsOpen,
      rostering,
      isEditingStudents,
      isAddStudentsFormOpen,
      isArchiveStudentsModalOpen,
      isMoveStudentsModalOpen
    } = this.state;
    const currentlySelectedStudents = students.filter(student => selectedStudents.includes(student._id));
    const isManageStudentPage = manageView === "students";
    const isUnarchivePage = this.isUnarchivePage();

    return (
      <div data-testid="manage-students-buttons">
        {this.props.isExternalRostering ? null : (
          <ManageStudentsButtons
            isManageStudentsPage={isManageStudentPage}
            isUploadStudentsOpen={isUploadStudentsOpen}
            selectedStudents={currentlySelectedStudents}
            showUploadStudents={this.showUploadStudents}
            hideUploadStudents={this.hideUploadStudents}
            showAddStudentForm={this.showAddStudentForm}
            openArchiveStudentsModal={this.openArchiveStudentsModal}
            moveStudents={this.moveStudents}
            isEditingStudents={isEditingStudents}
            setEditingStudents={this.setEditingStudents}
            cancelEditingStudents={this.cancelEditingStudents}
            rostering={rostering}
          />
        )}
        <div className="main-content mt-2">
          <div className="p-3">
            {isUploadStudentsOpen ? (
              <StudentsUpload studentGroup={studentGroup} uploadStudents={this.handleStudentUploadSubmit} />
            ) : (
              <table className="table table-condensed">
                {this.renderHeader()}
                <tbody>
                  {!isExternalRostering(this.state.rostering) && !isUnarchivePage && isAddStudentsFormOpen ? (
                    <AddStudentForm studentGroup={studentGroup} onCancel={this.hideAddStudentForm} grades={grades} />
                  ) : null}
                  {this.renderStudents()}
                </tbody>
              </table>
            )}
          </div>
        </div>

        {isMoveStudentsModalOpen && (
          <AppDataContext.Consumer>
            {({ schoolYear }) => (
              <MoveStudentsModal
                showModal={isMoveStudentsModalOpen}
                students={currentlySelectedStudents}
                studentGroup={studentGroup}
                orgid={orgid}
                siteId={siteId}
                onCloseModal={this.closeMoveStudentsModal}
                onMoveStudents={this.clearSelectedStudents}
                schoolYear={schoolYear}
              />
            )}
          </AppDataContext.Consumer>
        )}

        <ConfirmModal
          showModal={isArchiveStudentsModalOpen}
          onCloseModal={this.closeArchiveStudentsModal}
          confirmAction={this.archiveStudents}
          headerText="Are you sure you want to archive these students?"
          bodytext="The students will be unenrolled from this student group."
          confirmText="Yes, archive students"
        />
      </div>
    );
  }
}

ManageStudents.propTypes = {
  loading: PropTypes.bool,
  isExternalRostering: PropTypes.bool,
  studentGroup: PropTypes.object,
  students: PropTypes.array,
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  manageView: PropTypes.string,
  grades: PropTypes.array,
  history: PropTypes.object
};

export default withRouter(ManageStudents);
