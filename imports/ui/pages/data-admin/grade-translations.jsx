import React, { useState } from "react";
import PropTypes from "prop-types";

import { DEFAULT_GRADE_TRANSLATIONS } from "/imports/api/utilities/utilities";
import CreatableSelect from "react-select/creatable";
import Alert from "react-s-alert";

export function GradeTranslations({ orgid, customTranslationsByGrade = {} }) {
  return (
    <table className="table table-condensed">
      <thead>
        <tr>
          <th className="col-6">SpringMath Grade Level</th>
          <th className="col-6">District Grade Levels</th>
        </tr>
      </thead>
      <tbody>
        {DEFAULT_GRADE_TRANSLATIONS.map(({ grade, name, translations }) => (
          <tr key={grade}>
            <td className="col-6">
              {name} ({grade})
            </td>
            <td className="col-6">
              <GradeTranslationSelector
                orgid={orgid}
                grade={grade}
                defaultTranslations={translations}
                customTranslationsByGrade={customTranslationsByGrade}
              />
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}

GradeTranslations.propTypes = {
  orgid: PropTypes.string,
  customTranslationsByGrade: PropTypes.object
};

function mapTranslationOptions(translations = [], isFixed = false) {
  return translations.map(translation => ({
    value: translation,
    label: translation,
    isFixed
  }));
}

function GradeTranslationSelector({ orgid, grade, defaultTranslations = [], customTranslationsByGrade = {} }) {
  const defaultTranslationOptions = mapTranslationOptions(defaultTranslations, true);
  const customTranslationOptions = mapTranslationOptions(customTranslationsByGrade[grade]);

  const [selectedTranslations, setSelectedTranslations] = useState([
    ...defaultTranslationOptions,
    ...customTranslationOptions
  ]);

  const styles = {
    multiValue: (base, state) => {
      return state.data.isFixed ? { ...base, backgroundColor: "gray" } : base;
    },
    multiValueLabel: (base, state) => {
      return state.data.isFixed ? { ...base, fontWeight: "bold", color: "white", paddingRight: 6 } : base;
    },
    multiValueRemove: (base, state) => {
      return state.data.isFixed ? { ...base, display: "none" } : base;
    }
  };

  const handleTranslationChange = (newValue, actionMeta) => {
    const usedTranslations = [
      ...DEFAULT_GRADE_TRANSLATIONS.map(({ translations }) => translations).flat(1),
      ...Object.entries(customTranslationsByGrade)
        .map(([, translations]) => translations)
        .flat(1)
    ];
    switch (actionMeta.action) {
      case "create-option":
        // eslint-disable-next-line no-param-reassign
        if (usedTranslations.includes(actionMeta.option.value.trim().toUpperCase())) {
          return;
        }
        break;
      case "remove-value":
      case "pop-value":
        if (actionMeta.removedValue.isFixed) {
          return;
        }
        break;
      case "clear":
        // eslint-disable-next-line no-param-reassign
        newValue = selectedTranslations.filter(v => v.isFixed);
        break;
      default:
    }

    const chosenTranslations = newValue.map(t => ({
      ...t,
      label: t.value.toUpperCase(),
      value: t.value.toUpperCase()
    }));

    setSelectedTranslations(chosenTranslations);
    const translations = chosenTranslations.filter(v => !v.isFixed).map(v => v.value);
    Meteor.call("Organizations:saveGradeTranslation", { orgid, grade, translations }, err => {
      if (err) {
        Alert.error("There was a problem while saving grade translations", {
          timeout: 5000
        });
      }
    });
  };

  const value = selectedTranslations;

  return (
    <CreatableSelect
      value={value}
      isMulti
      styles={styles}
      isClearable={value.some(v => !v.isFixed)}
      name={`${grade}_translations`}
      noOptionsMessage={() => "Type in Grade Level and add with Enter or Tab"}
      onChange={handleTranslationChange}
    />
  );
}

GradeTranslationSelector.propTypes = {
  orgid: PropTypes.string,
  grade: PropTypes.string,
  defaultTranslations: PropTypes.array,
  customTranslationsByGrade: PropTypes.object
};
