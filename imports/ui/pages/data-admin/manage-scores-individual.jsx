import React, { Component } from "react";
import PropTypes from "prop-types";
import { withTracker } from "meteor/react-meteor-data";
import { Meteor } from "meteor/meteor";
import { areSubscriptionsLoading } from "../../utilities";
import IndividualInterventionProgress from "../../components/student-detail/individual-intervention-progress";
import Loading from "../../components/loading";

class ManageScoresIndividual extends Component {
  render() {
    if (this.props.loading) {
      return <Loading />;
    }
    return (
      <div className="studentDetailContent">
        <IndividualInterventionProgress
          printAllIndividualInterventionGoalSkills={false}
          printAllIndividualInterventionSkills={false}
        />
      </div>
    );
  }
}

ManageScoresIndividual.propTypes = {
  loading: PropTypes.bool
};

export default withTracker(({ currentGrade: grade }) => {
  const rulesSub = Meteor.subscribe("Rules:IndividualRootRulesByGrade", grade);
  const screeningAssignmentsSub = Meteor.subscribe("ScreeningAssignmentsByGrade", grade);
  const assessmentsSub = Meteor.subscribe("AssessmentsForGrade", grade);

  const loading = areSubscriptionsLoading(rulesSub, screeningAssignmentsSub, assessmentsSub);

  return {
    loading
  };
})(ManageScoresIndividual);
