import React, { Component } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import Alert from "react-s-alert";
import { omit } from "lodash";

import { getMinMaxBirthDate } from "./utilities";

const optionalFields = ["birthDate"];

export default class AddStudentForm extends Component {
  state = {
    newStudent: {
      lastName: "",
      firstName: "",
      studentGrade: this.props.studentGroup?.grade,
      birthDate: "",
      localId: "",
      stateId: ""
    }
  };

  isAddStudentFormValid = () =>
    Object.values(omit(this.state.newStudent, optionalFields)).every(value => value.trim().length);

  resetNewStudent = () => {
    this.setState({
      newStudent: {
        lastName: "",
        firstName: "",
        studentGrade: "",
        birthDate: "",
        localId: "",
        stateId: ""
      }
    });
  };

  onChangeNewStudent = field => e => {
    const newStudent = { ...this.state.newStudent, [field]: e.target.value };
    this.setState({ newStudent });
  };

  saveNewStudent = () => {
    const student = {};
    Object.entries(this.state.newStudent).forEach(([key, value]) => {
      student[key] = value.trim();
    });
    Meteor.call("addStudentToGroup", { student, studentGroupId: this.props.studentGroup._id }, error => {
      if (error) {
        Alert.error(error.reason || "There was a problem while adding a new student", {
          timeout: 5000
        });
      } else {
        Alert.success("Student has been added successfully", {
          timeout: 3000
        });
        this.resetNewStudent();
        this.props.onCancel();
      }
    });
  };

  render() {
    const { newStudent } = this.state;

    const defaultFormControlClasses = "me-2 form-control input-sm manage-students-row-input";
    const { minBirthDate, maxBirthDate } = getMinMaxBirthDate();

    return (
      <tr className="success" data-testid="addStudentForm">
        <td>
          <div className="d-flex">
            <input
              onChange={this.onChangeNewStudent("lastName")}
              value={newStudent.lastName}
              className="form-control form-control-sm me-2"
              placeholder="Last name"
              data-testid="newStudentLastName"
            />
            <input
              onChange={this.onChangeNewStudent("firstName")}
              value={newStudent.firstName}
              className={defaultFormControlClasses}
              placeholder="First name"
              data-testid="newStudentFirstName"
            />
          </div>
        </td>
        <td className="ps-0">
          <select
            className={defaultFormControlClasses}
            value={newStudent.studentGrade}
            onChange={this.onChangeNewStudent("studentGrade")}
          >
            {this.props.grades.map(grade => (
              <option key={grade._id} value={grade.display}>
                {grade.display}
              </option>
            ))}
          </select>
        </td>
        <td className="ps-0">
          <input
            onChange={this.onChangeNewStudent("birthDate")}
            type="date"
            value={newStudent.birthDate}
            className={defaultFormControlClasses}
            placeholder="YYYY-MM-DD"
            min={minBirthDate}
            max={maxBirthDate}
            data-testid="newStudentDOB"
          />
        </td>
        <td className="ps-0">
          <input
            onChange={this.onChangeNewStudent("localId")}
            value={newStudent.localId}
            className={defaultFormControlClasses}
            placeholder="Local ID"
            data-testid="newStudentLocalId"
          />
        </td>
        <td className="ps-0">
          <input
            onChange={this.onChangeNewStudent("stateId")}
            value={newStudent.stateId}
            className={defaultFormControlClasses}
            placeholder="State ID"
            data-testid="newStudentStateId"
          />
        </td>
        <td>
          <button
            className="btn btn-primary btn-wide btn-xs me-2"
            onClick={this.saveNewStudent}
            disabled={!this.isAddStudentFormValid()}
          >
            Save
          </button>
          <button className="btn btn-outline-blue btn-wide btn-xs" onClick={this.props.onCancel}>
            Cancel
          </button>
        </td>
      </tr>
    );
  }
}

AddStudentForm.propTypes = {
  studentGroup: PropTypes.object,
  onCancel: PropTypes.func,
  grades: PropTypes.array
};

AddStudentForm.defaultProps = {
  onCancel: () => {}
};
