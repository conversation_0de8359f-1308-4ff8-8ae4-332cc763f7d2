import React, { Component } from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>oot<PERSON>, <PERSON><PERSON> } from "react-bootstrap";

export default class ClearScreeningScoresModal extends Component {
  close = () => {
    this.props.onCloseModal();
  };

  confirmAction = () => {
    if (this.props.confirmAction) {
      this.props.confirmAction();
    }
    this.close();
  };

  render() {
    return (
      <Modal show={true} size="lg" onHide={this.close} dialogClassName="archive-students-modal" backdrop="static">
        <ModalHeader>
          <h2 className="w9">
            <i className="fa fa-2x fa-warning text-warning pull-left" />
            <div>Are you sure you want to clear these screening scores?</div>
          </h2>
        </ModalHeader>

        <ModalBody>
          <p>
            This will delete all scores entered for this screening so that they may be re-entered.
            <br />
            If this group is currently in classwide intervention and no progress monitoring scores have been entered the
            classwide intervention will be removed and we will decide if you need classwide intervention based upon your
            new scores.
            <br />
            If this group is in classwide intervention and progress monitoring scores have already been entered you will
            continue on with your current intervention.
          </p>

          <p>
            <strong>Do you wish to continue?</strong>
          </p>
        </ModalBody>

        <ModalFooter className="d-flex justify-content-center">
          <Button variant="success" onClick={this.confirmAction}>
            Yes, clear screening scores
          </Button>
          <Button variant="default" onClick={this.close}>
            No, Cancel
          </Button>
        </ModalFooter>
      </Modal>
    );
  }
}

ClearScreeningScoresModal.propTypes = {
  confirmAction: PropTypes.func,
  onCloseModal: PropTypes.func
};

ClearScreeningScoresModal.defaultProps = {
  onCloseModal: () => {}
};
