import get from "lodash/get";
import uniq from "lodash/uniq";
import moment from "moment";

import { Users } from "/imports/api/users/users";
import { getMeteorUserId, getMeteorUserSync } from "/imports/api/utilities/utilities";

export function isSingleSchoolDataAdmin() {
  const siteAccess = getMeteorUserSync()?.profile?.siteAccess || [];
  const isSuperAdminOrUniversalDataAdmin = siteAccess.find(sa =>
    ["arbitraryIdsuperAdmin", "arbitraryIduniversalDataAdmin"].includes(sa.role)
  );
  return !isSuperAdminOrUniversalDataAdmin && !["none", "allSites"].includes(siteAccess?.[0]?.siteId);
}

export function getMinMaxBirthDate() {
  return {
    minBirthDate: "1970-01-01",
    maxBirthDate: new Date().toISOString().split("T")[0]
  };
}

export function isDateValid(month, day) {
  const dateObject = {
    y: 2000,
    M: month - 1,
    d: day
  };

  return moment.utc(dateObject).isValid();
}

export function getUsersThatCanBeGroupOwners(orgid) {
  return Users.find(
    {
      "profile.orgid": orgid,
      $or: [
        { "profile.siteAccess.role": { $in: ["arbitraryIdteacher", "arbitraryIdadmin"] } },
        { "profile.siteAccess": [] }
      ]
    },
    { sort: { "profile.name.first": 1, "profile.name.last": 1 } }
  ).fetch();
}

export function getClassNameFromStudentGroupName(studentGroupName = "") {
  return get(studentGroupName.match(/(^.+)\(/), "[1]", studentGroupName).trim();
}

export function getCourseNameFromStudentGroupName(studentGroupName) {
  const regexpMatchingLastParentheses = /\(([^)]*)\)[^(]*$/;
  return (studentGroupName.match(regexpMatchingLastParentheses) || [""])[0];
}

export function getUserRoles() {
  let user = getMeteorUserSync();
  const userId = getMeteorUserId();
  if (!user && userId) {
    user = Users.findOne({ _id: userId }, { fields: { "profile.siteAccess": 1 } });
  }
  return uniq(user?.profile?.siteAccess?.map(sa => sa.role.slice(11))) || [];
}

export function getTeacherLabel(teacherId, teachers) {
  const teacher = teachers.find(t => t._id === teacherId);
  if (!teacher) {
    return "Select Teacher";
  }
  return `${teacher?.profile.name.first} ${teacher?.profile.name.last} (${teacher?.emails[0].address})`;
}
