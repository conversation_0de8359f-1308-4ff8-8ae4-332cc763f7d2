import React, { Component } from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Header } from "react-bootstrap";
import { Meteor } from "meteor/meteor";
import Alert from "react-s-alert";
import { withTracker } from "meteor/react-meteor-data";
import { Formik } from "formik";

import { BenchmarkPeriods } from "/imports/api/benchmarkPeriods/benchmarkPeriods";
import { districtSettingsSchema } from "/imports/api/organizations/organizations";
import { DistrictSettingsForm } from "./district-settings-form";

export class DistrictSettingsModal extends Component {
  validateSchoolBreaks = () => {};

  state = {
    areSchoolBreakDatesValid: true
  };

  setValidateSchoolBreaksFunction = func => {
    this.validateSchoolBreaks = func;
  };

  setAreSchoolBreakDatesValid = areSchoolBreakDatesValid => {
    if (this.state.areSchoolBreakDatesValid !== areSchoolBreakDatesValid) {
      this.setState({ areSchoolBreakDatesValid });
    }
  };

  close = () => {
    this.props.onCloseModal();
  };

  saveDistrictSettings = (settings, schoolBreaks) => {
    const schoolYearBoundary = this.props.benchmarkPeriods.find(({ name }) => name === "Spring")?.endDate?.[
      settings.benchmarkPeriodsGroupId
    ];
    Meteor.call(
      "Organizations:saveDistrictSettings",
      { orgid: this.props.org._id, settings: { ...settings, schoolYearBoundary }, schoolBreaks },
      err => {
        if (err) {
          Alert.error(err.reason || err.message || "There was a problem saving District Settings");
        } else {
          Alert.success(`Successfully saved District Settings`);
        }
      }
    );
  };

  handleSubmit = settings => {
    const { schoolBreaks, areDatesValid } = this.validateSchoolBreaks();
    if (areDatesValid) {
      this.saveDistrictSettings(settings, schoolBreaks);
    }
  };

  render() {
    const { showModal, org, isLoading, isSuperAdminOrUniversalDataAdmin, benchmarkPeriods } = this.props;

    if (isLoading) {
      return null;
    }

    const benchmarkPeriodsGroupId = org.benchmarkPeriodsGroupId || "default";

    return (
      <Formik
        validationSchema={districtSettingsSchema}
        onSubmit={this.handleSubmit}
        initialValues={{
          name: org.name,
          city: org.details.city,
          state: org.details.state,
          isTestOrg: org.isTestOrg,
          firstName: org.details.primaryContact?.firstName || "",
          lastName: org.details.primaryContact?.lastName || "",
          email: org.details.primaryContact?.email || "",
          phone: org.details.primaryContact?.phone || "",
          benchmarkPeriodsGroupId
        }}
      >
        {formProps => (
          <Modal
            show={showModal}
            onHide={this.close}
            dialogClassName="modal-95"
            backdrop
            scrollable
            data-testid="districtSettingsModal"
          >
            <ModalHeader className="align-content-center justify-content-center">
              <div className="text-center">
                <h3>District Settings</h3>
                <strong className={`small m-0 ${org.isActive ? "text-success" : "text-danger"}`}>
                  {org.isActive ? "Active" : "Inactive"} Organization
                </strong>
              </div>
            </ModalHeader>
            <ModalBody>
              <div className="container-fluid">
                <DistrictSettingsForm
                  {...formProps}
                  org={org}
                  isSuperAdminOrUniversalDataAdmin={isSuperAdminOrUniversalDataAdmin}
                  benchmarkPeriods={benchmarkPeriods}
                  setValidateSchoolBreaksFunction={this.setValidateSchoolBreaksFunction}
                  setAreSchoolBreakDatesValid={this.setAreSchoolBreakDatesValid}
                />
              </div>
            </ModalBody>

            <ModalFooter className="d-flex justify-content-center">
              <Button
                variant="success"
                onClick={formProps.handleSubmit}
                data-testid="confirm-modal-btn"
                disabled={!formProps.isValid || !this.state.areSchoolBreakDatesValid}
              >
                Save
              </Button>
              <Button variant="default" onClick={this.close} data-testid="cancel-modal-btn">
                Close
              </Button>
            </ModalFooter>
          </Modal>
        )}
      </Formik>
    );
  }
}

DistrictSettingsModal.propTypes = {
  showModal: PropTypes.bool.isRequired,
  onCloseModal: PropTypes.func.isRequired,
  isLoading: PropTypes.bool,
  org: PropTypes.object,
  isSuperAdminOrUniversalDataAdmin: PropTypes.bool,
  benchmarkPeriods: PropTypes.array
};

export default withTracker(() => {
  const benchmarkPeriodsSub = Meteor.subscribe("BenchmarkPeriods");

  const isLoading = !benchmarkPeriodsSub.ready();

  let benchmarkPeriods = [];
  if (!isLoading) {
    benchmarkPeriods = BenchmarkPeriods.find({ name: { $ne: "All" } }).fetch();
  }

  return {
    isLoading,
    benchmarkPeriods
  };
})(DistrictSettingsModal);
