import React from "react";
import { Link } from "react-router-dom";

export function AssessmentScoresUploadGuidelines() {
  return (
    <div>
      <h2 className="w7">External Assessment Scores file guidelines:</h2>
      <div>
        <ol className="file-upload-guidelines">
          <li>Files must be in a .CSV (comma separated value text file) format.</li>
          <li>
            <strong>
              Every row must correspond to an existing student record. Any discrepancies will prevent the upload until
              resolved.
            </strong>
          </li>
          <li>
            The file must contain values in every record for the mandatory fields. See the{" "}
            <Link to="/assets/Description of Variables_External Assessment Scores Fields.pdf" target="_blank">
              Description of variables
            </Link>{" "}
            for more information.
          </li>
        </ol>
      </div>
    </div>
  );
}
