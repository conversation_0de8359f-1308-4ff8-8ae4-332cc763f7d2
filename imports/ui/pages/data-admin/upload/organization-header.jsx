import PropTypes from "prop-types";
import React from "react";

export function OrganizationHeader(props) {
  return (
    <div className="row">
      <div className="col-3">
        <table className="table table-condensed uploadDataTable">
          <tbody>
            <tr>
              <th className="col-sm-1">District Name:</th>
              <th className="col-sm-2">{props.orgName}</th>
            </tr>
            {props.sites.length ? (
              <tr>
                <th className="col-sm-1">District ID:</th>
                <th className="col-sm-2">{props.sites[0]?.stateInformation.districtNumber}</th>
              </tr>
            ) : null}
          </tbody>
        </table>
      </div>
      {props.sites.length ? (
        <div className="col-4">
          <table className="table table-condensed uploadDataTable">
            <tbody>
              <tr>
                <td colSpan={2}>Schools:</td>
              </tr>
              <tr style={{ backgroundColor: "#f3f3f3" }}>
                <th>School Name</th>
                <th>School ID</th>
              </tr>
              {props.sites.map(site => (
                <tr key={site.name}>
                  <td>{site.name}</td>
                  <td>{site.stateInformation.schoolNumber}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : null}
    </div>
  );
}

OrganizationHeader.propTypes = {
  orgName: PropTypes.string,
  sites: PropTypes.array
};
