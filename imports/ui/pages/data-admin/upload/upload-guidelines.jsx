import PropTypes from "prop-types";
import React, { useState } from "react";
import { Button } from "react-bootstrap";
import { Link } from "react-router-dom";
import { exportRoster } from "../data-admin-dashboard";
import AddSchoolModal from "../add-school-modal";

export function UploadGuidelines(props) {
  const [isAddSchoolModalOpen, setIsAddSchoolModalOpen] = useState(false);

  return (
    <div>
      <h2 className="w7">Roster file guidelines:</h2>
      <div>
        <ol className="file-upload-guidelines">
          <li>Roster files must be in a .CSV (comma separated value text file) format.</li>
          <li>
            <strong>
              Every roster upload must contain all student records from all schools under this customer account (i.e.,
              school district). Uploading a subset will de-activate the other records.
            </strong>
          </li>
          <li>
            The file must contain values in every record for the mandatory fields. See the{" "}
            <Link to="/assets/Description of Variables_Import Records Fields.pdf" target="_blank">
              Description of variables
            </Link>{" "}
            for more information.
          </li>
          <li>
            If making changes to a subset of the student records, we strongly recommend using the Export Current Roster
            as CSV for a starting point for your updated roster.{" "}
            <button className="btn btn-sm btn-success" onClick={() => exportRoster(props.orgid)}>
              <i className="fa fa-download" /> Export current roster
            </button>
          </li>
          <li>
            Removing a student record from the roster file will result in that student record being
            archived/de-activated.
          </li>
          <li>
            <strong>
              When uploading a roster file to update the database (versus a first-time upload), DO NOT CHANGE the
              DistrictID, DistrictName, SchoolID, TeacherID, ClassSectionID, StudentLocalID, StudentStateID. Changing
              either field will result in the creation of a new student record.
            </strong>
          </li>
          <li>
            <strong>All schools in the .CSV file must have matching schools.</strong>{" "}
            <Button className="btn btn-sm btn-success" onClick={() => setIsAddSchoolModalOpen(true)}>
              <i className="fa fa-plus" /> Add School
            </Button>
          </li>
        </ol>
      </div>
      {isAddSchoolModalOpen && (
        <AddSchoolModal
          showModal={isAddSchoolModalOpen}
          onCloseModal={() => setIsAddSchoolModalOpen(false)}
          confirmAction={() => setIsAddSchoolModalOpen(false)}
          existingSchools={props.sites}
          orgid={props.orgid}
        />
      )}
    </div>
  );
}

UploadGuidelines.propTypes = {
  orgid: PropTypes.string,
  sites: PropTypes.array
};
