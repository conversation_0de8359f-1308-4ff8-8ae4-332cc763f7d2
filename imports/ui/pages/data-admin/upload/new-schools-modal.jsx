import React from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import { sortBy } from "lodash";

export default function NewSchoolsModal(props) {
  const displaySchoolItems = schools => {
    return sortBy(schools, ["SchoolName", "SchoolID"]).map(({ SchoolName, SchoolID }) => {
      return (
        <div key={`${SchoolName}${SchoolID}`}>
          {SchoolName} ({SchoolID})
        </div>
      );
    });
  };

  return (
    <Modal
      show={props.showModal}
      onHide={props.onCloseModal}
      dialogClassName="modal-80w"
      backdrop="static"
      data-testid="new-schools-modal"
    >
      <ModalHeader>
        <h2 className="w9">
          <div className="d-flex align-items-center gap-2">
            <i className="fa fa-2x fa-warning text-danger pull-left" />
            <div className="text-nowrap">Missing Schools</div>
          </div>
        </h2>
      </ModalHeader>

      <ModalBody>
        <p>
          Roster data can only be imported into an existing schools in SpringMath. New or missing schools need to be
          added on the Data Admin dashboard using &quot;Add School&quot; button.
        </p>
        {props.matchedSchoolData.length ? (
          <React.Fragment>
            <p>
              The following school match(es) were found and will import rosters, if &quot;Proceed with Import&quot; is
              selected.
            </p>
            {displaySchoolItems(props.matchedSchoolData)}
            <hr />
          </React.Fragment>
        ) : null}

        <p>
          The following school(s) do <strong>NOT</strong> have match(es) and will <strong>NOT</strong> import rosters,
          if &quot;Proceed with Import&quot; is selected.
        </p>
        <div>{displaySchoolItems(props.newSchoolsData)}</div>
      </ModalBody>

      <ModalFooter className="justify-content-center">
        <Button className="btn btn-default" onClick={props.confirmAction}>
          Proceed with Import
        </Button>
        <Button className="btn btn-default btn-danger" onClick={props.onCloseModal}>
          Cancel Import
        </Button>
      </ModalFooter>
    </Modal>
  );
}

NewSchoolsModal.propTypes = {
  showModal: PropTypes.bool.isRequired,
  confirmAction: PropTypes.func.isRequired,
  onCloseModal: PropTypes.func.isRequired,
  confirmText: PropTypes.string,
  cancelText: PropTypes.string,
  orgid: PropTypes.string,
  newSchoolsData: PropTypes.array,
  matchedSchoolData: PropTypes.array
};
