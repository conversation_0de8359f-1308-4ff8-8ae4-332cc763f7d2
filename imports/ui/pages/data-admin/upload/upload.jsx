import { Meteor } from "meteor/meteor";
import React, { Component } from "react";
import PropTypes from "prop-types";
import { withTracker } from "meteor/react-meteor-data";
import Dropzone from "react-dropzone";
import Papa from "papaparse";
import { withRouter } from "react-router-dom";
import filepicker from "filepicker-js";
import Alert from "react-s-alert";

import { safeTrim } from "/imports/api/rosterImportItems/methods";
import { getMeteorUserSync, getSectionGradeMajority } from "/imports/api/utilities/utilities";
import { uniqBy, difference, union } from "lodash";
import { Loading } from "../../../components/loading.jsx";
import { decWaitingOn, incWaitingOn } from "/imports/api/loadingCounter/methods";
import PageHeader from "../../../components/page-header.jsx";
import { Organizations } from "/imports/api/organizations/organizations";
import { Sites } from "/imports/api/sites/sites";
import {
  areSubscriptionsLoading,
  getSchoolNumberComparison,
  isExternalRostering,
  optionalRosterFields,
  requiredRosterFields
} from "/imports/ui/utilities";
import { getCSV, requiredFieldsExample } from "./file-upload-utils";
import { UploadGuidelines } from "./upload-guidelines";
import { OrganizationHeader } from "./organization-header";
import FileUploadErrors from "./file-upload-errors";
import { getUserRoles } from "../utilities";
import { validateAndSetSubtotals } from "./validation-helpers";
import NewSchoolsModal from "./new-schools-modal";

function prepareDataWithSpringMathGrades(data = []) {
  const gradesBySchoolIdByClassSectionId = {};
  data.forEach(datum => {
    if (!gradesBySchoolIdByClassSectionId[datum.SchoolID]) {
      gradesBySchoolIdByClassSectionId[datum.SchoolID] = {};
    }
    if (!gradesBySchoolIdByClassSectionId[datum.SchoolID][datum.ClassSectionID]) {
      gradesBySchoolIdByClassSectionId[datum.SchoolID][datum.ClassSectionID] = [];
    }
    gradesBySchoolIdByClassSectionId[datum.SchoolID][datum.ClassSectionID].push(safeTrim(datum.SpringMathGrade));
  });
  const gradeBySchoolIdByClassSectionId = {};
  Object.entries(gradesBySchoolIdByClassSectionId).forEach(([schoolId, gradesByClassSectionId]) => {
    Object.entries(gradesByClassSectionId).forEach(([classSectionId, grades]) => {
      if (!gradeBySchoolIdByClassSectionId[schoolId]) {
        gradeBySchoolIdByClassSectionId[schoolId] = {};
      }
      gradeBySchoolIdByClassSectionId[schoolId][classSectionId] = getSectionGradeMajority(grades);
    });
  });
  return data.map(datum => {
    // eslint-disable-next-line no-param-reassign
    datum.SpringMathGrade = gradeBySchoolIdByClassSectionId[datum.SchoolID][datum.ClassSectionID];
    return datum;
  });
}

class Upload extends Component {
  constructor(props) {
    super(props);

    this.file = null;

    this.state = {
      data: [],
      file: null,
      fileName: "",
      errors: [],
      hasUploadFailed: false,
      subtotals: {},
      fileDropped: false,
      rostering: "rosterUpload",
      fetchingPermissions: true,
      allowMultipleGradeLevels: false,
      isNewSchoolsModalOpen: false,
      newSchoolsData: [],
      matchedSchoolData: [],
      shouldDisplayNoSchoolsMatched: false,
      matchedIncomingSchoolNumbers: null
    };
  }

  componentDidMount() {
    this.getFileUploadPermission();
  }

  componentDidUpdate() {
    if (isExternalRostering(this.state.rostering)) {
      this.props.history.push("/");
    }
  }

  getFileUploadPermission = () => {
    const userRole = getUserRoles();
    const isSuperAdminOrUniversalDataAdmin = userRole.includes("universalDataAdmin") || userRole.includes("superAdmin");
    Meteor.call(
      "Organizations:getOrganizationFieldValues",
      this.props.orgid,
      ["rostering", "allowMultipleGradeLevels"],
      (err, resp) => {
        if (!err) {
          // SuperAdmin or UniversalDataAdmin needs access to import when organization has blocked roster imports
          const rostering =
            resp.rostering === "rosterUpload" && isSuperAdminOrUniversalDataAdmin ? "rosterImport" : resp.rostering;
          this.setState({
            rostering,
            fetchingPermissions: false,
            allowMultipleGradeLevels: resp.allowMultipleGradeLevels
          });
        }
      }
    );
  };

  getNormalizedOrgName = () => {
    return this.props.orgName.toLowerCase().replace(/ /gi, "_");
  };

  onDrop = files => {
    const [file] = files;
    if (this.state.rostering === "rosterUpload") {
      this.file = file;
    }
    this.fileName = file ? file.name : "No Name Found";
    this.setState({ hasUploadFailed: false });
    incWaitingOn(1, "Parsing CSV!");
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: this.papaParseComplete.bind(this)
    });
  };

  setSubtotalsAndCallbackWithErrors({ schools, teachers, classes, students, errors }) {
    if (!errors.length) {
      this.setState({
        subtotals: {
          schools: schools.size,
          teachers: teachers.size,
          classes: classes.size,
          students: students.size
        }
      });
    }
  }

  setData(data) {
    this.setState({ data });
  }

  setFileName(fileName) {
    let newFileName = fileName;
    if (this.state.rostering === "rosterUpload") {
      const date = new Date().toISOString().substring(0, 10);
      newFileName = `${this.getNormalizedOrgName()}_${date}_${fileName}`;
    }
    this.setState({ fileName: newFileName });
  }

  setErrors(errors) {
    this.setState({ errors });
  }

  papaParseComplete(results /* , file */) {
    decWaitingOn();
    if (!(results.data && results.data.length > 0)) {
      this.setState({ fileDropped: true });
      return;
    }
    const missingFields = difference(requiredRosterFields, results.meta.fields);
    const unsupportedFields = difference(results.meta.fields, [...requiredRosterFields, ...optionalRosterFields]);
    if (missingFields.length) {
      this.setInvalidFieldsError(missingFields);
      return;
    }
    if (unsupportedFields.length) {
      this.setInvalidFieldsError(unsupportedFields, true);
      return;
    }

    let errors = results.errors ? results.errors : [];
    const data = this.state.allowMultipleGradeLevels ? prepareDataWithSpringMathGrades(results.data) : results.data;

    validateAndSetSubtotals({
      data,
      callback: ({ schools, teachers, classes, students, errors: validationErrors }) => {
        this.setSubtotalsAndCallbackWithErrors({ schools, teachers, classes, students, errors });
        this.setData(data);
        this.setFileName(this.fileName);
        errors = union(results.errors, validationErrors);
        this.setErrors(errors);
        this.setState({ fileDropped: true });
      }
    });
  }

  setInvalidFieldsError(invalidFields, unsupportedFields = false) {
    this.setFileName(this.fileName);
    const errorText = unsupportedFields
      ? "Unsupported fields found:\n"
      : "Your CSV file is missing the following fields:\n";
    const otherSupportedFields =
      unsupportedFields && optionalRosterFields.length
        ? `\n\nAt the moment SpringMath supports the following additional fields:\n${optionalRosterFields.join("\n")}`
        : "";
    this.setErrors([
      `${errorText}${invalidFields.join(
        "\n"
      )}\n\nPlease make sure you are using the latest CSV Template for File Uploads.${otherSupportedFields}`
    ]);
    this.setState({ fileDropped: true });
  }

  handleCancel() {
    this.setData([]);
    this.setFileName("");
    this.setState({ fileDropped: false });
  }

  determineNextRosterImportStep = e => {
    if (this.state.rostering === "rosterImport") {
      const schoolsInOrg = this.props.sites;
      const uniqueImportData = uniqBy(this.state.data, "SchoolID");

      const { matchedExistingSchoolNumbers, schoolNumbersToBeAdded } = getSchoolNumberComparison(
        this.state.data,
        schoolsInOrg
      );

      const matchedSchoolData = matchedExistingSchoolNumbers
        .map(schoolNumber => {
          const school = this.props.sites.find(s => s.stateInformation.schoolNumber === schoolNumber);
          return school ? { SchoolName: school.name, SchoolID: school.stateInformation.schoolNumber } : null;
        })
        .filter(Boolean);

      if (schoolNumbersToBeAdded.length) {
        const newSchoolsData = uniqueImportData
          .filter(s => schoolNumbersToBeAdded.includes(s.SchoolID))
          .map(d => ({ SchoolName: d.SchoolName, SchoolID: d.SchoolID }));
        this.setState({ isNewSchoolsModalOpen: true, newSchoolsData, matchedSchoolData });
      } else {
        this.setState({ matchedIncomingSchoolNumbers: matchedExistingSchoolNumbers, matchedSchoolData }, () => {
          this.handleSubmit();
        });
      }
    } else {
      this.handleSubmit(e);
    }
  };

  handleSubmit = event => {
    event?.preventDefault();
    if (this.state.rostering === "rosterImport") {
      if (this.state.isNewSchoolsModalOpen) {
        this.closeNewSchoolsModal();
      }
      if (this.state.matchedIncomingSchoolNumbers === null || this.state.matchedIncomingSchoolNumbers.length) {
        const newSchoolNumbers = this.state.newSchoolsData.map(d => d.SchoolID);
        const matchedDataToImport = this.state.data.filter(d => !newSchoolNumbers.includes(d.SchoolID));
        Meteor.call(
          "RosterImports:insertRoster",
          { data: matchedDataToImport, source: "CSV" },
          this.props.orgid,
          err => {
            if (err) {
              decWaitingOn();
              this.setState({ hasUploadFailed: true });
            } else {
              decWaitingOn();
              this.props.history.push(`/data-admin/dashboard/${this.props.orgid}`);
            }
          }
        );
        incWaitingOn(1, "Inserting into the db!");
      } else {
        this.setState({ isNewSchoolsModalOpen: true });
      }
    } else {
      this.uploadNewRosterFile();
    }
  };

  closeNewSchoolsModal = () => {
    this.setState({ isNewSchoolsModalOpen: false });
  };

  uploadNewRosterFile = () => {
    incWaitingOn(1, "Checking environment variables");
    Meteor.call("getEnvironmentVariables", ["METEOR_ENVIRONMENT"], (err, env) => {
      decWaitingOn();
      if (err) {
        console.error("Error fetching environment variables:", err);
      } else {
        console.log("Environment variables:", env);
        if (env.METEOR_ENVIRONMENT === "TEST") {
          Alert.success("Successfully uploaded the roster file to the SpringMath Support", { timeout: 500 });
          decWaitingOn();
        } else {
          const orgFolderName = this.getNormalizedOrgName();
          const path = `/rosters/organizations/${orgFolderName}/`;
          filepicker.setKey(Meteor.settings.public.FILEPICKER_KEY);

          incWaitingOn(1, "Uploading the roster file");
          filepicker.store(
            this.file,
            {
              location: "S3",
              path,
              container: "springmath-fileupload",
              access: "private",
              filename: this.state.fileName,
              mimetype: "text/csv"
            },
            result => {
              const domain = window.location.host;
              Meteor.call(
                "sendEmailForNewRosterFile",
                { orgid: this.props.orgid, filename: result.filename, domain },
                error => {
                  decWaitingOn();
                  if (!error) {
                    Alert.success("Successfully uploaded the roster file to the SpringMath Support");
                    this.props.history.push(`/data-admin/dashboard/${this.props.orgid}`);
                  } else {
                    Alert.error("Error while sending an email to the SpringMath Support");
                  }
                }
              );
            },
            () => {
              decWaitingOn();
              Alert.error("Error while uploading the roster file to the SpringMath Support");
            }
          );
        }
      }
    });
  };

  getSubtotalColumn = (subtotal, label, idPrefix) => (
    <div className="col-sm-3">
      <div className="card card-header text-center">
        <h2 id={`${idPrefix}Subtotal`} data-testid={`${idPrefix}Subtotal`}>
          {subtotal}
        </h2>
        <p>{label}</p>
      </div>
    </div>
  );

  displayFileUploadResults() {
    if (this.state.errors.length > 0 || this.state.hasUploadFailed) {
      return (
        <FileUploadErrors
          validationErrors={this.state.errors}
          hasUploadFailed={this.state.hasUploadFailed}
          fileName={this.state.fileName}
          orgid={this.props.orgid}
        />
      );
    }
    if (this.state.data.length < 1) {
      return this.displayNoDataNotice();
    }

    return (
      <div>
        <h2 id="congratulationsHeader" className="w7">
          Congratulations!
        </h2>
        <p>
          {" "}
          Nice job on the upload. The data looks good to us! Below is a quick overview of what we are going to insert
          into the app.
        </p>
        <div className="row">
          {this.getSubtotalColumn(this.state.subtotals.schools, "Schools", "school")}
          {this.getSubtotalColumn(this.state.subtotals.teachers, "Teachers", "teacher")}
          {this.getSubtotalColumn(this.state.subtotals.classes, "Classes", "class")}
          {this.getSubtotalColumn(this.state.subtotals.students, "Students", "student")}
        </div>

        <fieldset className="form-group mt-3">
          <div className="row d-flex justify-content-between">
            <div className="col-4">
              <button type="button" className="btn btn-danger form-control" onClick={this.handleCancel.bind(this)}>
                Cancel
              </button>
            </div>
            <div className="col-4">
              <button
                type="submit"
                className="btn btn-primary form-control"
                onClick={this.determineNextRosterImportStep}
              >
                {this.state.rostering === "rosterImport" ? "Finalize Upload" : "Upload to SpringMath Support"}
              </button>
            </div>
          </div>
        </fieldset>
      </div>
    );
  }

  displayNoDataNotice = () => (
    <div>
      <h2 className="w7">Sorry</h2>
      <p className="text-danger"> The upload tool found no parsable data.</p>
    </div>
  );

  render() {
    if (this.props.loading) {
      return <Loading inline message="Loading..." />;
    }

    if (!this.props.isOrgFound) {
      return (
        <div className="conFullScreen">
          <PageHeader title="Organization not found" />
        </div>
      );
    }
    return (
      <div className="conFullScreen">
        <PageHeader
          title={this.props.orgName}
          description={
            this.state.rostering === "rosterImport"
              ? "Upload Your Data"
              : "Upload Your Data for processing by SpringMath Support"
          }
        />
        <div className="container animated fadeIn">
          <OrganizationHeader orgName={this.props.orgName} sites={this.props.sites} />
          <div className="row">
            <div className="col-3">
              <div className="card-box">
                <div className="row">
                  {!this.state.fetchingPermissions ? (
                    <div className="col-12" data-testid="roster-upload-dropzone-testid">
                      <Dropzone
                        id="dz1"
                        className="alert alert-success text-xs-center"
                        onDrop={this.onDrop}
                        disablePreview
                      >
                        <div className="text-center">
                          {this.state.fileName ? (
                            <h3 className="animated fadeIn drop-zone-file-name">{this.state.fileName}</h3>
                          ) : (
                            <div className="animated fadeIn">
                              <span>
                                Try dropping your file here,
                                <br />
                                or click to select a file to upload.
                              </span>
                            </div>
                          )}
                        </div>
                      </Dropzone>
                    </div>
                  ) : (
                    <Loading inline={true} />
                  )}
                </div>
              </div>
              <p htmlFor="exampleSelect1">Would you like a helper file to know how to layout your data?</p>
              <a download="CSVHelperFile.csv" href={`data:application/octet-stream,${getCSV([requiredFieldsExample])}`}>
                Download CSV Template for File Uploads
              </a>
              <br />
              <a href="/assets/Description of Variables_Import Records Fields.pdf" target="_blank">
                Description of variables
              </a>
            </div>
            <div id="resultsArea" className="col-9">
              <div className="animated fadeIn">
                {this.state.fileDropped ? (
                  this.displayFileUploadResults()
                ) : (
                  <UploadGuidelines orgid={this.props.orgid} sites={this.props.sites} />
                )}
              </div>
            </div>
          </div>
        </div>
        <NewSchoolsModal
          showModal={this.state.isNewSchoolsModalOpen}
          orgid={this.props.orgid}
          newSchoolsData={this.state.newSchoolsData}
          matchedSchoolData={this.state.matchedSchoolData}
          confirmAction={this.handleSubmit}
          onCloseModal={this.closeNewSchoolsModal}
        />
      </div>
    );
  }
}

Upload.propTypes = {
  importedRecords: PropTypes.array.isRequired,
  orgid: PropTypes.string,
  loading: PropTypes.bool,
  isOrgFound: PropTypes.bool,
  orgName: PropTypes.string,
  sites: PropTypes.array,
  history: PropTypes.object
};

export default withTracker(props => {
  const curUser = getMeteorUserSync();
  const orgid = props.orgid || curUser?.profile?.orgid;

  const organizationsHandler = Meteor.subscribe("Organizations", orgid);
  const usersHandler = Meteor.subscribe("Users", { orgid });
  const sitesHandler = Meteor.subscribe("Sites", orgid);

  let orgName = "";
  let isOrgFound = false;
  let sites = [];

  const importedRecords = [
    {
      name: "InitalUpload.csv",
      uploadDate: new Date()
    }
  ];

  const loading = areSubscriptionsLoading(organizationsHandler, usersHandler, sitesHandler);
  if (!loading) {
    const org = Organizations.findOne(orgid);
    sites = Sites.find().fetch();
    if (org) {
      isOrgFound = true;
      orgName = org.name || "";
    }
  }

  return { loading, isOrgFound, orgName, importedRecords, sites };
})(withRouter(Upload));
