import { Meteor } from "meteor/meteor";
import Alert from "react-s-alert";
import React, { Component } from "react";
import PropTypes from "prop-types";
import isEmpty from "lodash/isEmpty";
import get from "lodash/get";
import { getFormattedStudentGroupErrors } from "./file-upload-utils";
import { download } from "../../../utilities";
import Loading from "../../../components/loading";

export default class FileUploadErrors extends Component {
  constructor(props) {
    super(props);
    this.state = {
      uploadErrorData: props.errorData ? props.errorData : null,
      isFetching: false,
      fetchingErrors: null
    };
  }

  componentDidMount() {
    if (!this.props.errorData) {
      this.setState({ isFetching: true });
      Meteor.call("RosterImports:getLastUploadErrors", this.props.orgid, (err, res) => {
        const stateToSet = { uploadErrorData: null, isFetching: false, fetchingErrors: null };
        if (err) {
          const error = err.reason || "Could not get last upload errors";
          stateToSet.fetchingErrors = error;
          Alert.error(error);
        } else {
          stateToSet.uploadErrorData = res;
        }
        return this.setState(stateToSet);
      });
    }
  }

  saveErrorsToFile = errorsToSave => {
    const fileNameWithoutExtension =
      this.props.fileName.substring(0, this.props.fileName.lastIndexOf(".")) || this.props.fileName;
    const filename = `${fileNameWithoutExtension}_ERRORS.txt`;
    const hrefData = `data:text/plain;charset=utf-8,${encodeURIComponent(errorsToSave)}`;
    download({ filename, hrefData });
  };

  getErrorText = () => {
    let displayErrors = "";
    if (this.props.hasUploadFailed && this.state.uploadErrorData) {
      const hasGroupErrors = !isEmpty(get(this.state.uploadErrorData, "studentGroupErrors"));
      const hasUniqueEmailErrors = !isEmpty(get(this.state.uploadErrorData, "uniqueEmailErrors"));
      const hasDistrictErrors = !isEmpty(get(this.state.uploadErrorData, "districtErrors"));
      const isGenericError = !hasGroupErrors && !hasUniqueEmailErrors && !hasDistrictErrors;
      displayErrors += hasGroupErrors
        ? getFormattedStudentGroupErrors(this.state.uploadErrorData.studentGroupErrors)
        : get(this.state.uploadErrorData, "error.clientMessage", "Something went wrong");
      if (isGenericError) {
        return get(this.state.uploadErrorData, "error.errorMessage", displayErrors);
      }
      if (hasUniqueEmailErrors) {
        displayErrors += "\n\n";
        displayErrors += this.state.uploadErrorData.uniqueEmailErrors.join("\n");
      }
      if (hasDistrictErrors) {
        displayErrors += "\n\n";
        displayErrors += this.state.uploadErrorData.districtErrors.join("\n");
      }
    } else if (this.state.fetchingErrors) {
      displayErrors = this.state.fetchingErrors;
    } else {
      displayErrors = this.props.validationErrors.map(error => error).join("\n\n");
    }
    return displayErrors;
  };

  render() {
    if (this.state.isFetching) {
      return <Loading />;
    }
    const errorText = this.getErrorText();
    const newLinesCount = errorText.match(/\n/g) ? errorText.match(/\n/g).length : 0;
    const rows = Math.min(newLinesCount + 2, 40);
    return (
      <div>
        <div className="pull-right">
          <button className="btn btn-primary" onClick={() => this.saveErrorsToFile(errorText)}>
            Save errors to file
          </button>
        </div>
        {!this.props.errorData ? <h2 className="w7">Sorry</h2> : null}
        <p> The upload contained errors: </p>
        <textarea disabled className="form-control alert alert-danger" rows={rows} value={errorText} />
      </div>
    );
  }
}

FileUploadErrors.propTypes = {
  validationErrors: PropTypes.array,
  errorData: PropTypes.array,
  hasUploadFailed: PropTypes.bool,
  fileName: PropTypes.string,
  orgid: PropTypes.string
};
