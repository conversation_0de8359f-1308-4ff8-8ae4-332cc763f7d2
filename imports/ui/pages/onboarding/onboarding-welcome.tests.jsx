import React from "react";
import MockDate from "mockdate";
import { assert } from "chai";
import OnboardingWelcome from "./onboarding-welcome.jsx";
import { renderWithRouter } from "../../../../tests/helpers/testUtils";

describe("OnboardingWelcome UI", () => {
  const schoolYear = 2019;
  jest.mock("../../../api/utilities/utilities", () => ({
    ...jest.requireActual("../../../api/utilities/utilities"),
    getCurrentSchoolYear: jest.fn(() => schoolYear)
  }));
  beforeAll(() => {
    MockDate.set("2018-12-20");
  });
  afterAll(() => {
    jest.restoreAllMocks();
    MockDate.reset();
  });
  describe("Render", () => {
    let onboardingWelcomeComponent;
    beforeEach(() => {
      onboardingWelcomeComponent = renderWithRouter(<OnboardingWelcome usrToken="akdj9aj4na42wsd" />);
    });
    it("render", () => {
      // Verify that the method does what we expected
      assert.isDefined(onboardingWelcomeComponent, "onboardingWelcomeComponent did not render");
    });
  });
});
