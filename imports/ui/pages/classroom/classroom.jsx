// eslint-disable-next-line max-classes-per-file
import { Meteor } from "meteor/meteor";
import React, { Component } from "react";
import PropTypes from "prop-types";
import isEmpty from "lodash/isEmpty";
import { withTracker } from "meteor/react-meteor-data";
import { Loading } from "../../components/loading.jsx";
import { Students } from "/imports/api/students/students";
import { AssessmentResults } from "/imports/api/assessmentResults/assessmentResults";
import { reformatAssessmentResults } from "/imports/api/assessmentResults/helpers";
import { ClassContext, ClassProvider } from "../classContext";
import { ClassroomView } from "./classroom-view";
import { areSubscriptionsLoading } from "../../utilities";

const classroomTrackerProps = {
  groupStats: PropTypes.object,
  inActiveSchoolYear: PropTypes.bool,
  loading: PropTypes.bool,
  isReadOnly: PropTypes.bool,
  selectedStudent: PropTypes.object,
  studentGroup: PropTypes.object,
  studentGroupId: PropTypes.string,
  students: PropTypes.array,
  studentsInIntervention: PropTypes.array,
  sortedBenchmarkHistories: PropTypes.array,
  individualInterventionQueueTransferredStudentScores: PropTypes.array,
  shouldDisplayClasswideInterventionTableOnly: PropTypes.bool,
  individualAssessmentResults: PropTypes.array
};

class ClassroomContextProvider extends Component {
  render() {
    if (this.props.loading) return <Loading />;
    return (
      <ClassProvider>
        <ClassroomContextSetter {...this.props} />
      </ClassProvider>
    );
  }
}
ClassroomContextProvider.propTypes = classroomTrackerProps;

class ClassroomContextSetter extends Component {
  static contextType = ClassContext;

  constructor(props, context) {
    super(props, context);
    this.context.setStudentGroup(this.props.studentGroup);
    this.context.setStudents(this.props.students);
  }

  render() {
    if (isEmpty(this.context.studentGroup)) {
      return <Loading />;
    }
    const {
      inActiveSchoolYear,
      selectedStudent,
      studentGroupId,
      studentsInIntervention,
      sortedBenchmarkHistories,
      individualInterventionQueueTransferredStudentScores,
      shouldDisplayClasswideInterventionTableOnly,
      individualAssessmentResults
    } = this.props;
    return (
      <ClassroomView
        inActiveSchoolYear={inActiveSchoolYear}
        selectedStudent={selectedStudent}
        studentGroupId={studentGroupId}
        studentsInIntervention={studentsInIntervention}
        sortedBenchmarkHistories={sortedBenchmarkHistories}
        individualInterventionQueueTransferredStudentScores={individualInterventionQueueTransferredStudentScores}
        shouldDisplayClasswideInterventionTableOnly={shouldDisplayClasswideInterventionTableOnly}
        individualAssessmentResults={individualAssessmentResults}
      />
    );
  }
}
ClassroomContextSetter.propTypes = classroomTrackerProps;

// Data Container
export default withTracker(({ studentGroup, studentId, schoolYear }) => {
  const studentsSub = Meteor.subscribe("StudentsInStudentGroup", studentGroup._id);
  const sgeSub = Meteor.subscribe("StudentGroupEnrollmentsInStudentGroup", studentGroup._id);
  const assessmentsSub = Meteor.subscribe("Assessments");
  const assessmentResultsSub = Meteor.subscribe("AssessmentResults:FindStudentResultsFromOtherGroups", {
    studentGroupId: studentGroup._id,
    studentIds: studentGroup.individualInterventionQueue || [],
    schoolYear
  });

  const assessmentResultsForGroupSub = Meteor.subscribe(
    "AssessmentResultsForStudentGroup",
    studentGroup._id,
    studentGroup.orgid
  );
  const individualAssessmentResultsSub = Meteor.subscribe(
    "AssessmentResults:IndividualByStudentGroupIds",
    [studentGroup._id],
    schoolYear,
    studentGroup.grade
  );

  const loading = areSubscriptionsLoading(
    studentsSub,
    sgeSub,
    assessmentsSub,
    assessmentResultsSub,
    individualAssessmentResultsSub,
    assessmentResultsForGroupSub
  );

  let students;
  let studentsInIntervention = [];
  let selectedStudent = {};
  let sortedBenchmarkHistories = [];
  let individualInterventionQueueTransferredStudentScores = [];
  let individualAssessmentResults = [];
  if (!loading) {
    students = Students.find().fetch();
    if (studentGroup) {
      if (
        (studentGroup.currentAssessmentResultIds &&
          studentGroup.currentAssessmentResultIds.length > 0 &&
          studentGroup.history) ||
        (studentGroup.history && studentGroup.history.some(h => h.type === "benchmark"))
      ) {
        sortedBenchmarkHistories = studentGroup.history
          .filter(h => h.type === "benchmark")
          .sort((a, b) => b.whenEnded.on - a.whenEnded.on);

        if (studentGroup.individualInterventionQueue && studentGroup.individualInterventionQueue.length) {
          if (sortedBenchmarkHistories.length) {
            const benchmarkAssessmentResults = AssessmentResults.find(
              {
                benchmarkPeriodId: sortedBenchmarkHistories[0].benchmarkPeriodId,
                type: "benchmark",
                "measures.studentResults.studentId": {
                  $in: studentGroup.individualInterventionQueue
                }
              },
              { fields: { lastModified: 1, "measures.studentResults": 1 } }
            ).fetch();
            individualInterventionQueueTransferredStudentScores = reformatAssessmentResults(benchmarkAssessmentResults);
          }
        }

        individualAssessmentResults = AssessmentResults.find({
          studentGroupId: studentGroup._id,
          type: "individual"
        }).fetch();
      }

      studentsInIntervention =
        (studentGroup.currentAssessmentResultIds &&
          studentGroup.currentAssessmentResultIds.reduce((list, aid) => {
            const student = students.find(s => s.currentSkill && s.currentSkill.assessmentResultId === aid);
            if (student && student._id && list.every(s => s._id !== student._id)) {
              list.push(student);
            }
            return list;
          }, [])) ||
        [];
    }
    // Display the selected student's profile (if one was selected, otherwise leave it
    // null so it will display the classroom content)
    selectedStudent = studentId ? Students.findOne({ _id: studentId }) : {};
  }
  return {
    students,
    studentsInIntervention,
    selectedStudent,
    studentGroup,
    studentGroupId: studentGroup._id,
    loading,
    sortedBenchmarkHistories,
    individualInterventionQueueTransferredStudentScores,
    individualAssessmentResults
  };
})(ClassroomContextProvider);
