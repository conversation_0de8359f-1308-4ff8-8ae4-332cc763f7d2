import React, { Component } from "react";
import get from "lodash/get";
import { Meteor } from "meteor/meteor";
import PropTypes from "prop-types";
import { ClassContext } from "../classContext";
import getCurrentBMPScreeningStatus from "/imports/api/helpers/getCurrentBMPScreeningStatus";
import StudentList from "../student-groups/student-list.jsx";
import { Loading } from "../../components/loading";
import { isHighSchoolGrade } from "../../utilities";
import ScrollIndicator from "../../components/scrollIndicator";
import ScrollIndicatorView from "../../components/scrollIndicatorView";
import { ClasswideInterventionSection } from "../student-groups/classwide-intervention-section";

export class ClassroomView extends Component {
  static contextType = ClassContext;

  constructor(props) {
    super(props);
    this.state = {
      selectedStudentId: null,
      fetchingData: false,
      triedToFetch: false,
      currentBMPScreeningStatus: null
    };
  }

  componentDidMount() {
    if (this.context.studentGroup && this.context.studentGroup._id) {
      if (this.isGroupInActiveClasswideIntervention()) {
        this.getStats("classwide");
      } else {
        this.getStats();
      }
    }
  }

  isGroupInActiveClasswideIntervention() {
    return (
      get(this.context, "studentGroup.currentClasswideSkill.assessmentId") ||
      get(this.context, "studentGroup.currentClasswideSkill.message")
    );
  }

  // eslint-disable-next-line camelcase
  UNSAFE_componentWillReceiveProps() {
    const shouldGetCurrentBMPScreeningStatus = this.context.studentGroup && !this.state.currentBMPScreeningStatus;
    if (shouldGetCurrentBMPScreeningStatus) {
      getCurrentBMPScreeningStatus(this.context.studentGroup, (err, response) => {
        this.setState({ currentBMPScreeningStatus: response });
      });
    }
  }

  getStats = (type = "individual") => {
    let methodToCall = "CalculateIndividualStats";
    const methodArguments = [this.context.studentGroup];
    if (type === "classwide") {
      const includeIndividual = true;
      methodToCall = "CalculateClasswideStats";
      methodArguments.push(includeIndividual);
    }
    this.setState({ fetchingData: true, triedToFetch: true });
    // TODO(fmazur) - move to context to replace class context
    Meteor.call(methodToCall, ...methodArguments, (err, res) => {
      if (err) {
        console.log(`Failed to get ${type} stats`);
      } else {
        this.context.setStats(res);
        this.setState({ fetchingData: false });
      }
    });
  };

  getContent(isHighSchoolGroup, screeningContinues, shouldDisplayClasswideInterventionTableOnly) {
    const { studentGroup, students } = this.context;

    if (shouldDisplayClasswideInterventionTableOnly) {
      return (
        <div className="conStudentList">
          <ClasswideInterventionSection assessmentResults={this.props.individualAssessmentResults} />
        </div>
      );
    }
    return (
      <div className="conStudentList">
        <StudentList
          lastBenchmarkHistory={get(this.props, "sortedBenchmarkHistories[0]", null)}
          individualInterventionQueueStudentIds={students
            .filter(s => (studentGroup.individualInterventionQueue || []).includes(s._id))
            .map(s => s._id)}
          individualInterventionQueueTransferredStudentScores={
            this.props.individualInterventionQueueTransferredStudentScores
          }
          individualInterventionStudentIds={this.props.studentsInIntervention.map(s => s._id)}
          isHighSchoolGroup={isHighSchoolGroup}
          individualAssessmentResults={this.props.individualAssessmentResults}
          inActiveSchoolYear={this.props.inActiveSchoolYear}
        />
      </div>
    );
  }

  render() {
    if (this.state.fetchingData || !this.state.triedToFetch) {
      return <Loading />;
    }
    const { studentGroup } = this.context;
    if (!studentGroup) {
      return null;
    }
    const { hasScreening, completed } = this.state.currentBMPScreeningStatus || {};
    const screeningContinues = hasScreening && !completed;
    const isHighSchoolGroup = isHighSchoolGrade(studentGroup.grade);
    return (
      <ScrollIndicator
        container={this}
        targetSelector={"div"}
        indicatorComponent={<ScrollIndicatorView />}
        uniqKey={this.props.sortedBenchmarkHistories.length}
      >
        {this.getContent(isHighSchoolGroup, screeningContinues, this.props.shouldDisplayClasswideInterventionTableOnly)}
      </ScrollIndicator>
    );
  }
}

ClassroomView.propTypes = {
  inActiveSchoolYear: PropTypes.bool,
  selectedStudent: PropTypes.object,
  studentGroupId: PropTypes.string,
  studentsInIntervention: PropTypes.array,
  sortedBenchmarkHistories: PropTypes.array,
  individualInterventionQueueTransferredStudentScores: PropTypes.array,
  shouldDisplayClasswideInterventionTableOnly: PropTypes.bool,
  individualAssessmentResults: PropTypes.array
};
export { ClassroomView as PureClassroom };
