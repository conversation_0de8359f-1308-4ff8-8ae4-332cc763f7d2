import React from "react";
import { Meteor } from "meteor/meteor";
import { assert } from "chai";
import sinon from "sinon";
import { mount, render } from "enzyme";
import { PureClassroom as Classroom } from "./classroom-view.jsx";
import { ClassContext } from "../classContext";

describe("Classroom UI", () => {
  const meteorCallSpy = sinon.spy(Meteor, "call");
  afterAll(() => {
    meteorCallSpy.restore();
  });
  describe("Render", () => {
    it("render", () => {
      // Verify that the method does what we expected
      const classroomComponent = render(
        <ClassContext.Provider
          value={{
            studentGroup: { _id: "test" }
          }}
        >
          <Classroom studentId={null} />
        </ClassContext.Provider>
      );
      assert.isDefined(classroomComponent, "classroomComponent did not render");
    });
  });

  describe("componentDidMount gets called for Individuals", () => {
    it("componentDidMount was called for Individual", () => {
      mount(
        <ClassContext.Provider
          value={{
            studentGroup: {
              _id: "test_student_group_X"
            },
            students: [],
            setStats() {}
          }}
        >
          <Classroom sortedBenchmarkHistories={[]} />
        </ClassContext.Provider>
      );
      assert.isTrue(meteorCallSpy.calledWithMatch("CalculateIndividualStats"));
    });
  });

  describe("componentDidMount gets called for ClassWide", () => {
    it("componentDidMount was called for Classwide", () => {
      mount(
        <ClassContext.Provider
          value={{
            studentGroup: {
              _id: "test_student_group_X",
              currentClasswideSkill: {
                assessmentId: "1234"
              }
            },
            students: []
          }}
        >
          <Classroom sortedBenchmarkHistories={[]} />
        </ClassContext.Provider>
      );
      assert.isTrue(meteorCallSpy.calledWithMatch("CalculateClasswideStats"));
    });
  });
});
