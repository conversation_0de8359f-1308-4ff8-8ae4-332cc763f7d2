import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { withTracker } from "meteor/react-meteor-data";
import { get, keyBy, map } from "lodash";
import Alert from "react-s-alert";

import { Organizations } from "/imports/api/organizations/organizations";
import { Sites } from "/imports/api/sites/sites";
import { Grades } from "/imports/api/grades/grades";
import getUnitedStatesNames from "/imports/api/helpers/getUnitedStatesNames";
import { getOrganizationIdsForState } from "/imports/api/utilities/utilities";
import { areSubscriptionsLoading, formatPercentage } from "../../utilities";
import Loading from "../../components/loading";

export function InterventionIntegrity(props) {
  if (props.loading) {
    return <Loading />;
  }

  const [selectedState, setState] = useState("");
  const [selectedOrganization, setOrganization] = useState("");
  const [selectedSchool, setSchool] = useState("");
  const [selectedGrade, setGrade] = useState("");
  const [result, setResult] = useState();
  const [isLoading, setLoading] = useState(false);
  const [organizationIdsForState, setOrganizationIdsForState] = useState([]);

  const organizationsById = keyBy(props.organizations, "_id");
  const sitesById = keyBy(props.sites, "_id");

  useEffect(() => {
    if (selectedState) {
      getOrganizationIdsForState(selectedState).then(ids => {
        setOrganizationIdsForState(ids);
      });
    } else {
      setOrganizationIdsForState([]);
    }
  }, [selectedState]);

  const onOptionChange = (setFunction, stateSetFunctionsToReset = []) => e => {
    setFunction(e.target.value);
    stateSetFunctionsToReset.forEach(stateSetFunction => stateSetFunction(""));
  };

  const onSubmit = function() {
    setLoading(true);
    Meteor.call(
      "getInterventionIntegrity",
      {
        state: selectedState,
        organizationId: selectedOrganization,
        siteId: selectedSchool,
        grade: selectedGrade
      },
      (error, response) => {
        setLoading(false);
        if (!error) {
          setResult(response);
        } else if (error.reason) {
          Alert.error(error.reason);
        } else {
          Alert.error("Error while getting Intervention Integrity");
        }
      }
    );
  };

  const renderInterventionIntegrity = function(data) {
    return (
      <React.Fragment>
        <div className="row">
          <table className="table table-condensed table-striped table-bordered table-hover table-row-centered">
            <thead>
              <tr>
                <th>% of classes with a score entered for the previous week (previous Monday to Sunday)</th>
                <th>% of classes where the median score from the previous week was at or above the mastery target</th>
                <th>% of classes where 80% or more of the scores were greater than the previous week</th>
                <th>Total number of classes and number of classes with fewer than 10 students (xx/xx)</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <th>{formatPercentage(data.percentOfClassesWithPreviousWeekScore)}</th>
                <th>{formatPercentage(data.percentOfClassesWithPreviousWeekMedianScoreAtOrAboveMasteryTarget)}</th>
                <th>{formatPercentage(data.percentOfClassesWithScoresGreaterThanPreviousWeek)}</th>
                <th>
                  {data.totalNumberOfClasses}/{data.numberOfClassesWithFewerThan10Students}
                </th>
              </tr>
            </tbody>
          </table>
        </div>

        <div className="row table-responsive">
          <table className="table table-condensed table-striped table-bordered table-hover">
            <thead>
              <tr>
                <th rowSpan="2">State</th>
                <th rowSpan="2">Organization</th>
                <th rowSpan="2">School</th>
                <th rowSpan="2">Grade</th>
                <th rowSpan="2">Number of students enrolled in SpringMath</th>
                <th rowSpan="2">% of students screened</th>
                <th colSpan="2">% of scheduled classwide interventions</th>
                <th rowSpan="2">Number of classwide skills completed (median/range)</th>
              </tr>
              <tr>
                <th>with at least 1 score entry</th>
                <th>with all skills completed</th>
              </tr>
            </thead>
            <tbody>
              {data.integrity.map(({ state, rows: stateRowSpan, organizations }) =>
                organizations.map(({ orgid, rows: orgRowSpan, sites }, orgIndex) =>
                  sites.map(({ siteId, grades }, siteIndex) =>
                    grades.map((gradeData, gradeIndex) => {
                      const siteRowIndex = gradeIndex;
                      const orgRowIndex = siteIndex + siteRowIndex;
                      const stateRowIndex = orgIndex + orgRowIndex;
                      const orgName = get(organizationsById[orgid], "name", orgid);
                      const siteName = get(sitesById[siteId], "name", siteId);
                      return (
                        <tr key={`${state}_${orgid}_${siteId}_${gradeData.grade}`}>
                          {stateRowIndex === 0 && <th rowSpan={stateRowSpan}>{state}</th>}
                          {orgRowIndex === 0 && <th rowSpan={orgRowSpan}>{orgName}</th>}
                          {siteRowIndex === 0 && <th rowSpan={grades.length}>{siteName}</th>}
                          <th>{gradeData.grade}</th>
                          <th>{gradeData.numberOfStudentsEnrolledInSpringMath}</th>
                          <th>{formatPercentage(gradeData.percentOfStudentsScreened)}</th>
                          <th>
                            {formatPercentage(gradeData.percentOfScheduledClasswideInterventionsWithAtLeast1ScoreEntry)}
                          </th>
                          <th>
                            {formatPercentage(gradeData.percentOfScheduledClasswideInterventionsWithAllSkillsCompleted)}
                          </th>
                          <th>{gradeData.numberOfClasswideSkillsCompletedMedianAndRange}</th>
                        </tr>
                      );
                    })
                  )
                )
              )}
            </tbody>
          </table>
        </div>
      </React.Fragment>
    );
  };

  let availableOrganizations = [...props.organizations];
  let availableSchools = [...props.sites];

  if (selectedState) {
    availableOrganizations = availableOrganizations.filter(organization =>
      organizationIdsForState.includes(organization._id)
    );
    availableSchools = availableSchools.filter(school => organizationIdsForState.includes(school.orgid));
  }

  if (selectedOrganization) {
    availableSchools = availableSchools.filter(school => selectedOrganization === school.orgid);
  }

  return (
    <React.Fragment>
      <div className="row m-t-15 m-b-15">
        <div className="col-md-2">
          <select
            className="form-select"
            name="state"
            onChange={onOptionChange(setState, [setOrganization, setSchool])}
            value={selectedState || ""}
          >
            <option value="">All States</option>
            {map(props.states, (state, key) => (
              <option key={key} value={key}>
                {state}
              </option>
            ))}
            <option value="international">International</option>
          </select>
        </div>

        <div className="col-md-3">
          <select
            className="form-select"
            name="organization"
            onChange={onOptionChange(setOrganization, [setSchool])}
            value={selectedOrganization || ""}
          >
            <option value="">All Organizations</option>
            {availableOrganizations.map(organization => (
              <option key={organization._id} value={organization._id}>
                {organization.name}
              </option>
            ))}
          </select>
        </div>

        <div className="col-md-3">
          <select
            className="form-select"
            name="school"
            onChange={onOptionChange(setSchool)}
            value={selectedSchool || ""}
          >
            <option value="">All Schools</option>
            {availableSchools.map(school => (
              <option key={school._id} value={school._id}>
                {school.name}
              </option>
            ))}
          </select>
        </div>

        <div className="col-md-2">
          <select className="form-select" name="grade" onChange={onOptionChange(setGrade)} value={selectedGrade || ""}>
            <option value="">All Grades</option>
            {props.grades.map(grade => (
              <option key={grade._id}>{grade._id}</option>
            ))}
          </select>
        </div>

        <div className="col-md-2 d-grid">
          <button className="btn btn-success" onClick={onSubmit}>
            Submit
          </button>
        </div>
      </div>
      {isLoading ? <Loading /> : result && renderInterventionIntegrity(result)}
    </React.Fragment>
  );
}

InterventionIntegrity.propTypes = {
  states: PropTypes.object,
  organizations: PropTypes.array,
  sites: PropTypes.array,
  grades: PropTypes.array,
  loading: PropTypes.bool
};

export default withTracker(() => {
  const orgHandle = Meteor.subscribe("Organizations:NamesAndStates");
  const sitesHandle = Meteor.subscribe("Sites:List");
  const gradesHandle = Meteor.subscribe("Grades");

  const loading = areSubscriptionsLoading(orgHandle, sitesHandle, gradesHandle);

  const states = getUnitedStatesNames();
  let organizations = [];
  let sites = [];
  let grades = [];

  if (!loading) {
    organizations = Organizations.find({ isActive: true }, { sort: { name: 1 }, fields: { name: 1 } }).fetch();
    sites = Sites.find({}, { sort: { name: 1 } }).fetch();
    grades = Grades.find({}, { sort: { sortorder: 1 } }).fetch();
  }
  return {
    states,
    organizations,
    sites,
    grades,
    loading
  };
})(InterventionIntegrity);
