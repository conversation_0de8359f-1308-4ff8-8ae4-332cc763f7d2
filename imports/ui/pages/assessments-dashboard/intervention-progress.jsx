import React, { useState, useEffect } from "react";
import { Meteor } from "meteor/meteor";
import { useTracker, useSubscribe } from "meteor/react-meteor-data";
import { range } from "lodash";
import Alert from "react-s-alert";
import Select from "react-select";

import { Grades } from "/imports/api/grades/grades";
import {
  getCurrentSchoolYear,
  getLatestAvailableSchoolYear,
  getMeteorUserSync
} from "/imports/api/utilities/utilities";
import { Organizations } from "/imports/api/organizations/organizations";
import { formatPercentage, getSelectCustomStyles } from "../../utilities";
import Loading from "../../components/loading";
import { RangeBarChart } from "./range-bar-chart";
import { getMidYearGoalPosition } from "/imports/api/helpers/getMidYearGoalPosition";

export function InterventionProgress() {
  // Use useSubscribe for subscription management
  const isGradesLoading = useSubscribe("Grades");
  const isOrganizationsLoading = useSubscribe("Organizations:NameById");

  // State for async data
  const [currentSchoolYear, setCurrentSchoolYear] = useState(null);
  const [schoolYearSelection, setSchoolYearSelection] = useState([]);
  const [asyncLoading, setAsyncLoading] = useState(true);

  // Component state - must be declared at top level
  const [selectedGrade, setGrade] = useState("K");
  const [selectedSchoolYear, setSchoolYear] = useState(null); // Will be set when currentSchoolYear is available
  const [selectedOrganizations, setSelectedOrganizations] = useState([]);
  const [result, setResult] = useState();
  const [isLoading, setLoading] = useState(false);
  const [displayedGrade, setDisplayedGrade] = useState("");

  // Use useTracker for synchronous reactive data
  const { grades, availableOrganizations } = useTracker(() => {
    let fetchedGrades = [];
    let fetchedAvailableOrganizations = [];

    // Always try to fetch data - useSubscribe handles the loading
    fetchedGrades = Grades.find({}, { sort: { sortorder: 1 } }).fetch();
    const organizations = Organizations.find({}, { sort: { name: 1 } }).fetch();
    fetchedAvailableOrganizations = organizations.map(organization => ({
      label: organization.name,
      value: organization._id
    }));

    return {
      grades: fetchedGrades,
      availableOrganizations: fetchedAvailableOrganizations
    };
  }, []);

  // Use useTracker for user (synchronous)
  const user = useTracker(() => getMeteorUserSync(), []);

  // Use useEffect for async operations
  useEffect(() => {
    if (!user) {
      setAsyncLoading(false);
      return;
    }

    const loadSchoolYearData = async () => {
      try {
        setAsyncLoading(true);

        // Get school year data using await
        const [currentYear, latestYear] = await Promise.all([
          getCurrentSchoolYear(user),
          getLatestAvailableSchoolYear(user)
        ]);

        setCurrentSchoolYear(currentYear);

        const yearSelection = range(2016, latestYear + 1).map(schoolYear => {
          const schoolYearLabel = `${schoolYear - 1}-${schoolYear % 100}`;
          return {
            label: schoolYearLabel,
            value: schoolYear
          };
        });
        setSchoolYearSelection(yearSelection);
      } catch (error) {
        console.error("Error loading school year data:", error);
      } finally {
        setAsyncLoading(false);
      }
    };

    loadSchoolYearData();
  }, [user]); // Run when user changes

  // Set selectedSchoolYear when currentSchoolYear becomes available
  useEffect(() => {
    if (currentSchoolYear && selectedSchoolYear === null) {
      setSchoolYear(currentSchoolYear - 1);
    }
  }, [currentSchoolYear, selectedSchoolYear]);

  const subscriptionLoading = isGradesLoading() || isOrganizationsLoading();

  if (subscriptionLoading || asyncLoading || !currentSchoolYear) {
    return <Loading />;
  }

  const onOptionChange = setFunction => e => {
    setFunction(e.target.value);
  };

  const handleSelectedOrganizationsChange = organizations => {
    setSelectedOrganizations(organizations);
  };

  const onSubmit = () => {
    setLoading(true);
    Meteor.call(
      "getInterventionProgress",
      {
        grade: selectedGrade,
        schoolYear: parseInt(selectedSchoolYear),
        orgIds: selectedOrganizations.map(selectedOrganization => selectedOrganization.value)
      },
      (error, response) => {
        setLoading(false);
        setDisplayedGrade(selectedGrade);
        if (!error) {
          setResult(response);
        } else if (error.reason) {
          Alert.error(error.reason);
        } else {
          Alert.error("Error while getting Intervention Progress");
        }
      }
    );
  };

  const renderInterventionProgress = data => {
    if (!data.students.length && !data.classes.length) {
      return <div className="alert alert-info text-center">No assessments found</div>;
    }

    const scaleMax = Math.max(
      ...data.students
        .filter(student => student.weeksToMasteryMaximum >= 0)
        .map(student => student.weeksToMasteryMaximum)
    );
    const midYearGoalPosition = getMidYearGoalPosition(displayedGrade);
    const endYearGoalPosition = data.numberOfDefaultSkills < data.students.length ? data.numberOfDefaultSkills : 0;

    return (
      <React.Fragment>
        <div className="row">
          <table className="table table-condensed table-striped table-bordered table-hover table-row-centered">
            <thead>
              <tr>
                <th>Number of skills mastered minimum</th>
                <th>Number of skills mastered maximum</th>
                <th>Number of skills mastered mean</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <th>{data.numberOfSkillsMasteredMin}</th>
                <th>{data.numberOfSkillsMasteredMax}</th>
                <th>{data.numberOfSkillsMasteredMean}</th>
              </tr>
            </tbody>
          </table>
        </div>

        <h3>Individual student progress when the class hits mastery</h3>
        <div className="row table-responsive">
          <table className="table table-condensed table-striped table-bordered table-hover table-row-centered">
            <thead>
              <tr>
                <th rowSpan="2">Classwide skill</th>
                <th rowSpan="2">N of students</th>
                <th colSpan="3">Weeks to mastery</th>
                <th colSpan="4">
                  When the class median is ≥ the mastery target, <span className="text-nowrap">% of students</span>
                </th>
                <th rowSpan="2">Weeks to mastery</th>
              </tr>
              <tr>
                <th>min</th>
                <th>max</th>
                <th>mean</th>
                <th>absent</th>
                <th>below instructional target</th>
                <th>at or above instructional target and below mastery target</th>
                <th>at or above mastery target</th>
              </tr>
            </thead>
            <tbody>
              {data.students.map((datum, skillIndex) => {
                const isMidYearGoal = skillIndex === midYearGoalPosition - 1;
                const isEndYearGoal = skillIndex === endYearGoalPosition - 1;
                let rowClassName = isMidYearGoal ? "mid-year-goal" : "";
                rowClassName = isEndYearGoal ? "end-year-goal" : rowClassName;
                return (
                  <tr key={datum.assessmentId} className={rowClassName}>
                    <th>{datum.classwideSkill}</th>
                    <th>{datum.numberOfStudents}</th>
                    <th>{datum.weeksToMasteryMinimum}</th>
                    <th>{datum.weeksToMasteryMaximum}</th>
                    <th>{datum.weeksToMasteryMean}</th>
                    <th>{formatPercentage(datum.percentOfStudentsAbsent)}</th>
                    <th>{formatPercentage(datum.percentOfStudentsBelowInstructionalTarget)}</th>
                    <th>
                      {formatPercentage(datum.percentOfStudentsAtOrAboveInstructionalTargetAndBelowMasteryTarget)}
                    </th>
                    <th>{formatPercentage(datum.percentOfStudentsAtOrAboveMasteryTarget)}</th>
                    <th style={{ width: "280px" }}>
                      {datum.weeksToMasteryMinimum >= 0 && (
                        <RangeBarChart
                          chartId={`weeksToMastery_${datum.assessmentId}`}
                          minValue={datum.weeksToMasteryMinimum}
                          maxValue={datum.weeksToMasteryMaximum}
                          meanValue={datum.weeksToMasteryMean}
                          scaleMin={0}
                          scaleMax={scaleMax}
                        />
                      )}
                    </th>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        <h3>Classes</h3>
        <div className="row table-responsive">
          <table className="table table-condensed table-striped table-bordered table-hover table-row-centered">
            <thead>
              <tr>
                <th>Classwide skill</th>
                <th>Count of all classes</th>
                <th>
                  Count of classes where median for 1st score <span className="text-nowrap">≥ instructional</span>{" "}
                  target
                </th>
                <th>
                  Count of classes where median for 1st score <span className="text-nowrap">&lt; instructional</span>{" "}
                  target
                </th>
              </tr>
            </thead>
            <tbody>
              {data.classes.map((datum, skillIndex) => {
                const isMidYearGoal = skillIndex === midYearGoalPosition - 1;
                const isEndYearGoal = skillIndex === endYearGoalPosition - 1;
                let rowClassName = isMidYearGoal ? "mid-year-goal" : "";
                rowClassName = isEndYearGoal ? "end-year-goal" : rowClassName;
                return (
                  <tr key={datum.assessmentId} className={rowClassName}>
                    <th>{datum.classwideSkill}</th>
                    <th>{datum.countOfAllClasses}</th>
                    <th>{datum.countOfClassesWhereMedianForFirstScoreIsAtOrAboveInstructionalTarget}</th>
                    <th>{datum.countOfClassesWhereMedianForFirstScoreIsBelowInstructionalTarget}</th>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </React.Fragment>
    );
  };

  return (
    <React.Fragment>
      <div className="row m-t-15 m-b-15">
        <div className="col-md-1">
          <select
            className="form-select"
            name="grade"
            onChange={onOptionChange(setGrade)}
            value={selectedGrade || "Grade"}
          >
            <option disabled>Grade</option>
            {grades.map(grade => (
              <option key={grade._id}>{grade._id}</option>
            ))}
          </select>
        </div>
        <div className="col-md-2">
          <select
            className="form-select"
            name="schoolYear"
            onChange={onOptionChange(setSchoolYear)}
            value={selectedSchoolYear || "School Year"}
          >
            <option disabled>School Year</option>
            {schoolYearSelection.map(schoolYear => (
              <option key={schoolYear.value} value={schoolYear.value}>
                {schoolYear.label}
              </option>
            ))}
          </select>
        </div>
        <div className="col-md-7">
          <Select
            id="selectedOrganizations"
            value={selectedOrganizations}
            isMulti
            isSearchable={true}
            styles={getSelectCustomStyles()}
            name="selectedOrganizations"
            options={availableOrganizations}
            className="basic-multi-select"
            classNamePrefix="select"
            placeholder="Search in all organizations or select specific ones"
            onChange={handleSelectedOrganizationsChange}
          />
        </div>
        <div className="col-md-2 d-grid">
          <button className="btn btn-success" onClick={onSubmit}>
            Submit
          </button>
        </div>
      </div>
      {isLoading ? <Loading /> : result && renderInterventionProgress(result)}
    </React.Fragment>
  );
}

export default InterventionProgress;
