import React, { Component } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import Select from "react-select";
import Alert from "react-s-alert";
import { last } from "lodash";
import { saveAs } from "file-saver";
import J<PERSON><PERSON><PERSON> from "jszip";
import moment from "moment";

import getUnitedStatesNames from "/imports/api/helpers/getUnitedStatesNames";

import { download } from "../../utilities";
import Loading from "../../components/loading";
import { getUserRoles } from "../data-admin/utilities";

const typeNames = {
  classwide: "Classwide Intervention Progress Monitoring",
  individual: "Individual Intervention Progress Monitoring",
  benchmark: "Screening Assessments"
};

const states = getUnitedStatesNames();
const stateOptions = Object.entries(states).map(([value, label]) => ({ value, label }));
stateOptions.unshift({ value: "all", label: "All States" });
stateOptions.push({ value: "international", label: "International" });

export default class ExportScores extends Component {
  isDataAdmin = getUserRoles().includes("dataAdmin");

  state = {
    selectedSchoolYear: undefined,
    selectedStates: [],
    availableOrgs: this.props.orgs,
    selectedOrgids: this.isDataAdmin ? [this.props.orgs[0]._id] : [],
    selectedType: undefined,
    shouldDisplayInterventionNotesCheckbox: false,
    shouldGenerateInterventionNotes: false,
    hasFinishedGeneratingCsv: false,
    availableSchoolYears: new Set(),
    csvQueue: [],
    isGeneratingAllCSVs: false,
    orgsWithErrors: [],
    downloadFormat: "CSV",
    isLoadingAvailableYears: false
  };

  componentDidUpdate(prevProps, prevState) {
    const { csvQueue, selectedOrgids, isGeneratingAllCSVs, downloadFormat, orgsWithErrors } = this.state;
    if (
      csvQueue.length === selectedOrgids.length &&
      prevState.csvQueue.length !== csvQueue.length &&
      isGeneratingAllCSVs
    ) {
      if (csvQueue.filter(obj => obj.csv.length && !obj.error).length) {
        if (downloadFormat === "CSV") {
          this.downloadCsvQueue(csvQueue);
          this.setState({ csvQueue: [], isGeneratingAllCSVs: false });
        } else {
          this.zipCSVQueue(csvQueue);
        }
        if (orgsWithErrors.length) {
          const orgErrors = this.state.availableOrgs
            .filter(org => orgsWithErrors.includes(org._id))
            .map(org => org.name)
            .join(", ");
          Alert.error(`There was an issue getting data for following organizations: 
        ${orgErrors}`);
        }

        const emptyOrgsInQueue = csvQueue.filter(obj => !obj.csv).map(obj => obj.orgid);
        const emptyOrgsInQueueMessage = this.state.availableOrgs
          .filter(org => emptyOrgsInQueue.includes(org._id) && !orgsWithErrors.includes(org._id))
          .map(org => org.name)
          .join(", ");

        if (emptyOrgsInQueue.length) {
          Alert.warning(`Data was empty for following organizations: ${emptyOrgsInQueueMessage}`, { timeout: 10000 });
        }
      } else {
        this.setState({ csvQueue: [], isGeneratingAllCSVs: false });
        Alert.error("Export generated with those parameters is empty");
      }
    }
  }

  onSelectedStateChange = selectedStates => {
    const isAllStates = !!selectedStates.find(obj => obj.value === "all");
    const isInternational = !!selectedStates.find(obj => obj.value === "international");
    const newSelectedStates = isAllStates ? stateOptions.slice(1) : selectedStates;
    const statesToUse = newSelectedStates.map(state => [state.value.toLowerCase(), state.label.toLowerCase()]).flat();
    const allStates = stateOptions
      .slice(1)
      .map(state => [state.value.toLowerCase(), state.label.toLowerCase()])
      .flat();
    const availableOrgs =
      isAllStates || !selectedStates.length
        ? this.props.orgs
        : this.props.orgs.filter(org => {
            return (
              statesToUse.includes(org.details.state.toLowerCase()) ||
              (isInternational && !allStates.includes(org.details.state.toLowerCase()))
            );
          });
    this.setState({
      selectedStates: newSelectedStates.map(state => state.value),
      availableOrgs,
      selectedOrgids: availableOrgs.filter(org => this.state.selectedOrgids.includes(org._id)).map(org => org._id)
    });
  };

  onSelectedOrgsChange = selectedOrgs => {
    const isAllOrgs = selectedOrgs.find(org => org.value === "all_orgs");
    const selectedOrgids = isAllOrgs
      ? this.state.availableOrgs.map(org => org._id)
      : selectedOrgs.map(option => option.value);
    this.setState({
      selectedOrgids
    });
    if (this.state.selectedType) {
      this.getAvailableSchoolYears(selectedOrgids, this.state.selectedType);
    }
  };

  getAvailableSchoolYears = (selectedOrgids = [], selectedResultType) => {
    if (!selectedOrgids.length) {
      this.setState({
        isLoadingAvailableYears: false,
        availableSchoolYears: new Set(),
        selectedSchoolYear: undefined
      });
    } else {
      this.setState({
        isLoadingAvailableYears: true
      });
      Meteor.call(
        "getSchoolYearsWithAssessmentResults",
        { orgIds: selectedOrgids, type: selectedResultType },
        (err, availableSchoolYears) => {
          if (err) {
            Alert.error(err.reason);
            this.setState({
              isLoadingAvailableYears: false
            });
          } else {
            const sortedSet = availableSchoolYears.sort(); // with spread.
            this.setState({
              isLoadingAvailableYears: false,
              availableSchoolYears: new Set(availableSchoolYears),
              selectedSchoolYear: last(sortedSet)
            });
          }
        }
      );
    }
  };

  onChangeValue = type => e => {
    const additionalState = {};
    const targetValue = e.value || e.target.value;
    let selectedResultType = this.state.selectedType;
    if (type === "selectedType") {
      if (targetValue === "benchmark") {
        this.props.updateColumn({ key: "showInterventionNotesCheckbox", value: true });
        additionalState.shouldDisplayInterventionNotesCheckbox = true;
      } else {
        this.props.updateColumn({ key: "showInterventionNotesCheckbox", value: false });
        additionalState.shouldDisplayInterventionNotesCheckbox = false;
        additionalState.shouldGenerateInterventionNotes = false;
      }
      selectedResultType = targetValue;
    }
    if (type !== "selectedSchoolYear" && selectedResultType) {
      this.getAvailableSchoolYears(this.state.selectedOrgids, selectedResultType);
    }

    const stateToSet = { [type]: targetValue, ...additionalState };
    return this.setState(stateToSet);
  };

  toggleInterventionNotesCheckbox = () => () => {
    this.setState(state => ({ ...state, shouldGenerateInterventionNotes: !state.shouldGenerateInterventionNotes }));
  };

  generateFilename = orgid => {
    const { selectedSchoolYear, selectedType, shouldGenerateInterventionNotes } = this.state;
    const schoolYear = selectedSchoolYear || "allYears";
    const currentDate = new Date().toISOString().slice(0, 10);
    const isExtendedBenchmark = shouldGenerateInterventionNotes ? "-interventionsNoted" : "";
    return `scoresHistory-${schoolYear}-${orgid}-${selectedType}-${currentDate}${isExtendedBenchmark}.csv`;
  };

  generateReportsForSelectedOrgs = () => {
    this.setState({ isGeneratingAllCSVs: true });
    const { selectedSchoolYear, selectedOrgids, selectedType, shouldGenerateInterventionNotes } = this.state;

    selectedOrgids.forEach(orgid => {
      Meteor.apply(
        "generateReport",
        [{ selectedSchoolYear, selectedOrgid: orgid, selectedType, shouldGenerateInterventionNotes }],
        { wait: true, noRetry: true },
        (err, resp) => {
          this.setState({ hasFinishedGeneratingCsv: true });
          if (err) {
            return this.setState({
              orgsWithErrors: [...this.state.orgsWithErrors, orgid],
              csvQueue: [
                ...this.state.csvQueue,
                {
                  orgid,
                  csvName: this.generateFilename(orgid),
                  error: true,
                  csv: ""
                }
              ]
            });
          }
          const generatedReport = resp.join("");
          if (
            (selectedType === "classwide" && generatedReport.length < 10) ||
            (selectedType === "individual" && generatedReport.length < 10) ||
            (selectedType === "benchmark" && generatedReport.length < 10)
          ) {
            return this.setState({
              csvQueue: [
                ...this.state.csvQueue,
                {
                  orgid,
                  csvName: this.generateFilename(orgid),
                  csv: ""
                }
              ]
            });
          }
          return this.setState({
            csvQueue: [
              ...this.state.csvQueue,
              {
                orgid,
                csvName: this.generateFilename(orgid),
                csv: generatedReport
              }
            ]
          });
        }
      );
    });
  };

  getZipFileName = () => {
    return `ScoresReportsArchive_${moment().format("DD-MM-YY")}.zip`;
  };

  zipCSVQueue = csvList => {
    const filteredCsvList = csvList.filter(({ csv }) => csv !== "error");
    if (filteredCsvList.length) {
      const zip = new JSZip();
      filteredCsvList.forEach(({ csv, csvName }) => {
        zip.file(csvName, csv);
      });
      zip
        .generateAsync({
          type: "blob",
          compression: "DEFLATE",
          compressionOptions: {
            level: 6
          }
        })
        .then(content => {
          saveAs(content, this.getZipFileName());
          this.setState({ csvQueue: [], isGeneratingAllCSVs: false });
        });
    } else {
      Alert.error("There was a problem generating reports. Try again later.");
    }
  };

  prepareUnifiedHeader = csvList => {
    let resultingHeader = "";
    csvList
      .filter(({ csv }) => csv !== "error")
      .forEach(({ csv }) => {
        const csvHeaders = csv.slice(0, csv.indexOf("\r\n") + 1);
        if (resultingHeader.length < csvHeaders.length) {
          resultingHeader = csvHeaders;
        }
      });
    return resultingHeader;
  };

  csvWithUnifiedHeader = csvList => {
    const unifiedHeader = this.prepareUnifiedHeader(csvList);
    const resultContent = csvList.map(({ csv }) => csv.slice(csv.indexOf("\r\n") + 2)).join("");
    return `${unifiedHeader}${resultContent}`;
  };

  // eslint-disable-next-line consistent-return
  downloadCsvQueue = csvList => {
    const filteredCsvList = csvList.filter(({ csv }) => csv !== "error");
    if (filteredCsvList.length) {
      const blob = new Blob([this.csvWithUnifiedHeader(filteredCsvList)], { type: "text/csv" });
      const hrefData = URL.createObjectURL(blob);
      return download({
        filename: this.generateFilename(
          this.state.selectedOrgids.length === 1 ? this.state.selectedOrgids[0] : "multipleOrgs"
        ),
        hrefData
      });
    }
    Alert.error("There was a problem generating reports. Try again later.");
  };

  toggleExportTypeCheckbox = () => {
    this.setState({ downloadFormat: this.state.downloadFormat === "CSV" ? "ZIP" : "CSV" });
  };

  renderExportScores = () => {
    const parsedOrgData = this.state.availableOrgs
      .sort((a, b) => {
        const nameA = a.name.toUpperCase();
        const nameB = b.name.toUpperCase();
        if (nameA < nameB) {
          return -1;
        }
        if (nameA > nameB) {
          return 1;
        }
        return 0;
      })
      .map(org => ({ value: org._id, label: `${org.name}` }));
    parsedOrgData.unshift({ value: "all_orgs", label: "Select all" });
    const { selectedSchoolYear, selectedOrgids, selectedType } = this.state;
    const isSchoolYearInputActive =
      this.state.selectedOrgids.length === 0 ||
      !this.state.selectedType ||
      (!this.state.isLoadingAvailableYears && this.state.selectedType);
    const shouldEnableGenerateBtn = !!(
      selectedSchoolYear &&
      selectedOrgids.length &&
      selectedType &&
      isSchoolYearInputActive
    );
    return (
      <tr>
        <td className="col-md-1 middle-align text-nowrap">Export Scores</td>
        <td className={`${this.isDataAdmin ? "col-md-6" : "col-md-5"} middle-align`}>
          {this.isDataAdmin ? (
            this.state.availableOrgs[0].name
          ) : (
            <React.Fragment>
              <Select
                id="export_score_state_select"
                name="export_score_state_select"
                isMulti
                onChange={this.onSelectedStateChange}
                isSearchable={true}
                value={stateOptions.filter(state => this.state.selectedStates.includes(state.value))}
                options={stateOptions}
                placeholder={"State Name"}
                className="react-select-container basic-multi-select"
                classNamePrefix="select"
              />
              <Select
                id={`export_score_select`}
                name={"export_score_select"}
                isMulti
                onChange={this.onSelectedOrgsChange}
                isSearchable={true}
                value={parsedOrgData.filter(org => this.state.selectedOrgids.includes(org.value))}
                options={parsedOrgData}
                placeholder={"Organization Name or Id"}
                className="react-select-container basic-multi-select"
                classNamePrefix="select"
              />
            </React.Fragment>
          )}
        </td>
        <td className={`${this.state.shouldDisplayInterventionNotesCheckbox ? "col-md-2" : "col-md-3"} middle-align`}>
          {" "}
          <select
            className="form-select"
            name="type"
            onChange={this.onChangeValue("selectedType")}
            value={this.state.selectedType || "Type"}
          >
            <option disabled>Type</option>
            {Object.entries(typeNames).map(([typeId, typeName]) => (
              <option key={typeId} value={typeId}>
                {typeName}
              </option>
            ))}
          </select>
        </td>
        {this.state.shouldDisplayInterventionNotesCheckbox ? (
          <td className="col-md-1 middle-align text-center">
            {" "}
            <input
              type="checkbox"
              onChange={this.toggleInterventionNotesCheckbox()}
              checked={this.state.shouldGenerateInterventionNotes}
            />
          </td>
        ) : null}
        <td className="col-md-1 middle-align">
          {isSchoolYearInputActive ? (
            <select
              className="form-select"
              name="school-year"
              onChange={this.onChangeValue("selectedSchoolYear")}
              value={this.state.selectedSchoolYear || ""}
            >
              {this.state.availableSchoolYears.size ? (
                [...this.state.availableSchoolYears].map(schoolYear => <option key={schoolYear}>{schoolYear}</option>)
              ) : (
                <option disabled value="">
                  N/A
                </option>
              )}
            </select>
          ) : (
            <Loading />
          )}
        </td>
        {!this.isDataAdmin ? (
          <td className="col-md-1 middle-align text-center">
            {" "}
            <input
              type="checkbox"
              onChange={this.toggleExportTypeCheckbox}
              checked={this.state.downloadFormat === "ZIP"}
            />
          </td>
        ) : null}
        <td className="col-md-1 middle-align">
          {this.state.isGeneratingAllCSVs ? (
            <div className="text-center">
              <Loading />
            </div>
          ) : (
            <div className="d-grid">
              <button
                className="btn btn-primary btn-lg"
                onClick={this.generateReportsForSelectedOrgs}
                disabled={!shouldEnableGenerateBtn}
              >
                Generate CSV
              </button>
            </div>
          )}
        </td>
      </tr>
    );
  };

  render() {
    return this.renderExportScores();
  }
}
ExportScores.propTypes = {
  orgs: PropTypes.array,
  updateColumn: PropTypes.func
};
