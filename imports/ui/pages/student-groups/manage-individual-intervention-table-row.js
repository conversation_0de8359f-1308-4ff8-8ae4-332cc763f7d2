import React from "react";
import PropTypes from "prop-types";

const ManageIndividualInterventionTableRow = ({ student, selectedAction, index, onChange }) => (
  <tr className="table-row-centered-content">
    <td>{`${student.identity.name.lastName}, ${student.identity.name.firstName}`}</td>
    {["resumeIntervention", "removeInterventions", "noAction"].map(action => (
      <td key={`${student._id}_${action}`}>
        <input
          type="radio"
          className="input-xs"
          name={`radio${index}`}
          value={action}
          checked={selectedAction === action}
          onChange={onChange}
        />
      </td>
    ))}
  </tr>
);

ManageIndividualInterventionTableRow.propTypes = {
  student: PropTypes.object,
  selectedAction: PropTypes.string,
  index: PropTypes.number,
  onChange: PropTypes.func
};

export default ManageIndividualInterventionTableRow;
