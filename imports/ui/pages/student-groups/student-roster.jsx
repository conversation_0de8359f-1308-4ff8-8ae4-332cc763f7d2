import React, { Component } from "react";
import PropTypes from "prop-types";
import { DropdownButton, Dropdown } from "react-bootstrap";
import { Link } from "react-router-dom";
import sortBy from "lodash/sortBy";
import * as helpers from "../../components/student-groups/helperFunction";
import { StudentGroupContext } from "/imports/contexts/StudentGroupContext";

export class StudentRoster extends Component {
  state = {
    sortBy: localStorage.getItem("rosterSorting") || "lastFirst"
  };

  static contextType = StudentGroupContext;

  getMenuItems(student) {
    const isInActiveIndividualIntervention = this.props.individualInterventionStudentIds.includes(student._id);
    const menuItems = [];
    /* Flag for removal option is hidden for now - SPRIN-1029
    <Dropdown.Item eventKey="flag-for-removal">Flag for removal</Dropdown.Item> */
    if (!isInActiveIndividualIntervention) {
      menuItems.push(
        <Dropdown.Item
          key="schedule-intervention"
          eventKey="schedule-intervention"
          data-testid={`schedule_intervention_${student._id}`}
        >
          Schedule Individual Intervention
        </Dropdown.Item>
      );
    } else {
      menuItems.push(
        <Dropdown.Item
          key="end-intervention"
          eventKey="end-the-current-individual-intervention"
          data-testid={`end_intervention_${student._id}`}
        >
          End the current individual intervention
        </Dropdown.Item>
      );
    }
    return menuItems;
  }

  get sortByString() {
    return this.state.sortBy === "lastFirst" ? "Last, First" : "First / Last";
  }

  toggleSorting = () => {
    this.setState(prevState => {
      const newValue = prevState.sortBy === "lastFirst" ? "firstLast" : "lastFirst";
      localStorage.setItem("rosterSorting", newValue);
      return { sortBy: newValue };
    });
  };

  getSortingValue = student => {
    const { firstName, lastName } = student.identity.name;
    return this.state.sortBy === "lastFirst" ? `${lastName} ${firstName}` : `${firstName} ${lastName}`;
  };

  getStudents = () => sortBy(this.props.studentsRoster, this.getSortingValue);

  renderStudents() {
    const { studentGroup } = this.context;
    return this.getStudents().map((student, index) => {
      const studentDisplayId = helpers.getStudentDispayId(student);
      const menuItems = this.getMenuItems(student);
      const shouldDisplayGradeLevel = !!student?.studentGrade && student.studentGrade !== studentGroup.grade;
      return (
        <tr key={index} data-student-grade={student.grade}>
          <td>
            <Link to={helpers.getStudentHref(student._id, studentGroup)}>
              {helpers.getStudentName(student, this.state.sortBy)}
            </Link>
            {studentDisplayId && <small className="student-display-id">{studentDisplayId}</small>}
            {shouldDisplayGradeLevel && (
              <div className="small w7">
                <span className="small">Student is at Grade Level {student.studentGrade}</span>
              </div>
            )}
          </td>
          <td>
            {this.props.canManageInterventions && (
              <div className="pull-right">
                <DropdownButton
                  variant="default"
                  title="⋯"
                  className="no-caret"
                  id={`student-dropdown-${student._id}`}
                  data-testid={`student-dropdown-${student._id}`}
                  onSelect={this.props.onStudentActionSelect(student._id)}
                >
                  {menuItems}
                </DropdownButton>
              </div>
            )}
          </td>
        </tr>
      );
    });
  }

  render() {
    return this.props.studentsRoster.length ? (
      <table className="table student-roster-table">
        <thead>
          <tr>
            <th>
              <h2 className="no-bottom-border">
                <i className="fa fa-users" />
                <span> Roster</span>
              </h2>
            </th>
            <th>
              {this.props.hasSuggestedStudentPairings ? (
                <h2 className="pull-right no-bottom-border">
                  <button className="btn btn-primary" onClick={this.props.suggestStudentPairings}>
                    <b>View Suggested Student Pairings</b>
                  </button>
                </h2>
              ) : null}
            </th>
          </tr>
          <tr>
            <th>
              Sort by:&nbsp;{" "}
              <u role="button" onClick={this.toggleSorting} data-testid="sort-by">
                {this.sortByString}
              </u>
            </th>
            <th>&nbsp;</th>
          </tr>
        </thead>
        <tbody>{this.renderStudents()}</tbody>
      </table>
    ) : null;
  }
}

StudentRoster.propTypes = {
  studentsRoster: PropTypes.array,
  suggestStudentPairings: PropTypes.func,
  onStudentActionSelect: PropTypes.func,
  isHighSchoolGroup: PropTypes.bool,
  canManageInterventions: PropTypes.bool,
  individualInterventionStudentIds: PropTypes.array,
  hasSuggestedStudentPairings: PropTypes.bool
};
