import React, { useContext, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { useTracker } from "meteor/react-meteor-data";
import chunk from "lodash/chunk";
import { Meteor } from "meteor/meteor";
import Alert from "react-s-alert";
import { isEmpty } from "lodash";
import { AssessmentGrowth } from "/imports/api/assessmentGrowth/assessmentGrowth";
import { Assessments } from "/imports/api/assessments/assessments";
import { getGrowthResults } from "/imports/api/assessmentGrowth/utilities";
import { Loading } from "../../components/loading";
import { GrowthChartWrapper } from "./growth-chart-wrapper";
import Classroom from "../classroom/classroom";
import PageHeader from "../../components/page-header";
import { isOnPrintPage } from "../../utilities";
import { StaticDataContext } from "../../../contexts/StaticDataContext";

function Growth({ studentGroup, inActiveSchoolYear, isReadOnly, schoolYear }) {
  const { benchmarkPeriods } = useContext(StaticDataContext);
  const [growthStats, setGrowthStats] = useState({});
  const [hsError, setHsError] = useState(null);

  const { growthChartResults, loading, error } = useTracker(() => {
    let trackerError = null;
    let trackerGrowthChartResults = {};
    const growthSub = Meteor.subscribe("AssessmentGrowth", studentGroup.grade);
    const assessmentSub = Meteor.subscribe("Assessment:Names");
    const trackerLoading = !growthSub.ready() || !assessmentSub.ready();

    if (!trackerLoading) {
      const assessments = Assessments.find({}, { fields: { name: 1 } }).fetch();
      const assessmentComparisonMap = AssessmentGrowth.findOne();
      if (studentGroup.grade !== "HS") {
        try {
          // Store the data needed for async getGrowthResults call
          if (assessmentComparisonMap && assessments.length > 0) {
            trackerGrowthChartResults = {
              assessmentComparisonMap,
              assessments,
              history: studentGroup.history
            };
          }
        } catch (e) {
          if (new RegExp(/getGrowthResults/).test(e.message)) {
            trackerError = e.message;
          } else {
            trackerError = "Error getting Growth chart data";
          }
        }
      }
    }

    return {
      loading: trackerLoading,
      growthChartResults: trackerGrowthChartResults,
      error: trackerError
    };
  }, [studentGroup.grade, studentGroup.history]);

  // Handle growth results calculation
  useEffect(() => {
    if (!loading && !error && growthChartResults.assessmentComparisonMap && studentGroup.grade !== "HS") {
      try {
        const results = getGrowthResults({
          history: growthChartResults.history,
          assessmentComparisonMap: growthChartResults.assessmentComparisonMap,
          assessments: growthChartResults.assessments,
          bmPeriods: benchmarkPeriods
        });
        setGrowthStats(results || {});
      } catch (e) {
        // eslint-disable-next-line no-console
        console.error("Error calculating growth results:", e);
        setGrowthStats({});
      }
    }
  }, [
    loading,
    error,
    growthChartResults.assessmentComparisonMap,
    growthChartResults.history,
    growthChartResults.assessments,
    studentGroup.grade
  ]);

  // Handle HS growth data
  useEffect(() => {
    if (studentGroup.grade === "HS") {
      Meteor.call(
        "Growth:getHSClasswideInterventionProgress",
        {
          orgid: studentGroup.orgid,
          studentGroupIds: [studentGroup._id],
          siteId: studentGroup.siteId
        },
        (err, res) => {
          if (err) {
            Alert.error(err.message);
            setHsError("Error getting Growth chart data");
          } else {
            setGrowthStats(res);
          }
        }
      );
    }
  }, [studentGroup._id, studentGroup.orgid, studentGroup.siteId, studentGroup.grade]);

  if (loading) {
    return <Loading />;
  }
  if (error || hsError) {
    return <h3 className="text-center">{error || hsError}</h3>;
  }

  if (isEmpty(growthStats) && studentGroup.grade !== "HS") {
    return <h3 className="text-center">No growth data available</h3>;
  }

  const isPrinting = isOnPrintPage();
  const title = `Growth for ${studentGroup.name} in ${schoolYear - 1}-${schoolYear % 100}`;
  return (
    <React.Fragment>
      {isPrinting ? <PageHeader title={title} /> : null}
      <div className="studentDetailContent">
        <Classroom
          shouldDisplayClasswideInterventionTableOnly={true}
          studentGroup={studentGroup}
          inActiveSchoolYear={inActiveSchoolYear}
          isReadOnly={isReadOnly}
          schoolYear={schoolYear}
        />
        {studentGroup.grade !== "HS" ? (
          <>
            <GrowthChartWrapper comparisonPeriod={"fall"} growthChartResults={growthStats} grade={studentGroup.grade} />
            <GrowthChartWrapper
              comparisonPeriod={"spring"}
              growthChartResults={growthStats}
              grade={studentGroup.grade}
            />
          </>
        ) : (
          chunk(growthStats, 4).map((statChunk, index) => {
            return (
              <GrowthChartWrapper
                comparisonPeriod={"all"}
                growthChartResults={statChunk}
                grade={studentGroup.grade}
                key={`growthStats${index}`}
                chartId={`growth_All_HS${index}`}
                noChartTitleAndLegend={index > 0}
              />
            );
          })
        )}
      </div>
    </React.Fragment>
  );
}

Growth.propTypes = {
  studentGroup: PropTypes.object,
  inActiveSchoolYear: PropTypes.bool,
  isReadOnly: PropTypes.bool,
  schoolYear: PropTypes.number
};

export default Growth;
