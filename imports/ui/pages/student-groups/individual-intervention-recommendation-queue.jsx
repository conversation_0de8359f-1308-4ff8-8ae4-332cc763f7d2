import React, { Component } from "react";
import PropTypes from "prop-types";
import IndividualInterventionStudentList from "../../components/individual-intervention/individual-intervention-student-list";

export class IndividualInterventionRecommendationQueue extends Component {
  renderBeginIndividualInterventionButton = () => {
    if (!this.props.inActiveSchoolYear) {
      return null;
    }
    if (this.props.checkedStudents && this.props.checkedStudents.length) {
      return (
        <button className="btn btn-success btn-center" onClick={this.props.assignIndividualIntervention}>
          Begin Individual Intervention
        </button>
      );
    }
    return (
      <button className="btn btn-default btn-center cursor-not-allowed" disabled>
        Begin Individual Intervention
      </button>
    );
  };

  render() {
    return this.props.shouldDisplay ? (
      <section className="studentListIndividualInterventionQueue">
        <h2>
          <i className="fa fa-user" />
          <span> Eligible for Individual Intervention</span>
        </h2>
        <p>
          The following students would benefit from individual interventions. If you have additional capacity, you may
          choose to begin interventions with some of these students. Intervention takes 10-15 minutes a day per student,
          so we recommend selecting 1 or 2 students to work with.
        </p>
        <IndividualInterventionStudentList
          recommendationData={this.props.recommendationData}
          addStudent={this.props.addStudent}
          schedulingEnabled={this.props.inActiveSchoolYear}
          scheduledStudentIds={[]}
          checkedStudents={this.props.checkedStudents}
          screeningData={this.props.screeningData}
        />
        {this.renderBeginIndividualInterventionButton()}
      </section>
    ) : null;
  }
}

IndividualInterventionRecommendationQueue.propTypes = {
  shouldDisplay: PropTypes.bool,
  inActiveSchoolYear: PropTypes.bool,
  recommendationData: PropTypes.array,
  addStudent: PropTypes.func,
  checkedStudents: PropTypes.array,
  assignIndividualIntervention: PropTypes.func,
  screeningData: PropTypes.object
};
