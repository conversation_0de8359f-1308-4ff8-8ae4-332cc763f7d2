import React from "react";
import { withTracker } from "meteor/react-meteor-data";
import PropTypes from "prop-types";
import PageHeader from "../../components/page-header.jsx";
import { areSubscriptionsLoading } from "../../utilities";
import { Users } from "../../../api/users/users";
import { ROLE_IDS } from "../../../../tests/cypress/support/common/constants";

function NotAssignedTeacherPage(props) {
  return (
    <div>
      <PageHeader title="Welcome to SpringMath" />
      <div className="conFullScreen">
        <div className="container">
          {props.dataAdmins.length ? (
            <React.Fragment>
              <h5 className="text-center">
                You are not assigned to any student groups in the active school year. Please contact your district
                SpringMath Data Administrator.
              </h5>
              <br />
              <h6 className="m-l-15">District Data Administrators:</h6>
              <ul className="m-l-15">
                {props.dataAdmins.map((a, index) => {
                  const dataAdminName = `${a.profile.name.last}, ${a.profile.name.first}`;
                  const dataAdminEmail = a.emails?.[0]?.address || "";
                  return (
                    <li key={index}>
                      {dataAdminName} - ({" "}
                      <a className="" href={`mailto:${dataAdminEmail}`}>
                        {dataAdminEmail}
                      </a>{" "}
                      )
                    </li>
                  );
                })}
              </ul>
            </React.Fragment>
          ) : null}
        </div>
      </div>
    </div>
  );
}

NotAssignedTeacherPage.propTypes = {
  dataAdmins: PropTypes.array
};

export default withTracker(() => {
  let dataAdmins = [];
  const dataAdminSub = Meteor.subscribe("Users:DataAdminsInOrganizationFromUser");
  const loading = areSubscriptionsLoading(dataAdminSub);
  if (!loading) {
    dataAdmins = Users.find({
      "profile.siteAccess.role": ROLE_IDS.dataAdmin
    }).fetch();
  }
  return { dataAdmins };
})(NotAssignedTeacherPage);
