import { Meteor } from "meteor/meteor";
import React, { Component } from "react";
import PropTypes from "prop-types";
import { get, isEqual, keyBy, sortBy, uniq } from "lodash";
import Alert from "react-s-alert";
import * as helpers from "../../components/student-groups/helperFunction";
import SuggestedStudentPairingsModal from "../screening/suggested-student-pairings-modal";
import { isAdminOrUniversalCoach } from "/imports/api/roles/methods";
import { StudentGroupContext } from "/imports/contexts/StudentGroupContext";
import ConfirmModal from "../data-admin/confirm-modal";
import ManageIndividualInterventionModal from "./manage-individual-intervention-modal";
import { StudentRoster } from "./student-roster";
import { IndividualInterventionSection } from "./individual-intervention-section";
import { IndividualInterventionRecommendationQueue } from "./individual-intervention-recommendation-queue";
import { StudentsFlaggedForRemoval } from "./students-flagged-for-removal";
import InstructionalVideoModal from "../../components/instructional-video-modal";
import { getMeteorUserSync } from "/imports/api/utilities/utilities";

export default class StudentList extends Component {
  static contextType = StudentGroupContext;

  constructor(props) {
    super(props);
    this.state = {
      checkedStudents: [],
      isFlagForRemovalModalOpen: false,
      isSuggestedStudentPairingsModalOpen: false,
      studentFlaggedForRemoval: null,
      hasNewSuggestedStudentPairingsAvailable: false,
      isModalOpen: false,
      onCloseModal: () => {},
      videoId: "",
      videoTimestamp: 0,
      individualRecommendationList: []
    };
  }

  componentDidMount() {
    const initialStudentResults = this.getInitialStudentResults();
    this.setState({
      studentResults: initialStudentResults,
      initialStudentResults
    });
    this.fetchIndividualRecommendationList();
  }

  componentDidUpdate(prevProps) {
    if (
      !isEqual(prevProps.individualInterventionStudentIds, this.props.individualInterventionStudentIds) ||
      !isEqual(prevProps.individualInterventionQueueStudentIds, this.props.individualInterventionQueueStudentIds) ||
      !isEqual(
        prevProps.individualInterventionQueueTransferredStudentScores,
        this.props.individualInterventionQueueTransferredStudentScores
      )
    ) {
      this.fetchIndividualRecommendationList();
    }
  }

  fetchIndividualRecommendationList = () => {
    const {
      studentGroup: { history, grade, schoolYear },
      studentsInStudentGroup: students
    } = this.context;
    const {
      individualInterventionStudentIds,
      individualInterventionQueueStudentIds,
      individualInterventionQueueTransferredStudentScores,
      isHighSchoolGroup
    } = this.props;
    Meteor.call(
      "getIndividualRecommendationList",
      {
        history,
        grade,
        schoolYear,
        students,
        isHighSchoolGroup,
        individualInterventionStudentIds,
        individualInterventionQueueStudentIds,
        individualInterventionQueueTransferredStudentScores
      },
      (err, resp) => {
        if (!err) {
          this.setState({ individualRecommendationList: resp || [] });
        } else {
          Alert.error("There was an issue fetching individual recommendation list");
        }
      }
    );
  };

  addStudent = (isChecked, studentId) => {
    this.setState(prevState => ({
      ...prevState,
      checkedStudents: isChecked
        ? [...prevState.checkedStudents, studentId]
        : prevState.checkedStudents.filter(sId => sId !== studentId)
    }));
  };

  callProcessIndividualRule = ({ assessmentResultId, studentIds, allowNoScore = true }) => {
    studentIds.forEach((studentId, index) => {
      let cb = () => {};
      if (studentIds.length - 1 === index) {
        cb = () => {
          Alert.success("Individual interventions scheduled successfully", {
            timeout: 3000
          });
        };
      }
      this.context.processIndividualRule({
        assessmentResultId,
        allowNoScore,
        studentId,
        cb
      });
    });
  };

  assignIndividualIntervention = () => {
    if (this.state.checkedStudents.length === 0) {
      Alert.error("Please select a student(s)", {
        timeout: 5000
      });
    } else {
      const { _id: studentGroupId, siteId, orgid } = this.context.studentGroup;
      Meteor.call(
        "AssessmentResult:hasGroupOpenIndividualIntervention",
        studentGroupId,
        siteId,
        orgid,
        this.showInstructionalVideoForNewIntervention
      );
    }
  };

  showInstructionalVideoForNewIntervention = (
    errorCheckingForOpenInterventions /* , hasOpenIndividualIntervention */
  ) => {
    // const { siteId, _id } = this.context.studentGroup;
    if (errorCheckingForOpenInterventions) {
      Alert.error("There was a problem checking for open interventions.", {
        timeout: 5000
      });
    }
    // if (!hasOpenIndividualIntervention) {
    //   // eslint-disable-next-line consistent-return
    //   Meteor.call("users:isOwnerOrSecondaryTeacherOfGroup", _id, (err, { isOwner, userRole }) => {
    //     if (userRole === "admin" ? isOwner : true) {
    //       const videoType = "individual";
    //       return Meteor.call(
    //         "InstructionalVideos:getIdFromVideoUrl",
    //         videoType,
    //         siteId,
    //         (error, { videoId, videoTimestamp }) => {
    //           if (error) {
    //             Alert.error("There was a problem fetching video url.", {
    //               timeout: 5000
    //             });
    //             return null;
    //           }
    //
    //           return this.openModal({
    //             onCloseModal: () => this.processStudentsIndividualRule(),
    //             videoId,
    //             videoTimestamp
    //           });
    //         }
    //       );
    //     }
    //     this.processStudentsIndividualRule();
    //   });
    // } else {
    this.processStudentsIndividualRule();
    // }
  };

  processStudentsIndividualRule = () => {
    this.callProcessIndividualRule({
      assessmentResultId: this.props.isHighSchoolGroup
        ? null
        : get(this.props.lastBenchmarkHistory, "assessmentResultId", null),
      studentIds: this.state.checkedStudents
    });
    return this.setState({
      checkedStudents: []
    });
  };

  closeModal = () => {
    this.setState({ isModalOpen: false });
  };

  openModal = ({ onCloseModal, videoId = "", videoTimestamp }) => {
    this.setState({ isModalOpen: true, onCloseModal, videoId, videoTimestamp });
  };

  onStudentActionSelect = studentId => e => {
    if (e === "flag-for-removal") {
      this.setState({
        isFlagForRemovalModalOpen: true,
        studentFlaggedForRemoval: studentId
      });
    } else if (e === "schedule-intervention") {
      const { isHighSchoolGroup } = this.props;
      this.callProcessIndividualRule({
        assessmentResultId: isHighSchoolGroup ? null : get(this.props.lastBenchmarkHistory, "assessmentResultId", null),
        studentIds: [studentId]
      });
    } else if (e === "end-the-current-individual-intervention") {
      Meteor.call(
        "endCurrentIndividualIntervention",
        {
          studentId,
          studentGroupId: this.context.studentGroupId
        },
        err => {
          if (err) {
            helpers.displayError(`There was an error: ${err.message}`);
          }
        }
      );
    }
  };

  flagStudentForRemoval = () => {
    Meteor.call(
      "flagStudentForRemoval",
      {
        studentGroupId: this.context.studentGroupId,
        studentId: this.state.studentFlaggedForRemoval
      },
      err => {
        if (err) {
          helpers.displayError(`There was an error: ${err.message}`);
        } else {
          Alert.success(
            `${this.getStudentNameByStudentId(this.state.studentFlaggedForRemoval)} has been flagged for removal`,
            {
              timeout: 3000
            }
          );
        }
        this.setState({
          isFlagForRemovalModalOpen: false,
          studentFlaggedForRemoval: null
        });
      }
    );
  };

  unflagStudentForRemoval = studentId => () => {
    Meteor.call("flagStudentForRemoval", {
      studentGroupId: this.context.studentGroupId,
      studentId,
      unflag: true
    });
  };

  suggestStudentPairings = () => {
    this.setState({ isSuggestedStudentPairingsModalOpen: true });
  };

  getMostRecentClasswideHistoryItem = () =>
    this.context.studentGroup.history?.find(historyItem => historyItem.type === "classwide");

  sortStudentResults = (results = []) => {
    return sortBy(results, studentResult => -studentResult.score);
  };

  parseAssessmentResultItem = (studentResults = []) => {
    return this.context.studentsInStudentGroup.map(({ _id, identity }) => {
      const studentResult = studentResults.find(result => result.studentId === _id);
      const score = get(studentResult, "score", "");
      return {
        id: _id,
        score: score ? parseInt(score) : score,
        firstName: identity.name.firstName,
        lastName: identity.name.lastName
      };
    });
  };

  getNextStudentResults = (currentScores, comparePool) => {
    if (currentScores && comparePool) {
      return comparePool.find(scores => {
        return (
          currentScores.length !== scores.length ||
          !scores.every((value, index) => value?.id === currentScores[index]?.id)
        );
      });
    }
    return null;
  };

  getInitialStudentResults() {
    // NOTE(fmazur) - hasNewSuggestedStudentPairingsAvailable only requires proper setting during initial calculations
    const mostRecentClasswideHistory = this.getMostRecentClasswideHistoryItem();
    const mostRecentBenchmarkHistory = this.props.lastBenchmarkHistory;
    if (
      !mostRecentClasswideHistory?.assessmentResultMeasures?.length &&
      !mostRecentBenchmarkHistory?.assessmentResultMeasures?.length
    ) {
      return [];
    }
    const classwideStudentResults = mostRecentClasswideHistory?.assessmentResultMeasures?.[0];
    let classwideParsedScores = [];
    const benchmarkParsedScores = mostRecentBenchmarkHistory?.assessmentResultMeasures.map(resultMeasure => {
      const { studentResults } = resultMeasure;
      return this.sortStudentResults(this.parseAssessmentResultItem(studentResults));
    });
    if (classwideStudentResults && Object.keys(classwideStudentResults).length) {
      classwideParsedScores = this.sortStudentResults(
        this.parseAssessmentResultItem(classwideStudentResults?.studentResults, classwideStudentResults?.assessmentId)
      );
      const nextResults = this.getNextStudentResults(classwideParsedScores, benchmarkParsedScores);
      const hasNewPairsAvailable = !!nextResults?.length;
      if (hasNewPairsAvailable) {
        this.setState({
          hasNewSuggestedStudentPairingsAvailable: true,
          initialNextStudentResults: nextResults
        });
      } else {
        this.setState({ currentStudentResultType: "classwide" });
      }
      return classwideParsedScores;
    }
    const uniqueBmMeasures = uniq(
      benchmarkParsedScores.map(measure => {
        return JSON.stringify(measure.map(m => m.id));
      })
    );
    if (uniqueBmMeasures.length > 1) {
      const nextResults = this.getNextStudentResults(benchmarkParsedScores[0], benchmarkParsedScores);
      this.setState({
        hasNewSuggestedStudentPairingsAvailable: true,
        initialNextStudentResults: nextResults
      });
    }
    return benchmarkParsedScores[0];
  }

  getStudentResults() {
    const { studentResults, initialNextStudentResults, initialStudentResults } = this.state;

    const currentStudentResultIds = studentResults.map(s => s.id);
    const initialStudentResultIds = initialStudentResults.map(s => s.id);
    const shouldSwapResults = isEqual(currentStudentResultIds, initialStudentResultIds);
    this.setState({
      studentResults: shouldSwapResults ? initialNextStudentResults : initialStudentResults,
      nextStudentResults: !shouldSwapResults ? initialNextStudentResults : initialStudentResults
    });
  }

  closeSuggestedStudentPairingModal = () => {
    this.setState({ isSuggestedStudentPairingsModalOpen: false });
  };

  closeFlagStudentForRemovalModal = () => {
    this.setState({ isFlagForRemovalModalOpen: false });
  };

  getScreeningData() {
    const mostRecentBenchmark = this.context.studentGroup.history?.find(item => item.type === "benchmark");
    if (mostRecentBenchmark) {
      const assessmentResultMeasuresByAssessmentId = keyBy(
        mostRecentBenchmark.assessmentResultMeasures,
        "assessmentId"
      );
      Object.entries(assessmentResultMeasuresByAssessmentId).forEach(([key, assessmentResultMeasure]) => {
        assessmentResultMeasuresByAssessmentId[key].screeningResults = keyBy(
          assessmentResultMeasure.studentResults,
          "studentId"
        );
      });
      return assessmentResultMeasuresByAssessmentId;
    }
    return null;
  }

  getStudentNameByStudentId = studentId => {
    const student = this.context.studentsInStudentGroup.find(s => s._id === studentId);
    if (!student) return "";
    const { firstName, lastName } = student.identity.name;
    return `${firstName} ${lastName}`;
  };

  removeIndividualInterventionProgressAndStartOver = studentId => {
    const { lastBenchmarkHistory, isHighSchoolGroup } = this.props;
    const { studentGroup } = this.context;
    const assessmentResultId = isHighSchoolGroup ? null : get(lastBenchmarkHistory, "assessmentResultId", null);

    Meteor.call(
      "removeIndividualInterventionProgressAndStartOver",
      {
        assessmentResultId,
        studentId,
        siteId: studentGroup.siteId,
        studentGroupId: studentGroup._id
      },
      err => {
        if (err) {
          helpers.displayError(
            `There was an error removing the individual intervention for: ${this.getStudentNameByStudentId(
              studentId
            )}. ${err.message}`
          );
        } else {
          Alert.success("Intervention removed", {
            timeout: 6000
          });
        }
      }
    );
  };

  resumeIndividualIntervention = studentId => {
    const { siteId, orgid } = this.context.studentGroup;
    Meteor.call("resumeIndividualIntervention", studentId, siteId, err => {
      if (err) {
        helpers.displayError(
          `There was an error resuming the individual intervention for: ${this.getStudentNameByStudentId(studentId)}. ${
            err.message
          }`
        );
      } else {
        Meteor.call("Students:setStudentContinueSkill", { studentId, orgid, siteId }, e => {
          if (e) {
            helpers.displayError(
              `There was an error resuming the individual intervention for: ${this.getStudentNameByStudentId(
                studentId
              )}. ${e.message}`
            );
          } else {
            Alert.success("Intervention resumed", {
              timeout: 6000
            });
          }
        });
      }
    });
  };

  restoreIndividualInterventions = actionByStudentId => {
    Object.entries(actionByStudentId).forEach(([studentId, action]) => {
      if (action === "resumeIntervention") {
        this.resumeIndividualIntervention(studentId);
      } else if (action === "removeInterventions") {
        this.removeIndividualInterventionProgressAndStartOver(studentId);
      }
    });
  };

  getRosterAndStudentsFlaggedForRemoval(allStudents) {
    const studentsRoster = [];
    const studentsFlaggedForRemoval = [];
    allStudents.forEach(student => {
      if ((this.context.studentGroup.flaggedForRemovalStudentIds || []).includes(student._id)) {
        studentsFlaggedForRemoval.push(student);
      } else {
        studentsRoster.push(student);
      }
    });
    const sortedStudentRoster = helpers.sortStudentsByName(studentsRoster);
    const sortedStudentsFlaggedForRemoval = helpers.sortStudentsByName(studentsFlaggedForRemoval);
    return { sortedStudentRoster, sortedStudentsFlaggedForRemoval };
  }

  render() {
    const { individualInterventionStudentIds, individualAssessmentResults } = this.props;
    const { studentsInStudentGroup: students, alreadyCompletedTreeData } = this.context;
    const individualInterventionStudents = students.filter(s => individualInterventionStudentIds.includes(s._id));
    const displayIndividualInterventionRecommendationQueue = this.state.individualRecommendationList.length > 0;
    const { sortedStudentRoster, sortedStudentsFlaggedForRemoval } = this.getRosterAndStudentsFlaggedForRemoval(
      students
    );
    const user = getMeteorUserSync();
    const canManageInterventions =
      user && isAdminOrUniversalCoach(this.context.studentGroup.siteId, user.profile?.siteAccess);
    let studentName;
    let skillName;

    if (alreadyCompletedTreeData) {
      studentName = this.getStudentNameByStudentId(alreadyCompletedTreeData.studentId) || "Student";
      skillName = alreadyCompletedTreeData.skillName || "current Classwide Intervention skill";
    }

    return (
      <div className="conStudentListContent">
        <IndividualInterventionSection
          shouldDisplay={individualInterventionStudents.length > 0}
          individualInterventionStudents={individualInterventionStudents}
          individualAssessmentResults={individualAssessmentResults}
        />
        <IndividualInterventionRecommendationQueue
          shouldDisplay={displayIndividualInterventionRecommendationQueue}
          recommendationData={this.state.individualRecommendationList}
          addStudent={this.addStudent}
          checkedStudents={this.state.checkedStudents}
          assignIndividualIntervention={this.assignIndividualIntervention}
          screeningData={this.getScreeningData()}
          inActiveSchoolYear={this.props.inActiveSchoolYear}
        />
        {this.state.isModalOpen ? (
          <InstructionalVideoModal
            showModal={this.state.isModalOpen}
            closeModal={this.closeModal}
            onCloseModal={this.state.onCloseModal}
            videoId={this.state.videoId}
            videoTimestamp={this.state.videoTimestamp}
            headerText="If this is your first time using individual intervention please watch this video."
            testIdPrefix="individual-intervention"
          />
        ) : null}
        <StudentRoster
          studentsRoster={sortedStudentRoster}
          suggestStudentPairings={this.suggestStudentPairings}
          isHighSchoolGroup={this.props.isHighSchoolGroup}
          canManageInterventions={canManageInterventions}
          individualInterventionStudentIds={individualInterventionStudentIds}
          onStudentActionSelect={this.onStudentActionSelect}
          hasSuggestedStudentPairings={!!this.state.studentResults?.length}
        />
        <StudentsFlaggedForRemoval
          shouldDisplay={sortedStudentsFlaggedForRemoval.length > 0}
          studentsFlaggedForRemoval={sortedStudentsFlaggedForRemoval}
          unflagStudentForRemoval={this.unflagStudentForRemoval}
        />
        <ConfirmModal
          showModal={this.state.isFlagForRemovalModalOpen}
          onCloseModal={this.closeFlagStudentForRemovalModal}
          confirmAction={this.flagStudentForRemoval}
          headerText="Are you sure you want to flag this student for removal?"
          bodyText="This will notify the administrator that this student is no longer in this class so they can be removed from the system.
            The student will be removed from all current interventions.
            You will still see their data in screening results for past assessments."
          bodyQuestion="Do you wish to continue?"
          confirmText="Yes, flag this student for removal"
          cancelText="No, Cancel"
        />
        {this.state.studentResults?.length ? (
          <SuggestedStudentPairingsModal
            showModal={this.state.isSuggestedStudentPairingsModalOpen}
            closeModal={this.closeSuggestedStudentPairingModal}
            studentResults={this.state.studentResults}
            hasNewPairings={this.state.hasNewSuggestedStudentPairingsAvailable}
            getNewPairings={() => this.getStudentResults()}
          />
        ) : null}
        <ConfirmModal
          confirmAction={() => this.context.setAlreadyCompletedTreeData(null)}
          onCloseModal={() => {}}
          cancelText=""
          confirmText="Close"
          headerText="Skill already completed"
          bodyQuestion=""
          bodyText={`${studentName} has already completed individual interventions for ${skillName}. For additional practice materials related to this skill see the support portal. Once the class has moved on to the next skill in the classwide skill sequence ${studentName} can be referred again for individual intervention.`}
          showModal={!!alreadyCompletedTreeData}
        />
        <ManageIndividualInterventionModal restoreIndividualInterventions={this.restoreIndividualInterventions} />
      </div>
    );
  }
}

StudentList.propTypes = {
  lastBenchmarkHistory: PropTypes.shape({
    assessmentResultMeasures: PropTypes.array,
    assessmentResultId: PropTypes.string
  }),
  individualInterventionQueueStudentIds: PropTypes.arrayOf(PropTypes.string),
  individualInterventionStudentIds: PropTypes.arrayOf(PropTypes.string),
  individualInterventionQueueTransferredStudentScores: PropTypes.array,
  endInterventionHandling: PropTypes.func,
  addUnprocessedStudent: PropTypes.func,
  showInterventionModal: PropTypes.bool,
  isHighSchoolGroup: PropTypes.bool,
  individualAssessmentResults: PropTypes.array,
  inActiveSchoolYear: PropTypes.bool
};

StudentList.defaultProps = {
  individualInterventionQueueTransferredStudentScores: [],
  individualInterventionStudentIds: [],
  showInterventionModal: false
};
