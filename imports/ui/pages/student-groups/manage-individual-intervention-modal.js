import React, { Component } from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import ManageIndividualInterventionTableRow from "./manage-individual-intervention-table-row";
import { StudentGroupContext } from "/imports/contexts/StudentGroupContext";

export default class ManageIndividualInterventionModal extends Component {
  static contextType = StudentGroupContext;

  state = {
    actionByStudent: {}
  };

  componentDidUpdate() {
    const { actionByStudent } = this.state;
    if (Object.values(actionByStudent).length === this.context.studentsWithIndividualRuleNotProcessed.length) return;
    this.context.studentsWithIndividualRuleNotProcessed.forEach(student => {
      actionByStudent[student._id] = "noAction";
    });
    this.setState({
      actionByStudent
    });
  }

  close = () => {
    this.resetSelection();
    this.context.endInterventionHandling();
  };

  resetSelection() {
    this.setState({
      actionByStudent: {}
    });
  }

  onChangeAction = studentId => event => {
    const actionByStudent = { ...this.state.actionByStudent, [studentId]: event.target.value };
    this.setState({
      actionByStudent
    });
  };

  submit = () => {
    this.props.restoreIndividualInterventions(this.state.actionByStudent);
    this.close();
  };

  render() {
    const { showInterventionModal } = this.context;
    let tableContent = [];
    if (this.context.studentsWithIndividualRuleNotProcessed.length) {
      tableContent = this.context.studentsWithIndividualRuleNotProcessed.map((student, index) => (
        <ManageIndividualInterventionTableRow
          student={student}
          index={index}
          key={student._id}
          selectedAction={this.state.actionByStudent[student._id]}
          onChange={this.onChangeAction(student._id)}
        />
      ));
    }
    return (
      <Modal
        show={showInterventionModal}
        size="lg"
        onHide={this.close}
        dialogClassName="manage-individual-intervention-modal"
        backdrop="static"
        data-testid="manage-individual-intervention-modal"
      >
        <ModalHeader>
          <h2 className="w9 pull-right">
            <i className="fa fa-2x fa-warning text-warning pull-left" />
            <div className="text-center">
              The student(s) already have scores entered for a previous individual intervention.
            </div>
          </h2>
        </ModalHeader>

        <ModalBody>
          <div className="well">
            <p>Choose one of the following options:</p>
            <ul>
              <li>
                <strong>Resume intervention:</strong>
                <br />
                Resume the previous individual intervention where you left off
              </li>
              <li className="mt-1">
                <strong>Remove interventions:</strong>
                <br />
                Permanently delete all of their scores from the previous individual intervention and start over
              </li>
              <li className="mt-1">
                <strong>No action:</strong>
                <br />
                Do not perform any action
              </li>
            </ul>
          </div>
          <table className="table">
            <thead>
              <tr className="table-row-centered-content">
                <th>Student Name</th>
                <th>Resume intervention</th>
                <th>Remove interventions</th>
                <th>No action</th>
              </tr>
            </thead>
            <tbody>{tableContent}</tbody>
          </table>
        </ModalBody>

        <ModalFooter className="d-flex justify-content-center">
          <Button variant="success" onClick={this.submit}>
            Submit
          </Button>
          <Button variant="default" onClick={this.close}>
            Cancel
          </Button>
        </ModalFooter>
      </Modal>
    );
  }
}

ManageIndividualInterventionModal.propTypes = {
  restoreIndividualInterventions: PropTypes.func.isRequired
};
