import isEmpty from "lodash/isEmpty";
import PropTypes from "prop-types";
import React from "react";
import GrowthChart from "../admin-view/graphs/growth-chart";
import {
  getParsedGrowthResultsForChart,
  getParsedHSGrowthResultsForChart
} from "/imports/api/assessmentGrowth/utilities";
import HSGrowthChart from "../admin-view/graphs/hs-growth-chart";
import { getGrowthGraphSubtitle } from "../program-evaluation/helpers";

export const getGrowthChartMarkup = (
  comparisonPeriod,
  growthResults,
  generateAllPeriods,
  grade,
  customColorsByMeasurePeriod
) => {
  if (grade !== "HS") {
    const parsedResultsForChart = getParsedGrowthResultsForChart(growthResults, customColorsByMeasurePeriod);

    if (generateAllPeriods) {
      return {
        titleText: "Fall to Spring",
        chartId: `growth_${comparisonPeriod}_${grade}`,
        chartItems: parsedResultsForChart.winterToSpring
      };
    }

    return {
      titleText: comparisonPeriod === "fall" ? "Fall To Winter" : "Winter To Spring",
      chartId: `growth_chart_winter_${comparisonPeriod}`,
      chartItems:
        comparisonPeriod === "fall" ? parsedResultsForChart.fallToWinter : parsedResultsForChart.winterToSpring
    };
  }
  const parsedResultsForChart = getParsedHSGrowthResultsForChart(growthResults);
  return {
    titleText: "Classwide Intervention Progress",
    chartId: `growth_All_${grade}`,
    chartItems: parsedResultsForChart
  };
};

export function GrowthChartWrapper(props) {
  if (isEmpty(props.growthChartResults)) {
    return null;
  }
  const { titleText, chartId, chartItems } = getGrowthChartMarkup(
    props.comparisonPeriod,
    props.growthChartResults,
    props.generateAllPeriods,
    props.grade
  );

  let subtitleText = "";
  if (props.grade !== "HS") {
    subtitleText = getGrowthGraphSubtitle(chartItems.scores);
  }
  return (
    <div className={`growth-chart-area${props.generateAllPeriods ? "" : " page-break-after"}`}>
      {!props.generateAllPeriods && !props.noChartTitleAndLegend ? (
        <h3 className="growth-chart-title">{titleText}</h3>
      ) : null}
      {props.grade !== "HS" ? (
        <GrowthChart
          chartId={chartId}
          chartItems={chartItems}
          grade={props.grade}
          shouldDisplayModifiedTitle={props.generateAllPeriods}
          generateAllPeriods={props.generateAllPeriods}
          subtitleText={subtitleText}
        />
      ) : (
        <HSGrowthChart
          chartId={props.chartId || chartId}
          chartItems={chartItems}
          grade={props.grade}
          shouldDisplayModifiedTitle={props.generateAllPeriods}
          noChartTitleAndLegend={props.noChartTitleAndLegend}
          subtitleText={subtitleText}
        />
      )}
    </div>
  );
}

GrowthChartWrapper.propTypes = {
  grade: PropTypes.string,
  growthChartResults: PropTypes.oneOfType([PropTypes.object, PropTypes.array]), // array for HS
  comparisonPeriod: PropTypes.string,
  generateAllPeriods: PropTypes.bool,
  chartId: PropTypes.string,
  noChartTitleAndLegend: PropTypes.bool
};
