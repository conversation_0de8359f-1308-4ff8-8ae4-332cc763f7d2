import React, { Component } from "react";
import PropTypes from "prop-types";
import * as helpers from "../../components/student-groups/helperFunction";

export class StudentsFlaggedForRemoval extends Component {
  render() {
    return this.props.shouldDisplay ? (
      <section className="studentListClassRoster">
        <div className="row header-row">
          <div className="col-12">
            <h2 className="no-bottom-border">
              <span>Flagged for removal</span>
            </h2>
          </div>
        </div>
        {this.props.studentsFlaggedForRemoval.map((s, i) => {
          const studentDisplayId = helpers.getStudentDispayId(s);
          return (
            <div key={i} className="row">
              <div className="col-12" data-student-grade={s.grade}>
                {helpers.getStudentName(s)}
                {studentDisplayId && <small className="student-display-id">{studentDisplayId}</small>}
                <div className="pull-right">
                  <button className="btn btn-default btn-xs" onClick={this.props.unflagStudentForRemoval(s._id)}>
                    Unflag
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </section>
    ) : null;
  }
}

StudentsFlaggedForRemoval.propTypes = {
  shouldDisplay: PropTypes.bool,
  studentsFlaggedForRemoval: PropTypes.array,
  unflagStudentForRemoval: PropTypes.func
};
