import React, { Component } from "react";
import PropTypes from "prop-types";
import IndividualInterventionStudents from "../../components/student-groups/individual-intervention-students";

export class IndividualInterventionSection extends Component {
  render() {
    return this.props.shouldDisplay ? (
      <section className="studentListIndividualInterventionList">
        <div className="row header-row">
          <div className="col-md-4 text-start">
            <h2 className="no-bottom-border">
              <i className="fa fa-user" />
              <span> Individual Intervention Students</span>
            </h2>
          </div>
          <div className="col-2 header">
            Current <br /> Intervention
          </div>
          <div className="col-2 header">
            Intervention <br /> Consistency
          </div>
          <div className="col-2 header">
            Average Weeks <br /> Per Skill
          </div>
          <div className="col-2 header">
            Most Recent <br /> Score Entry
          </div>
        </div>
        <IndividualInterventionStudents
          individualInterventionStudents={this.props.individualInterventionStudents}
          individualAssessmentResults={this.props.individualAssessmentResults}
        />
      </section>
    ) : null;
  }
}

IndividualInterventionSection.propTypes = {
  shouldDisplay: PropTypes.bool,
  individualInterventionStudents: PropTypes.array,
  individualAssessmentResults: PropTypes.array
};
