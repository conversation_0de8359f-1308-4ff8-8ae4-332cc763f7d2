import React from "react";
import { render, within, cleanup } from "@testing-library/react";
import "@testing-library/jest-dom";
import { MemoryRouter } from "react-router-dom";
import { PureManageUsers as ManageUsers } from "./manage-users.jsx";

describe("ManageUsers", () => {
  let manageUsersProps;
  beforeEach(() => {
    manageUsersProps = {
      loading: false,
      users: [],
      organizations: []
    };
  });
  afterEach(cleanup);
  it("should display a message when there are no users with a specific role", () => {
    const { getByText } = render(
      <MemoryRouter>
        <ManageUsers {...manageUsersProps} role="universalCoach" />
      </MemoryRouter>
    );
    expect(getByText("No users with this role")).toBeVisible();
  });

  it("should display add user buttons", () => {
    const { getByTestId } = render(
      <MemoryRouter>
        <ManageUsers {...manageUsersProps} role="superAdmin" />
      </MemoryRouter>
    );

    expect(getByTestId("addSupportUserButton")).toBeVisible();
    expect(getByTestId("addSuperAdminAccountButton")).toBeVisible();
    expect(getByTestId("addUniversalCoachButton")).toBeVisible();
    expect(getByTestId("addUniversalDataAdminButton")).toBeVisible();
    expect(getByTestId("addDownloaderButton")).toBeVisible();
  });

  it("should display correct buttons for selecting roles", () => {
    const result = render(
      <MemoryRouter>
        <ManageUsers {...manageUsersProps} role="superAdmin" />
      </MemoryRouter>
    );
    const { getByTestId } = within(result.container.querySelector("#user-role-selector"));
    expect(getByTestId("manage_support")).toBeVisible();
    expect(getByTestId("manage_superAdmin")).toBeVisible();
    expect(getByTestId("manage_universalCoach")).toBeVisible();
    expect(getByTestId("manage_universalDataAdmin")).toBeVisible();
    expect(getByTestId("manage_downloader")).toBeVisible();
  });

  it("should display a list of users with a specific role", () => {
    const users = [
      {
        _id: 1,
        profile: {
          name: { first: "Super", last: "Admin" },
          siteAccess: [{ role: "arbitraryIdsuperAdmin" }]
        },
        emails: [{ address: "<EMAIL>" }]
      }
    ];
    const { getAllByTestId, getByTestId } = render(
      <MemoryRouter>
        <ManageUsers {...manageUsersProps} role="superAdmin" users={users} />
      </MemoryRouter>
    );
    expect(getAllByTestId("user-row")).toHaveLength(1);
    const { getByText } = within(getByTestId("user-row"));
    expect(getByText("Super")).toBeVisible();
    expect(getByText("Admin")).toBeVisible();
    expect(getByText("<EMAIL>")).toBeVisible();
  });
});
