import React, { Component } from "react";
import { Meteor } from "meteor/meteor";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import Select from "react-select";

const selectAllOption = {
  label: "Select All",
  value: "selectAll"
};

export default class OrganizationAccessSelect extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedOrganizations: this.getSelectedOrganizationOptions(),
      availableOrganizations: this.generateOrganizationOptions(props.organizations)
    };
  }

  getSelectedOrganizationOptions = () => {
    const { user, organizations } = this.props;
    const selectedOrganizations = organizations.filter(org =>
      (user.profile.organizationAccess || []).includes(org._id)
    );
    return this.generateOrganizationOptions(selectedOrganizations);
  };

  generateOrganizationOptions = organizations =>
    organizations.map(org => ({
      label: org.name,
      value: org._id
    }));

  handleOrganizationsChange = organizations => {
    const isSelectAllOptionSelected =
      organizations.length && organizations[organizations.length - 1].value === selectAllOption.value;
    const selectedOrganizations = isSelectAllOptionSelected ? this.state.availableOrganizations : organizations;
    this.setState({ selectedOrganizations });
    const organizationAccess = selectedOrganizations.map(org => org.value);
    this.updateSupportUser(organizationAccess);
  };

  updateSupportUser = organizationAccess => {
    Meteor.call("users:updateSupportUser", this.props.user._id, { organizationAccess }, err => {
      if (err) {
        Alert.error("There was a problem while updating the support user", {
          timeout: 5000
        });
      } else {
        Alert.success("Support User updated successfully", {
          timeout: 5000
        });
      }
    });
  };

  render() {
    const { availableOrganizations } = this.state;

    return (
      <Select
        id="selectActiveOrganizations"
        value={this.state.selectedOrganizations}
        isMulti
        isSearchable={true}
        name={"organizationsActive"}
        options={[selectAllOption, ...availableOrganizations]}
        className="basic-multi-select"
        classNamePrefix="select"
        onChange={this.handleOrganizationsChange}
      />
    );
  }
}

OrganizationAccessSelect.propTypes = {
  user: PropTypes.object,
  organizations: PropTypes.array
};
