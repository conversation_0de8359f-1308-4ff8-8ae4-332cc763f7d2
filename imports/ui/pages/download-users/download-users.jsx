import { Meteor } from "meteor/meteor";
import { withTracker } from "meteor/react-meteor-data";
import React, { useCallback, useState } from "react";
import PropTypes from "prop-types";
import { sortBy, uniqBy } from "lodash";
import Alert from "react-s-alert";

import { Organizations } from "/imports/api/organizations/organizations";
import getUnitedStatesNames from "/imports/api/helpers/getUnitedStatesNames";
import Loading from "/imports/ui/components/loading";
import PageHeader from "/imports/ui/components/page-header";

import { OrganizationsFilter } from "./organizations-filter";
import { getCSV } from "../data-admin/upload/file-upload-utils";
import { download } from "../../utilities";

const availableStatuses = ["All", "Active", "Inactive"];
const availableUserRoles = ["Data Admins", "Coaches", "Teachers", "Support Users"];
const rosteringTypesByRostering = {
  rosterImport: "CSV File",
  rosterUpload: "CSV File",
  rosterEdFi: "Ed-Fi",
  rosterOR: "OneRoster"
};

export function DownloadUsers({ loading, states, organizations }) {
  const [selectedUserStatus, setSelectedUserStatus] = useState(availableStatuses[0]);
  const [selectedUserRoles, setSelectedUserRoles] = useState(availableUserRoles);
  const [isStudentCountGreater, setIsStudentCountGreater] = useState(true);
  const [selectedStudentCount, setSelectedStudentCount] = useState(10);
  const [orgIds, setOrgIds] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);

  const onSelectedOrganizations = useCallback(organizationIds => {
    setOrgIds(organizationIds);
  }, []);

  if (loading) {
    return <Loading />;
  }

  const toggleUserRole = (userRole, isChecked) => {
    setSelectedUserRoles(userRoles => {
      return isChecked ? [...userRoles, userRole] : userRoles.filter(ur => ur !== userRole);
    });
  };

  const downloadUsers = () => {
    setIsGenerating(true);
    const data = {
      orgIds,
      userStatus: selectedUserStatus,
      userRoles: selectedUserRoles,
      isStudentCountGreater,
      studentCount: selectedStudentCount
    };
    Meteor.call("fetchFilteredUsersForDownloader", data, (err, resp) => {
      setIsGenerating(false);
      if (err) {
        Alert.error(err.reason || err.error || "There was a problem while fetching users", {
          timeout: 5000
        });
      } else if (resp.length) {
        const hrefData = `data:application/octet-stream,${getCSV(resp)}`;
        download({
          filename: `users_${new Date().toISOString().slice(0, 10)}.csv`,
          hrefData
        });
      } else {
        Alert.info("No users found based on specified filters");
      }
    });
  };

  return (
    <div className="conFullScreen">
      <PageHeader title="Download Users" />
      <div className="container">
        <OrganizationsFilter
          states={states}
          organizations={organizations}
          onSelectedOrganizations={onSelectedOrganizations}
        />
        <div className="row">
          <div className="form-group col-6">
            <label>User Status</label>
            <select
              className="form-select form-select-sm"
              type="select"
              name="userStatus"
              onChange={e => setSelectedUserStatus(e.target.value)}
              defaultValue={selectedUserStatus}
            >
              {availableStatuses.map(status => (
                <option key={status}>{status}</option>
              ))}
            </select>
          </div>
          <div className="form-group col-6">
            <label>User Roles</label>
            <div>
              {availableUserRoles.map(userRole => (
                <label key={userRole}>
                  <input
                    type="checkbox"
                    checked={selectedUserRoles.includes(userRole)}
                    onChange={e => toggleUserRole(userRole, e.target.checked)}
                    className="me-2"
                  />
                  <small>{userRole}</small>
                </label>
              ))}
            </div>
          </div>
        </div>
        <div className="form-group">
          <label>Student Count</label>
          <div>
            <label>
              <input
                type="radio"
                className="me-2"
                name="isStudentCountGreater"
                checked={isStudentCountGreater}
                onChange={e => setIsStudentCountGreater(e.target.checked)}
              />
              <small>Greater Than</small>
            </label>
            <label>
              <input
                type="radio"
                className="me-2"
                name="isStudentCountGreater"
                checked={!isStudentCountGreater}
                onChange={e => setIsStudentCountGreater(!e.target.checked)}
              />
              <small>Less Than</small>
            </label>
            <div className="row">
              <div className="col-6">
                <input
                  className="form-control"
                  type="number"
                  min="0"
                  value={selectedStudentCount}
                  onChange={e => setSelectedStudentCount(parseInt(e.target.value || 0))}
                />
              </div>
            </div>
          </div>
        </div>
        <div className="d-grid">
          <button className="btn btn-success" onClick={downloadUsers} disabled={!orgIds.length || isGenerating}>
            {isGenerating ? <Loading message="Generating..." inline /> : "Download"}
          </button>
        </div>
      </div>
    </div>
  );
}

DownloadUsers.propTypes = {
  loading: PropTypes.bool,
  states: PropTypes.array,
  organizations: PropTypes.array
};

export default withTracker(() => {
  const orgSub = Meteor.subscribe("Organizations");
  const loading = !orgSub.ready();

  const unitedStatesNames = getUnitedStatesNames(); // { "NY": "New York", ... }

  let states = [];
  let organizations = [];

  if (!loading) {
    organizations = Organizations.find(
      {},
      { fields: { name: 1, "details.state": 1, isActive: 1, rostering: 1, ssoIssuerOrgId: 1, isMFARequired: 1 } }
    )
      .fetch()
      .map(org => {
        return {
          _id: org._id,
          name: org.name,
          state: unitedStatesNames[org.details.state] || org.details.state,
          isActive: org.isActive,
          rosteringType: rosteringTypesByRostering[org.rostering],
          hasSSO: org.ssoIssuerOrgId?.length > 0,
          hasMFA: !!org.isMFARequired
        };
      });
    states = uniqBy(sortBy(organizations, "state"), "state").map(org => ({ _id: org.state, name: org.state }));
  }

  return { loading, states, organizations };
})(DownloadUsers);
