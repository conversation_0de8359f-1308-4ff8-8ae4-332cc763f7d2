import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import { intersection } from "lodash";
import DualListBox from "react-dual-listbox";

import { mapToOptions, onOptionChange } from "../data-admin/roster-filtering/utilities";

const availableStatuses = ["All", "Active", "Inactive"];
const availableRosteringTypes = ["CSV File", "Ed-Fi", "OneRoster"];
const isDistrictActiveByDistrictStatus = {
  Active: true,
  Inactive: false
};

export function OrganizationsFilterComponent({ organizations = [], states = [], onSelectedOrganizations = () => {} }) {
  const [selectedStates, setSelectedStates] = useState([]);
  const [selectedDistrictStatus, setSelectedDistrictStatus] = useState(availableStatuses[0]);
  const [selectedOrganizations, setSelectedOrganizations] = useState([]);
  const [selectedRosteringTypes, setSelectedRosteringTypes] = useState(availableRosteringTypes);
  const [includeSSO, setIncludeSSO] = useState(true);
  const [includeNonSSO, setIncludeNonSSO] = useState(true);
  const [includeMFA, setIncludeMFA] = useState(true);
  const [includeNonMFA, setIncludeNonMFA] = useState(true);

  console.table(organizations);
  const filteredOrganizations = organizations.filter(org => {
    return (
      selectedStates.includes(org.state) &&
      (!selectedRosteringTypes.length || selectedRosteringTypes.includes(org.rosteringType)) &&
      (selectedDistrictStatus === "All" || org.isActive === isDistrictActiveByDistrictStatus[selectedDistrictStatus]) &&
      (org.hasSSO === includeSSO || org.hasSSO !== includeNonSSO) &&
      (org.hasMFA === includeMFA || org.hasMFA !== includeNonMFA)
    );
  });

  useEffect(() => {
    const orgIds = intersection(
      filteredOrganizations.map(org => org._id),
      selectedOrganizations
    );

    onSelectedOrganizations(orgIds);
  }, [filteredOrganizations, selectedOrganizations]);

  const toggleRosteringType = (rosteringType, isChecked) => {
    setSelectedRosteringTypes(rosteringTypes => {
      return isChecked ? [...rosteringTypes, rosteringType] : rosteringTypes.filter(rt => rt !== rosteringType);
    });
  };

  return (
    <>
      <div className="form-group">
        <label>States</label>
        <DualListBox
          options={mapToOptions(states)}
          selected={selectedStates}
          onChange={onOptionChange(setSelectedStates)}
          canFilter
          className="select-with-ellipsis"
        />
      </div>
      <div className="row">
        <div className="form-group col-6">
          <label>District Status</label>
          <select
            className="form-select form-select-sm"
            type="select"
            name="districtStatus"
            onChange={e => setSelectedDistrictStatus(e.target.value)}
            defaultValue={selectedDistrictStatus}
          >
            {availableStatuses.map(status => (
              <option key={status}>{status}</option>
            ))}
          </select>
        </div>
        <div className="form-group col-3">
          <label>Rostering Types</label>
          <div>
            {availableRosteringTypes.map(rosteringType => (
              <label key={rosteringType}>
                <input
                  type="checkbox"
                  checked={selectedRosteringTypes.includes(rosteringType)}
                  onChange={e => toggleRosteringType(rosteringType, e.target.checked)}
                  className="me-2"
                />
                <small>{rosteringType}</small>
              </label>
            ))}
          </div>
        </div>
        <div className="form-group col-3">
          <label>District Sign in</label>
          <div>
            <label>
              <input
                type="checkbox"
                checked={includeSSO}
                onChange={e => setIncludeSSO(e.target.checked)}
                className="me-2"
              />
              <small>Single Sign On (SSO)</small>
            </label>
            <label>
              <input
                type="checkbox"
                checked={includeNonSSO}
                onChange={e => setIncludeNonSSO(e.target.checked)}
                className="me-2"
              />
              <small>Non-SSO</small>
            </label>
            <label>
              <input
                type="checkbox"
                checked={includeMFA}
                onChange={e => setIncludeMFA(e.target.checked)}
                className="me-2"
              />
              <small>MFA</small>
            </label>
            <label>
              <input
                type="checkbox"
                checked={includeNonMFA}
                onChange={e => setIncludeNonMFA(e.target.checked)}
                className="me-2"
              />
              <small>Non-MFA</small>
            </label>
          </div>
        </div>
      </div>
      <div className="form-group">
        <label>District Name</label>
        <DualListBox
          options={mapToOptions(filteredOrganizations)}
          selected={selectedOrganizations}
          onChange={onOptionChange(setSelectedOrganizations)}
          canFilter
          className="select-with-ellipsis"
        />
      </div>
    </>
  );
}

OrganizationsFilterComponent.propTypes = {
  states: PropTypes.array,
  organizations: PropTypes.array,
  onSelectedOrganizations: PropTypes.func
};

export const OrganizationsFilter = React.memo(OrganizationsFilterComponent);
