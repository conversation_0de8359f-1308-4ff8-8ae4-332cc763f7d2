import React, { Component } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { withTracker } from "meteor/react-meteor-data";
import { groupBy, isEmpty } from "lodash";

import GetAssessmentPrintout from "./getAssessmentPrintout";
import { ScreeningAssignments } from "/imports/api/screeningAssignments/screeningAssignments";
import { Assessments } from "/imports/api/assessments/assessments";
import { BenchmarkPeriods } from "/imports/api/benchmarkPeriods/benchmarkPeriods";

class SampleAssessments extends Component {
  render() {
    return (
      <div className="main-content">
        <h2 className="m-10">Grade {this.props.grade}</h2>
        <div className="list-group p-3 p-t-0">
          {Object.keys(this.props.assessmentsByBenchmark).map((benchmarkName, benchmarkIndex) => {
            if (isEmpty(this.props.assessmentsByBenchmark[benchmarkName])) {
              return null;
            }
            return (
              <div className="m-t-30" key={benchmarkIndex} data-testid="assessment-screening-container">
                <h3 className="w9">{benchmarkName} Screening</h3>
                {this.props.assessmentsByBenchmark[benchmarkName].map((assessment, index) => (
                  <GetAssessmentPrintout
                    key={assessment._id}
                    assessment={assessment}
                    grade={this.props.grade}
                    ordinal={index + 1}
                  />
                ))}
              </div>
            );
          })}
        </div>
      </div>
    );
  }
}

SampleAssessments.propTypes = {
  grade: PropTypes.string,
  assessmentsByBenchmark: PropTypes.object
};

/** ****************************************************************
 // Data Container
 ***************************************************************** */
export default withTracker(({ grade }) => {
  const screeningAssignmentsSub = Meteor.subscribe("ScreeningAssignmentsByGrade", grade);
  const aHandle = Meteor.subscribe("Assessments");
  const benchmarkSub = Meteor.subscribe("BenchmarkPeriods");

  const loading = !screeningAssignmentsSub.ready() && !aHandle.ready() && !benchmarkSub.ready();

  const assessmentsWithScreeningInfo = [];
  if (!loading) {
    const screeningAssignmentsForCurrentGrade = ScreeningAssignments.find(
      { grade },
      { fields: { assessmentIds: 1, benchmarkPeriodId: 1 } }
    ).fetch();
    const benchmarkPeriods = {};
    BenchmarkPeriods.find({}, { fields: { _id: 1, name: 1 } }).forEach(benchmarkPeriod => {
      benchmarkPeriods[benchmarkPeriod._id] = benchmarkPeriod.name;
    });

    const assessmentScreeningAssignment = [];
    let assessmentsInCurrentGrade = [];
    screeningAssignmentsForCurrentGrade.forEach(screening => {
      assessmentsInCurrentGrade = [...assessmentsInCurrentGrade, ...screening.assessmentIds];
      screening.assessmentIds.forEach(assessment => {
        const name = benchmarkPeriods[screening.benchmarkPeriodId];
        const assessmentScreeningAssignmentData = {
          _id: assessment,
          name,
          benchmarkPeriodId: screening.benchmarkPeriodId
        };
        assessmentScreeningAssignment.push(assessmentScreeningAssignmentData);
      });
    });

    const assessments = Assessments.find(
      {
        _id: { $in: assessmentsInCurrentGrade }
      },
      { fields: { _id: 1, name: 1, monitorAssessmentMeasure: 1 } }
    ).fetch();
    Object.values(assessmentScreeningAssignment).forEach(screeningAssignmentData => {
      const currentAssessment = assessments.find(assessment => assessment._id === screeningAssignmentData._id);
      if (currentAssessment) {
        const printOutAssessment = {
          _id: currentAssessment._id,
          name: currentAssessment.name,
          benchmarkName: screeningAssignmentData.name,
          benchmarkPeriodId: screeningAssignmentData.benchmarkPeriodId,
          monitorAssessmentMeasure: currentAssessment.monitorAssessmentMeasure
        };
        assessmentsWithScreeningInfo.push(printOutAssessment);
      }
    });
  }

  const { Fall, Winter, Spring, All } = groupBy(assessmentsWithScreeningInfo, "benchmarkName");
  const assessmentsByBenchmark = { Fall, Winter, Spring, All };

  return {
    loading,
    assessmentsByBenchmark,
    grade
  };
})(SampleAssessments);
