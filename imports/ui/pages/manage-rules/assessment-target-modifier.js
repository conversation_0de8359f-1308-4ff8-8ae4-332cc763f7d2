import React, { Component } from "react";
import PropTypes from "prop-types";
import get from "lodash/get";
import cloneDeep from "lodash/cloneDeep";
import set from "lodash/set";
import { Assessments } from "/imports/api/assessments/assessments";
import { indexToOutcomeName } from "./TreeDisplayData";
import { AssessmentTargetsTable } from "./assessment-targets-table";
import Loading from "../../components/loading";
import { RuleOutcomesTable } from "./rule-outcomes-table";

const possibleTargets = ["default", "benchmark", "individual"];

export class AssessmentTargetModifier extends Component {
  /* eslint-disable class-methods-use-this */

  state = {
    gradeAssessments: [],
    isAddingTargets: false,
    interventionOutcomes: {
      below: null,
      at: null,
      above: null
    },
    ruleOutcomes: {
      below: null,
      at: null,
      above: null
    }
  };

  componentDidMount() {
    this.setState({
      gradeAssessments: Assessments.find({
        associatedGrades: this.props.grade,
        monitorAssessmentMeasure: { $exists: true }
      }).fetch()
    });
  }

  componentDidUpdate(prevProps, prevState) {
    const { selectedAssessment, selectedAssessmentDocument, selectedRuleDocument } = this.props;
    if (
      this.wereAssessmentsFetched(prevState) ||
      this.wasAssessmentChanged(selectedAssessment, prevProps) ||
      this.wereTargetsModified(selectedAssessmentDocument, prevProps) ||
      this.wereOutcomesModified(selectedRuleDocument, prevProps)
    ) {
      if (selectedAssessment.children && selectedAssessment.children.length) {
        const stateToSet = {};
        const { currentInterventions, ruleOutcomes } = this.getCurrentRulesAndOutcomes(
          selectedAssessment,
          selectedRuleDocument
        );
        stateToSet.interventionOutcomes = currentInterventions;
        stateToSet.ruleOutcomes = ruleOutcomes;
        this.setState(stateToSet);
      }
    }
  }

  getCurrentRulesAndOutcomes(selectedAssessment, selectedRuleDocument) {
    const currentInterventions = {};
    const ruleOutcomes = {};
    selectedAssessment.children.forEach((child, index) => {
      const ruleOutcomeData = selectedRuleDocument.outcomes[indexToOutcomeName[index]];
      let interventionsToSet = child.isEnding ? [] : null;
      if (this.shouldSetInterventions(child, ruleOutcomeData)) {
        interventionsToSet = this.getInterventionToDisplay(ruleOutcomeData);
      }
      currentInterventions[child.outcome] = interventionsToSet;
      ruleOutcomes[child.outcome] = this.getRuleToDisplay(ruleOutcomeData);
    });
    return { currentInterventions, ruleOutcomes };
  }

  getRuleToDisplay(ruleOutcomeData) {
    return ruleOutcomeData && ruleOutcomeData.assessmentId
      ? {
          assessmentId: ruleOutcomeData.assessmentId,
          label: this.state.gradeAssessments.find(ass => ass._id === ruleOutcomeData.assessmentId).name
        }
      : null;
  }

  getInterventionToDisplay(ruleOutcomeData) {
    return ruleOutcomeData.interventionIds.map(intId => {
      const intervention = this.props.interventions.find(int => int._id === intId);
      return { interventionId: intervention._id, label: intervention.name };
    });
  }

  shouldSetInterventions(child, ruleOutcomeData) {
    return (
      child.isEnding && ruleOutcomeData && ruleOutcomeData.interventionIds && ruleOutcomeData.interventionIds.length
    );
  }

  wereAssessmentsFetched(prevState) {
    return prevState.gradeAssessments.length !== this.state.gradeAssessments.length;
  }

  wereOutcomesModified(selectedRuleDocument, prevProps) {
    return (
      JSON.stringify(get(prevProps, "selectedRuleDocument.outcomes", {})) !==
      JSON.stringify(get(selectedRuleDocument, "outcomes", {}))
    );
  }

  wereTargetsModified(selectedAssessmentDocument, prevProps) {
    const queryStringForTargets = "strands[0].scores[0].targets";
    return (
      selectedAssessmentDocument &&
      get(prevProps.selectedAssessmentDocument, queryStringForTargets, []).length !==
        get(this.props.selectedAssessmentDocument, queryStringForTargets, []).length
    );
  }

  wasAssessmentChanged(selectedAssessment, prevProps) {
    return (
      selectedAssessment && selectedAssessment.assessmentId !== get(prevProps, "selectedAssessment.assessmentId", "")
    );
  }

  handleRuleChange = outcomeName => e => {
    const { value, label } = e;
    const updateString = `ruleOutcomes.${outcomeName}`;
    const clonedState = cloneDeep(this.state);
    set(clonedState, updateString, { label, assessmentId: value });
    this.setState(clonedState);
  };

  handleInterventionsChange = outcomeName => e => {
    const updateString = `interventionOutcomes.${outcomeName}`;
    const clonedState = cloneDeep(this.state);
    const selectedInterventions = e.map(intervention => ({
      interventionId: intervention.value,
      label: intervention.label
    }));
    set(clonedState, updateString, selectedInterventions);
    this.setState(clonedState);
  };

  setAddingTargets = (value = true) => {
    this.setState({ isAddingTargets: value });
  };

  addCustomTarget = e => {
    e.preventDefault();
    this.setAddingTargets();
    this.props.addTargetToAssessment(
      {
        assessmentId: this.props.selectedAssessment.assessmentId,
        targetName: e.target.name,
        grade: this.props.grade
      },
      () => {
        this.setAddingTargets(false);
      }
    );
  };

  getMissingTargets() {
    return this.props.selectedAssessmentDocument.strands[0].scores[0].targets
      .filter(target => target.grade === this.props.grade && target.assessmentType !== "classwide")
      .reduce(
        (acc, cv) => {
          if (!cv.assessmentType) {
            acc.splice(acc.indexOf("default"), 1);
          } else {
            acc.splice(acc.indexOf(cv.assessmentType), 1);
          }
          return acc;
        },
        [...possibleTargets]
      );
  }

  updateOutcomes = e => {
    e.preventDefault();
    this.props.updateRuleOutcomes(this.state.ruleOutcomes);
  };

  updateInterventions = e => {
    e.preventDefault();
    this.props.updateInterventionOutcomes(this.state.interventionOutcomes);
  };

  render() {
    if (this.props.isUpdatingRules) {
      return <Loading />;
    }
    const availableInterventions = this.props.interventions.map(int => ({
      label: int.name,
      value: int._id
    }));
    const availableAssessments = this.state.gradeAssessments
      .sort((a, b) => a.monitorAssessmentMeasure - b.monitorAssessmentMeasure)
      .map(ass => ({
        label: `AM ${ass.monitorAssessmentMeasure} - ${ass.name}`,
        value: ass._id
      }));
    const allGradeTargetsForAssessment = (
      get(this.props.selectedAssessmentDocument, "strands[0].scores[0].targets") || []
    ).filter(target => target.grade === this.props.grade && target.assessmentType !== "classwide");
    const shouldDisplayRulesTable = this.props.selectedAssessment.children && this.state.gradeAssessments.length;
    const benchmarkPeriodName = this.props.benchmarkPeriods.find(bp => bp._id === this.props.benchmarkPeriodId)?.name;
    return (
      <div>
        <hr />
        {shouldDisplayRulesTable && (
          <div>
            <h4 className="text-center">
              Outcomes for rule of <b>{this.props.selectedAssessment.fullName}</b> in &nbsp;
              <b>{benchmarkPeriodName}</b>
              &nbsp;for grade: <b>{this.props.grade}</b>:
            </h4>
            <RuleOutcomesTable
              selectedAssessment={this.props.selectedAssessment}
              updateOutcomes={this.updateOutcomes}
              updateInterventions={this.updateInterventions}
              availableInterventions={availableInterventions}
              availableAssessments={availableAssessments}
              ruleOutcomes={this.state.ruleOutcomes}
              interventionOutcomes={this.state.interventionOutcomes}
              handleInterventionsChange={this.handleInterventionsChange}
              handleRuleChange={this.handleRuleChange}
              addInterventionsToOutcome={this.props.addInterventionsToOutcome}
              addOutcomeToRule={this.props.addOutcomeToRule}
              removeRuleOutcome={this.props.removeRuleOutcome}
              isPreviewOnly={this.props.isPreviewOnly}
            />
            <div>
              <small>
                * The interventions should only be added if the outcome rule is already a part of decision tree for the
                chosen root rule
              </small>
            </div>
          </div>
        )}
        {this.props.selectedAssessment.assessmentId && this.props.selectedAssessmentDocument._id && (
          <div>
            <AssessmentTargetsTable
              selectedAssessment={this.props.selectedAssessment}
              grade={this.props.grade}
              allTargets={allGradeTargetsForAssessment}
              isAddingTargets={this.state.isAddingTargets}
              missingTargets={this.getMissingTargets()}
              addCustomTarget={this.addCustomTarget}
              updateAssessmentTargets={this.props.updateAssessmentTargets}
              isModifyingTargets={this.props.isModifyingTargets}
              removeTargetFromAssessment={this.props.removeTargetFromAssessment}
              isPreviewOnly={this.props.isPreviewOnly}
            />
          </div>
        )}
      </div>
    );
  }
}

AssessmentTargetModifier.propTypes = {
  selectedAssessment: PropTypes.object,
  selectedAssessmentDocument: PropTypes.object,
  selectedRuleDocument: PropTypes.object,
  grade: PropTypes.string,
  benchmarkPeriodId: PropTypes.string,
  benchmarkPeriods: PropTypes.array,
  interventions: PropTypes.array,
  addTargetToAssessment: PropTypes.func,
  updateAssessmentTargets: PropTypes.func,
  updateRuleOutcomes: PropTypes.func,
  updateInterventionOutcomes: PropTypes.func,
  addInterventionsToOutcome: PropTypes.func,
  addOutcomeToRule: PropTypes.func,
  removeTargetFromAssessment: PropTypes.func,
  removeRuleOutcome: PropTypes.func,
  isModifyingTargets: PropTypes.bool,
  isUpdatingRules: PropTypes.bool,
  isPreviewOnly: PropTypes.bool
};
