/* eslint-disable */
import React, { useCallback } from "react";
import Select, { components } from "react-select";
import { DndContext } from "@dnd-kit/core";
import { restrictToParentElement } from "@dnd-kit/modifiers";
import { arrayMove, rectSortingStrategy, SortableContext, useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

const MultiValue = props => {
  const onMouseDown = e => {
    e.preventDefault();
    e.stopPropagation();
  };
  const innerProps = { ...props.innerProps, onMouseDown };
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({
    id: props.data.value
  });
  const style = {
    transform: CSS.Translate.toString(transform),
    transition
  };

  return (
    <div style={style} ref={setNodeRef} {...attributes} {...listeners}>
      <components.MultiValue {...props} innerProps={innerProps} />
    </div>
  );
};

const MultiValueRemove = props => {
  return (
    <components.MultiValueRemove
      {...props}
      innerProps={{
        onPointerDown: e => e.stopPropagation(),
        ...props.innerProps
      }}
    />
  );
};

const DraggableSelect = props => {
  const { setSelected, selected, options, onChange } = props;
  const onDragEnd = useCallback(
    event => {
      const { active, over } = event;

      if (!active || !over) {
        return;
      }

      setSelected(items => {
        const oldIndex = items.findIndex(item => item.value === active.id);
        const newIndex = items.findIndex(item => item.value === over.id);
        return arrayMove(items, oldIndex, newIndex);
      });
    },
    [setSelected]
  );

  return (
    <DndContext modifiers={[restrictToParentElement]} onDragEnd={onDragEnd}>
      <SortableContext items={selected?.map(o => o.value) || []} strategy={rectSortingStrategy}>
        <Select
          isSearchable={true}
          isMulti={true}
          options={options}
          value={selected}
          styles={{
            valueContainer: base => ({ ...base, padding: 2 }),
            clearIndicator: base => ({ ...base, padding: "0 4px" }),
            dropdownIndicator: base => ({ ...base, padding: "0 4px" }),
            control: base => ({ ...base, minHeight: 0 }),
            input: base => ({ ...base, maxHeight: 30, fontSize: "0.8rem" }),
            multiValue: base => ({ ...base, padding: 2, margin: 2, fontSize: "0.95rem" }),
            multiValueLabel: base => ({ ...base, padding: 0 })
          }}
          onChange={onChange}
          components={{
            MultiValue,
            MultiValueRemove
          }}
        />
      </SortableContext>
    </DndContext>
  );
};

export default DraggableSelect;
