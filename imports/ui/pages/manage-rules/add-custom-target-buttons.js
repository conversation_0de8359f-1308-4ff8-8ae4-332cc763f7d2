import React, { Component } from "react";
import { But<PERSON> } from "react-bootstrap";
import PropTypes from "prop-types";
import Loading from "../../components/loading";
import { capitalizeFirstLetter } from "/imports/api/utilities/utilities";

export class AddCustomTargetButtons extends Component {
  render() {
    return (
      <div className="pull-left">
        {this.props.isAddingTargets ? (
          <Loading inline={true} />
        ) : (
          this.props.missingTargets.map((targetName, index) => (
            <React.Fragment key={`${targetName}_fragment_${index}`}>
              <Button className="btn btn-success" name={targetName} onClick={this.props.addCustomTarget}>
                Add Custom {capitalizeFirstLetter(targetName)} Targets
              </Button>
              &nbsp;
            </React.Fragment>
          ))
        )}
      </div>
    );
  }
}

AddCustomTargetButtons.propTypes = {
  isAddingTargets: PropTypes.bool,
  missingTargets: PropTypes.array,
  addCustomTarget: PropTypes.func
};
