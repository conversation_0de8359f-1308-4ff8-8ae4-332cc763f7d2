const winterBenchmarkPeriodId = "nEsbWokBWutTZFkTh";
const fallBenchmarkPeriodId = "8S52Gz5o85hRkECgq";
const springBenchmarkPeriodId = "cjCMnZKARBJmG8suT";
const allBenchmarkPeriodId = "allPeriods";
const interventionIds = ["interventionId0", "interventionId1", "interventionId2", "interventionId3"];
const assessmentIds = ["assessmentId0", "assessmentId1", "assessmentId2"];
const ruleIds = ["rule0", "rule1", "rule2", "rule3"];
const componentData = {
  screeningAssignments: [
    {
      _id: "29aTTozPyZdNugTui",
      assessmentIds,
      benchmarkPeriodId: fallBenchmarkPeriodId,
      grade: "07"
    },
    {
      _id: "6iQ9CApWaEqZoSytc",
      assessmentIds: [assessmentIds[0], assessmentIds[1]],
      benchmarkPeriodId: winterBenchmarkPeriodId,
      grade: "06"
    },
    {
      _id: "7952qRvxAdqdLGjcf",
      assessmentIds,
      benchmarkPeriodId: springBenchmarkPeriodId,
      grade: "07"
    },
    {
      _id: "BC4jbBgBtjYbiGPvm",
      assessmentIds: [assessmentIds[0], assessmentIds[1]],
      benchmarkPeriodId: winterBenchmarkPeriodId,
      grade: "07"
    },
    {
      _id: "BC4jbBgBtjYbiGPvc",
      assessmentIds: [assessmentIds[2]],
      benchmarkPeriodId: fallBenchmarkPeriodId,
      grade: "06"
    }
  ],
  assessments: [
    {
      _id: assessmentIds[0],
      associatedGrades: ["06", "07", "08"],
      monitorAssessmentMeasure: "21",
      name: "Mixed Operations with Integers of Varying Sign - Paper Pencil",
      strands: [
        {
          name: "Overall Scores",
          scores: [
            {
              name: "Number Correct",
              targets: [
                {
                  grade: "06",
                  periods: [
                    {
                      name: "Fall",
                      benchmarkPeriodId: fallBenchmarkPeriodId,
                      values: [11, 23, 100]
                    },
                    {
                      name: "Spring",
                      benchmarkPeriodId: springBenchmarkPeriodId,
                      values: [11, 23, 100]
                    },
                    {
                      name: "Winter",
                      benchmarkPeriodId: winterBenchmarkPeriodId,
                      values: [11, 23, 100]
                    }
                  ]
                },
                {
                  grade: "07",
                  periods: [
                    {
                      name: "Fall",
                      benchmarkPeriodId: fallBenchmarkPeriodId,
                      values: [14, 29, 100]
                    },
                    {
                      name: "Spring",
                      benchmarkPeriodId: springBenchmarkPeriodId,
                      values: [14, 29, 100]
                    },
                    {
                      name: "Winter",
                      benchmarkPeriodId: winterBenchmarkPeriodId,
                      values: [14, 29, 100]
                    }
                  ]
                },
                {
                  grade: "08",
                  periods: [
                    {
                      name: "Fall",
                      benchmarkPeriodId: fallBenchmarkPeriodId,
                      values: [14, 29, 100]
                    },
                    {
                      name: "Spring",
                      benchmarkPeriodId: springBenchmarkPeriodId,
                      values: [14, 29, 100]
                    },
                    {
                      name: "Winter",
                      benchmarkPeriodId: winterBenchmarkPeriodId,
                      values: [14, 29, 100]
                    }
                  ]
                }
              ],
              externalId: ""
            }
          ]
        }
      ]
    },
    {
      _id: assessmentIds[1],
      associatedGrades: ["06", "07", "08"],
      monitorAssessmentMeasure: "81",
      name: "Add 3-Digit w/o Regrouping",
      strands: [
        {
          name: "Overall",
          scores: [
            {
              name: "Number Correct",
              externalId: "number_correct",
              targets: [
                {
                  grade: "06",
                  periods: [
                    {
                      name: "Fall",
                      benchmarkPeriodId: fallBenchmarkPeriodId,
                      values: [6, 11, 100]
                    },
                    {
                      name: "Spring",
                      benchmarkPeriodId: springBenchmarkPeriodId,
                      values: [6, 11, 100]
                    },
                    {
                      name: "Winter",
                      benchmarkPeriodId: winterBenchmarkPeriodId,
                      values: [6, 11, 100]
                    }
                  ]
                },
                {
                  grade: "07",
                  periods: [
                    {
                      name: "Fall",
                      benchmarkPeriodId: fallBenchmarkPeriodId,
                      values: [6, 11, 100]
                    },
                    {
                      name: "Spring",
                      benchmarkPeriodId: springBenchmarkPeriodId,
                      values: [6, 11, 100]
                    },
                    {
                      name: "Winter",
                      benchmarkPeriodId: winterBenchmarkPeriodId,
                      values: [6, 11, 100]
                    }
                  ]
                },
                {
                  grade: "08",
                  periods: [
                    {
                      name: "Fall",
                      benchmarkPeriodId: fallBenchmarkPeriodId,
                      values: [14, 29, 100]
                    },
                    {
                      name: "Spring",
                      benchmarkPeriodId: springBenchmarkPeriodId,
                      values: [14, 29, 100]
                    },
                    {
                      name: "Winter",
                      benchmarkPeriodId: winterBenchmarkPeriodId,
                      values: [14, 29, 100]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    },
    {
      _id: assessmentIds[2],
      associatedGrades: ["06", "07", "08"],
      monitorAssessmentMeasure: "63",
      name: "Find the Least Common Denominator",
      strands: [
        {
          name: "Overall",
          scores: [
            {
              name: "Number Correct",
              externalId: "number_correct",
              targets: [
                {
                  grade: "06",
                  periods: [
                    {
                      name: "Fall",
                      benchmarkPeriodId: fallBenchmarkPeriodId,
                      values: [6, 12, 300]
                    },
                    {
                      name: "Spring",
                      benchmarkPeriodId: springBenchmarkPeriodId,
                      values: [6, 12, 300]
                    },
                    {
                      name: "Winter",
                      benchmarkPeriodId: winterBenchmarkPeriodId,
                      values: [6, 12, 300]
                    }
                  ]
                },
                {
                  grade: "07",
                  periods: [
                    {
                      name: "Fall",
                      benchmarkPeriodId: fallBenchmarkPeriodId,
                      values: [8, 15, 300]
                    },
                    {
                      name: "Spring",
                      benchmarkPeriodId: springBenchmarkPeriodId,
                      values: [8, 15, 300]
                    },
                    {
                      name: "Winter",
                      benchmarkPeriodId: winterBenchmarkPeriodId,
                      values: [8, 15, 300]
                    }
                  ]
                },
                {
                  grade: "08",
                  periods: [
                    {
                      name: "Fall",
                      benchmarkPeriodId: fallBenchmarkPeriodId,
                      values: [8, 15, 300]
                    },
                    {
                      name: "Spring",
                      benchmarkPeriodId: springBenchmarkPeriodId,
                      values: [8, 15, 300]
                    },
                    {
                      name: "Winter",
                      benchmarkPeriodId: winterBenchmarkPeriodId,
                      values: [8, 15, 300]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  ],
  rules: [
    {
      _id: ruleIds[0],
      name: ruleIds[0],
      enabled: true,
      attributeValues: {
        grade: "07",
        benchmarkPeriod: "winter-period",
        assessmentId: assessmentIds[0]
      },
      outcomes: {
        above: null,
        at: {
          assessmentId: assessmentIds[0],
          interventionIds: [interventionIds[3]]
        },
        below: {
          assessmentId: assessmentIds[1],
          interventionIds: []
        }
      },
      rootRuleId: ruleIds[0]
    },
    {
      _id: ruleIds[1],
      name: ruleIds[1],
      enabled: true,
      attributeValues: {
        grade: "07",
        benchmarkPeriod: "winter-period",
        assessmentId: assessmentIds[1]
      },
      outcomes: {
        above: {
          assessmentId: assessmentIds[0],
          interventionIds: [interventionIds[1], interventionIds[2]]
        },
        at: {
          assessmentId: assessmentIds[1],
          interventionIds: [interventionIds[0], interventionIds[3]]
        },
        below: {
          assessmentId: assessmentIds[1],
          interventionIds: []
        }
      },
      rootRuleId: ruleIds[0]
    },
    {
      _id: ruleIds[2],
      name: "rule3",
      enabled: true,
      attributeValues: {
        grade: "08",
        benchmarkPeriod: "fall-period",
        assessmentId: assessmentIds[2]
      },
      outcomes: {
        above: null,
        at: {
          assessmentId: assessmentIds[2],
          interventionIds: [interventionIds[0], interventionIds[3]]
        },
        below: {
          assessmentId: assessmentIds[2],
          interventionIds: null
        }
      },
      rootRuleId: ruleIds[2]
    }
  ],
  grades: [
    {
      _id: "06",
      display: "06",
      sortorder: 10
    },
    {
      _id: "07",
      display: "07",
      sortorder: 11
    },
    {
      _id: "08",
      display: "08",
      sortorder: 12
    }
  ],
  benchmarkPeriods: [
    {
      label: "fall-period",
      name: "Fall",
      _id: fallBenchmarkPeriodId
    },
    {
      label: "winter-period",
      name: "Winter",
      _id: winterBenchmarkPeriodId
    },
    {
      label: "spring-period",
      name: "Spring",
      _id: springBenchmarkPeriodId
    },
    {
      label: "all-periods",
      name: "All",
      _id: allBenchmarkPeriodId
    }
  ],
  interventions: [
    {
      _id: interventionIds[0],
      abbreviation: "IR",
      name: "Intervention Adviser - Incremental Rehearsal Multiplication 0-12"
    },
    {
      _id: interventionIds[1],
      abbreviation: "CCC",
      name: "Intervention Adviser - Cover Copy and Compare"
    },
    {
      _id: interventionIds[2],
      abbreviation: "GP",
      name: "Intervention Adviser - Guided Practice"
    },
    {
      _id: interventionIds[3],
      abbreviation: "TT",
      name: "Intervention Adviser - Timed Trial"
    }
  ],
  loading: false
};

export default componentData;
