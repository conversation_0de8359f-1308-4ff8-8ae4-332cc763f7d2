import React, { Component } from "react";
import { But<PERSON> } from "react-bootstrap";
import PropTypes from "prop-types";
import get from "lodash/get";
import set from "lodash/set";
import cloneDeep from "lodash/cloneDeep";
import { capitalizeFirstLetter } from "/imports/api/utilities/utilities";
import Loading from "../../components/loading";
import { AssessmentTargetsTableRow } from "./assessment-targets-table-row";
import { AddCustomTargetButtons } from "./add-custom-target-buttons";

export class AssessmentTargetsTable extends Component {
  constructor(props) {
    super(props);
    const stateToSet = this.getTargets();
    this.state = {
      assessmentId: props.selectedAssessment.assessmentId,
      targets: {
        ...stateToSet
      },
      isHidden: false
    };
  }

  componentDidUpdate(prevProps) {
    if (this.wasAssessmentChanged() || this.wereTargetsModified(prevProps)) {
      const stateToSet = this.getTargets();
      this.setState(() => ({
        assessmentId: this.props.selectedAssessment.assessmentId,
        targets: {
          ...stateToSet
        },
        isHidden: false
      }));
    }
  }

  getTargets = () => {
    const targetsToSet = {};
    this.props.allTargets.forEach(target => {
      target.periods.forEach(periodTarget => {
        const targetType = target.assessmentType ? target.assessmentType : "default";
        ["instructionalTarget", "masteryTarget"].forEach((targetName, index) => {
          set(targetsToSet, `${targetType}[${periodTarget.name}][${targetName}]`, periodTarget.values[index]);
        });
      });
    });
    return targetsToSet;
  };

  wasAssessmentChanged() {
    return this.props.selectedAssessment.assessmentId !== this.state.assessmentId;
  }

  wereTargetsModified(prevProps) {
    return JSON.stringify(prevProps.allTargets) !== JSON.stringify(this.props.allTargets);
  }

  updateTarget = e => {
    const valueToSet = parseInt(e.target.value);
    const updateString = `targets.${e.target.dataset.updateString}.${e.target.name}`;
    const clonedState = cloneDeep(this.state);
    set(clonedState, updateString, valueToSet);
    this.setState({ assessmentId: this.state.assessmentId, ...clonedState });
  };

  saveAllTargets = e => {
    e.preventDefault();
    const { assessmentId, targets } = this.state;
    this.props.updateAssessmentTargets({ assessmentId, grade: this.props.grade, targets }, () => {
      this.setState({ isHidden: true });
    });
  };

  getTargetNameRow = (numberOfTargets, target) => {
    const hasTarget = !!target.assessmentType;
    if (!hasTarget) {
      return <span>Default targets for all assessment types</span>;
    }
    return (
      <React.Fragment>
        {capitalizeFirstLetter(target.assessmentType)}
        {numberOfTargets > 1 ? (
          <Button
            className="btn btn-danger pull-right"
            onClick={this.props.removeTargetFromAssessment(target.assessmentType)}
          >
            <span className="fa fa fa-close" />
          </Button>
        ) : null}
      </React.Fragment>
    );
  };

  getAssessmentsTableHeader(numberOfTargets, target) {
    return (
      <React.Fragment>
        <tr style={{ backgroundColor: "#f3f3f3" }}>
          <td colSpan="3">
            <strong>{this.getTargetNameRow(numberOfTargets, target)}</strong>
          </td>
        </tr>
        <tr>
          <td className="col-md-2">Period</td>
          <td className="col-md-4">Instructional Target</td>
          <td className="col-md-4">Mastery Target</td>
        </tr>
      </React.Fragment>
    );
  }

  getSaveTargetsButton() {
    if (this.props.isPreviewOnly) {
      return null;
    }
    return (
      <div className="pull-right">
        {this.props.isModifyingTargets ? (
          <Loading inline={true} />
        ) : (
          <Button className="btn btn-primary" name={"saveTargets"} onClick={this.saveAllTargets}>
            Save All Targets!
          </Button>
        )}
      </div>
    );
  }

  render() {
    if (this.state.isHidden) {
      return null;
    }
    const numberOfTargets = this.props.allTargets.length;
    return (
      <div className="clearfix">
        <h4 className="text-center">
          Targets for <b>{this.props.selectedAssessment.fullName}</b> assessment for grade: <b>{this.props.grade}</b>:
        </h4>
        <table className="table table-bordered">
          <tbody>
            {this.props.allTargets.map(target => (
              <React.Fragment key={target.id}>
                {this.getAssessmentsTableHeader(numberOfTargets, target)}
                {["Fall", "Winter", "Spring", "All"].map(periodName => {
                  const periodTarget = target.periods.find(period => period.name === periodName);
                  const targetType = target.assessmentType ? target.assessmentType : "default";
                  const currentValue = get(this.state, `targets.[${targetType}][${periodName}]`);
                  if (!currentValue) {
                    return null;
                  }
                  return (
                    <AssessmentTargetsTableRow
                      updateQueryString={`${targetType}.${periodName}`}
                      targets={currentValue}
                      periodTargetName={periodName}
                      updateTarget={this.updateTarget}
                      key={periodTarget.id}
                      isPreviewOnly={this.props.isPreviewOnly}
                    />
                  );
                })}
              </React.Fragment>
            ))}
          </tbody>
        </table>
        {!this.props.isPreviewOnly ? (
          <AddCustomTargetButtons
            isAddingTargets={this.props.isAddingTargets}
            missingTargets={this.props.missingTargets}
            addCustomTarget={this.props.addCustomTarget}
          />
        ) : null}
        {this.getSaveTargetsButton()}
      </div>
    );
  }
}

AssessmentTargetsTable.propTypes = {
  selectedAssessment: PropTypes.object,
  grade: PropTypes.string,
  allTargets: PropTypes.arrayOf(PropTypes.object),
  isAddingTargets: PropTypes.bool,
  missingTargets: PropTypes.array,
  addCustomTarget: PropTypes.func,
  updateAssessmentTargets: PropTypes.func,
  removeTargetFromAssessment: PropTypes.func,
  isModifyingTargets: PropTypes.bool,
  isPreviewOnly: PropTypes.bool
};
