import { Meteor } from "meteor/meteor";
import React from "react";
import { cleanup, render } from "@testing-library/react";
import "@testing-library/jest-dom";
import { PureManageRules as ManageRules } from "./manage-rules";
import componentData from "./manage-rules-test-data";

jest.mock("../data-admin/upload/file-upload-utils", () => ({
  ...jest.requireActual("../data-admin/upload/file-upload-utils"),
  getCSV: jest.fn()
}));

const fileUploadUtils = require("../data-admin/upload/file-upload-utils");

describe("ManageRules", () => {
  let meteorSpy;
  beforeEach(() => {
    meteorSpy = jest.spyOn(Meteor, "call");
  });
  afterEach(() => {
    cleanup();
    meteorSpy.mockRestore();
    fileUploadUtils.getCSV.mockClear();
  });
  it("should show inner loaders when getting data", () => {
    const { getByText, getAllByTestId } = render(<ManageRules loading={true} />);

    expect(getByText("Rule management")).toBeVisible();
    getAllByTestId("loading-icon").forEach(element => {
      expect(element).toBeVisible();
    });
  });
  it("should render component with minimum data", () => {
    const params = {
      screeningAssignments: [],
      assessments: [],
      rules: [],
      grades: [],
      benchmarkPeriods: [],
      interventions: [],
      loading: false
    };

    const { getByText, getByTestId, queryByTestId } = render(<ManageRules {...params} />);

    expect(getByText("Rule management")).toBeVisible();
    expect(getByTestId("grade-selector")).toBeVisible();
    expect(getByTestId("benchmarkPeriod-selector")).toBeVisible();
    expect(queryByTestId("screeningAssignment-selector")).toBeNull();
    expect(queryByTestId("rule-tree-container")).toBeNull();
  });
  it("should generate valid object for individual rule outcome CSV", () => {
    const { getByTestId } = render(<ManageRules {...componentData} />);
    getByTestId("exportOutcomeCSV").click();
    expect(fileUploadUtils.getCSV).toHaveBeenCalledTimes(1);
    expect(fileUploadUtils.getCSV).toHaveBeenCalledWith([
      {
        "Assessment Measure": 81,
        "Assessment Name": "Add 3-Digit w/o Regrouping",
        Grade: "07",
        "Node Number": "0 - 0",
        "Screening Assignment": "Add 3-Digit w/o Regrouping",
        Season: "Fall",
        "Target Above - Interventions": "No interventions",
        "Target Above - Outcome Rule Name": "N/A",
        "Target Above Assessment - Outcome Rule": "N/A",
        "Target At - Interventions": "No interventions",
        "Target At - Outcome Rule Name": "N/A",
        "Target At Assessment - Outcome Rule": "N/A",
        "Target Below - Interventions": "No interventions",
        "Target Below - Outcome Rule Name": "N/A",
        "Target Below Assessment - Outcome Rule": "N/A"
      },
      {
        "Assessment Measure": 21,
        "Assessment Name": "Mixed Operations with Integers of Varying Sign - Paper Pencil",
        Grade: "07",
        "Node Number": "0 - 0",
        "Screening Assignment": "Mixed Operations with Integers of Varying Sign - Paper Pencil",
        Season: "Fall",
        "Target Above - Interventions": "No interventions",
        "Target Above - Outcome Rule Name": "N/A",
        "Target Above Assessment - Outcome Rule": "N/A",
        "Target At - Interventions": "No interventions",
        "Target At - Outcome Rule Name": "N/A",
        "Target At Assessment - Outcome Rule": "N/A",
        "Target Below - Interventions": "No interventions",
        "Target Below - Outcome Rule Name": "N/A",
        "Target Below Assessment - Outcome Rule": "N/A"
      },
      {
        "Assessment Measure": 81,
        "Assessment Name": "Add 3-Digit w/o Regrouping",
        Grade: "07",
        "Node Number": "0 - 0",
        "Screening Assignment": "Add 3-Digit w/o Regrouping",
        Season: "Winter",
        "Target Above - Interventions": "No interventions",
        "Target Above - Outcome Rule Name": "N/A",
        "Target Above Assessment - Outcome Rule": "N/A",
        "Target At - Interventions": "No interventions",
        "Target At - Outcome Rule Name": "N/A",
        "Target At Assessment - Outcome Rule": "N/A",
        "Target Below - Interventions": "No interventions",
        "Target Below - Outcome Rule Name": "N/A",
        "Target Below Assessment - Outcome Rule": "N/A"
      },
      {
        "Assessment Measure": 81,
        "Assessment Name": "Add 3-Digit w/o Regrouping",
        Grade: "07",
        "Node Number": "0 - 0",
        "Screening Assignment": "Add 3-Digit w/o Regrouping",
        Season: "Winter",
        "Target Above - Interventions": "No interventions",
        "Target Above - Outcome Rule Name": "N/A",
        "Target Above Assessment - Outcome Rule": "N/A",
        "Target At - Interventions": "No interventions",
        "Target At - Outcome Rule Name": "N/A",
        "Target At Assessment - Outcome Rule": "N/A",
        "Target Below - Interventions": "No interventions",
        "Target Below - Outcome Rule Name": "N/A",
        "Target Below Assessment - Outcome Rule": "N/A"
      },
      {
        "Assessment Measure": 21,
        "Assessment Name": "Mixed Operations with Integers of Varying Sign - Paper Pencil",
        Grade: "07",
        "Node Number": "0 - 0",
        "Screening Assignment": "Mixed Operations with Integers of Varying Sign - Paper Pencil",
        Season: "Winter",
        "Target Above - Interventions": "No defined outcome",
        "Target Above - Outcome Rule Name": "No defined outcome",
        "Target Above Assessment - Outcome Rule": "No defined outcome",
        "Target At - Interventions": "Timed Trial",
        "Target At - Outcome Rule Name": "Mixed Operations with Integers of Varying Sign - Paper Pencil",
        "Target At Assessment - Outcome Rule": "21",
        "Target Below - Interventions": "No interventions",
        "Target Below - Outcome Rule Name": "Add 3-Digit w/o Regrouping",
        "Target Below Assessment - Outcome Rule": "81"
      },
      {
        "Assessment Measure": 81,
        "Assessment Name": "Add 3-Digit w/o Regrouping",
        Grade: "07",
        "Node Number": "1 - 0",
        "Screening Assignment": "Mixed Operations with Integers of Varying Sign - Paper Pencil",
        Season: "Winter",
        "Target Above - Interventions": "Cover Copy and Compare, Guided Practice",
        "Target Above - Outcome Rule Name": "Mixed Operations with Integers of Varying Sign - Paper Pencil",
        "Target Above Assessment - Outcome Rule": "21",
        "Target At - Interventions": "Incremental Rehearsal Multiplication 0-12, Timed Trial",
        "Target At - Outcome Rule Name": "Add 3-Digit w/o Regrouping",
        "Target At Assessment - Outcome Rule": "81",
        "Target Below - Interventions": "No interventions",
        "Target Below - Outcome Rule Name": "Add 3-Digit w/o Regrouping",
        "Target Below Assessment - Outcome Rule": "81"
      },
      {
        "Assessment Measure": 81,
        "Assessment Name": "Add 3-Digit w/o Regrouping",
        Grade: "07",
        "Node Number": "0 - 0",
        "Screening Assignment": "Add 3-Digit w/o Regrouping",
        Season: "Spring",
        "Target Above - Interventions": "No interventions",
        "Target Above - Outcome Rule Name": "N/A",
        "Target Above Assessment - Outcome Rule": "N/A",
        "Target At - Interventions": "No interventions",
        "Target At - Outcome Rule Name": "N/A",
        "Target At Assessment - Outcome Rule": "N/A",
        "Target Below - Interventions": "No interventions",
        "Target Below - Outcome Rule Name": "N/A",
        "Target Below Assessment - Outcome Rule": "N/A"
      },
      {
        "Assessment Measure": 63,
        "Assessment Name": "Find the Least Common Denominator",
        Grade: "07",
        "Node Number": "0 - 0",
        "Screening Assignment": "Find the Least Common Denominator",
        Season: "Spring",
        "Target Above - Interventions": "No interventions",
        "Target Above - Outcome Rule Name": "N/A",
        "Target Above Assessment - Outcome Rule": "N/A",
        "Target At - Interventions": "No interventions",
        "Target At - Outcome Rule Name": "N/A",
        "Target At Assessment - Outcome Rule": "N/A",
        "Target Below - Interventions": "No interventions",
        "Target Below - Outcome Rule Name": "N/A",
        "Target Below Assessment - Outcome Rule": "N/A"
      },
      {
        "Assessment Measure": 21,
        "Assessment Name": "Mixed Operations with Integers of Varying Sign - Paper Pencil",
        Grade: "07",
        "Node Number": "0 - 0",
        "Screening Assignment": "Mixed Operations with Integers of Varying Sign - Paper Pencil",
        Season: "Spring",
        "Target Above - Interventions": "No interventions",
        "Target Above - Outcome Rule Name": "N/A",
        "Target Above Assessment - Outcome Rule": "N/A",
        "Target At - Interventions": "No interventions",
        "Target At - Outcome Rule Name": "N/A",
        "Target At Assessment - Outcome Rule": "N/A",
        "Target Below - Interventions": "No interventions",
        "Target Below - Outcome Rule Name": "N/A",
        "Target Below Assessment - Outcome Rule": "N/A"
      },
      {
        "Assessment Measure": 63,
        "Assessment Name": "Find the Least Common Denominator",
        Grade: "08",
        "Node Number": "0 - 0",
        "Screening Assignment": "Find the Least Common Denominator",
        Season: "Fall",
        "Target Above - Interventions": "No defined outcome",
        "Target Above - Outcome Rule Name": "No defined outcome",
        "Target Above Assessment - Outcome Rule": "No defined outcome",
        "Target At - Interventions": "Incremental Rehearsal Multiplication 0-12, Timed Trial",
        "Target At - Outcome Rule Name": "Find the Least Common Denominator",
        "Target At Assessment - Outcome Rule": "63",
        "Target Below - Interventions": "No interventions",
        "Target Below - Outcome Rule Name": "Find the Least Common Denominator",
        "Target Below Assessment - Outcome Rule": "63"
      }
    ]);
  });

  it("should generate valid object for targets CSV", () => {
    const { getByTestId } = render(<ManageRules {...componentData} />);
    getByTestId("exportTargetsCSV").click();
    expect(fileUploadUtils.getCSV).toHaveBeenCalledTimes(1);
    expect(fileUploadUtils.getCSV).toHaveBeenCalledWith([
      {
        "All Periods benchmark instructional target": undefined,
        "All Periods benchmark mastery target": undefined,
        "All Periods default instructional target": undefined,
        "All Periods default mastery target": undefined,
        "All Periods individual instructional target": undefined,
        "All Periods individual mastery target": undefined,
        "Assessment Measure": "21",
        "Assessment Name": "Mixed Operations with Integers of Varying Sign - Paper Pencil",
        "Fall benchmark instructional target": undefined,
        "Fall benchmark mastery target": undefined,
        "Fall default instructional target": 14,
        "Fall default mastery target": 29,
        "Fall individual instructional target": undefined,
        "Fall individual mastery target": undefined,
        Grade: "07",
        "Spring benchmark instructional target": undefined,
        "Spring benchmark mastery target": undefined,
        "Spring default instructional target": 14,
        "Spring default mastery target": 29,
        "Spring individual instructional target": undefined,
        "Spring individual mastery target": undefined,
        "Winter benchmark instructional target": undefined,
        "Winter benchmark mastery target": undefined,
        "Winter default instructional target": 14,
        "Winter default mastery target": 29,
        "Winter individual instructional target": undefined,
        "Winter individual mastery target": undefined
      },
      {
        "All Periods benchmark instructional target": undefined,
        "All Periods benchmark mastery target": undefined,
        "All Periods default instructional target": undefined,
        "All Periods default mastery target": undefined,
        "All Periods individual instructional target": undefined,
        "All Periods individual mastery target": undefined,
        "Assessment Measure": "81",
        "Assessment Name": "Add 3-Digit w/o Regrouping",
        "Fall benchmark instructional target": undefined,
        "Fall benchmark mastery target": undefined,
        "Fall default instructional target": 6,
        "Fall default mastery target": 11,
        "Fall individual instructional target": undefined,
        "Fall individual mastery target": undefined,
        Grade: "07",
        "Spring benchmark instructional target": undefined,
        "Spring benchmark mastery target": undefined,
        "Spring default instructional target": 6,
        "Spring default mastery target": 11,
        "Spring individual instructional target": undefined,
        "Spring individual mastery target": undefined,
        "Winter benchmark instructional target": undefined,
        "Winter benchmark mastery target": undefined,
        "Winter default instructional target": 6,
        "Winter default mastery target": 11,
        "Winter individual instructional target": undefined,
        "Winter individual mastery target": undefined
      },
      {
        "All Periods benchmark instructional target": undefined,
        "All Periods benchmark mastery target": undefined,
        "All Periods default instructional target": undefined,
        "All Periods default mastery target": undefined,
        "All Periods individual instructional target": undefined,
        "All Periods individual mastery target": undefined,
        "Assessment Measure": "63",
        "Assessment Name": "Find the Least Common Denominator",
        "Fall benchmark instructional target": undefined,
        "Fall benchmark mastery target": undefined,
        "Fall default instructional target": 8,
        "Fall default mastery target": 15,
        "Fall individual instructional target": undefined,
        "Fall individual mastery target": undefined,
        Grade: "08",
        "Spring benchmark instructional target": undefined,
        "Spring benchmark mastery target": undefined,
        "Spring default instructional target": 8,
        "Spring default mastery target": 15,
        "Spring individual instructional target": undefined,
        "Spring individual mastery target": undefined,
        "Winter benchmark instructional target": undefined,
        "Winter benchmark mastery target": undefined,
        "Winter default instructional target": 8,
        "Winter default mastery target": 15,
        "Winter individual instructional target": undefined,
        "Winter individual mastery target": undefined
      }
    ]);
  });
});
