import React, { useEffect, useState } from "react";
import { Meteor } from "meteor/meteor";
import PropTypes from "prop-types";
import { Button, Collapse } from "react-bootstrap";
import { keyBy } from "lodash";
import DraggableSelect from "./draggable-select";
import Loading from "../../components/loading";

function ManageGroupedAssessmentsRow({
  skillName,
  groupedAssessmentsMeasuresBySkillName = {},
  availableAssessmentsByMeasure = {},
  setDidRowUpdate
}) {
  const [selectedSkillOption, setSelectedSkillOption] = useState(skillName || null);
  const [selectedGroupedAssessments, setSelectedGroupedAssessments] = useState(null);
  const [didSkillOptionChange, setDidSkillOptionChange] = useState(false);

  useEffect(() => {
    if (skillName) {
      setSelectedGroupedAssessments(groupedAssessmentsMeasuresBySkillName[skillName]);
      setSelectedSkillOption({ label: skillName, value: skillName });
    } else {
      setSelectedGroupedAssessments(null);
      setSelectedSkillOption(null);
    }
  }, []);

  const getSelectedOptions = () => {
    return (
      groupedAssessmentsMeasuresBySkillName[selectedSkillOption?.value]?.assessmentMeasures.map(am => {
        const assessment = availableAssessmentsByMeasure[am];
        return {
          label: `AM ${assessment.monitorAssessmentMeasure} - ${assessment.name}`,
          value: assessment.monitorAssessmentMeasure
        };
      }) || []
    );
  };

  const getGroupedAssessmentsOptions = () => {
    const selectedMeasures = selectedGroupedAssessments?.map(a => a.value) || [];
    return Object.values(availableAssessmentsByMeasure)
      .filter(a => !selectedMeasures.includes(a.monitorAssessmentMeasure))
      .map(a => ({
        label: `AM ${a.monitorAssessmentMeasure} - ${a.name}`,
        value: a.monitorAssessmentMeasure
      }));
  };

  useEffect(() => {
    if (selectedSkillOption?.value) {
      setSelectedGroupedAssessments(getSelectedOptions());
    } else {
      setSelectedGroupedAssessments([]);
    }
  }, [selectedSkillOption]);

  useEffect(() => {
    if (selectedSkillOption?.value && !didSkillOptionChange) {
      const selectedMeasures = selectedGroupedAssessments.map(g => g.value);
      // eslint-disable-next-line no-param-reassign
      groupedAssessmentsMeasuresBySkillName[skillName].assessmentMeasures = selectedMeasures;
      Meteor.call("updateAssessmentMeasuresForGroupedAssessments", selectedSkillOption.value, selectedMeasures);
    }
    setDidSkillOptionChange(false);
  }, [selectedGroupedAssessments]);

  const groupedAssessmentsOptions = selectedSkillOption ? getGroupedAssessmentsOptions() : [];

  const onChange = selected => {
    setDidRowUpdate(true);
    if (selectedGroupedAssessments) {
      setSelectedGroupedAssessments(selected);
    } else {
      setSelectedGroupedAssessments([]);
    }
  };

  return (
    <div key={skillName} className="form-group row d-flex justify-content-center">
      <div className="col-4">
        <p className="text-black">{skillName}</p>
      </div>
      <div className="col-8">
        <DraggableSelect
          selected={selectedGroupedAssessments}
          onChange={onChange}
          options={groupedAssessmentsOptions}
          setSelected={setSelectedGroupedAssessments}
        />
      </div>
    </div>
  );
}

ManageGroupedAssessmentsRow.propTypes = {
  groupedAssessmentsMeasuresBySkillName: PropTypes.object,
  availableAssessmentsByMeasure: PropTypes.object,
  isLoading: PropTypes.bool,
  skillName: PropTypes.string,
  setDidRowUpdate: PropTypes.func
};

export default function ManageGroupedAssessments() {
  const [skillNames, setSkillNames] = useState([]);
  const [groupedAssessmentsMeasuresBySkillName, setGroupedAssessmentsMeasuresBySkillName] = useState({});
  const [availableAssessmentsByMeasure, setAvailableAssessmentsByMeasure] = useState({});
  const [remainingAssessmentsByMeasure, setRemainingAssessmentsByMeasure] = useState({});
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [didRowUpdate, setDidRowUpdate] = useState(false);

  useEffect(() => {
    if (!didRowUpdate) {
      setLoading(true);
    }
    Meteor.call("getAvailableAndGroupedAssessments", (err, resp) => {
      if (!err) {
        setSkillNames(resp.groupedAssessments.map(g => g.skillName).sort());
        setGroupedAssessmentsMeasuresBySkillName(keyBy(resp.groupedAssessments, "skillName"));
        setAvailableAssessmentsByMeasure(keyBy(resp.availableAssessments, "monitorAssessmentMeasure"));
      }
      setLoading(false);
      setDidRowUpdate(false);
    });
  }, [setDidRowUpdate]);

  useEffect(() => {
    const usedMeasures = Object.values(groupedAssessmentsMeasuresBySkillName)
      .map(g => g.assessmentMeasures)
      .flat(2);
    const remainingAssessments = {};
    Object.entries(availableAssessmentsByMeasure).forEach(([am, doc]) => {
      if (!usedMeasures.includes(am)) {
        remainingAssessments[am] = doc;
      }
    });
    setRemainingAssessmentsByMeasure(remainingAssessments);
    setDidRowUpdate(false);
  }, [groupedAssessmentsMeasuresBySkillName, availableAssessmentsByMeasure, didRowUpdate]);

  const renderHeaders = () => {
    return (
      <div className="row d-flex justify-content-center">
        <div className="col-4">
          <label className="">Skill Group Name: </label>
        </div>
        <div className="col-8">
          <label className="">Grouped Assessments: </label>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center">
        <Loading inline={true} cssClasses="p-0" />
      </div>
    );
  }

  return (
    <div>
      <div className="row">
        <div className="col-md-2 d-grid">
          <Button onClick={() => setOpen(!open)}>Show All Skills</Button>
        </div>
        <div className="col-md-10 align-self-center text-center">
          Skills included using the small group setup selector can be pulled and combined from multiple grade levels.
        </div>
      </div>
      <Collapse in={open}>
        <div>
          <hr />
          {renderHeaders()}
          {skillNames.map(s => {
            const availableSkills = { ...remainingAssessmentsByMeasure };
            groupedAssessmentsMeasuresBySkillName[s]?.assessmentMeasures.forEach(am => {
              availableSkills[am] = availableAssessmentsByMeasure[am];
            });
            return (
              <ManageGroupedAssessmentsRow
                key={s}
                skillName={s}
                groupedAssessmentsMeasuresBySkillName={groupedAssessmentsMeasuresBySkillName}
                availableAssessmentsByMeasure={availableSkills}
                setDidRowUpdate={setDidRowUpdate}
                setGroupedAssessmentsMeasuresBySkillName={setGroupedAssessmentsMeasuresBySkillName}
              />
            );
          })}
        </div>
      </Collapse>
    </div>
  );
}
