import React, { Component } from "react";
import PropTypes from "prop-types";
import { withRouter } from "react-router-dom";
import Alert from "react-s-alert";
import { Meteor } from "meteor/meteor";
import { withTracker } from "meteor/react-meteor-data";
import { isEqual, keyBy } from "lodash";

import ConfirmModal from "../data-admin/confirm-modal";
import PageHeader from "../../components/page-header.jsx";
import { Loading } from "../../components/loading.jsx";
import { areSubscriptionsLoading } from "../../utilities";
import { Users } from "/imports/api/users/users";
import { Organizations } from "/imports/api/organizations/organizations";
import { Sites } from "/imports/api/sites/sites";

const rolesMap = {
  arbitraryIdteacher: "Teacher",
  arbitraryIdadmin: "Coach"
};

class ManageOrgUsers extends Component {
  state = {
    appliedUserId: null,
    modifiedAccessByUserId: {},
    isModifyUserModalOpen: false
  };

  componentDidUpdate(prevProps) {
    if (!isEqual(this.props.users, prevProps.users)) {
      let result = { modifiedAccessByUserId: {} };
      this.props.users.forEach(user => {
        result = { modifiedAccessByUserId: { ...result.modifiedAccessByUserId, [user._id]: user.siteAccess } };
      });
      this.setState(result);
    }
  }

  modifySelectedUser = () => {
    const { appliedUserId, modifiedAccessByUserId } = this.state;
    if (!appliedUserId || !modifiedAccessByUserId[appliedUserId]) return;
    this.setState({ isModifyUserModalOpen: false, appliedUserId: null });
    Meteor.call("users:modifyAccess", appliedUserId, modifiedAccessByUserId[appliedUserId], err => {
      if (err) {
        Alert.error("There was a problem while updating user", {
          timeout: 5000
        });
      } else {
        this.setState({ selectedUsers: [] });
        Alert.success("Selected user updated successfully", {
          timeout: 5000
        });
      }
    });
  };

  showModifyAccessModal = () => {
    this.setState({ isModifyUserModalOpen: true });
  };

  closeModifyAccessModal = () => {
    this.setState({ isModifyUserModalOpen: false });
  };

  handleAccessToggle = ({ userId, siteId, role }) => {
    const { modifiedAccessByUserId } = this.state;
    const modifiedAccess = modifiedAccessByUserId[userId].map(obj => {
      if (siteId === obj.siteId && role === obj.role) {
        // eslint-disable-next-line no-param-reassign
        obj.isActive = !obj.isActive;
      }
      return obj;
    });
    this.setState({ modifiedAccessByUserId: { ...modifiedAccessByUserId, [userId]: modifiedAccess } });
  };

  handleSelectAll = userId => {
    const { modifiedAccessByUserId } = this.state;
    const checkboxState = this.state.modifiedAccessByUserId[userId]?.every(val => val.isActive);
    const modifiedAccess = modifiedAccessByUserId[userId].map(obj => ({ ...obj, isActive: !checkboxState }));
    this.setState({ modifiedAccessByUserId: { ...modifiedAccessByUserId, [userId]: modifiedAccess } });
  };

  renderUsers() {
    const { users, sites } = this.props;
    const sitesBySiteId = keyBy(sites, "_id");

    return (
      <React.Fragment>
        <table className="table">
          <thead>
            <tr>
              <th>Name</th>
              <th>Email</th>
              <th>Access</th>
              <th>&nbsp;</th>
            </tr>
          </thead>
          <tbody>
            {users.map(user => (
              <tr key={user._id}>
                <td>{`${user.profile.name.first} ${user.profile.name.last}`}</td>
                <td>{user.emails[0].address}</td>
                <td>
                  <table>
                    <tbody>
                      <tr>
                        <td>
                          <input
                            data-testid="user-select-all-checkbox"
                            className="me-2"
                            type="checkbox"
                            checked={this.state.modifiedAccessByUserId[user._id]?.every(val => val.isActive)}
                            onChange={() => {
                              this.handleSelectAll(user._id);
                            }}
                          />
                        </td>
                        <td className="text-primary">Select All</td>
                      </tr>
                      {this.state.modifiedAccessByUserId[user._id]?.map((access, index) => {
                        const key = `${user._id}_${rolesMap[access.role]}_${access.siteId}_${access.schoolYear}`;
                        return (
                          <tr key={key}>
                            <td>
                              <input
                                data-testid={`user-access-checkbox-${key}`}
                                className="me-2"
                                type="checkbox"
                                checked={this.state.modifiedAccessByUserId[user._id][index].isActive}
                                onChange={() => {
                                  this.handleAccessToggle({
                                    userId: user._id,
                                    siteId: access.siteId,
                                    role: access.role
                                  });
                                }}
                              />
                            </td>
                            <td>
                              <strong>{rolesMap[access.role]}</strong> in{" "}
                              <strong>{sitesBySiteId[access.siteId]?.name || access.siteId}</strong>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </td>
                <td>
                  <button
                    type="button"
                    disabled={false}
                    className="btn btn-success"
                    onClick={() => {
                      this.setState({ appliedUserId: user._id });
                      this.showModifyAccessModal();
                    }}
                  >
                    Apply
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </React.Fragment>
    );
  }

  render() {
    if (this.props.loading) {
      return <Loading />;
    }

    const { users } = this.props;

    return (
      <div className="conFullScreen">
        <PageHeader title={"Manage Users"} />
        <div className="container">
          <div className="row">
            <div className="col-12 m-t-5">
              {users.length ? this.renderUsers() : <div className="alert alert-info text-center m-t-5">No users</div>}
            </div>
          </div>
        </div>
        <ConfirmModal
          showModal={this.state.isModifyUserModalOpen}
          onCloseModal={this.closeModifyAccessModal}
          confirmAction={this.modifySelectedUser}
          headerText="Are you sure you want to change access for the selected user?"
          bodyQuestion="If you remove access, some or all of the data that this user could view will be inaccessible to them."
          confirmText="Yes, change access"
        />
      </div>
    );
  }
}

ManageOrgUsers.propTypes = {
  loading: PropTypes.bool,
  users: PropTypes.array,
  organizations: PropTypes.array,
  sites: PropTypes.array,
  role: PropTypes.string,
  history: PropTypes.object
};

export default withTracker(({ orgid }) => {
  const organizationsSub = Meteor.subscribe("Organizations");
  const sitesSub = Meteor.subscribe("Sites", orgid);
  const usersSub = Meteor.subscribe("Users:InOrganization", { orgid });

  const loading = areSubscriptionsLoading(organizationsSub, usersSub, sitesSub);
  let users = [];
  let organizations = [];
  let sites = [];
  if (!loading) {
    organizations = Organizations.find().fetch();
    sites = Sites.find({}, { sort: { name: 1 } }).fetch();
    if (orgid) {
      users = Users.find({ "profile.orgid": orgid }).fetch();
      users = users
        .map(user => ({
          ...user,
          siteAccess: sites
            .map(site => {
              const siteId = site._id;
              const adminAccess = user.profile.siteAccess.filter(
                accessObj => accessObj.siteId === siteId && accessObj.role === "arbitraryIdadmin"
              );

              const accessWithRoles = [];
              const isActive = !!adminAccess.find(accessObj => accessObj.isActive);
              accessWithRoles.push({
                siteId,
                role: "arbitraryIdadmin",
                isActive
              });
              return accessWithRoles;
            })
            .flat(1)
        }))
        .filter(user => !!user.siteAccess.length);
    }
  }

  return { loading, users, organizations, sites };
})(withRouter(ManageOrgUsers));
