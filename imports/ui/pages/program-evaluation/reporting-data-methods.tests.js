import { getStudentIdsThatFinishedAtLeastOneSkillTree } from "./reporting-data-methods";

describe("getStudentIdsThatFinishedAtLeastOneSkillTree", () => {
  function generateBasicAssessmentResult({
    benchmarkAssessmentId,
    benchmarkAssessmentTargets,
    assessmentId,
    scores,
    hasPassed,
    studentId
  }) {
    return {
      status: "COMPLETED",
      type: "individual",
      studentId,
      individualSkills: {
        benchmarkAssessmentId
      },
      measures: scores.map((s, index) => ({
        assessmentId: index === 0 ? benchmarkAssessmentId : assessmentId,
        studentResults: [
          {
            studentId,
            score: s,
            meetsTarget: s >= benchmarkAssessmentTargets[1]
          }
        ]
      })),
      ruleResults: {
        passed: hasPassed
      }
    };
  }
  it("should not return studentIds for students without individual assessment results", () => {
    expect(getStudentIdsThatFinishedAtLeastOneSkillTree([])).toEqual([]);
  });
  it("should not return studentIds for students that have not completed at least one skill tree", () => {
    // NOTE(fmazur) - exact same case as when student had individual intervention manually ended
    const assessmentResults = [
      generateBasicAssessmentResult({
        benchmarkAssessmentId: "bId1",
        benchmarkAssessmentTargets: [5, 10, 50],
        assessmentId: "aId1",
        scores: [5, 10],
        hasPassed: true,
        studentId: "sId1"
      }),
      generateBasicAssessmentResult({
        benchmarkAssessmentId: "bId1",
        benchmarkAssessmentTargets: [5, 10, 50],
        assessmentId: "aId1",
        scores: [0, 0],
        hasPassed: false,
        studentId: "sId1"
      })
    ];
    expect(getStudentIdsThatFinishedAtLeastOneSkillTree(assessmentResults)).toEqual([]);
  });
  it("should return studentIds for students that had completed at least one skill tree", () => {
    const assessmentResults = [
      generateBasicAssessmentResult({
        benchmarkAssessmentId: "bId1",
        benchmarkAssessmentTargets: [5, 10, 50],
        assessmentId: "aId1",
        scores: [10, 0],
        hasPassed: true,
        studentId: "sId1"
      }),
      generateBasicAssessmentResult({
        benchmarkAssessmentId: "bId1",
        benchmarkAssessmentTargets: [5, 10, 50],
        assessmentId: "aId1",
        scores: [0, 0],
        hasPassed: false,
        studentId: "sId1"
      })
    ];
    expect(getStudentIdsThatFinishedAtLeastOneSkillTree(assessmentResults)).toEqual(["sId1"]);
  });
});
