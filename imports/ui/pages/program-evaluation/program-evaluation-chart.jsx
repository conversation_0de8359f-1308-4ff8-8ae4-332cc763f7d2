import React, { Component } from "react";
import PropTypes from "prop-types";
import Highcharts from "highcharts/highstock";
import { isEqual } from "lodash";

import { isOnPrintPage } from "../../utilities";

export class ProgramEvaluation<PERSON>hart extends Component {
  // When the DOM is ready, create the chart.
  componentDidMount() {
    this.updateChart();
  }

  // Only update the pmGraph after the latestResultDoc for the whole group has changed
  shouldComponentUpdate(nextProps) {
    return this.props.chartName !== nextProps.chartName || !isEqual(nextProps.data, this.props.data);
  }

  // Update the chart if the component is updated
  componentDidUpdate() {
    this.updateChart();
  }

  //  Destroy chart before unmount.
  componentWillUnmount() {
    if (this.chart) {
      this.chart.destroy();
    }
  }

  updateChart() {
    const isPrinting = isOnPrintPage();
    const { options, chartId, data, chartName } = this.props;
    this.chart = new Highcharts.Chart(chartId, {
      chart: {
        type: "column",
        ...options,
        ...(options.shouldUseSecondaryLabel ? { marginBottom: 85 + (options.subtitleText ? 10 : 0) } : {}),
        spacingRight: 2,
        spacingLeft: 2
      },
      accessibility: {
        enabled: false
      },
      subtitle: {
        text: options.subtitleText || "",
        style: {
          color: "#000"
        },
        verticalAlign: "bottom",
        y: 20
      },
      credits: {
        enabled: false
      },
      title: {
        text: chartName
      },
      xAxis: {
        // categories not defined therefore this.point.name is being used
        type: "category",
        labels: {
          autoRotationLimit: 0,
          style: {
            fontSize: "10px",
            ...(isPrinting ? { color: "#000" } : {})
          }
        }
      },
      yAxis: {
        title: {
          text: ""
        },
        labels: {
          style: {
            fontSize: isPrinting ? "6px" : "9px",
            ...(isPrinting ? { color: "#000" } : {})
          },
          x: -4
        },
        min: 0,
        max: 100
      },
      legend: {
        enabled: true,
        floating: false,
        layout: "horizontal",
        align: "center",
        verticalAlign: "top",
        itemHiddenStyle: {
          color: "#ccc",
          textDecoration: "none"
        }
      },
      plotOptions: {
        series: {
          borderWidth: 0,
          groupPadding: 0.07,
          dataLabels: [
            {
              inside: false,
              enabled: true,
              allowOverlap: true,
              ...(isPrinting ? { color: "#000" } : {}),
              formatter() {
                if (this.point.isNA || this.point.y === undefined) {
                  return "N/A";
                }
                const { y } = this.point;
                return `${y % 100 === 0 ? y : y.toFixed(1)}%`;
              },
              rotation: 0,
              style: {
                fontSize: isPrinting ? "7px" : "9px",
                ...(isPrinting ? { textOutline: "0px" } : {})
              }
            },
            options.shouldUseSecondaryLabel
              ? {
                  color: "#000",
                  inside: true,
                  enabled: true,
                  crop: false,
                  formatter() {
                    const { n } = this.point;
                    return n ? `(n=${n})` : "";
                  },
                  rotation: -90,
                  verticalAlign: "bottom",
                  y: 50
                }
              : {}
          ]
        },
        column: {
          maxPointWidth: 100,
          minPointLength: 3
        }
      },
      tooltip: {
        headerFormat: '<span style="font-size:11px">{series.name}</span><br>',
        pointFormat: '<span style="color:{point.color}">{point.name}</span>: <b>{point.y:.2f}%</b> of total<br/>'
      },
      series: data
    });
  }
  // Create the div which the chart will be rendered to.

  render() {
    return <div id={this.props.chartId} className="chart" />;
  }
}

ProgramEvaluationChart.propTypes = {
  chartName: PropTypes.string.isRequired,
  type: PropTypes.string,
  chartId: PropTypes.string,
  options: PropTypes.object,
  data: PropTypes.array,
  shouldUseSecondaryLabel: PropTypes.bool
};

ProgramEvaluationChart.defaultProps = {
  shouldUseSecondaryLabel: false
};
