import { difference, flatten, groupBy, intersection, isNaN, keyBy, sortBy, sum, uniq, uniqBy } from "lodash";
import React from "react";
import { compileResultsByGradesInSchool } from "../admin-view/school-overview";
import sortByPropertyFor from "../../../api/utilities/sortingHelpers/sortByPropertyFor";
import * as utils from "../../../api/utilities/utilities";
import { getMedianNumber, hasGroupPassedClasswideIntervention } from "../../../api/utilities/utilities";
import { normalizeGrade } from "../../../api/utilities/sortingHelpers/normalizeSortItem";
import { getGrowthChartMarkup } from "../student-groups/growth-chart-wrapper";
import { getGradeDetailData } from "../../../api/utilities/skillProgressMethods";
import {
  getFallToSpringGrowthResultsForGrade,
  getNumberOfDigitsForSeries,
  getProficientOnExternalMeasureFallToSpringData,
  getProficientOnExternalMeasureSpringByYearData,
  getProficientOnExternalMeasureSpringData,
  getStudentIdsManuallyRecommendedForIndividualIntervention
} from "/imports/api/districtReporting/helpers";
import { getGradeLabel, getGrowthGraphSubtitle, getPercentage } from "./helpers";
import { colors } from "/imports/api/constants";
import { GrowthChartLegend } from "../admin-view/growth-chart-legend";
import BarGrowthGraph from "../district-reporting/bar-growth-graph";
import BarGraph from "../district-reporting/bar-graph";

export const loadingMessage = "Please wait while your data is compiled. This can take up to a minute.";

export function calculatePercentageMeetingTargetByGradeAndSeason(params) {
  const { availableGrades, bmPeriods, schoolYear, siteName, assessmentResultsForSite, studentGroups } = params;
  const studentGroupIds = studentGroups.map(sg => sg._id);
  const benchmarkAssessmentResults = assessmentResultsForSite.filter(
    ar => ar.type === "benchmark" && ar.status === "COMPLETED" && studentGroupIds.includes(ar.studentGroupId)
  );
  return compileResultsByGradesInSchool({
    bmPeriods,
    schoolYear,
    grades: availableGrades.filter(({ _id: grade }) => grade !== "HS"),
    siteName,
    allAssessmentResults: benchmarkAssessmentResults
  });
}

export function getChartOptions(customOptions) {
  return {
    chartType: "line",
    title: "",
    height: 400,
    xAxisTitle: "x",
    yAxisTitle: "y",
    marginRight: 10,
    isLegendEnabled: true,
    ...customOptions
  };
}

export function getDocumentsByGradeTypeAndStatus(documents, type, status) {
  const filteredDocuments = documents.filter(d => d.type === type && status.includes(d.status));
  return filteredDocuments.length
    ? groupBy(sortByPropertyFor({ list: filteredDocuments, paths: ["grade"], order: 1 }), ar => ar.grade)
    : {};
}

export function getBenchmarkPeriods(bmPeriods) {
  return bmPeriods
    .filter(bm => bm._id !== "allPeriods")
    .map(bmPeriod => ({
      ...bmPeriod,
      ...utils.translateBenchmarkPeriod(bmPeriod._id),
      name: bmPeriod.name
    }));
}

export function calculateTotalStudentsEnrolled({
  studentGroups,
  bmPeriodId,
  enrollmentCutoffTimestamp,
  studentGroupEnrollmentsByGroupId
}) {
  return sum(
    studentGroups.reduce((numberOfStudentsEnrolledInGroups, sg) => {
      const historyItem = sg.history?.find(sgh => sgh.benchmarkPeriodId === bmPeriodId && sgh.type === "benchmark");
      const enrollmentsForStudentsInBmPeriod = (studentGroupEnrollmentsByGroupId[sg._id] || []).filter(
        sge => sge.created.on <= enrollmentCutoffTimestamp && sge.isActive
      );
      if (historyItem) {
        numberOfStudentsEnrolledInGroups.push(historyItem.enrolledStudentIds.length);
      } else {
        numberOfStudentsEnrolledInGroups.push(enrollmentsForStudentsInBmPeriod.length);
      }
      return numberOfStudentsEnrolledInGroups;
    }, [])
  );
}

export function getBenchmarkPeriodEndTimestamp({ bmPeriod, schoolYear, benchmarkPeriodsGroupId }) {
  const bmPeriodEndDateObject = Object.keys(bmPeriod.endDate[benchmarkPeriodsGroupId] || {}).length
    ? bmPeriod.endDate[benchmarkPeriodsGroupId]
    : bmPeriod.endDate.default;
  const bmPeriodEndDate = new Date(
    bmPeriod.name === "Fall" ? schoolYear - 1 : schoolYear,
    bmPeriodEndDateObject.month - 1,
    bmPeriodEndDateObject.day
  );
  return bmPeriodEndDate.getTime();
}

export function getScreeningDataForSchools({ schoolsInOrg, schoolYear, benchmarkPeriodsGroupId, bmPeriods }) {
  const colorBySeasonName = {
    Fall: colors.orange,
    Winter: colors.darkBlue,
    Spring: colors.steelBlue30
  };
  const finalData = {
    Fall: {
      name: "Fall",
      color: colorBySeasonName.Fall,
      data: []
    },
    Winter: {
      name: "Winter",
      color: colorBySeasonName.Winter,
      data: []
    },
    Spring: {
      name: "Spring",
      color: colorBySeasonName.Spring,
      data: []
    }
  };
  schoolsInOrg.forEach((school, i) => {
    const screeningData = getScreeningByGradeData({
      schoolYear,
      benchmarkPeriodsGroupId,
      bmPeriods,
      studentGroupsByGrade: school.studentGroupsByGrade,
      studentGroupEnrollmentsByGroupId: school.studentGroupEnrollmentsByGroupId,
      assessmentResultsForSite: school.completedAssessmentResults,
      isDistrictReporting: true
    });

    screeningData.forEach(season => {
      let wasAnyNonHS = false;
      let totalScore = 0;

      const averageSchoolResult = season.data.reduce(
        (average, gradeData, index) => {
          delete average.noData;

          // NOTE(fmazur) - total number of students that were enrolled during screening
          average.n += gradeData.n || 0;

          if (!gradeData.isNA) {
            totalScore += (gradeData.y || 0) * (gradeData.n || 0);
          }

          if (!gradeData.isHS && !wasAnyNonHS) {
            wasAnyNonHS = true;
          }

          // NOTE(fmazur) - Calculate average on last index
          if (season.data.length - 1 === index) {
            if (!totalScore || average.n === 0) {
              average.isNA = true;
            } else {
              average.noScreeningData = false;
              average.y = totalScore / average.n;
            }
            if (!wasAnyNonHS) {
              // NOTE(fmazur) - property for "No screening is necessary for high schools" label
              average.isHS = true;
            }
          }
          return average;
        },
        {
          schoolId: school._id,
          y: 0,
          n: 0,
          isNA: false,
          isHS: false,
          noScreeningData: true,
          noData: true
        }
      );
      finalData[season.name].data.push(averageSchoolResult);
    });
    const hasAnyScreeningData =
      !finalData.Fall.data[i].noScreeningData ||
      !finalData.Winter.data[i].noScreeningData ||
      !finalData.Spring.data[i].noScreeningData;
    Object.values(finalData).forEach(season => {
      if (hasAnyScreeningData) {
        season.data[i].noScreeningData = false;
      }
    });
  });
  return Object.values(finalData);
}

export function getScreeningByGradeData({
  schoolYear,
  benchmarkPeriodsGroupId,
  bmPeriods,
  studentGroupsByGrade,
  studentGroupEnrollmentsByGroupId,
  assessmentResultsForSite,
  isDistrictReporting = false
}) {
  const screeningAssessmentResultsByGrade = getDocumentsByGradeTypeAndStatus(
    assessmentResultsForSite.filter(({ grade }) => grade !== "HS"),
    "benchmark",
    "COMPLETED"
  );
  return getBenchmarkPeriods(bmPeriods).map(bmPeriod => {
    const benchmarkPeriodEndTimestamp = getBenchmarkPeriodEndTimestamp({
      bmPeriod,
      schoolYear,
      benchmarkPeriodsGroupId
    });
    const colorBySeason = {
      fall: colors.orange,
      winter: colors.darkBlue,
      spring: colors.steelBlue30
    };
    return {
      name: bmPeriod.name,
      color: colorBySeason[bmPeriod.name.toLowerCase()],
      data: Object.entries(studentGroupsByGrade)
        .filter(([grade]) => isDistrictReporting || grade !== "HS")
        .map(([grade, studentGroups]) => {
          if (grade === "HS") {
            return {
              name: getGradeLabel(grade),
              y: 0,
              isNA: true,
              isHS: true
            };
          }
          const benchmarkEndDateTimestampsForStudentGroups = studentGroups.reduce((timestamps, sg) => {
            const timestamp = sg.history?.find(
              sgh => sgh.benchmarkPeriodId === bmPeriod._id && sgh.type === "benchmark"
            )?.whenEnded.on;
            if (timestamp) {
              timestamps.push(timestamp);
            }
            return timestamps;
          }, []);
          const mostRecentScreeningTimestampInAllGroups = Math.max(
            ...(benchmarkEndDateTimestampsForStudentGroups.length
              ? benchmarkEndDateTimestampsForStudentGroups
              : [benchmarkPeriodEndTimestamp])
          );
          const totalNumberOfStudentsInGrade = calculateTotalStudentsEnrolled({
            studentGroups,
            bmPeriodId: bmPeriod._id,
            enrollmentCutoffTimestamp: mostRecentScreeningTimestampInAllGroups,
            studentGroupEnrollmentsByGroupId
          });
          const numberOfScreeningsInGrade = studentGroups.reduce(
            (total, sg) =>
              total +
              (sg.history?.find(sgh => sgh.type === "benchmark" && sgh.benchmarkPeriodId === bmPeriod._id) ? 1 : 0),
            0
          );
          if (isDistrictReporting && numberOfScreeningsInGrade <= 0) {
            return {
              name: getGradeLabel(grade),
              n: totalNumberOfStudentsInGrade,
              y: undefined,
              isNA: true
            };
          }

          if (numberOfScreeningsInGrade <= 0 || totalNumberOfStudentsInGrade <= 0) {
            return {
              name: getGradeLabel(grade),
              y: 0,
              isNA: true
            };
          }
          const numberScreened = (screeningAssessmentResultsByGrade[grade] || [])
            .filter(sAR => sAR.benchmarkPeriodId === bmPeriod._id)
            .map(sAR => sAR.classwideResults.totalStudentsAssessedOnAllMeasures)
            .reduce((totalScreened, currentNumScreened) => totalScreened + currentNumScreened, 0);
          return {
            name: getGradeLabel(grade),
            n: totalNumberOfStudentsInGrade,
            y: getPercentage(numberScreened, totalNumberOfStudentsInGrade)
          };
        })
    };
  });
}

export function getCWIStudentGroupIdsByGrade(status, assessmentResultsForSite) {
  const firstAssessmentResultForStudentGroups = uniqBy(
    sortBy(
      assessmentResultsForSite.filter(arm => arm.type === "classwide" && arm.status === status),
      "created.on"
    ),
    "studentGroupId"
  );
  return firstAssessmentResultForStudentGroups.reduce((idListByGrade, { grade, studentGroupId }) => {
    // eslint-disable-next-line no-param-reassign
    (idListByGrade[grade] || (idListByGrade[grade] = [])).push(studentGroupId);
    return idListByGrade;
  }, {});
}

export function getNumberOfPassingAssessmentResults(studentGroupIds, assessmentResultsForSite) {
  return assessmentResultsForSite
    .filter(ar => studentGroupIds.includes(ar.studentGroupId) && ar.type === "classwide" && ar.status === "COMPLETED")
    .reduce((a, c) => a + (c.ruleResults.passed === true ? 1 : 0), 0);
}

export function getAverageNumberOfSkillsCompletedByGradeData({
  schoolsInOrg,
  ruleAssessmentIdsByGrade,
  schoolNameById
}) {
  const gradeDataForEachSchool = {};
  schoolsInOrg.forEach(school => {
    const gradesInSchool = school.gradesWithGroups.sort((a, b) => {
      return normalizeGrade(a) < normalizeGrade(b) ? -1 : 1;
    });
    gradesInSchool.forEach(grade => {
      if (!gradeDataForEachSchool[grade]) {
        gradeDataForEachSchool[grade] = [];
      }
      gradeDataForEachSchool[grade][school._id] = null;
    });
  });
  schoolsInOrg.forEach(school => {
    Object.entries(school.studentGroupsByGrade).forEach(([grade, studentGroups]) => {
      const groupsWithNumberOfPassedSkills = [];
      studentGroups.forEach(group => {
        const numberOfPassedSkills =
          group.history?.filter(historyItem => {
            const {
              enrolledStudentIds,
              assessmentResultMeasures: [{ medianScore, totalStudentsAssessed, targetScores, studentScores }],
              type
            } = historyItem;
            if (type !== "classwide") {
              return false;
            }
            return hasGroupPassedClasswideIntervention({
              medianScore,
              totalStudentsAssessed,
              targetScores,
              studentScores,
              numberOfEnrolledStudents: enrolledStudentIds.length
            });
          })?.length || 0;
        groupsWithNumberOfPassedSkills.push(numberOfPassedSkills);
      });

      gradeDataForEachSchool[grade][school._id] = getMedianNumber(groupsWithNumberOfPassedSkills);
    });
  });

  const finalData = {};
  const listOfColors = Object.values(colors);
  Object.entries(gradeDataForEachSchool).forEach(([grade, valuesBySchoolId]) => {
    const schoolIds = Object.keys(valuesBySchoolId);
    if (!finalData[grade]) {
      finalData[grade] = {
        categories: [],
        plotData: [
          {
            color: colors.orange,
            data: [],
            name: "Median number of skills completed"
          }
        ],
        numberOfClasswideSkills: ruleAssessmentIdsByGrade[grade].length
      };
    }
    schoolIds.forEach((schoolId, index) => {
      finalData[grade].categories.push(schoolNameById[schoolId] || "N/A");
      finalData[grade].plotData[0].data.push({
        schoolId,
        y: valuesBySchoolId[schoolId],
        color: listOfColors[index % 4]
      });
    });
  });
  return finalData;
}

export function renderNoScoresBanner({ message, isPrinting }) {
  return (
    <div className={`alert alert-info text-center ${isPrinting ? "text-black" : ""}`}>
      {message || "Scores are not yet available"}
    </div>
  );
}

export function renderSeasonToSeason({ seasonData, graphContext, schoolYear, isPrinting }) {
  const seasonToSeasonColors = [colors.orange, colors.darkBlue, colors.steelBlue30];
  const numberOfItems = Object.keys(seasonData).length - 1;
  let renderedAtLeastOneGradeGraph = false;
  return Object.entries(seasonData)
    .sort(([a], [b]) => {
      return normalizeGrade(a) < normalizeGrade(b) ? -1 : 1;
    })
    .map(([grade, growthForSchoolYears], i) => {
      const shouldDisplayGrade =
        intersection(
          growthForSchoolYears.map(s => parseInt(s.schoolYear)),
          [schoolYear - 1, schoolYear]
        ).length === 2;
      if (!growthForSchoolYears.length || !shouldDisplayGrade) {
        if (Object.entries(seasonData).length - 1 === i && !renderedAtLeastOneGradeGraph) {
          return renderNoScoresBanner("No screening data available");
        }
        return null;
      }
      const sortedGrowth = sortBy(growthForSchoolYears, "schoolYear");

      const numberOfSchoolYears = sortedGrowth.length;
      const series = sortedGrowth.map((schoolYearData, index) => {
        const { n } = schoolYearData.categories[0];
        return {
          name: `${schoolYearData.schoolYear}`,
          n,
          data: [],
          color: seasonToSeasonColors[numberOfSchoolYears - index - 1]
        };
      });

      const hasAnyStudentsAssessed = !!series.find(s => s.n !== 0);

      let maxNumberOfDigits = 0;

      sortedGrowth.forEach((growth, index) => {
        growth.scores.forEach((score, scoreIndex) => {
          delete score.color;
          const categoryN = growth.categories[scoreIndex]?.n || 0;
          if (maxNumberOfDigits < categoryN) {
            maxNumberOfDigits = categoryN;
          }
          series[index].data.push({ ...score, n: categoryN });
        });
      });
      const categories = sortedGrowth[0].categories.map(s => ({ name: s.name }));

      if (!hasAnyStudentsAssessed) {
        if (Object.entries(seasonData).length - 1 === i && !renderedAtLeastOneGradeGraph) {
          return renderNoScoresBanner("No screening data available");
        }
        return null;
      }
      if (!renderedAtLeastOneGradeGraph) {
        renderedAtLeastOneGradeGraph = true;
      }
      const getBreakPageStyleForEveryOtherItem = (index, nItems) => {
        return (index !== 0 && index % 2 === 0) || nItems === index ? "page-break-before" : "";
      };

      return (
        <div
          key={`${graphContext}Growth_${grade}_key`}
          className={getBreakPageStyleForEveryOtherItem(i, numberOfItems)}
        >
          <BarGrowthGraph
            chartName={`Percent of Students at the Instructional Target - ${graphContext} for Grade ${grade}`}
            chartId={`${graphContext}Growth_${grade}`}
            data={series}
            options={{
              height: categories.length * 120,
              groupPadding: 0.07,
              maxPointWidth: 25,
              categories,
              isLegendEnabled: true,
              categoryWidth: "350px",
              xAxisTitle: "Screening Assessments",
              yAxisTitle: "Percent at instructional target",
              useSeriesName: true,
              nOffset: -35 - 5 * maxNumberOfDigits.toString().length,
              shouldPrepareSpaceForN: true,
              ...(isPrinting ? { titleFontSize: "15px" } : {})
            }}
          />
          <br />
        </div>
      );
    });
}

export function getSkillProgressByGrade({ schoolsInOrg, assessmentNameById, classwideRules }) {
  const allGradesInSchools = uniq(schoolsInOrg.flatMap(s => s.gradesWithGroups)).sort((a, b) =>
    normalizeGrade(a) < normalizeGrade(b) ? -1 : 1
  );

  const finalGradeData = allGradesInSchools.reduce((acc, grade) => ({ ...acc, [grade]: [] }), {});
  const rulesByGrade = keyBy(classwideRules, "grade");

  schoolsInOrg.forEach(school => {
    const assessmentResultsByGroupId = groupBy(
      (school.completedAssessmentResults || []).filter(a => a.type === "classwide"),
      "studentGroupId"
    );

    school.gradesWithGroups
      .filter(grade => allGradesInSchools.includes(grade))
      .forEach(grade => {
        const shouldCalculateData = school.completedAssessmentResults.some(a => a.type === "classwide");
        if (!shouldCalculateData) {
          return;
        }

        const gradeData = getGradeDetailData({
          studentGroups: school.studentGroupsByGrade[grade],
          gradeSkills: rulesByGrade[grade]?.skills,
          assessmentResultsByGroupId,
          shouldCalculateData,
          assessmentNameById
        })?.summaryAll;

        if (gradeData) {
          finalGradeData[grade].push({
            columns: gradeData.columns.map(c => ({
              ...c,
              totalNumberOfStudents: c.numberOfStudents,
              mastery: c.masteryCount,
              instructional: c.instructionalCount,
              frustrational: c.frustrationalCount,
              absent: c.absentCount
            }))
          });
        }
      });
  });

  return Object.entries(finalGradeData).reduce((accumulator, [grade, gradeDataSets]) => {
    if (!gradeDataSets.length) {
      return { ...accumulator, [grade]: [] };
    }

    // NOTE(fmazur) - initialize first data set in grade
    const totalColumns = [...gradeDataSets[0].columns];
    const gradeDataSetsCount = gradeDataSets.length;

    // NOTE(fmazur) - skip first data set that has been initialized
    for (let i = 1; i < gradeDataSetsCount; i++) {
      const currentColumns = gradeDataSets[i]?.columns;

      totalColumns.forEach((col, index) => {
        const currentCol = currentColumns[index];
        if (currentCol?.numberOfGroupsWithData > 0) {
          col.numberOfSchoolsWithData = (col.numberOfSchoolsWithData || 0) + 1;
        }

        Object.entries(currentCol).forEach(([key, value]) => {
          if (typeof value === "number" && !isNaN(value)) {
            col[key] = (col[key] || 0) + value;
          }
        });
      });
    }

    totalColumns.forEach(col => {
      ["mastery", "instructional", "frustrational", "absent"].forEach(key => {
        if (col[key] && col.totalNumberOfStudents > 0) {
          col[key] = Math.round((col[key] / col.totalNumberOfStudents) * 100);
        }
      });
    });

    accumulator[grade] = totalColumns.map(c => {
      const shouldDisplayNumberOfStudents = ["mastery", "instructional", "frustrational", "absent"].some(key => c[key]);
      return {
        rowName: c.skillName,
        columns: [
          shouldDisplayNumberOfStudents ? c.totalNumberOfStudents : null,
          c.absent,
          c.frustrational,
          c.instructional,
          c.mastery
        ]
      };
    });
    return accumulator;
  }, {});
}

export function getClasswideProficiencyDataForSchools({ schoolsInOrg, ruleAssessmentIdsByGrade }) {
  const colorByStatus = {
    Started: colors.orange,
    Mastered: colors.darkBlue,
    Completed: colors.steelBlue30
  };
  const finalData = {
    "% of Classwide Interventions Started": {
      name: "% of Classes Beginning Classwide Interventions",
      color: colorByStatus.Started,
      data: []
    },
    "% of Skills Mastered": {
      name: "% of Skills Mastered",
      color: colorByStatus.Mastered,
      data: []
    },
    "% of Classwide Classes Finishing All Interventions": {
      name: "% of Classes Finishing all Interventions",
      color: colorByStatus.Completed,
      data: []
    }
  };

  schoolsInOrg.forEach(school => {
    const classwideProficiencyDataForSchool = getClasswideInterventionUseByGrade({
      assessmentResultsForSite: [...school.openAssessmentResults, ...school.completedAssessmentResults],
      studentGroupsByGrade: school.studentGroupsByGrade,
      gradesWithStudentGroupsInSite: school.gradesWithGroups,
      ruleAssessmentIdsByGrade
    });
    classwideProficiencyDataForSchool.forEach(dataSet => {
      let totalNumerator = 0;
      let totalDenominator = 0;
      const averageResult = dataSet.data.reduce(
        (average, set, index) => {
          if (average.noData && !set.isNA) {
            average.noData = false;
          }
          if (!set.isNA) {
            if (set.numerator > 0) {
              totalNumerator += set.numerator;
            }
            if (set.denominator > 0) {
              totalDenominator += set.denominator;
            }
          }
          if (dataSet.data.length - 1 === index) {
            average.y = (totalNumerator / (totalDenominator || 1)) * 100;
            if (!average.y && average.noData) {
              average.isNA = true;
              average.y = 0;
            }
          }
          return average;
        },
        {
          schoolId: school._id,
          y: 0,
          isNA: false,
          noData: true
        }
      );
      finalData[dataSet.name].data.push(averageResult);
    });
  });
  return Object.values(finalData);
}

export function getClasswideInterventionUseByGrade({
  gradesWithStudentGroupsInSite,
  assessmentResultsForSite,
  ruleAssessmentIdsByGrade,
  studentGroupsByGrade
}) {
  const options = [
    { name: "% of Classwide Interventions Started", color: colors.orange },
    { name: "% of Skills Mastered", color: colors.darkBlue },
    { name: "% of Classwide Classes Finishing All Interventions", color: colors.steelBlue30 }
  ];

  const sgIdsWithAtLeastOneCompletedCWISkillByGrade = getCWIStudentGroupIdsByGrade(
    "COMPLETED",
    assessmentResultsForSite
  );
  const studentGroupIdsWithOpenClasswideByGrade = getCWIStudentGroupIdsByGrade("OPEN", assessmentResultsForSite);
  const studentGroupIdsWithNotStartedCWI = gradesWithStudentGroupsInSite.reduce((sgIdsByGrade, grade) => {
    const completedIds = sgIdsWithAtLeastOneCompletedCWISkillByGrade[grade] || [];
    const openIds = studentGroupIdsWithOpenClasswideByGrade[grade] || [];
    const scheduledIds = difference(openIds, completedIds);
    if (scheduledIds.length) {
      // eslint-disable-next-line no-param-reassign
      sgIdsByGrade[grade] = scheduledIds;
    }
    return sgIdsByGrade;
  }, {});

  const proficiencyData = {
    cwiStarted: { ...options[0], data: [] },
    skillsMastered: { ...options[1], data: [] },
    cwiCompleted: { ...options[2], data: [] }
  };

  gradesWithStudentGroupsInSite.forEach(grade => {
    const hasClasswideInterventions =
      !!studentGroupIdsWithNotStartedCWI[grade] ||
      !!studentGroupIdsWithOpenClasswideByGrade[grade] ||
      !!sgIdsWithAtLeastOneCompletedCWISkillByGrade[grade];
    const gradeLabel = getGradeLabel(grade);
    if (!hasClasswideInterventions) {
      const NAMetric = {
        name: gradeLabel,
        y: 0,
        isNA: true
      };
      proficiencyData.cwiStarted.data.push(NAMetric);
      proficiencyData.skillsMastered.data.push(NAMetric);
      proficiencyData.cwiCompleted.data.push(NAMetric);
      return;
    }

    const studentGroups = studentGroupsByGrade[grade] || [];
    const studentGroupIds = studentGroups.map(s => s._id);
    const studentGroupIdsWithScheduledCWI = studentGroupIdsWithNotStartedCWI[grade] || [];
    const studentGroupIdsWithAtLeastOneSkillCompleted = sgIdsWithAtLeastOneCompletedCWISkillByGrade[grade] || [];
    const studentGroupIdsWithFinishedCWI = studentGroups
      .filter(
        s =>
          s.hasCompletedCWI ||
          (!s?.currentClasswideSkill?.assessmentId && s.history?.find(history => history.type === "classwide"))
      )
      .map(s => s._id);
    const studentGroupIdsWithScheduledOrStartedOrFinishedCWI = uniq([
      ...studentGroupIdsWithScheduledCWI,
      ...studentGroupIdsWithAtLeastOneSkillCompleted,
      ...studentGroupIdsWithFinishedCWI
    ]);

    let numberOfSkillsForGrade = ruleAssessmentIdsByGrade[grade]?.map(s => s.assessmentId)?.length;

    numberOfSkillsForGrade = studentGroupIdsWithFinishedCWI[0]
      ? getNumberOfPassingAssessmentResults([studentGroupIdsWithFinishedCWI[0]], assessmentResultsForSite)
      : numberOfSkillsForGrade;

    const numberOfPassedSkillsForGroups = getNumberOfPassingAssessmentResults(
      studentGroupIds,
      assessmentResultsForSite
    );
    const skillsCompletedPercentageForStudentGroups = getPercentage(
      numberOfPassedSkillsForGroups,
      numberOfSkillsForGrade * studentGroupIdsWithAtLeastOneSkillCompleted.length
    );
    const numberOfGroupsWithFinishedCWI = studentGroupIdsWithFinishedCWI.length;

    const startedMetric = {
      name: gradeLabel,
      y: getPercentage(
        studentGroupIdsWithAtLeastOneSkillCompleted.length,
        studentGroupIdsWithScheduledOrStartedOrFinishedCWI.length
      ),
      n: studentGroupIdsWithScheduledOrStartedOrFinishedCWI.length,
      numerator: studentGroupIdsWithAtLeastOneSkillCompleted.length,
      denominator: studentGroupIdsWithScheduledOrStartedOrFinishedCWI.length
    };
    proficiencyData.cwiStarted.data.push(startedMetric);
    const masteredMetric = {
      name: gradeLabel,
      y: skillsCompletedPercentageForStudentGroups,
      n: sgIdsWithAtLeastOneCompletedCWISkillByGrade[grade]?.length,
      denominator: numberOfSkillsForGrade * sgIdsWithAtLeastOneCompletedCWISkillByGrade[grade]?.length,
      numerator: numberOfPassedSkillsForGroups
    };
    proficiencyData.skillsMastered.data.push(masteredMetric);
    const completedMetric = {
      name: gradeLabel,
      y: getPercentage(numberOfGroupsWithFinishedCWI, studentGroupIdsWithScheduledOrStartedOrFinishedCWI.length),
      n: studentGroupIdsWithScheduledOrStartedOrFinishedCWI.length,
      numerator: numberOfGroupsWithFinishedCWI,
      denominator: studentGroupIdsWithScheduledOrStartedOrFinishedCWI.length
    };
    proficiencyData.cwiCompleted.data.push(completedMetric);
  });

  return [proficiencyData.cwiStarted, proficiencyData.skillsMastered, proficiencyData.cwiCompleted];
}

export function renderProficientOnExternalMeasureSpringChart(params) {
  const { district, state } = getProficientOnExternalMeasureSpringData(params);
  const stateMaxNumberOfDigits = getNumberOfDigitsForSeries(state.stateSeries);
  const districtMaxNumberOfDigits = getNumberOfDigitsForSeries(district.districtSeries);

  return (
    <React.Fragment>
      {district.shouldDisplayDistrictGraph ? (
        <React.Fragment>
          <div className="page-break-after">
            <BarGraph
              chartName={`Percent Proficient on External Measure <b>${district.districtAssessmentName}</b> - Spring`}
              data={district.districtSeries}
              chartId={`programEvaluation_proficientExternalMeasureDistrictSpring`}
              options={{
                ...getChartOptions({ shouldUseSecondaryLabel: true }),
                height: district.districtSeries[0].data.length * 60,
                nOffset: -35 - 5 * districtMaxNumberOfDigits,
                categoryOffset: -45 - 5 * districtMaxNumberOfDigits,
                categoryOffsetY: -10,
                animation: false
              }}
            />
          </div>
          {!params.isPrinting ? <br /> : null}
        </React.Fragment>
      ) : null}
      {state.shouldDisplayStateGraph ? (
        <div className="page-break-after">
          <BarGraph
            chartName={`Percent Proficient on External Measure <b>${state.stateAssessmentName}</b> - Spring`}
            data={state.stateSeries}
            chartId={`programEvaluation_proficientExternalMeasureStateSpring`}
            options={{
              ...getChartOptions({ shouldUseSecondaryLabel: true }),
              height: state.stateSeries[0].data.length * 60,
              nOffset: -35 - 5 * stateMaxNumberOfDigits,
              categoryOffset: -45 - 5 * stateMaxNumberOfDigits,
              categoryOffsetY: -10,
              animation: false
            }}
          />
        </div>
      ) : null}
    </React.Fragment>
  );
}

export function renderProficientOnExternalMeasureSpringByYearGraph(params) {
  const { state, district } = getProficientOnExternalMeasureSpringByYearData(params);
  const stateMaxNumberOfDigits = getNumberOfDigitsForSeries(state.stateSeries);
  const districtMaxNumberOfDigits = getNumberOfDigitsForSeries(district.districtSeries);

  return (
    <React.Fragment>
      {state.shouldDisplayState || district.shouldDisplayDistrict ? (
        <ul>
          <li>Do you see growth in the percentage of students who are proficient from year to year?</li>
        </ul>
      ) : null}
      {district.shouldDisplayDistrict ? (
        <React.Fragment>
          <div className="page-break-after">
            <BarGraph
              chartName={`Percent Proficient on External Measure <b>${district.districtAssessmentName}</b> - Spring By Year`}
              data={district.districtSeries}
              chartId={`programEvaluation_proficientExternalMeasureDistrictSpringByYear`}
              options={{
                ...getChartOptions({ shouldUseSecondaryLabel: true }),
                height: district.districtSeries[0].data.length * 80,
                nOffset: -35 - 5 * districtMaxNumberOfDigits,
                categoryOffset: -45 - 5 * districtMaxNumberOfDigits,
                categoryOffsetY: -10,
                animation: false
              }}
            />
          </div>
          {!params.isPrinting ? <br /> : null}
        </React.Fragment>
      ) : null}
      {state.shouldDisplayState ? (
        <div>
          <BarGraph
            chartName={`Percent Proficient on External Measure <b>${state.stateAssessmentName}</b> - Spring By Year`}
            data={state.stateSeries}
            chartId={`programEvaluation_proficientExternalMeasureStateSpringByYear`}
            options={{
              ...getChartOptions({ shouldUseSecondaryLabel: true }),
              height: state.stateSeries[0].data.length * 80,
              nOffset: -35 - 5 * stateMaxNumberOfDigits,
              categoryOffset: -45 - 5 * stateMaxNumberOfDigits,
              categoryOffsetY: -10,
              animation: false
            }}
          />
        </div>
      ) : null}
    </React.Fragment>
  );
}

export function getIndividualInterventionsMarkup({
  assessmentResultsForSite = [],
  gradesWithStudentGroupsInSite,
  studentGroupsByGrade,
  studentsByGroupId,
  userRolesById
}) {
  const individualInterventionRows = [];
  gradesWithStudentGroupsInSite.forEach(grade => {
    let totalNeeding = 0;
    let totalGetting = 0;
    let totalCompleted = 0;
    let improvingStudentsValues = [];
    const idsOfStudentsGettingInterventions = [];
    let totalImproving = 0;
    const studentGroups = studentGroupsByGrade[grade] || [];
    const studentGroupIds = studentGroups.map(sg => sg._id);
    const manuallyScheduledStudentIds = getStudentIdsManuallyRecommendedForIndividualIntervention({
      assessmentResults: assessmentResultsForSite.filter(ar => studentGroupIds.includes(ar.studentGroupId)),
      studentGroupsInSchool: studentGroups,
      userRolesById
    });

    const idsOfStudentsWithOpenInterventions = assessmentResultsForSite
      .filter(ar => ar.type === "individual" && ar.grade === grade && ar.status === "OPEN")
      .map(ar => ar.studentId);

    const completedIndividualInterventions = assessmentResultsForSite.filter(
      ar => ar.status === "COMPLETED" && ar.type === "individual" && ar.individualSkills?.interventions?.length
    );
    const idsOfStudentsWithAtLeastOneIndividualInterventionScore = [
      ...new Set(completedIndividualInterventions.map(s => s.studentId))
    ];
    studentGroups.forEach(sg => {
      const studentIdsInGroup = (studentsByGroupId[sg._id] || []).map(s => s._id);
      if (sg.individualInterventionQueue?.length) {
        totalNeeding += sg.individualInterventionQueue.length;
      }

      const completedStudentIds = (studentsByGroupId[sg._id] || [])
        .filter(s => s.currentSkill && !s.currentSkill.benchmarkAssessmentId)
        .map(s => s._id);
      const studentIdsWithAtLeastOneSkillTreeFinished = getStudentIdsThatFinishedAtLeastOneSkillTree(
        assessmentResultsForSite.filter(
          ar => ar.status === "COMPLETED" && ar.type === "individual" && studentIdsInGroup.includes(ar.studentId)
        )
      );

      const gettingStudentIds = studentIdsInGroup.filter(sId =>
        idsOfStudentsWithAtLeastOneIndividualInterventionScore.includes(sId)
      );
      idsOfStudentsGettingInterventions.push(...gettingStudentIds);
      const gettingIds = difference(gettingStudentIds, studentIdsWithAtLeastOneSkillTreeFinished);
      const completedIds = studentIdsWithAtLeastOneSkillTreeFinished;
      totalNeeding += difference([...gettingIds, ...completedIds], manuallyScheduledStudentIds).length;
      totalGetting += gettingIds.length + completedIds.length;
      totalCompleted += completedIds.length;
      totalImproving += difference(
        uniq([...gettingStudentIds, ...studentIdsWithAtLeastOneSkillTreeFinished]),
        completedStudentIds
      ).length;
      const eligibleImprovingStudents = studentIdsInGroup.filter(sId => gettingStudentIds.includes(sId));
      improvingStudentsValues.push(
        eligibleImprovingStudents
          .map(s => {
            const scoringTrend = utils.getScoringTrend({ group: s, type: "individual", numStudents: 1 });
            if (scoringTrend) {
              return parseInt(scoringTrend);
            }
            return undefined;
          })
          .filter(s => s || s === 0)
      );
    });

    const idsOfStudentsWithOnlyOpenInterventions = idsOfStudentsWithOpenInterventions.filter(
      studentId => !idsOfStudentsGettingInterventions.includes(studentId)
    );
    totalNeeding += idsOfStudentsWithOnlyOpenInterventions.length;

    improvingStudentsValues = flatten(improvingStudentsValues);
    if (totalNeeding || totalGetting || totalCompleted) {
      individualInterventionRows.push({
        grade,
        numNeeding: totalNeeding,
        numGetting: totalGetting,
        numCompleting: totalCompleted,
        averageScoresIncreasing: improvingStudentsValues.length
          ? sum(improvingStudentsValues) / Math.max(totalImproving, improvingStudentsValues.length)
          : undefined
      });
    }
  });

  return individualInterventionRows;
}

export function getStudentIdsThatFinishedAtLeastOneSkillTree(assessmentResults) {
  const assessmentResultsByStudentId = groupBy(assessmentResults, "studentId");
  const studentIdsThatFinishedAtLeastOneSkillTree = [];
  Object.entries(assessmentResultsByStudentId).forEach(([studentId, assessmentResultsForStudent]) => {
    const passedSkillTreeArm = assessmentResultsForStudent.find(ar => {
      if (!ar.ruleResults.passed) {
        return false;
      }
      const { benchmarkAssessmentId } = ar?.individualSkills || {};
      if (!benchmarkAssessmentId) {
        return false;
      }
      const { meetsTarget } = ar.measures.find(m => m.assessmentId === benchmarkAssessmentId)?.studentResults[0] || {};
      return !!meetsTarget;
    });
    if (passedSkillTreeArm) {
      studentIdsThatFinishedAtLeastOneSkillTree.push(studentId);
    }
  });
  return studentIdsThatFinishedAtLeastOneSkillTree;
}

export function getIndividualInterventionImplementationForSchools({ schools = [] }) {
  const individualInterventionImplementationForSchools = [];

  schools.forEach(
    ({
      _id,
      name,
      students,
      numberOfStudentsInIndividualInterventionQueue = 0,
      manuallyScheduledStudentIds,
      completedAssessmentResults,
      openAssessmentResults
    }) => {
      let totalNeeding = 0;
      let totalGetting = 0;
      let totalCompleted = 0;
      let improvingStudentsValues = [];
      let totalImproving = 0;

      const mergedAssessmentResults = [...openAssessmentResults, ...completedAssessmentResults];
      const idsOfStudentsWithOpenInterventions = uniq(
        mergedAssessmentResults.filter(ar => ar.type === "individual" && ar.status === "OPEN").map(ar => ar.studentId)
      );

      const completedIndividualInterventions = mergedAssessmentResults.filter(
        ar => ar.status === "COMPLETED" && ar.type === "individual" && ar.individualSkills?.interventions?.length
      );
      const idsOfStudentsWithAtLeastOneIndividualInterventionScore = [
        ...new Set(completedIndividualInterventions.map(s => s.studentId))
      ];

      const idsOfStudentsWithOnlyOpenInterventions = idsOfStudentsWithOpenInterventions.filter(
        id => !idsOfStudentsWithAtLeastOneIndividualInterventionScore.includes(id)
      );
      totalNeeding += numberOfStudentsInIndividualInterventionQueue + idsOfStudentsWithOnlyOpenInterventions.length;

      const gettingStudentIds = students
        .filter(s => idsOfStudentsWithAtLeastOneIndividualInterventionScore.includes(s._id))
        .map(s => s._id);
      const completedStudentIds = students
        .filter(s => s.currentSkill && !s.currentSkill.benchmarkAssessmentId)
        .map(s => s._id);
      const studentIdsWithAtLeastOneSkillTreeFinished = getStudentIdsThatFinishedAtLeastOneSkillTree(
        completedAssessmentResults.filter(ar => ar.type === "individual")
      );
      const gettingIds = difference(gettingStudentIds, studentIdsWithAtLeastOneSkillTreeFinished);
      const completedIds = studentIdsWithAtLeastOneSkillTreeFinished;
      totalNeeding += difference([...gettingIds, ...completedIds], manuallyScheduledStudentIds).length;
      totalGetting += gettingIds.length + completedIds.length;
      totalCompleted += completedIds.length;
      totalImproving += difference(
        uniq([...gettingStudentIds, ...studentIdsWithAtLeastOneSkillTreeFinished]),
        completedStudentIds
      ).length;

      const eligibleImprovingStudents = students.filter(s => gettingStudentIds.includes(s._id));
      improvingStudentsValues.push(
        eligibleImprovingStudents
          .map(s => {
            const scoringTrend = utils.getScoringTrend({ group: s, type: "individual", numStudents: 1 });
            if (scoringTrend) {
              return parseInt(scoringTrend);
            }
            return undefined;
          })
          .filter(s => s || s === 0)
      );
      improvingStudentsValues = flatten(improvingStudentsValues);

      individualInterventionImplementationForSchools.push({
        _id,
        name,
        numNeeding: totalNeeding,
        numGetting: totalGetting,
        numCompleting: totalCompleted,
        averageScoresIncreasing: improvingStudentsValues.length
          ? sum(improvingStudentsValues) / Math.max(totalImproving, improvingStudentsValues.length)
          : undefined
      });
    }
  );

  return individualInterventionImplementationForSchools;
}

export function renderProficientOnExternalMeasureFallToSpringChart(params) {
  const {
    shouldDisplayFallToSpring,
    districtAssessmentName,
    districtSeries
  } = getProficientOnExternalMeasureFallToSpringData(params);
  const maxNumberOfDigits = getNumberOfDigitsForSeries(districtSeries);
  return shouldDisplayFallToSpring ? (
    <React.Fragment>
      <div className="page-break-after">
        <ul>
          <li>Do you see growth across the school year on your external measure of math proficiency?</li>
          <li>
            Do you see a correlation between those grade levels that had high SpringMath implementation and dosage and
            growth on the external measures?
          </li>
        </ul>
        <div>
          <BarGraph
            chartName={`Percent Proficient on External Measure <b>${districtAssessmentName}</b> - Fall to Spring`}
            data={districtSeries}
            chartId={`programEvaluation_proficientExternalMeasureFallToSpring`}
            options={{
              ...getChartOptions({ shouldUseSecondaryLabel: true }),
              height: districtSeries[0].data.length * 60,
              nOffset: -35 - 5 * maxNumberOfDigits,
              categoryOffset: -45 - 5 * maxNumberOfDigits,
              categoryOffsetY: -10,
              animation: false
            }}
          />
        </div>
      </div>
      {!params.isPrinting ? <br /> : null}
    </React.Fragment>
  ) : null;
}

export function renderGrowthByGradeGraphs({
  gradesWithStudentGroupsInSite,
  studentGroupsByGrade,
  assessmentGrowths,
  assessments,
  isPrinting,
  bmPeriods
}) {
  return gradesWithStudentGroupsInSite
    .map(grade => {
      const mergedGrowthResults = getFallToSpringGrowthResultsForGrade({
        studentGroupsByGrade,
        assessmentGrowths,
        assessments,
        grade,
        bmPeriods
      });

      // If no growth results available, skip this grade
      if (!mergedGrowthResults) {
        return null;
      }

      const { chartId, chartItems } = getGrowthChartMarkup("all", mergedGrowthResults, true, grade);

      let subtitleText = "";
      if (grade !== "HS") {
        subtitleText = getGrowthGraphSubtitle(chartItems.scores);
      }
      const colorsByType = {
        fall: colors.orange,
        winter: colors.darkBlue,
        spring: colors.steelBlue30,
        classwide: colors.violet
      };

      return (
        <React.Fragment key={`seasonal_growth_container_${grade}`}>
          <div className="page-break-after" key={`seasonal_growth_${grade}`}>
            <h5 className="text-center">Seasonal Growth for Grade {grade}</h5>
            <GrowthChartLegend comparisonPeriod={"all"} />
            <BarGrowthGraph
              chartName={""}
              chartId={chartId}
              data={[
                {
                  showInLegend: false,
                  data: chartItems.scores.map(s => (s ? { ...s, color: colorsByType[s.name] } : null))
                }
              ]}
              options={{
                categories: chartItems.categories.map(c => (c === null ? c : { ...c, n: c.n ? c.n : 0 })),
                height: chartItems.categories.length * (isPrinting ? 40 : 30),
                categoryWidth: "600px",
                usePlotLines: true,
                subtitleText,
                yAxisTitle: "% at/above instructional target"
              }}
            />
          </div>
          {!isPrinting ? <br /> : null}
        </React.Fragment>
      );
    })
    .filter(Boolean);
}
