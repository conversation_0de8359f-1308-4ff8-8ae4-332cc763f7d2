export function getGradeLabel(grade) {
  switch (grade) {
    case "00":
    case "K":
      return "Kindergarten";
    case "HS":
      return "High School";
    case "01":
      return "1st Grade";
    case "02":
      return "2nd Grade";
    case "03":
      return "3rd Grade";
    case "04":
    case "05":
    case "06":
    case "07":
    case "08":
      return `${parseInt(grade)}th Grade`;
    default:
      return "Unknown Grade";
  }
}

export function getPercentage(num, dividedBy) {
  if (num === undefined || !dividedBy) {
    return undefined;
  }
  const percentage = dividedBy ? Number(((num / dividedBy) * 100).toFixed(2)) : 0;
  return percentage > 100 ? 100 : percentage;
}

export function getGrowthGraphSubtitle(dataItems) {
  let subtitleText = "";
  const isAnyFinalClasswideNA = !!dataItems.find(s => s?.labelText === "N/A" && s?.name === "classwide");
  const isAnyScreeningNA = !!dataItems.find(
    s => s?.labelText === "N/A" && ["fall", "winter", "spring"].includes(s?.name)
  );
  if (isAnyFinalClasswideNA && isAnyScreeningNA) {
    subtitleText =
      "N/A indicates that the screening assessment was not completed or that the class has not yet mastered this skill in the classwide sequence";
  } else if (isAnyFinalClasswideNA) {
    subtitleText = "N/A indicates that the group has not yet mastered this skill in the classwide sequence";
  } else if (isAnyScreeningNA) {
    subtitleText = "N/A indicates that the screening assessment was not completed";
  }
  return subtitleText;
}
