import React from "react";
import PropTypes from "prop-types";
import ProgramEvaluationIndividualInterventionRow from "./program-evaluation-individual-intervention-row";

const ProgramEvaluationIndividualInterventionTable = ({ rows }) => {
  return (
    <div className="tblIntvSummaryTable" data-testid={"programEvaluation_individualInterventionTable"}>
      <div className="row row-margin-fixed rowIndvSummaryHeading">
        <div className="col-1 header text-center">Grade</div>
        <div className="col-3 header">Individual Interventions Recommended</div>
        <div className="col-3 header">Individual Interventions Started</div>
        <div className="col-3 header">Individual Interventions Completed</div>
        <div className="col-2 header">% of Scores Increasing</div>
      </div>
      <div className="individual-interventions-group">
        {rows.map(row => (
          <ProgramEvaluationIndividualInterventionRow key={`groupIndividualInterventions_${row.grade}`} {...row} />
        ))}
      </div>
    </div>
  );
};

ProgramEvaluationIndividualInterventionTable.propTypes = {
  rows: PropTypes.array
};

export default ProgramEvaluationIndividualInterventionTable;
