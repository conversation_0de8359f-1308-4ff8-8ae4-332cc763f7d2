import React, { useCallback, useState, useRef, useEffect } from "react";
import * as PropTypes from "prop-types";
import * as yup from "yup";
import { Formik } from "formik";
import { Form } from "react-bootstrap";
import { debounce, omit } from "lodash";
import TooltipWrapper from "../../components/tooltip-wrapper";
import { FieldError } from "../data-admin/field-error";
import { colors } from "../../../api/constants";
import NewsBanner from "../../components/dashboard/news-banner";

// eslint-disable-next-line no-useless-escape
const urlPattern = /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/;

export const defaultColors = {
  messageColor: colors.orange,
  messageTextColor: colors.white,
  buttonIconColor: colors.white,
  buttonColor: colors.brightBlue
};

function getStartingState(props) {
  let defaultMessageState = {
    messageColor: defaultColors.messageColor,
    messageTextColor: defaultColors.messageTextColor,
    buttonIconColor: defaultColors.buttonIconColor,
    buttonColor: defaultColors.buttonColor,
    messageActive: false,
    messageContent: "",
    learnMoreUrl: "",
    isLearnMoreActive: false,
    isSupportLinkActive: false
  };
  if (!props.isNewMessage) {
    defaultMessageState = {
      ...defaultMessageState,
      ...omit(props.messageProps, "_id")
    };
  }
  return defaultMessageState;
}

export default function ManageMessageRow(props) {
  const [messageParams, setMessageParams] = useState(getStartingState(props));
  const handleDebouncedTouchRef = useRef();
  const handleDebouncedValidationRef = useRef();
  const timeoutRef = useRef();

  useEffect(() => {
    return () => {
      handleDebouncedTouchRef.current?.cancel();
      handleDebouncedValidationRef.current?.cancel();
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const submitMessage = (values, actions) => {
    const localMessageParams = { ...values };
    const messageProps = {
      ...omit(localMessageParams, "messageActive"),
      ...(localMessageParams.type ? { type: localMessageParams.type } : {})
    };
    if (!props.isNewMessage) {
      messageProps._id = props.messageProps._id;
    }
    return props.submit(messageProps, () => {
      setMessageParams(getStartingState(props));
      actions.resetForm();
    });
  };

  const { messageProps, isNewMessage, removeMessage, changeActiveMessage } = props;

  const validationSchema = yup.object().shape({
    messageContent: yup
      .string()
      .trim()
      .label("Message Content")
      .required("Message Content is required"),
    learnMoreUrl: yup
      .string()
      .trim()
      .matches(urlPattern, "Make sure the provided url starts with http(s)://")
      .optional()
      .test(
        "required-if-learnMoreActive",
        "Required field if Learn More is active and must start with http(s)://",
        function(value) {
          const { isLearnMoreActive } = this.parent;
          if (isLearnMoreActive) {
            return !!value && urlPattern.test(value);
          }
          return true;
        }
      )
      .label("Learn More URL"),
    messageColor: yup.string().label("Message Color"),
    messageTextColor: yup.string().label("Message Text Color"),
    buttonColor: yup.string().label("Button Color"),
    buttonIconColor: yup.string().label("Button Icon Color"),
    isLearnMoreActive: yup
      .bool()
      .optional()
      .label("Is Learn More Active"),
    isSupportLinkActive: yup
      .bool()
      .optional()
      .label("Is Support Link Active")
  });

  handleDebouncedTouchRef.current = debounce((setFieldTouched, fieldName) => {
    setFieldTouched(fieldName, true);
  }, 750);

  const handleDebouncedTouch = useCallback(handleDebouncedTouchRef.current, []);

  const colorElementStyle = "border border-1 border-black d-inline-block";

  return (
    <Formik validationSchema={validationSchema} onSubmit={submitMessage} initialValues={getStartingState(props)}>
      {formProps => {
        const {
          values,
          handleChange,
          errors,
          touched,
          handleBlur,
          setFieldTouched,
          handleSubmit,
          setTouched,
          dirty,
          validateForm
        } = formProps;

        const hasErrors = Object.keys(errors).length > 0;
        const hasUnsavedChanges = Object.keys(touched).length > 0 && dirty;

        handleDebouncedValidationRef.current = debounce(async () => {
          const formErrors = await validateForm();
          Object.keys(formErrors).forEach(field => {
            setFieldTouched(field, true, false);
          });
        }, 750);

        const handleDebouncedValidation = useCallback(handleDebouncedValidationRef.current, []);

        const handleChangeWithDebounce = e => {
          const { name } = e.target;
          handleChange(e);
          handleDebouncedTouch(setFieldTouched, name);

          handleDebouncedValidation();
        };

        const handleCheckboxChange = e => {
          const { name } = e.target;
          handleChange(e);
          setFieldTouched(name, true);

          if (name === "isLearnMoreActive") {
            setFieldTouched(name, true);
            setFieldTouched("learnMoreUrl", true);
            // NOTE(fmazur) - defer validation to ensure yup tests finish
            timeoutRef.current = setTimeout(() => {
              validateForm();
            }, 100);
          }
        };

        const handleSave = e => {
          handleSubmit(e);
          setTouched({}, false);
        };

        return (
          <React.Fragment>
            <tr className="table-row-centered borderless">
              <td className="text-center">
                {!isNewMessage ? (
                  <Form.Check
                    type={messageProps.type === "rostering" ? "checkbox" : "radio"}
                    name="messageActive"
                    data-testid="setMessageActive"
                    checked={messageProps.messageActive}
                    onClick={() => changeActiveMessage(messageProps?._id, messageProps.type)}
                    onChange={() => {}}
                  />
                ) : null}
              </td>
              <td className="col-md-3">
                <Form.Group className="position-relative">
                  <Form.Control
                    as="textarea"
                    className={`dynamic-size${errors.messageContent ? " withError" : ""}`}
                    placeholder="Message text"
                    name="messageContent"
                    onChange={handleChangeWithDebounce}
                    onBlur={handleBlur}
                    value={values.messageContent}
                    isInvalid={!!errors.messageContent && touched.messageContent}
                    rows={2}
                    data-overflow={values.messageContent.split("\n").length > 2 || values.messageContent.length > 120}
                  />
                  <FieldError>{errors.messageContent}</FieldError>
                </Form.Group>
              </td>
              <td className="col-md-3">
                <Form.Group className="position-relative">
                  <Form.Control
                    as="textarea"
                    className={`dynamic-size${errors.learnMoreUrl ? " withError" : ""}`}
                    placeholder="Learn More Url"
                    name="learnMoreUrl"
                    title="URL starting with http(s)://"
                    onChange={handleChangeWithDebounce}
                    onBlur={handleBlur}
                    value={values.learnMoreUrl}
                    isInvalid={!!errors.learnMoreUrl && touched.learnMoreUrl}
                    rows={2}
                    data-overflow={values.learnMoreUrl.split("\n").length > 2 || values.learnMoreUrl.length > 120}
                  />
                  <FieldError>{errors.learnMoreUrl}</FieldError>
                </Form.Group>
              </td>
              <td className="text-center">
                <Form.Control
                  type="color"
                  name="messageColor"
                  className={colorElementStyle}
                  onChange={handleChangeWithDebounce}
                  onBlur={handleBlur}
                  value={values.messageColor}
                  isInvalid={!!errors.messageColor && touched.messageColor}
                />
              </td>
              <td className="text-center">
                <Form.Control
                  type="color"
                  name="messageTextColor"
                  className={colorElementStyle}
                  onChange={handleChangeWithDebounce}
                  onBlur={handleBlur}
                  value={values.messageTextColor}
                  isInvalid={!!errors.messageTextColor && touched.messageTextColor}
                />
              </td>
              <td className="text-center">
                <Form.Control
                  type="color"
                  name="buttonColor"
                  className={colorElementStyle}
                  onChange={handleChangeWithDebounce}
                  onBlur={handleBlur}
                  value={values.buttonColor}
                  isInvalid={!!errors.buttonColor && touched.buttonColor}
                />
              </td>
              <td className="text-center">
                <Form.Control
                  type="color"
                  name="buttonIconColor"
                  className={colorElementStyle}
                  onChange={handleChangeWithDebounce}
                  onBlur={handleBlur}
                  value={values.buttonIconColor}
                  isInvalid={!!errors.buttonIconColor && touched.buttonIconColor}
                />
              </td>
              <td className="text-center">
                <Form.Check
                  type="checkbox"
                  name="isLearnMoreActive"
                  data-testid="setIsLearnMoreActive"
                  checked={values.isLearnMoreActive}
                  onChange={handleCheckboxChange}
                />
              </td>
              <td className="text-center">
                <Form.Check
                  type="checkbox"
                  name="isSupportLinkActive"
                  data-testid="setIsSupportLinkActive"
                  checked={values.isSupportLinkActive}
                  onChange={handleCheckboxChange}
                />
              </td>
              {isNewMessage ? (
                <td>
                  <button
                    className="btn btn-sm btn-success"
                    type="submit"
                    onClick={handleSave}
                    data-testid="add-new-message"
                  >
                    <TooltipWrapper
                      tooltipText="Add New Message"
                      text={<i className="fa fa-plus" />}
                      customClassName={""}
                      isClickTriggerEnabled={false}
                    />
                  </button>
                </td>
              ) : (
                <td>
                  <div className="d-flex flex-row gap-1">
                    <button
                      className="btn btn-xs btn-success"
                      type="submit"
                      onClick={handleSave}
                      disabled={!hasUnsavedChanges || hasErrors}
                      data-testid="save-message"
                    >
                      <TooltipWrapper
                        tooltipText="Save Changes"
                        text={<i className="fa fa-save" />}
                        customClassName={""}
                        isClickTriggerEnabled={false}
                      />
                    </button>
                    {messageParams.type === "rostering" ? null : (
                      <button
                        className="btn btn-xs btn-danger"
                        type="submit"
                        onClick={() => removeMessage(messageProps._id)}
                      >
                        <TooltipWrapper
                          tooltipText="Delete Message"
                          text={<i className="fa fa-trash" />}
                          customClassName={""}
                          isClickTriggerEnabled={false}
                        />
                      </button>
                    )}
                  </div>
                </td>
              )}
            </tr>
            <tr>
              <td colSpan="10">
                <div className={messageProps?.type === "rostering" ? "rostering-news-banner" : ""}>
                  <NewsBanner message={{ ...messageProps, ...values }} />
                </div>
              </td>
            </tr>
          </React.Fragment>
        );
      }}
    </Formik>
  );
}

ManageMessageRow.propTypes = {
  submit: PropTypes.func.isRequired,
  isNewMessage: PropTypes.bool,
  messageProps: PropTypes.object,
  changeActiveMessage: PropTypes.func,
  removeMessage: PropTypes.func
};
