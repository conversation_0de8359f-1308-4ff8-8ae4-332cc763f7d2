import { Meteor } from "meteor/meteor";
import React, { Component } from "react";
import PropTypes from "prop-types";

import { Link } from "react-router-dom";
import { ninjalog } from "/imports/api/utilities/utilities";
import IndividualInterventionStudentsAdmin from "/imports/ui/components/admin-view/individual-intervention-students-admin.jsx";

export default class IndividualOverviewItem extends Component {
  componentDidMount() {
    if (this.props.group) {
      this.getIndividualStats();
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.group && this.props.group && prevProps.group._id !== this.props.group._id) {
      this.getIndividualStats();
    }
  }

  getIndividualStats = () => {
    Meteor.call("CalculateIndividualStats", this.props.group, (err, resp) => {
      if (err) {
        ninjalog.error({
          msg: "error getting individual intervention stats",
          context: "admin"
        });
      } else {
        this.setState({ individualStats: resp });
      }
    });
  };

  ownerName() {
    let ownerName;
    if (this.props.ownerIds !== undefined) {
      const user = Meteor.users.findOne({ _id: this.props.ownerIds[0] });
      if (user) {
        ownerName = `${user.profile.name.first.slice(0, 1)} ${user.profile.name.last}`;
      }
    }
    return ownerName;
  }

  render() {
    const studentsInIntervention =
      this.props.students &&
      this.props.students.filter(
        st =>
          st.currentSkill &&
          this.props.group.currentAssessmentResultIds.some(
            assessmentResultId => assessmentResultId === st.currentSkill.assessmentResultId
          )
      );
    if (this.state && this.state.individualStats) {
      return (
        <div className="individual-interventions-group">
          <div className="row rowIndvSummary" data-testid="rowIndvSummary">
            <div className="col-md-4">
              <Link
                to={`/${this.props.group.orgid}/site/${this.props.group.siteId}/student-groups/${this.props.group._id}`}
              >
                {this.ownerName()} ({this.props.group.name})
              </Link>
            </div>
          </div>
          {studentsInIntervention && studentsInIntervention.length ? (
            <IndividualInterventionStudentsAdmin
              {...this.props}
              individualInterventionStudents={studentsInIntervention}
              groupStats={this.state.individualStats}
              studentGroup={this.props.group}
              updateStats={this.getIndividualStats}
            />
          ) : null}
        </div>
      );
    }
    return <div>Loading...</div>;
  }
}

IndividualOverviewItem.propTypes = {
  group: PropTypes.shape({
    _id: PropTypes.string,
    name: PropTypes.string,
    siteId: PropTypes.string,
    orgid: PropTypes.string,
    currentAssessmentResultIds: PropTypes.array
  }),
  ownerIds: PropTypes.arrayOf(PropTypes.string),
  currentBMPeriod: PropTypes.object,
  students: PropTypes.array,
  assessmentResults: PropTypes.array
};
