import React, { Component } from "react";
import PropTypes from "prop-types";
import Highcharts from "highcharts/highstock";
import _isNumber from "lodash/isNumber";
import { getLegendText, isOnPrintPage } from "../../../utilities";

function isCategoryValid(categoryValue) {
  // With Highcharts API a null value is assigned a number under the hood that indicates a position in the array of categories.
  // That means we have to check if the value is something else than a number here.
  return !_isNumber(categoryValue);
}

function getCategoryValue(value, isFirst = false) {
  const maxLineLength = 35;
  const shouldDisplayInline = isFirst && value && value.name && value.name.length > maxLineLength;
  return isCategoryValid(value)
    ? `<div><p>${value.name}</p>${
        value.n ? `${shouldDisplayInline ? " " : "<br />"}<b>(n = ${value.n})</b></div>` : ""
      }`
    : "";
}

export default class GrowthChart extends Component {
  // When the DOM is ready, create the chart.
  componentDidMount() {
    this.updateChart();
  }

  // Update the chart if the component is updated
  componentDidUpdate() {
    this.updateChart();
  }

  //  Destroy chart before unmount.
  componentWillUnmount() {
    this.chart.destroy();
  }

  getSeriesLegend = (scores = []) => {
    const series = {};
    if (this.props.generateAllPeriods) {
      return [
        { name: "Fall Screening", color: "#f9bf68", enableMouseTracking: false },
        { name: "Winter Screening", color: "#70a1d9", enableMouseTracking: false },
        { name: "Spring Screening", color: "#aee57c", enableMouseTracking: false },
        { name: "Final Classwide Intervention", color: "#541f21", enableMouseTracking: false }
      ];
    }
    scores.forEach(score => {
      if (score && !series[score.name]) {
        series[score.name] = {
          name: getLegendText(score.name),
          color: score.color,
          enableMouseTracking: false
        };
      }
    });
    return Object.values(series);
  };

  updateChart() {
    const { scores, categories } = this.props.chartItems;
    const isPrinting = isOnPrintPage();
    const customPrintingTextColor = isPrinting ? { color: "#000" } : {};

    const chartTitle = this.props.shouldDisplayModifiedTitle
      ? `Seasonal Growth for Grade ${this.props.grade}`
      : "Seasonal Growth";
    const style = this.props.shouldDisplayModifiedTitle
      ? {
          style: {
            textOverflow: "none",
            fontSize: "8px",
            ...customPrintingTextColor
          },
          rotation: -65
        }
      : {
          style: { textOverflow: "none", fontSize: "10px", ...customPrintingTextColor },
          rotation: -60
        };

    this.chart = new Highcharts.Chart(this.props.chartId, {
      credits: {
        enabled: false
      },
      chart: {
        type: "column",
        height: 450,
        zoomType: "xy",
        zooming: {
          mouseWheel: false
        }
      },
      accessibility: {
        enabled: false
      },
      subtitle: {
        text: this.props.subtitleText || "",
        style: {
          ...customPrintingTextColor
        },
        verticalAlign: "bottom",
        y: 10
      },
      legend: {
        floating: false,
        layout: "horizontal",
        align: "center",
        verticalAlign: "top",
        itemHiddenStyle: {
          color: "#ccc",
          textDecoration: "none"
        }
      },
      plotOptions: {
        column: {
          grouping: false,
          minPointLength: 3,
          dataLabels: {
            enabled: true,
            format: "{point.options.labelText}",
            style: {
              ...(isPrinting ? { textOutline: "0px" } : {})
            }
          }
        },
        series: {
          pointPadding: 0,
          groupPadding: 0.0
        }
      },
      title: {
        text: chartTitle
      },
      series: [
        {
          showInLegend: false,
          data: scores
        },
        ...this.getSeriesLegend(scores)
      ],
      xAxis: {
        categories,
        labels: {
          formatter() {
            return getCategoryValue(this.value, this.isFirst);
          },
          ...style
        }
      },
      yAxis: {
        title: {
          text: "% At/Above Instructional Target",
          style: customPrintingTextColor
        },
        max: 100,
        labels: {
          formatter() {
            return `${this.value}%`;
          },
          style: customPrintingTextColor
        }
      },
      tooltip: {
        enabled: false
      }
    });
    this.chart.reflow();
  }

  // Create the div which the chart will be rendered to.
  render() {
    return <div id={this.props.chartId} className="growth-chart" />;
  }
}

GrowthChart.propTypes = {
  chartId: PropTypes.string,
  grade: PropTypes.string,
  shouldDisplayModifiedTitle: PropTypes.bool,
  generateAllPeriods: PropTypes.bool,
  chartItems: PropTypes.object,
  subtitleText: PropTypes.string
};

GrowthChart.defaultProps = {
  grade: "",
  subtitleText: "",
  shouldDisplayModifiedTitle: false,
  generateAllPeriods: false
};
