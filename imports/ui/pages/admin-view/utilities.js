import moment from "moment";
import { Users } from "/imports/api/users/users";
import { getFormattedSchoolYear } from "/imports/api/utilities/utilities";
import { getCurrentEnrolledGrade } from "/imports/api/students/utils";

export function compileResultsByStudentGroupsInGrade(bmPeriods, grade, studentGroups, assessmentResults) {
  const percentMeetingTargetByClassAndSeason = [];
  // Compile data for Students Meeting Target By Class Graph
  let prevPercentMeetingTarget = null;
  bmPeriods.forEach(bmPeriod => {
    const displaySchoolYear = getFormattedSchoolYear(studentGroups[0].schoolYear);
    const seasonalData = {
      name: bmPeriod.name,
      periodId: bmPeriod._id,
      startDate: moment(bmPeriod.startDate).format("MMMM Do YYYY"),
      endDate: moment(bmPeriod.endDate).format("MMMM Do YYYY"),
      data: [],
      displaySchoolYear
    };

    let totalStudentsAtGradeLevelAssessed = 0;
    let totalStudentsAtGradeLevelMeetingTarget = 0;
    studentGroups.forEach(sg => {
      let groupLabel = sg.name;
      if (sg.ownerIds && sg.ownerIds.length) {
        const groupOwner = Users.findOne({ _id: sg.ownerIds[0] });
        if (groupOwner) {
          groupLabel = `${sg.name} - ${groupOwner.profile.name.first} ${groupOwner.profile.name.last}`;
        } else {
          groupLabel = `${sg.name} - NOT ASSIGNED`;
        }
      }
      let yData = null;
      let numberOfStudentsAssessed = null;
      // Check if there is an assessmentResult for this group and period
      const assResult = assessmentResults.find(
        ar => ar.studentGroupId === sg._id && ar.benchmarkPeriodId === seasonalData.periodId
      );
      if (assResult) {
        yData = assResult.classwideResults.percentMeetingTarget;
        numberOfStudentsAssessed = assResult.classwideResults.totalStudentsAssessedOnAllMeasures;

        totalStudentsAtGradeLevelAssessed += assResult.classwideResults.totalStudentsAssessedOnAllMeasures;
        totalStudentsAtGradeLevelMeetingTarget += assResult.classwideResults.totalStudentsMeetingAllTargets;
      }
      seasonalData.data.push({
        name: groupLabel,
        y: yData,
        numberOfStudentsAssessed
      });
    });

    let seasonalChange = null;
    let gradeLevelPercent = null;
    if (totalStudentsAtGradeLevelAssessed > 0) {
      gradeLevelPercent = Math.round(
        (totalStudentsAtGradeLevelMeetingTarget / totalStudentsAtGradeLevelAssessed) * 100
      );
      // Calculate change from previous season
      if (prevPercentMeetingTarget && gradeLevelPercent) {
        seasonalChange = gradeLevelPercent - prevPercentMeetingTarget;
      }
    }
    // Push summary results for grade
    seasonalData.summaryData = {
      name: getCurrentEnrolledGrade(grade),
      y: gradeLevelPercent,
      numberOfStudentsAssessed: totalStudentsAtGradeLevelAssessed,
      seasonalChange
    };
    percentMeetingTargetByClassAndSeason.push(seasonalData);
    // Keep track of previous seasons value for next comparison
    prevPercentMeetingTarget = gradeLevelPercent;
  });
  return percentMeetingTargetByClassAndSeason;
}

export function compileScreeningStatusForGrade(bmPeriod, studentGroups, grade, allScreeningResults) {
  const screeningResultsForThisPeriod = allScreeningResults.filter(
    ar => ar.benchmarkPeriodId === bmPeriod._id && ar.type === "benchmark" && ar.status === "COMPLETED"
  );
  const groupsInThisGrade = studentGroups.filter(sg => sg.grade === grade);
  const assessmentResultsInThisGrade = screeningResultsForThisPeriod.filter(ar => ar.grade === grade);
  // Determine which teachers have finished screening
  const gradeGroups = [];
  const groupOwnerIds = groupsInThisGrade.map(g => (g.ownerIds && g.ownerIds.length ? g.ownerIds[0] : ""));
  const gradeGroupTeachers = Users.find({
    _id: { $in: groupOwnerIds }
  }).fetch();
  // Loop through each staffId in this grade
  groupsInThisGrade.forEach(group => {
    const groupOwnerId = group.ownerIds && group.ownerIds.length ? group.ownerIds[0] : "";
    const teacher = gradeGroupTeachers.find(t => t._id === groupOwnerId);
    const teacherName = teacher && teacher.profile && teacher.profile.name;
    const lastName = teacherName && teacherName.last ? teacherName.last : "NOT ASSIGNED";
    const firstName = teacherName && teacherName.first ? teacherName.first : "";
    const screeningComplete = !!assessmentResultsInThisGrade.find(ar => ar.studentGroupId === group._id);
    const individualInterventionQueue = group.individualInterventionQueue || [];
    gradeGroups.push({
      lastName,
      firstName,
      screeningComplete,
      groupName: group.name,
      groupId: group._id,
      siteId: group.siteId,
      individualInterventionQueue
    });
  });
  return {
    grade,
    gradeGroups
  };
  // return results;
}
