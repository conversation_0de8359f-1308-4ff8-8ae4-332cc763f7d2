import React from "react";
import { assert } from "chai";
import sinon from "sinon";
import "@testing-library/jest-dom";
import { Meteor } from "meteor/meteor";
import MockDate from "mockdate";
import td from "testdouble";

import { cleanup, waitFor } from "@testing-library/react";
import stubUtils from "../../../test-helpers/methods/stubUtils";
import GradeOverview, { PureGradeOverview, GradeOverviewComponentWithTracker } from "./grade-overview.jsx";
import { AssessmentResults } from "/imports/api/assessmentResults/assessmentResults";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { Users } from "/imports/api/users/users";
import { getRouterProps, renderWithRouter } from "../../../../tests/helpers/testUtils";

// Mock getCurrentSchoolYear to return a resolved value
jest.mock("/imports/api/utilities/utilities", () => ({
  ...jest.requireActual("/imports/api/utilities/utilities"),
  getCurrentSchoolYear: jest.fn(() => Promise.resolve(2019)),
  getMeteorUser: jest.fn(() => ({ profile: { orgid: "test_organization_id" } })),
  getMeteorUserId: jest.fn(() => "test_user_id")
}));

const responseWithClasswideStats = {
  allClasswideResults: [
    {
      studentGroupId: "testStudentGroupId",
      studentGroupName: "testStudentGroupName",
      numberOfStudentsInGroup: 5,
      numberOfSkillsPracticed: 2,
      numberOfWeeksActive: 22,
      weekNumbersWithScoresEntered: [44],
      numberOfWeeksWithScoresEntered: 1,
      averageWeeksPerSkill: 11,
      interventionConsistency: 5,
      individualResults: [],
      numberOfSkillsInClasswideTree: 10,
      numberOfWeeksPracticingCurrentSkill: 22,
      worseScoresRatio: 0,
      orgid: "test_organization_id",
      extendedIndividualResults: {
        studentGroupId: "testStudentGroupId",
        studentGroupName: "testStudentGroupName",
        numberOfStudentsInIndividualInterventions: 0,
        numberOfWeeksActive: 0,
        numberOfWeeksWithScoresEntered: 0,
        averageWeeksPerSkill: null,
        interventionConsistency: null,
        individualResults: []
      }
    }
  ],
  studentGroupEnrollments: []
};

const pureComponentDefaultProps = {
  assessmentResults: [],
  currentBMPeriod: {},
  grade: "",
  gradeLevelScreeningProgress: [],
  headerTitle: "",
  loading: false,
  percentMeetingTargetByClassAndSeason: [],
  quickInfoData: [],
  ruleSkillsCount: 0,
  siteId: "testSiteId",
  siteName: "testSiteName",
  studentGroups: [],
  inActiveSchoolYear: true,
  orgid: "test_organization_id"
};
const testGrade = "03";
const testTeacherId = "testTeacherId";
const testStudentGroup = {
  _id: "testStudentGroupId",
  name: "testStudentGroupName",
  schoolYear: "2017",
  ownerIds: [testTeacherId],
  grade: testGrade,
  history: []
};

// Helper functions to reduce test repetition
const setupMeteorCallMocks = (classwideMockData = responseWithClasswideStats, individualMockData = {}) => {
  td.when(Meteor.call("calculateClasswideStatsForMultipleGroups", [testStudentGroup._id], true)).thenCallback(
    null,
    classwideMockData
  );

  if (individualMockData) {
    td.when(Meteor.call("CalculateIndividualStats", td.matchers.anything())).thenCallback(null, individualMockData);
  }

  td.when(Meteor.call("users:getGroupOwners", [testStudentGroup.ownerIds[0]])).thenCallback(null, [
    { _id: testTeacherId, profile: { name: { first: "Test", last: "Name" } } }
  ]);
};

const renderGradeOverviewWithProps = (props = {}) => {
  const defaultProps = {
    ...pureComponentDefaultProps,
    ...props
  };

  return renderWithRouter(<PureGradeOverview {...defaultProps} />);
};

const testQuickInfoMessage = async (
  mockData,
  expectedMessage,
  studentGroups,
  sectionTestId = "quickInfoClasswideSection"
) => {
  setupMeteorCallMocks(mockData);
  const { getByTestId } = renderGradeOverviewWithProps({
    studentGroups
  });

  await waitFor(() => {
    const quickInfoSection = getByTestId(sectionTestId);
    assert.match(quickInfoSection.innerHTML, expectedMessage);
  });
};

describe("Grade Overview UI", () => {
  const schoolYear = 2019;
  jest.mock("../../../api/utilities/utilities", () => ({
    ...jest.requireActual("../../../api/utilities/utilities"),
    getCurrentSchoolYear: jest.fn(() => schoolYear)
  }));
  beforeAll(() => {
    MockDate.set("2018-12-20");
  });
  afterAll(() => {
    jest.restoreAllMocks();
    MockDate.reset();
  });
  beforeEach(() => {
    td.replace(Meteor, "call");
    // Mock Meteor methods and subscriptions
    Meteor.user = jest.fn(() => ({ profile: { orgid: "test_organization_id" } }));
    Meteor.subscribe = jest.fn(() => ({ ready: () => true }));
  });
  afterEach(() => {
    cleanup();
    td.reset();
  });
  describe("Render", () => {
    describe("when no student groups exist for the site and grade", () => {
      it("should stay in the loading state", () => {
        const { history } = getRouterProps();
        const { getByTestId } = renderWithRouter(<GradeOverviewComponentWithTracker history={history} />);
        const loadingComponent = getByTestId("loading-icon");
        expect(loadingComponent).toBeVisible();
      });
    });
    describe("when there are student group and teacher owners present for the site and grade", () => {
      afterEach(() => {
        stubUtils.safeRestore(Meteor.subscribe);
      });
      let assessmentResultsFindStub;
      let studentGroupsFindStub;
      beforeEach(() => {
        const subscribeStub = sinon.stub(Meteor, "subscribe");
        subscribeStub.withArgs("Users").returns({ ready: () => true });
        subscribeStub.withArgs("Students:PerGrade").returns({ ready: () => true });
        subscribeStub.callThrough();
        const testTeacher = {
          _id: testTeacherId,
          profile: {
            name: {
              first: "testFirstName",
              last: "testLastName"
            }
          }
        };
        studentGroupsFindStub = sinon.stub(StudentGroups, "find");
        studentGroupsFindStub.callsFake(() => ({
          fetch: () => [testStudentGroup]
        }));
        assessmentResultsFindStub = sinon.stub(AssessmentResults, "find");
        assessmentResultsFindStub.callsFake(() => ({ fetch: () => [] }));
        sinon.stub(Users, "findOne").callsFake(() => testTeacher);
        sinon.stub(Users, "find").callsFake(() => ({
          fetch: () => [testTeacher]
        }));
      });
      it("should render the page successfully", () => {
        renderWithRouter(
          <GradeOverview bmPeriods={[1, 2, 3]} grade={testGrade} currentBMPeriod={{ name: "testBmPeriod" }} />
        );
      });
      const testStudentGroupWithCurrentClasswideSkill = {
        ...testStudentGroup,
        currentClasswideSkill: {
          assessmentId: "testAssessmentId",
          message: {
            messageCode: "3"
          }
        }
      };

      describe("when assessmentResults exist for at least one group in the grade and the group has proper setup", () => {
        let testAssessmentResults = [];
        beforeEach(() => {
          testAssessmentResults = []; // reset to empty
          assessmentResultsFindStub.callsFake(() => ({
            fetch: () => testAssessmentResults
          }));
        });
        describe("and at least one assessmentResult is not a benchmark assessmentResult", () => {
          describe("and the assessmentResult is a classwide intervention assessmentResult", () => {
            beforeEach(() => {
              studentGroupsFindStub.callsFake(() => ({
                fetch: () => [testStudentGroupWithCurrentClasswideSkill]
              }));
              testAssessmentResults.push({
                _id: "testAssessmentResultId",
                type: "classwide"
              });
            });
            it("should display at least one row in the classwide intervention area", async () => {
              setupMeteorCallMocks();
              const { getAllByTestId } = renderGradeOverviewWithProps({
                assessmentResults: testAssessmentResults,
                studentGroups: [testStudentGroupWithCurrentClasswideSkill]
              });

              await waitFor(() => {
                const classwideSummaryRows = getAllByTestId("rowIndvSummary");
                assert.isTrue(classwideSummaryRows.length >= 1, "no body row found in classwide intervention area");
              });
            });
            it("should display a row for the student group that the classwide intervention assessmentResult is for", async () => {
              setupMeteorCallMocks();
              const { container } = renderGradeOverviewWithProps({
                assessmentResults: testAssessmentResults,
                studentGroups: [testStudentGroupWithCurrentClasswideSkill]
              });

              await waitFor(() => {
                assert.match(container.innerHTML, new RegExp(`(${testStudentGroup._id})`, "i"));
              });
            });
          });
          describe("and the assessmentResult is an individual intervention assessmentResult", () => {
            const testStudentGroupWithIndividualInterventions = {
              ...testStudentGroup,
              currentAssessmentResultIds: ["testAssessmentResultId"]
            };

            it("should display at least one row in the individual intervention area", async () => {
              setupMeteorCallMocks(responseWithClasswideStats, {});

              const testIndividualAssessmentResults = [
                {
                  _id: "testAssessmentResultId",
                  studentGroupId: testStudentGroup._id,
                  type: "individual"
                }
              ];

              const { getByTestId } = renderGradeOverviewWithProps({
                assessmentResults: testIndividualAssessmentResults,
                studentGroups: [testStudentGroupWithIndividualInterventions],
                currentBMPeriod: { name: "testBmPeriod" }
              });

              await waitFor(() => {
                const individualSummaryRow = getByTestId("rowIndvSummary");
                expect(individualSummaryRow).toBeVisible();
              });
            });
          });
          describe("regarding the quickinfo feature", () => {
            const testSingleClasswideStatsDefault = {
              studentGroupId: testStudentGroup._id,
              studentGroupName: testStudentGroup.name,
              orgid: "test_organization_id"
            };

            describe("when the grade has no quick info messages", () => {
              it("should not display the quick info message area", async () => {
                // Create test data that won't trigger any quick info messages
                const noQuickInfoStats = [
                  {
                    ...testSingleClasswideStatsDefault,
                    averageWeeksPerSkill: 5, // > 2 weeks, so no "fantastic progress"
                    numberOfWeeksPracticingCurrentSkill: 2, // < 4 weeks, so no "over 4 weeks"
                    interventionConsistency: 85, // between 80-95%, so no consistency messages
                    worseScoresRatio: 0.3, // < 0.5, so no "scores not improving"
                    extendedIndividualResults: {
                      studentGroupId: testStudentGroup._id,
                      studentGroupName: testStudentGroup.name,
                      numberOfStudentsInIndividualInterventions: 0,
                      numberOfWeeksActive: 0,
                      numberOfWeeksWithScoresEntered: 0,
                      averageWeeksPerSkill: null,
                      interventionConsistency: null,
                      individualResults: []
                    }
                  }
                ];

                setupMeteorCallMocks({
                  allClasswideResults: noQuickInfoStats,
                  studentGroupEnrollments: []
                });
                const { queryByTestId } = renderGradeOverviewWithProps({
                  studentGroups: [testStudentGroupWithCurrentClasswideSkill]
                });

                await waitFor(() => {
                  const quickInfoSection = queryByTestId("quickInfoClasswideSection");
                  expect(quickInfoSection).toBeNull();
                });
              });
            });
            describe("when the grade has quick info messages to display", () => {
              it("should display the quick info message area", async () => {
                responseWithClasswideStats.allClasswideResults[0].averageWeeksPerSkill = 2;
                td.when(
                  Meteor.call("calculateClasswideStatsForMultipleGroups", [testStudentGroup._id], true)
                ).thenCallback(null, responseWithClasswideStats);
                const { getByTestId } = renderWithRouter(
                  <PureGradeOverview
                    {...pureComponentDefaultProps}
                    assessmentResults={testAssessmentResults}
                    studentGroups={[testStudentGroupWithCurrentClasswideSkill]}
                  />
                );

                await waitFor(() => {
                  const quickInfoSection = getByTestId("quickInfoClasswideSection");
                  expect(quickInfoSection).toBeVisible();
                });
              });
            });
          });
        });
      });
      describe("regarding Grade Overview quick info logic", () => {
        const testSingleClasswideStatsDefault = {
          studentGroupId: testStudentGroup._id,
          studentGroupName: testStudentGroup.name,
          numberOfSkillsPracticed: 2,
          numberOfSkillsInClasswideTree: 10,
          numberOfStudentsInGroup: 5,
          numberOfWeeksActive: 22,
          weekNumbersWithScoresEntered: [44],
          numberOfWeeksWithScoresEntered: 1,
          averageWeeksPerSkill: 11,
          interventionConsistency: 5,
          individualResults: [],
          numberOfWeeksPracticingCurrentSkill: 22,
          worseScoresRatio: 0,
          orgid: "test_organization_id",
          extendedIndividualResults: {
            studentGroupId: testStudentGroup._id,
            studentGroupName: testStudentGroup.name,
            numberOfStudentsInIndividualInterventions: 0,
            numberOfWeeksActive: 0,
            numberOfWeeksWithScoresEntered: 0,
            averageWeeksPerSkill: null,
            interventionConsistency: null,
            individualResults: []
          }
        };
        const testSingleIndividualStatsDefault = {
          studentGroupId: testStudentGroup._id,
          studentGroupName: testStudentGroup.name,
          orgid: "test_organization_id",
          extendedIndividualResults: {
            individualResults: []
          }
        };
        describe("when the grade has a class that is progressing at or less than 2 weeks per skill", () => {
          it('should display a "Progress is fantastic" message in the quick info area for that group', async () => {
            const testClasswideStats = [
              {
                ...testSingleClasswideStatsDefault,
                averageWeeksPerSkill: 2, // <= 2 weeks triggers "fantastic progress"
                numberOfWeeksPracticingCurrentSkill: 2, // < 4 weeks, so no "over 4 weeks" message
                interventionConsistency: 85, // between 80-95%, so no consistency messages
                worseScoresRatio: 0.3 // < 0.5, so no "scores not improving"
              }
            ];

            await testQuickInfoMessage({ allClasswideResults: testClasswideStats }, /Progress is fantastic/i, [
              testStudentGroupWithCurrentClasswideSkill
            ]);
          });
        });
        describe("when the grade has a class that is on one intervention for over 4 weeks", () => {
          it('should dispaly a "This class has been on one skill for over 4 weeks" message in the quick info area for that group', async () => {
            const testClasswideStats = [
              {
                ...testSingleClasswideStatsDefault,
                numberOfWeeksPracticingCurrentSkill: 5
              }
            ];
            await testQuickInfoMessage({ allClasswideResults: testClasswideStats }, /over 4 weeks/i, [
              testStudentGroupWithCurrentClasswideSkill
            ]);
          });
        });
        describe("when the grade has a class that has consistency above 95%", () => {
          it('should display a "This class has excellent intervention consistency" message in the quick info area for that group', async () => {
            const testClasswideStats = [
              {
                ...testSingleClasswideStatsDefault,
                interventionConsistency: 96, // > 95% triggers "excellent consistency"
                numberOfWeeksPracticingCurrentSkill: 2, // < 4 weeks, so no "over 4 weeks" message
                averageWeeksPerSkill: 5, // > 2 weeks, so no "fantastic progress"
                worseScoresRatio: 0.3 // < 0.5, so no "scores not improving"
              }
            ];

            await testQuickInfoMessage(
              { allClasswideResults: testClasswideStats },
              /excellent intervention consistency/i,
              [testStudentGroupWithCurrentClasswideSkill]
            );
          });
        });
        describe("when the grade has a class that has intervention consistency below 80%", () => {
          it('should display a "This class has low intervention consistency" message in the quick info area for that group', async () => {
            const testClasswideStats = [
              {
                ...testSingleClasswideStatsDefault,
                interventionConsistency: 79
              }
            ];
            td.when(
              Meteor.call("calculateClasswideStatsForMultipleGroups", [testStudentGroup._id], true)
            ).thenCallback(null, { allClasswideResults: testClasswideStats });
            const { getByTestId } = renderWithRouter(
              <PureGradeOverview
                {...pureComponentDefaultProps}
                studentGroups={[
                  {
                    ...testStudentGroupWithCurrentClasswideSkill,
                    history: [{ type: "classwide" }]
                  }
                ]}
              />
            );

            await waitFor(() => {
              const quickInfoSection = getByTestId("quickInfoClasswideSection");
              assert.match(quickInfoSection.innerHTML, /low intervention consistency/i);
            });
          });
        });

        describe("when the grade has a group that two after weeks of assessment has scores lower than on the beginning", () => {
          it('should display a "The scores for most of the students in this class are not improving" message in the quick info area for that class', async () => {
            const testClasswideStats = [
              {
                ...testSingleClasswideStatsDefault,
                worseScoresRatio: 79
              }
            ];
            td.when(
              Meteor.call("calculateClasswideStatsForMultipleGroups", [testStudentGroup._id], true)
            ).thenCallback(null, { allClasswideResults: testClasswideStats });
            const { getByTestId } = renderWithRouter(
              <PureGradeOverview
                {...pureComponentDefaultProps}
                studentGroups={[testStudentGroupWithCurrentClasswideSkill]}
              />
            );

            await waitFor(() => {
              const quickInfoSection = getByTestId("quickInfoClasswideSection");
              assert.match(
                quickInfoSection.innerHTML,
                /The scores for most of the students in this class are not improving/i
              );
            });
          });
        });

        describe("when the grade has a student that is progressing at or less than 2 weeks per skill", () => {
          it('should display a "Progress is great" message in the quick info area for that student', async () => {
            const testIndividualStats = [
              {
                ...testSingleIndividualStatsDefault,
                extendedIndividualResults: {
                  individualResults: [
                    {
                      ...testSingleIndividualStatsDefault,
                      averageWeeksPerSkill: 1
                    }
                  ],
                  interventionConsistency: 100
                }
              }
            ];

            td.when(
              Meteor.call("calculateClasswideStatsForMultipleGroups", [testStudentGroup._id], true)
            ).thenCallback(null, { allClasswideResults: testIndividualStats });
            const { getByTestId } = renderWithRouter(
              <PureGradeOverview
                {...pureComponentDefaultProps}
                studentGroups={[testStudentGroupWithCurrentClasswideSkill]}
              />
            );

            await waitFor(() => {
              const quickInfoSection = getByTestId("quickInfoIndividualSection");
              assert.match(quickInfoSection.innerHTML, /Progress is great/i);
            });
          });
        });

        describe("when the grade has a student that has been on one intervention for 4 weeks or more", () => {
          it('should display a "This student has been on one skill for 4 weeks or more" message in the quick info area for that student', async () => {
            const testIndividualStats = [
              {
                ...testSingleIndividualStatsDefault,
                extendedIndividualResults: {
                  individualResults: [
                    {
                      ...testSingleIndividualStatsDefault,
                      averageWeeksPerSkill: 5
                    }
                  ],
                  interventionConsistency: 100
                }
              }
            ];
            td.when(
              Meteor.call("calculateClasswideStatsForMultipleGroups", [testStudentGroup._id], true)
            ).thenCallback(null, { allClasswideResults: testIndividualStats });
            const { getByTestId } = renderWithRouter(
              <PureGradeOverview
                {...pureComponentDefaultProps}
                studentGroups={[testStudentGroupWithCurrentClasswideSkill]}
              />
            );

            await waitFor(() => {
              const quickInfoSection = getByTestId("quickInfoIndividualSection");
              assert.match(quickInfoSection.innerHTML, /This student has been on one skill for 4 weeks or more/i);
            });
          });
        });

        describe("when the grade has a student that has been on one intervention for 4 weeks", () => {
          it('should display a "This student has been on one skill for 4 weeks or more" message in the quick info area for that student', async () => {
            const testIndividualStats = [
              {
                ...testSingleIndividualStatsDefault,
                extendedIndividualResults: {
                  individualResults: [
                    {
                      ...testSingleIndividualStatsDefault,
                      averageWeeksPerSkill: 4
                    }
                  ],
                  interventionConsistency: 100
                }
              }
            ];
            td.when(
              Meteor.call("calculateClasswideStatsForMultipleGroups", [testStudentGroup._id], true)
            ).thenCallback(null, { allClasswideResults: testIndividualStats });
            const { getByTestId } = renderWithRouter(
              <PureGradeOverview
                {...pureComponentDefaultProps}
                studentGroups={[testStudentGroupWithCurrentClasswideSkill]}
              />
            );

            await waitFor(() => {
              const quickInfoSection = getByTestId("quickInfoIndividualSection");
              assert.match(quickInfoSection.innerHTML, /This student has been on one skill for 4 weeks or more/i);
            });
          });
        });

        describe("when the grade has a student that has intervention consistency above 95%", () => {
          it('should display a "This student has excellent intervention consistency!" message in the quick info area for that student', async () => {
            const testIndividualStats = [
              {
                ...testSingleIndividualStatsDefault,
                extendedIndividualResults: {
                  individualResults: [
                    {
                      ...testSingleIndividualStatsDefault,
                      interventionConsistency: 100
                    }
                  ],
                  interventionConsistency: 100
                }
              }
            ];
            td.when(
              Meteor.call("calculateClasswideStatsForMultipleGroups", [testStudentGroup._id], true)
            ).thenCallback(null, { allClasswideResults: testIndividualStats });
            const { getByTestId } = renderWithRouter(
              <PureGradeOverview
                {...pureComponentDefaultProps}
                studentGroups={[testStudentGroupWithCurrentClasswideSkill]}
              />
            );

            await waitFor(() => {
              const quickInfoSection = getByTestId("quickInfoIndividualSection");
              assert.match(quickInfoSection.innerHTML, /This student has excellent intervention consistency!/i);
            });
          });
        });

        describe("when the grade has a group that has intervention consistency below 80%", () => {
          it('should display a "This student has low intervention consistency" message in the quick info area for that student', async () => {
            const testIndividualStats = [
              {
                ...testSingleIndividualStatsDefault,
                extendedIndividualResults: {
                  individualResults: [
                    {
                      ...testSingleIndividualStatsDefault,
                      interventionConsistency: 70
                    }
                  ],
                  interventionConsistency: 70
                }
              }
            ];
            td.when(
              Meteor.call("calculateClasswideStatsForMultipleGroups", [testStudentGroup._id], true)
            ).thenCallback(null, { allClasswideResults: testIndividualStats });
            const { getByTestId } = renderWithRouter(
              <PureGradeOverview
                {...pureComponentDefaultProps}
                studentGroups={[testStudentGroupWithCurrentClasswideSkill]}
              />
            );

            await waitFor(() => {
              const quickInfoSection = getByTestId("quickInfoIndividualSection");
              assert.match(quickInfoSection.innerHTML, /This student has low intervention consistency/i);
            });
          });
        });

        describe("when the grade has a group that has intervention consistency below 100% in the first two weekks of assessment", () => {
          it('should display a "This student has low intervention consistency" message in the quick info area for that student', async () => {
            const testIndividualStats = [
              {
                ...testSingleIndividualStatsDefault,
                extendedIndividualResults: {
                  individualResults: [
                    {
                      ...testSingleIndividualStatsDefault,
                      interventionConsistency: 90,
                      numberOfWeeksActive: 2
                    }
                  ],
                  interventionConsistency: 90
                }
              }
            ];
            td.when(
              Meteor.call("calculateClasswideStatsForMultipleGroups", [testStudentGroup._id], true)
            ).thenCallback(null, { allClasswideResults: testIndividualStats });
            const { getByTestId } = renderWithRouter(
              <PureGradeOverview
                {...pureComponentDefaultProps}
                studentGroups={[testStudentGroupWithCurrentClasswideSkill]}
              />
            );

            await waitFor(() => {
              const quickInfoSection = getByTestId("quickInfoIndividualSection");
              assert.match(quickInfoSection.innerHTML, /This student has low intervention consistency/i);
            });
          });
        });
      });
      afterEach(() => {
        [StudentGroups.find, AssessmentResults.find, Users.findOne, Users.find].forEach(func => {
          stubUtils.safeRestore(func);
        });
      });
    });
  });
});
