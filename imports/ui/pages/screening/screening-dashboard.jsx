import React from "react";
import PropTypes from "prop-types";
import { Link } from "react-router-dom";
import { uniqBy } from "lodash";
import ContinueScreeningNotice from "./continue-screening-notice";
import { getBenchmarkPeriodLabel } from "./screening";
import * as utils from "/imports/api/utilities/utilities";
import ScreeningResultsSummaryChart from "../../components/screeningResultsSummaryChart";

export default function ScreeningDashboard(props) {
  const canContinueCurrentBenchmarkPeriodScreening =
    props.canManageScreenings &&
    props.currentBMPScreeningStatus.hasScreening &&
    !props.currentBMPScreeningStatus.completed;
  const shouldStartCurrentBenchmarkPeriodScreening =
    props.canManageScreenings && !props.currentBMPScreeningStatus.hasScreening;
  return (
    <div>
      {shouldStartCurrentBenchmarkPeriodScreening ? (
        <div
          className="information-box information-box-notice"
          key="beginScreeningNotice"
          data-testid="currentScreeningInformationBox"
        >
          <h4 className="w7 text-center">{props.formatScreeningTitle()} is underway!</h4>
          <div className="text-center">
            <p className="small-details">
              {`It's time to assess how your class is doing to determine your students' specific needs.`}
            </p>
            <p>
              <button className="btn btn-success" onClick={props.startNewScreening}>
                Begin {props.formatScreeningTitle()}
              </button>
            </p>
          </div>
          <p className="text-center tiny-details m-t-40">
            Feeling a little lost? No problem. The support site provides additional resources and training videos. Just
            click on your name in the upper right-hand corner of the page and select support.
          </p>
        </div>
      ) : null}
      {canContinueCurrentBenchmarkPeriodScreening ? (
        <ContinueScreeningNotice screeningTitle={props.formatScreeningTitle()} studentGroup={props.studentGroup} />
      ) : null}
      {props.previousIncompleteScreenings.length
        ? props.previousIncompleteScreenings.map(benchmarkPeriodId => (
            <ContinueScreeningNotice
              screeningTitle={props.formatScreeningTitle(getBenchmarkPeriodLabel(benchmarkPeriodId))}
              studentGroup={props.studentGroup}
              benchmarkPeriodId={benchmarkPeriodId}
              key={benchmarkPeriodId}
            />
          ))
        : null}
      {uniqBy(
        (props.studentGroup.history || []).filter(h => h.type === "benchmark"),
        "benchmarkPeriodId"
      ).map((h, i) => {
        const chartId = `chart-${h.assessmentResultId}`;
        const seasonTitle =
          `${props.benchmarkPeriods.find(bmp => bmp._id === h.benchmarkPeriodId).name}` +
          ` ${utils.getFormattedSchoolYear(props.studentGroup.schoolYear)}`;
        return (
          <div key={`chartDiv-${i}`}>
            <h3 className="w9">{seasonTitle} Results Summary</h3>
            <p>Percent of Students At-Target for Each Measure</p>
            <ScreeningResultsSummaryChart historyMeasures={h.assessmentResultMeasures} chartId={chartId} />
            <div className="text-center">
              <Link
                className="btn btn-success screening-results-btn"
                data-testid="viewScreening"
                to={
                  `/${props.studentGroup.orgid}/site/${props.studentGroup.siteId}/student-groups/` +
                  `${props.studentGroup._id}/${utils.dashboardNavs.screening}/results/${h.assessmentResultId}`
                }
              >
                View {seasonTitle} Details
              </Link>
            </div>
          </div>
        );
      })}
    </div>
  );
}

ScreeningDashboard.propTypes = {
  canManageScreenings: PropTypes.bool,
  currentBMPScreeningStatus: PropTypes.object,
  formatScreeningTitle: PropTypes.func,
  startNewScreening: PropTypes.func,
  studentGroup: PropTypes.object,
  previousIncompleteScreenings: PropTypes.array,
  benchmarkPeriods: PropTypes.array
};
