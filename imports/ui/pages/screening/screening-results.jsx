import React, { Component, useContext } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { useTracker } from "meteor/react-meteor-data";
import { Link } from "react-router-dom";
import intersection from "lodash/intersection";
import Alert from "react-s-alert";
import { Organizations } from "/imports/api/organizations/organizations";
import ClasswideProblem from "../../components/screening/classwide-problem";
import IndividualProblem from "../../components/screening/individual-problem";
import PageHeader from "../../components/page-header";
import * as utils from "/imports/api/utilities/utilities";
import { AssessmentResults } from "/imports/api/assessmentResults/assessmentResults";
import { Loading } from "../../components/loading";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { getBenchmarkPeriodByDate } from "/imports/api/benchmarkPeriods/methods";
import { Students } from "/imports/api/students/students";
import InstructionalVideoModal from "../../components/instructional-video-modal";
import { isOnPrintPage } from "../../utilities";
import { getMeteorUserSync } from "/imports/api/utilities/utilities";
import { SchoolYearContext } from "/imports/contexts/SchoolYearContext";
import { OrganizationContext } from "/imports/contexts/OrganizationContext";
import { StaticDataContext } from "/imports/contexts/StaticDataContext";

function renderNoResultsNotice() {
  return (
    <div className="text-center">
      <h1 className="stamped">No Results found.</h1>
      <h3>{`This student group's screening results have not been processed.`}</h3>
      <Link to="/overview" className="btn btn-success">
        See Student List
      </Link>
    </div>
  );
}

class ScreeningResults extends Component {
  state = {
    isModalOpen: false,
    onCloseModal: () => {},
    videoId: "",
    videoTimestamp: 0
  };

  componentDidUpdate(prevProps) {
    if (!this.props.loading && this.props.loading !== prevProps.loading) {
      this.props.refreshScroll();
    }
  }

  isPrinting = isOnPrintPage();

  getScopeOfTheProblem() {
    const title =
      `${utils.translateBenchmarkPeriod(this.props.screeningResult.benchmarkPeriodId).name} ` +
      `${utils.getFormattedSchoolYear(this.props.screeningResult.schoolYear)} Screening Results`;
    return (
      <div>
        <PageHeader title={title} description={"The results are in. Let's take a look..."} />
        {this.isPrinting ? <h4 className="w6 text-center">{this.props.studentGroup.name}</h4> : null}
        {this.props.shouldGenerateClasswideIntervention ? (
          <ClasswideProblem
            assessmentResults={this.props.screeningResult}
            isClasswideInterventionComplete={this.props.isClasswideInterventionComplete}
            isClasswideInterventionProgressed={this.props.isClasswideInterventionProgressed}
            currentBenchmarkPeriod={this.props.currentBenchmarkPeriod}
            scheduledStudentIds={this.props.scheduledStudentIds}
            students={this.props.studentsInGroup}
            classwideEnabled={this.props.isClasswideEnabled}
            studentGroup={this.props.studentGroup}
            isInCurrentSchoolYear={this.props.isInCurrentSchoolYear}
            user={this.props.user}
            onlyOneStudentTookAndFailedBenchmark={this.props.onlyOneStudentTookAndFailedBenchmark}
            isPrinting={this.isPrinting}
            noIndividualScreeningResults={true}
          />
        ) : (
          <IndividualProblem
            assessmentResults={this.props.screeningResult}
            currentBenchmarkPeriod={this.props.currentBenchmarkPeriod}
            scheduledStudentIds={this.props.scheduledStudentIds}
            students={this.props.studentsInGroup}
            classwideEnabled={this.props.isClasswideEnabled}
            firstClasswideInterventionCreatedAt={this.props.firstClasswideInterventionCreatedAt}
            studentGroup={this.props.studentGroup}
            isInCurrentSchoolYear={this.props.isInCurrentSchoolYear}
            user={this.props.user}
            onlyOneStudentTookAndFailedBenchmark={this.props.onlyOneStudentTookAndFailedBenchmark}
            isClasswideInterventionComplete={this.props.isClasswideInterventionComplete}
            isPrinting={this.isPrinting}
          />
        )}
        {this.renderCurrentInterventionLink()}
      </div>
    );
  }

  shouldShowCurrentInterventionLink() {
    const {
      areIndividualInterventionsScheduled,
      shouldGenerateClasswideIntervention,
      isClasswideInterventionComplete
    } = this.props;
    return (
      !this.isPrinting &&
      ((shouldGenerateClasswideIntervention && !isClasswideInterventionComplete) || areIndividualInterventionsScheduled)
    );
  }

  closeModal = () => {
    this.setState({ isModalOpen: false });
  };

  openModal = ({ onCloseModal, videoId = "", videoTimestamp }) => {
    this.setState({ isModalOpen: true, onCloseModal, videoId, videoTimestamp });
  };

  showInstructionalVideoForNewClasswideIntervention = (
    errorCheckingForNewClasswideInterventions
    /* hasNewClasswideIntervention */
  ) => {
    // const { siteId } = this.props;
    if (errorCheckingForNewClasswideInterventions) {
      Alert.error("There was a problem checking for a new classwide intervention.", {
        timeout: 5000
      });
    }
    // if (hasNewClasswideIntervention) {
    //   const videoType = "classwide";
    //   return Meteor.call(
    //     "InstructionalVideos:getIdFromVideoUrl",
    //     videoType,
    //     siteId,
    //     (error, { videoId, videoTimestamp }) => {
    //       if (error) {
    //         Alert.error("There was a problem fetching video url.", {
    //           timeout: 5000
    //         });
    //       }
    //       return this.openModal({
    //         onCloseModal: () => this.goToCurrentIntervention(),
    //         videoId,
    //         videoTimestamp
    //       });
    //     }
    //   );
    // }
    return this.goToCurrentIntervention();
  };

  goToCurrentIntervention = () => {
    this.props.history.push(this.getCurrentInterventionLink());
  };

  checkCurrentInterventionStatus = () => {
    const { studentGroupId, siteId, orgid } = this.props;
    Meteor.call(
      "StudentGroup:hasAnyClasswideInterventionsScores",
      studentGroupId,
      siteId,
      orgid,
      this.showInstructionalVideoForNewClasswideIntervention
    );
  };

  renderCurrentInterventionLink() {
    if (this.shouldShowCurrentInterventionLink()) {
      const informationText =
        this.props.areIndividualInterventionsScheduled || this.props.isClasswideInterventionProgressed
          ? "You can continue the intervention under Current Intervention."
          : "You can access the first skill packets under Current Intervention.";
      return (
        <div className="screening-recommendation">
          <p className="text-end">{informationText}</p>
          <p className="text-end">
            <button onClick={this.checkCurrentInterventionStatus} className="btn btn-primary">
              To the Current Intervention
              <i className="fa fa-chevron-right fa-right" />
            </button>
            {this.state.isModalOpen ? (
              <InstructionalVideoModal
                showModal={this.state.isModalOpen}
                closeModal={this.closeModal}
                onCloseModal={this.state.onCloseModal}
                videoId={this.state.videoId}
                videoTimestamp={this.state.videoTimestamp}
                headerText="If this is your first time using classwide intervention please watch this video."
                testIdPrefix="classwide-intervention"
              />
            ) : null}
          </p>
        </div>
      );
    }
    return <div className="screening-recommendation">&nbsp;</div>;
  }

  getCurrentInterventionLink() {
    const { studentGroup, studentGroupId, areIndividualInterventionsScheduled } = this.props;
    let currentInterventionLink = `/${studentGroup.orgid}/site/${studentGroup.siteId}/student-groups/${studentGroupId}/`;
    if (!this.props.shouldGenerateClasswideIntervention && areIndividualInterventionsScheduled) {
      currentInterventionLink = currentInterventionLink.concat("individual");
    } else {
      currentInterventionLink = currentInterventionLink.concat("classwide");
    }
    return currentInterventionLink;
  }

  render() {
    if (this.props.loading) return <Loading />;
    return this.props.noResults ? renderNoResultsNotice() : this.getScopeOfTheProblem();
  }
}

ScreeningResults.propTypes = {
  screeningResult: PropTypes.object,
  currentBenchmarkPeriod: PropTypes.object,
  loading: PropTypes.bool,
  noResults: PropTypes.bool,
  studentsInGroup: PropTypes.array,
  studentGroup: PropTypes.object,
  history: PropTypes.object,
  studentGroupId: PropTypes.string,
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  scheduledStudentIds: PropTypes.array,
  isClasswideEnabled: PropTypes.bool,
  firstClasswideInterventionCreatedAt: PropTypes.number,
  onlyOneStudentTookAndFailedBenchmark: PropTypes.bool,
  shouldGenerateClasswideIntervention: PropTypes.bool,
  isClasswideInterventionComplete: PropTypes.bool,
  areIndividualInterventionsScheduled: PropTypes.bool,
  isClasswideInterventionProgressed: PropTypes.bool,
  refreshScroll: PropTypes.func,
  isInCurrentSchoolYear: PropTypes.bool,
  user: PropTypes.object
};

ScreeningResults.defaultProps = {
  refreshScroll: () => {}
};

function getClasswideInterventionData(screeningResult, studentGroup) {
  let isClasswideInterventionComplete = false;
  let isClasswideInterventionProgressed = false;
  const { percentAtRisk, totalStudentsAssessedOnAllMeasures } = screeningResult.classwideResults;
  const onlyOneStudentTookAndFailedBenchmark = percentAtRisk === 100 && totalStudentsAssessedOnAllMeasures === 1;
  const shouldGenerateClasswideIntervention = percentAtRisk > 50 && !onlyOneStudentTookAndFailedBenchmark;
  const hasClasswideHistory = studentGroup.history && studentGroup.history.find(item => item.type === "classwide");
  if (hasClasswideHistory) {
    const classwideCompleteMessageCode = "5";
    isClasswideInterventionComplete =
      !studentGroup.currentClasswideSkill ||
      studentGroup.currentClasswideSkill.message.messageCode === classwideCompleteMessageCode;
    isClasswideInterventionProgressed = !isClasswideInterventionComplete;
  }
  return {
    shouldGenerateClasswideIntervention,
    isClasswideInterventionComplete,
    isClasswideInterventionProgressed,
    onlyOneStudentTookAndFailedBenchmark
  };
}

// Data Container
const ScreeningResultsContainer = ({ assessmentResultId, studentGroupId, orgid, students, ...props }) => {
  const { schoolYear: currentSchoolYear, latestAvailableSchoolYear, customDate } = useContext(SchoolYearContext) || {};
  const { org, orgId: contextOrgId } = useContext(OrganizationContext) || {};
  const { benchmarkPeriods } = useContext(StaticDataContext);
  const { benchmarkPeriodsGroupId, isTestOrg } = org || {};

  const trackerData = useTracker(() => {
    if (!currentSchoolYear || !latestAvailableSchoolYear) {
      return { loading: true };
    }

    // TODO(fmazur) - remove?
    const benchmarkPeriodsSub = Meteor.subscribe("BenchmarkPeriods");
    const assResultsSub = Meteor.subscribe("getAssessmentResultsForStudentGroupOrByIds", {
      studentGroupId,
      orgid: orgid || contextOrgId,
      ids: [assessmentResultId]
    });
    const user = getMeteorUserSync();
    const studentGroupsHandle = Meteor.subscribe(
      "StudentGroups:ScreeningResultsData",
      studentGroupId,
      currentSchoolYear
    );
    const orgSub = Meteor.subscribe("Organizations", orgid || contextOrgId);
    const studentsHandle = !students
      ? Meteor.subscribe("StudentsInStudentGroup", studentGroupId)
      : { ready: () => true };
    let screeningResult;
    let scheduledStudentIds = [];
    let areIndividualInterventionsScheduled = false;
    let noResults = true;
    let studentGroup = {};
    let isClasswideEnabled;
    let onlyOneStudentTookAndFailedBenchmark = false;
    let shouldGenerateClasswideIntervention = false;
    let isClasswideInterventionComplete = false;
    let isClasswideInterventionProgressed = false;
    let isInCurrentSchoolYear = false;
    let studentsInGroup = students || [];
    let firstClasswideInterventionCreatedAt;
    const loading =
      !assResultsSub.ready() ||
      !benchmarkPeriodsSub.ready() ||
      !orgSub.ready() ||
      !studentGroupsHandle.ready() ||
      !studentsHandle.ready();
    if (!loading) {
      screeningResult = AssessmentResults.findOne({
        _id: assessmentResultId,
        type: "benchmark"
      });
      firstClasswideInterventionCreatedAt = AssessmentResults.findOne(
        {
          studentGroupId,
          schoolYear: currentSchoolYear,
          type: "classwide"
        },
        { fields: { created: 1 }, sort: { "created.on": 1 } }
      )?.created?.on;
      if (screeningResult && screeningResult.classwideResults) {
        noResults = false;
      }
      if (!students) {
        // TODO(fmazur) - replace with context?
        studentsInGroup = Students.find().fetch();
      }
      // TODO(fmazur) - replace with context?
      studentGroup = StudentGroups.findOne(studentGroupId);
      const individualInterventionStudentIds = AssessmentResults.find(
        { type: "individual" },
        { fields: { studentId: 1 } }
      )
        .fetch()
        .map(result => result.studentId);
      scheduledStudentIds = intersection(
        screeningResult.classwideResults.studentIdsNotMeetingTarget,
        individualInterventionStudentIds
      );
      areIndividualInterventionsScheduled = scheduledStudentIds.length > 0;
      const organization = Organizations.findOne({ _id: studentGroup.orgid }) || {};
      isClasswideEnabled = typeof organization.classwideEnabled !== "undefined" ? organization.classwideEnabled : true;
      if (isClasswideEnabled) {
        ({
          shouldGenerateClasswideIntervention,
          isClasswideInterventionComplete,
          isClasswideInterventionProgressed,
          onlyOneStudentTookAndFailedBenchmark
        } = getClasswideInterventionData(screeningResult, studentGroup));
      }
      isInCurrentSchoolYear = latestAvailableSchoolYear === studentGroup.schoolYear;
    }
    const currentBenchmarkPeriod = getBenchmarkPeriodByDate({
      customDate,
      benchmarkPeriodsGroupId,
      isTestOrg,
      benchmarkPeriods
    });
    return {
      screeningResult,
      currentBenchmarkPeriod,
      loading,
      noResults,
      scheduledStudentIds,
      studentGroup,
      studentGroupId,
      isClasswideEnabled,
      onlyOneStudentTookAndFailedBenchmark,
      shouldGenerateClasswideIntervention,
      firstClasswideInterventionCreatedAt,
      isClasswideInterventionComplete,
      areIndividualInterventionsScheduled,
      isClasswideInterventionProgressed,
      isInCurrentSchoolYear,
      user,
      studentsInGroup
    };
  }, [assessmentResultId, studentGroupId, orgid, contextOrgId, currentSchoolYear, latestAvailableSchoolYear, students]);

  if (trackerData.loading) {
    return <Loading />;
  }

  return <ScreeningResults {...trackerData} {...props} />;
};

ScreeningResultsContainer.propTypes = {
  assessmentResultId: PropTypes.string,
  studentGroupId: PropTypes.string,
  orgid: PropTypes.string,
  students: PropTypes.array
};

export default ScreeningResultsContainer;
