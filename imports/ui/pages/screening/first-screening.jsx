import PropTypes from "prop-types";
import React, { Component } from "react";
import { Meteor } from "meteor/meteor";
import Alert from "react-s-alert";
import { isHighSchoolGrade } from "../../utilities";
import * as utils from "/imports/api/utilities/utilities";
import ActiveSchoolYearMessage from "../../components/ActiveSchoolYearMessage";
import InstructionalVideoModal from "../../components/instructional-video-modal";

export default class FirstScreening extends Component {
  state = {
    isScreeningModalOpen: false,
    isClasswideInterventionModalOpen: false,
    videoId: "",
    onCloseModal: () => {},
    videoTimestamp: 0
  };

  showInstructionalVideoForScreening = () => {
    const videoType = "screening";
    const { siteId } = this.props.studentGroup;
    return Meteor.call(
      "InstructionalVideos:getIdFromVideoUrl",
      videoType,
      siteId,
      (error, { videoId, videoTimestamp }) => {
        if (error) {
          Alert.error("There was a problem fetching video url.", {
            timeout: 5000
          });
        }
        return this.openModal({
          onCloseModal: () => this.props.startNewScreening(),
          videoId,
          videoTimestamp,
          key: "isScreeningModalOpen"
        });
      }
    );
  };

  showInstructionalVideoForNewClasswideIntervention = (
    errorCheckingForNewClasswideInterventions,
    hasNewClasswideIntervention
  ) => {
    const { siteId } = this.props.studentGroup;
    if (errorCheckingForNewClasswideInterventions) {
      Alert.error("There was a problem checking for a new classwide intervention.", {
        timeout: 5000
      });
    }
    if (hasNewClasswideIntervention) {
      const videoType = "classwide";
      return Meteor.call(
        "InstructionalVideos:getIdFromVideoUrl",
        videoType,
        siteId,
        (error, { videoId, videoTimestamp }) => {
          if (error) {
            Alert.error("There was a problem fetching video url.", {
              timeout: 5000
            });
          }
          return this.openModal({
            onCloseModal: () => this.props.startNewClasswide(),
            videoId,
            videoTimestamp,
            key: "isClasswideInterventionModalOpen"
          });
        }
      );
    }
    return this.props.startNewClasswide();
  };

  checkCurrentInterventionStatus = () => {
    const { _id: studentGroupId, siteId, orgid } = this.props.studentGroup;
    Meteor.call("StudentGroup:hasAnyClasswideInterventionsScores", studentGroupId, siteId, orgid, error => {
      if (error) {
        Alert.error(error.reason || "There was an error while checking the status of the Classwide Intervention", {
          timeout: 5000
        });
      } else {
        this.props.startNewClasswide();
      }
    });
  };

  closeModal = () => key => {
    this.setState({ [key]: false });
  };

  openModal = ({ onCloseModal, videoId = "", videoTimestamp, key }) => {
    this.setState({ [key]: true, onCloseModal, videoId, videoTimestamp });
  };

  render() {
    const { props } = this;

    const topMarginStyle = { marginTop: "10px" };
    const orgIsSML = utils.isSML(props.studentGroup.orgid);
    const infoText = orgIsSML ? "student(s)" : "class";

    return (
      <div className="conGuide">
        <h1>Getting started with SpringMath</h1>
        <div className="row">
          <ul className="guideBoxList clearfix">
            <li className="guideBoxItem guideNumBox">
              {isHighSchoolGrade(props.studentGroup.grade) ? (
                <p style={topMarginStyle}>
                  In grades 9-12 all students start with classwide intervention. This provides them with the
                  foundational skills they need.
                </p>
              ) : (
                <React.Fragment>
                  <h4 className="guideProcessTitle">
                    <a
                      target="_blank"
                      href="https://springmath.s3.amazonaws.com/pdf/How%20to%20Conduct%20Screening%20Assessments.pdf"
                      rel="noreferrer"
                    >
                      Screen Your {utils.capitalizeFirstLetter(infoText)}
                    </a>
                  </h4>
                  <p>
                    First, you’ll administer a screening assessment to your {infoText}. This helps us better understand
                    your {infoText}’ specific Math needs.
                  </p>
                </React.Fragment>
              )}
              <span className="guideProcessNumber">1</span>
            </li>
            <li className="guideBoxItem guideNumBox">
              {isHighSchoolGrade(props.studentGroup.grade) ? (
                <p style={topMarginStyle}>
                  For more information on how classwide interventions work{" "}
                  <a
                    className="w6"
                    target="_blank"
                    href="https://springmath.s3.amazonaws.com/pdf/Classwide%20Interventions.pdf"
                    rel="noreferrer"
                  >
                    click here
                  </a>
                  . To begin click the green box below.
                </p>
              ) : (
                <React.Fragment>
                  <h4 className="guideProcessTitle">Review Results</h4>

                  {orgIsSML ? (
                    <p>SpringMath will then recommend an intervention for each student who needs one.</p>
                  ) : (
                    <p>
                      {`SpringMath will then show you exactly what's going on with your class and recommend a `}
                      <a
                        className="w6"
                        target="_blank"
                        href="https://springmath.s3.amazonaws.com/pdf/Classwide%20Interventions.pdf"
                        rel="noreferrer"
                      >
                        classwide
                      </a>
                      &nbsp;or{" "}
                      <a
                        className="w6"
                        target="_blank"
                        href="https://springmath.s3.amazonaws.com/pdf/Conducting%20Individual%20Intervention.pdf"
                        rel="noreferrer"
                      >
                        individual
                      </a>{" "}
                      intervention(s)
                    </p>
                  )}
                </React.Fragment>
              )}
              <span className="guideProcessNumber">2</span>
            </li>
            <li className="guideBoxItem guideNumBox">
              {isHighSchoolGrade(props.studentGroup.grade) ? (
                <p style={topMarginStyle}>
                  {`For students who aren't making progress you will also have the option of adding `}
                  <a
                    className="w6"
                    target="_blank"
                    href="https://springmath.s3.amazonaws.com/pdf/Conducting%20Individual%20Intervention.pdf"
                    rel="noreferrer"
                  >
                    individualized interventions
                  </a>{" "}
                  later.{" "}
                </p>
              ) : (
                <React.Fragment>
                  <h4 className="guideProcessTitle">Path to Math Mastery</h4>

                  {orgIsSML ? (
                    <p>
                      Print off the intervention{" "}
                      <a
                        className="w6"
                        target="_blank"
                        href="https://springmath.s3.amazonaws.com/pdf/Conducting%20Individual%20Intervention.pdf"
                        rel="noreferrer"
                      >
                        and administer it daily
                      </a>
                      . This will provide a clear path towards math proficiency.
                    </p>
                  ) : (
                    <p>
                      Once an intervention type is set, SpringMath will provide a clear path toward Math proficiency for
                      the {infoText}.
                    </p>
                  )}
                </React.Fragment>
              )}
              <span className="guideProcessNumber">3</span>
            </li>
          </ul>
        </div>
        <ActiveSchoolYearMessage inActiveSchoolYear={props.inActiveSchoolYear} />
        {isHighSchoolGrade(props.studentGroup.grade) ? (
          <button
            className="btnStartScreening btn btn-success"
            onClick={this.checkCurrentInterventionStatus}
            disabled={!props.canManageScreenings}
          >
            Begin Interventions
          </button>
        ) : (
          <button
            className="btnStartScreening btn btn-success"
            onClick={this.props.startNewScreening}
            disabled={!props.canManageScreenings}
            data-testid="beginScreening"
          >
            Begin Screening
          </button>
        )}
        <p className="text-center tiny-details m-t-40">
          Feeling a little lost? No problem. The support site provides additional resources and training videos. Just
          click on your name in the upper right-hand corner of the page and select support.
        </p>
        {this.state.isClasswideInterventionModalOpen ? (
          <InstructionalVideoModal
            showModal={this.state.isClasswideInterventionModalOpen}
            closeModal={this.closeModal("isClasswideInterventionModalOpen")}
            onCloseModal={this.state.onCloseModal}
            videoId={this.state.videoId}
            videoTimestamp={this.state.videoTimestamp}
            headerText="If this is your first time using classwide intervention please watch this video."
            testIdPrefix="classwide-intervention"
          />
        ) : null}
        {this.state.isScreeningModalOpen ? (
          <InstructionalVideoModal
            showModal={this.state.isScreeningModalOpen}
            closeModal={this.closeModal("isScreeningModalOpen")}
            videoId={this.state.videoId}
            headerText="If this is your first time using SpringMath please view this video to learn how to conduct screening"
            testIdPrefix="screening"
            onCloseModal={this.state.onCloseModal}
          />
        ) : null}
      </div>
    );
  }
}

FirstScreening.propTypes = {
  studentGroup: PropTypes.object,
  inActiveSchoolYear: PropTypes.bool,
  startNewClasswide: PropTypes.func,
  canManageScreenings: PropTypes.bool,
  startNewScreening: PropTypes.func
};
