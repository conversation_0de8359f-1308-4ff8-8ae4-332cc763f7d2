import { Meteor } from "meteor/meteor";
import { assert } from "chai";

import React from "react";
import { shallow } from "enzyme";

import NewScreening from "./new-screening.jsx";

if (Meteor.isClient) {
  describe("NewScreening UI", () => {
    describe("Render", () => {
      it("render", () => {
        // Verify that the method does what we expected
        const studentGroupId = "test_student_group_id";
        const newScreeningComponent = shallow(<NewScreening params={{ studentGroupId }} />);
        assert.isDefined(newScreeningComponent, "newScreeningComponent did not render");
      });
    });
  });
}
