import PropTypes from "prop-types";
import React, { Component } from "react";
import { withRouter } from "react-router-dom";
import { Meteor } from "meteor/meteor";
import Alert from "react-s-alert";
import ActiveSchoolYearMessage from "../../components/ActiveSchoolYearMessage";
import InstructionalVideoModal from "../../components/instructional-video-modal";
import * as utils from "/imports/api/utilities/utilities";

class InterventionWithoutScreening extends Component {
  state = {
    isClasswideInterventionModalOpen: false,
    videoId: "",
    onCloseModal: () => {},
    videoTimestamp: 0
  };

  componentDidMount() {
    const { studentGroup, history } = this.props;
    if (studentGroup.currentClasswideSkill) {
      history.push(
        `/${studentGroup.orgid}/site/${studentGroup.siteId}/student-groups/${studentGroup._id}/${utils.dashboardNavs.classwide}`
      );
    }
  }

  showInstructionalVideoForNewClasswideIntervention = (
    errorCheckingForNewClasswideInterventions
    /* hasNewClasswideIntervention */
  ) => {
    // const { siteId } = this.props.studentGroup;
    if (errorCheckingForNewClasswideInterventions) {
      Alert.error("There was a problem checking for a new classwide intervention.", {
        timeout: 5000
      });
    }
    // if (hasNewClasswideIntervention) {
    //   const videoType = "classwide";
    //   return Meteor.call(
    //     "InstructionalVideos:getIdFromVideoUrl",
    //     videoType,
    //     siteId,
    //     (error, { videoId, videoTimestamp }) => {
    //       if (error) {
    //         Alert.error("There was a problem fetching video url.", {
    //           timeout: 5000
    //         });
    //       }
    //       return this.openModal({
    //         onCloseModal: () => this.props.startNewClasswide(this.props.studentGroup, this.props.history),
    //         videoId,
    //         videoTimestamp,
    //         key: "isClasswideInterventionModalOpen"
    //       });
    //     }
    //   );
    // }
    return this.props.startNewClasswide(this.props.studentGroup, this.props.history);
  };

  checkCurrentInterventionStatus = () => {
    const { _id: studentGroupId, siteId, orgid } = this.props.studentGroup;
    Meteor.call(
      "StudentGroup:hasAnyClasswideInterventionsScores",
      studentGroupId,
      siteId,
      orgid,
      this.showInstructionalVideoForNewClasswideIntervention
    );
  };

  closeModal = () => key => {
    this.setState({ [key]: false });
  };

  openModal = ({ onCloseModal, videoId = "", videoTimestamp, key }) => {
    this.setState({ [key]: true, onCloseModal, videoId, videoTimestamp });
  };

  render() {
    const { inActiveSchoolYear, isReadOnly } = this.props;

    const canManageScreenings = inActiveSchoolYear && !isReadOnly;
    return (
      <div className="conGuide">
        <h1>Getting started with SpringMath</h1>
        <div className="information-box">
          <h4 className="guideProcessTitle" />
          <p>
            If your students are working remotely you may need to begin classwide intervention prior to screening. Be
            sure to complete the screening assessments when your students return to school. Click the button below to
            begin classwide interventions.
          </p>
        </div>
        <ActiveSchoolYearMessage inActiveSchoolYear={inActiveSchoolYear} />
        <button
          className="btnStartScreening btn btn-success"
          onClick={this.checkCurrentInterventionStatus}
          disabled={!canManageScreenings}
        >
          Begin Classwide Interventions
        </button>
        <p className="text-center tiny-details m-t-40">
          Feeling a little lost? No problem. The support site provides additional resources and training videos. Just
          click on your name in the upper right-hand corner of the page and select support.
        </p>
        {this.state.isClasswideInterventionModalOpen ? (
          <InstructionalVideoModal
            showModal={this.state.isClasswideInterventionModalOpen}
            closeModal={this.closeModal("isClasswideInterventionModalOpen")}
            onCloseModal={this.state.onCloseModal}
            videoId={this.state.videoId}
            videoTimestamp={this.state.videoTimestamp}
            headerText="If this is your first time using classwide intervention please watch this video."
            testIdPrefix="classwide-intervention"
          />
        ) : null}
      </div>
    );
  }
}

InterventionWithoutScreening.propTypes = {
  studentGroup: PropTypes.object,
  inActiveSchoolYear: PropTypes.bool,
  startNewClasswide: PropTypes.func,
  isReadOnly: PropTypes.bool,
  startNewScreening: PropTypes.func,
  screeningBenchmarkPeriodId: PropTypes.string,
  history: PropTypes.object
};

const InterventionWithoutScreeningWithRouter = withRouter(InterventionWithoutScreening);

export default InterventionWithoutScreeningWithRouter;
