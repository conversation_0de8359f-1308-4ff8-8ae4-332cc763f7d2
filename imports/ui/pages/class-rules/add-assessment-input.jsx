import React, { Component } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { Meteor } from "meteor/meteor";
import every from "lodash/every";

export class AddAssessmentInput extends Component {
  state = {};

  setSearchString = event => {
    const { grade } = event.target.dataset;
    const state = {};
    state[`searchString${grade}`] = this[`input${grade}`].value;
    this.setState(state);
  };

  matchesSearchString({ grade, assessmentName, monitorAssessmentMeasure = "", associatedGrades }) {
    if (associatedGrades.indexOf(grade) < 0) {
      return false;
    }
    const searchString = this.state[`searchString${grade}`];
    if (!searchString || searchString === "" || monitorAssessmentMeasure.includes(searchString)) {
      return true;
    }
    const doesMatch = [];
    searchString.split(" ").forEach(match => {
      doesMatch.push(assessmentName.toLowerCase().indexOf(match.toLowerCase()) !== -1);
    });
    return every(doesMatch);
  }

  toggleFuzzyBox = event => {
    const { grade } = event.target.dataset;
    const state = {};
    state[`fuzzyBox${grade}`] = !this.state[`fuzzyBox${grade}`];
    this.setState(state);
  };

  updateRule(ruleId, assessmentId) {
    Meteor.call("Rules:addSkill", ruleId, {
      assessmentId
    });
  }

  addAssessment = event => {
    const { grade, assessmentId } = event.target.dataset;
    const rule = this.props.rules.find(r => r.grade === grade);
    if (rule) {
      this.updateRule(rule._id, assessmentId);
    } else {
      Alert.error("Could not find grade the right rule");
    }
    this.toggleFuzzyBox(event);
  };

  render() {
    const gradeId = this.props.grade._id;
    const hasFuzzyBoxEnabled = this.state[`fuzzyBox${gradeId}`];
    const filteredAssessments = hasFuzzyBoxEnabled
      ? this.props.assessments.filter(assessment =>
          this.matchesSearchString({
            grade: gradeId,
            assessmentName: assessment.name,
            monitorAssessmentMeasure: assessment.monitorAssessmentMeasure,
            associatedGrades: assessment.associatedGrades
          })
        )
      : this.props.assessments;

    return (
      <div
        className="form-group"
        style={{
          position: "relative"
        }}
      >
        <input
          type="text"
          className="form-control"
          placeholder="Click to add assessment"
          data-grade={gradeId}
          ref={r => {
            this[`input${gradeId}`] = r;
          }}
          value={this.state[`searchString${gradeId}`]}
          onChange={this.setSearchString}
          onFocus={this.toggleFuzzyBox}
          data-testid={`${gradeId}-add-assessment`}
        />
        {hasFuzzyBoxEnabled ? (
          <div className="fuzzyBox">
            <div className="fuzzyBox-header">
              Total: {filteredAssessments.length}
              <i
                className="fa fa-times pull-right text-danger"
                aria-hidden="true"
                onClick={this.toggleFuzzyBox}
                data-grade={gradeId}
                style={{
                  cursor: "pointer"
                }}
              />
            </div>
            {filteredAssessments.map(assessment => {
              return (
                <div
                  className="fuzzyBox-item"
                  key={assessment._id}
                  data-grade={gradeId}
                  data-assessment-id={assessment._id}
                  onClick={this.addAssessment}
                >
                  AM {assessment.monitorAssessmentMeasure} - {assessment.name}
                </div>
              );
            })}
          </div>
        ) : null}
      </div>
    );
  }
}

AddAssessmentInput.propTypes = {
  grade: PropTypes.object,
  assessments: PropTypes.array,
  rules: PropTypes.array
};
