import React, { useEffect, useRef } from "react";
import PropTypes from "prop-types";
import Highcharts from "highcharts";
import isNumber from "lodash/isNumber";
import { isOnPrintPage } from "../../utilities";
import { capitalizeFirstLetter } from "../../../api/utilities/utilities";

const BarGrowthGraph = ({ chartName, chartId, data, options = {} }) => {
  const chartRef = useRef(null);
  const isPrinting = isOnPrintPage();
  const customPrintingTextColor = isPrinting ? { color: "#000" } : {};

  const settings = {
    title: {
      text: chartName || "",
      style: {
        fontSize: options.titleFontSize
      }
    },
    chart: {
      type: "bar",
      height: options.height
    },
    accessibility: {
      enabled: false
    },
    subtitle: {
      text: options.subtitleText || "",
      style: {
        ...customPrintingTextColor
      },
      verticalAlign: "bottom",
      y: 10
    },
    credits: {
      enabled: false
    },
    legend: {
      layout: options.legendLayout || "horizontal",
      enabled: options.isLegendEnabled || false,
      floating: false,
      maxHeight: 140,
      align: "center",
      verticalAlign: "top",
      itemHiddenStyle: {
        color: "#ccc",
        textDecoration: "none"
      }
    },
    xAxis: {
      type: "category",
      labels: {
        autoRotationLimit: 0,
        style: {
          whiteSpace: "normal",
          width: isPrinting ? "350px" : options.categoryWidth,
          "min-width": isPrinting ? "350px" : options.categoryWidth,
          "text-align": "right",
          fontSize: "10px",
          ...customPrintingTextColor
        },
        x: -10,
        formatter() {
          if (this.value.name !== undefined) {
            if (isNumber(this.value.n)) {
              return `
            <div class="row">
              <div class="col-10">${this.value.name}</div>
              <div class="col-2 text-center fw-bold">
                (n=${this.value.n})
              </div>
            </div>
            `;
            }
            if (options.shouldPrepareSpaceForN) {
              return `
            <div class="row">
              <div class="col-10">${this.value.name}</div>
              <div class="col-2"></div>
            </div>
            `;
            }
            return this.value.name;
          }
          return undefined;
        },
        useHTML: true
      },
      title: {
        text: options.xAxisTitle || ""
      },
      ...(options.usePlotLines
        ? {
            plotLines: options.categories.map((c, index) => {
              if (c === null) {
                return {
                  color: "#cdcdcd",
                  width: 2,
                  value: index,
                  zIndex: 100
                };
              }
              return {};
            })
          }
        : {}),
      categories: options.categories
    },
    yAxis: {
      min: 0,
      max: 100,
      ...(isPrinting ? { tickInterval: 10 } : {}),
      title: {
        text: options.yAxisTitle || ""
      },
      labels: {
        style: {
          fontSize: isPrinting ? "6px" : "9px",
          ...customPrintingTextColor
        },
        y: 10
      }
    },
    plotOptions: {
      bar: {
        ...(options.maxPointWidth ? { maxPointWidth: options.maxPointWidth } : {}),
        groupPadding: options.groupPadding || 0.04,
        pointPadding: 0.01
      },
      series: {
        animation: false,
        dataLabels: [
          {
            inside: false,
            enabled: true,
            y: -1,
            allowOverlap: true,
            ...customPrintingTextColor,
            formatter() {
              const { y, labelText } = this.point;
              if (labelText === "N/A") {
                return labelText;
              }
              const pointText = `${Number.isInteger(y) ? y : y.toFixed(1)}%`;
              return pointText;
            },
            style: {
              fontSize: "10px"
            }
          },
          {
            overflow: "allow",
            align: "left",
            color: "#000",
            inside: true,
            enabled: true,
            crop: false,
            formatter() {
              const { n } = this.point;
              return n || n === 0 ? `(n=${n})` : "";
            },
            style: {
              fontSize: "9px",
              ...(isPrinting ? { textOutline: "0px" } : {})
            },
            x: options.nOffset
          }
        ]
      }
    },
    tooltip: {
      headerFormat: "",
      formatter() {
        let pointName = capitalizeFirstLetter(this.key);
        if (options.useSeriesName) {
          pointName = this.series.name;
        }
        const pointValue = `${Number.isInteger(this.y) ? this.y : this.y.toFixed(2)}%`;
        return `<div><span>${pointName}</span>: ${
          options.useSeriesName ? "<br />" : ""
        }<b>${pointValue}</b> of total</div>`;
      },
      outside: true
    },
    series: data
  };
  const updateChart = () => {
    chartRef.current = new Highcharts.Chart(chartId, settings);
  };

  useEffect(() => {
    updateChart();
    return () => {
      if (chartRef.current) {
        chartRef.current.destroy();
      }
    };
  }, [chartName, chartId, data, options]);

  return <div id={chartId} />;
};

BarGrowthGraph.propTypes = {
  chartName: PropTypes.string.isRequired,
  chartId: PropTypes.string,
  options: PropTypes.object,
  data: PropTypes.array,
  shouldUseSecondaryLabel: PropTypes.bool
};

BarGrowthGraph.defaultProps = {
  shouldUseSecondaryLabel: false
};

export default BarGrowthGraph;
