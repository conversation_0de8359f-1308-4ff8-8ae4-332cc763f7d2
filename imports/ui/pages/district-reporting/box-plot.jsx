import React, { useEffect, useRef } from "react";
import PropTypes from "prop-types";
import Highcharts from "highcharts/highstock";

import { isOnPrintPage } from "../../utilities";

// NOTE(fmazur) - SPRIN-2782 - unused for the time being
const BoxPlot = ({ chartName, chartId, data, options }) => {
  const chartRef = useRef(null);

  const getMasteryPoints = that => {
    const customPoints = [];
    const { points } = that.series[0];
    const sortedNumberOfClasswideSkills = points.map(p => p.numberOfClasswideSkills).reverse();
    // NOTE(fmazur) - !!! points are in correct order but are rendered in reverse in this case
    // NOTE(fmazur) - !!! which means first category would have point.options from last category
    points.forEach((point, index) => {
      // NOTE(fmazur) - SPRIN-2683 - Place extra line 2 above the number of classwide skills
      const numberOfClasswideSkills = sortedNumberOfClasswideSkills[index];
      const width = 2;
      const height = point.pointWidth;
      const x = that.plotLeft + (that.plotBox.width / options.scaleMax) * numberOfClasswideSkills - width / 2;
      const y = point.plotX + point.pointWidth + width;
      customPoints.push(
        that.renderer
          .rect(x, y, width, height)
          .attr({ fill: "red", zIndex: 3 })
          .add()
      );
    });
    return customPoints;
  };

  const getRowLabel = that => {
    const customLabels = [];
    const { points } = that.series[0];
    const sortedLabels = points
      .map(point => {
        const { low, q1, median, q3, high } = point.options;
        return { text: `Low: ${low}, Q1: ${q1}, Median: ${median}, Q3: ${q3}, High: ${high}`, low };
      })
      .reverse();
    points.forEach((point, index) => {
      const y = point.plotX + point.pointWidth + point.shapeArgs.width;
      const xOffset = (that.plotBox.width / options.scaleMax) * sortedLabels[index].low;
      const labelWidth = 276;
      const outOfBoundsOffset =
        that.plotBox.width - xOffset < labelWidth ? that.plotBox.width - xOffset - labelWidth : 0;
      const tickOffset = 3;
      const x = that.plotLeft + xOffset + outOfBoundsOffset - tickOffset;
      customLabels.push(
        that.renderer
          .label(sortedLabels[index].text, x, y)
          .attr({
            zIndex: 4
          })
          .css({
            color: "#000",
            fontSize: "12px"
          })
          .add()
      );
    });
    return customLabels;
  };

  const updateChart = () => {
    const isPrinting = isOnPrintPage();
    chartRef.current = new Highcharts.Chart(chartId, {
      title: { text: chartName || "" },
      chart: {
        type: "boxplot",
        inverted: true,
        height: options.chartHeight,
        events: {
          load() {
            this.customPoints = [];
            this.customLabels = [];
          },
          render() {
            // NOTE(fmazur) - remove all custom points before redrawing to avoid duplicates
            this.customPoints.forEach(point => {
              point.destroy();
            });
            this.customPoints = getMasteryPoints(this);

            this.customLabels.forEach(point => {
              point.destroy();
            });
            this.customLabels = getRowLabel(this);
          }
        }
      },
      accessibility: {
        enabled: false
      },
      subtitle: {
        text: options.subtitleText || "",
        style: { color: "#000" },
        verticalAlign: "bottom",
        y: 20
      },
      credits: { enabled: false },
      yAxis: {
        min: options.scaleMin,
        max: options.scaleMax,
        tickInterval: 1,
        title: { text: "" },
        labels: {
          overflow: "allow",
          style: { ...(isPrinting ? { color: "#000", fontSize: "6pt" } : {}) },
          ...(isPrinting ? { y: 15 } : {})
        }
      },
      legend: {
        enabled: false
      },
      xAxis: {
        type: "category",
        labels: {
          autoRotationLimit: 0,
          align: "right",
          style: { whiteSpace: "normal", ...(isPrinting ? { color: "#000" } : {}) }
        },
        ...(options.usePlotLines
          ? {
              plotLines: options.categories.map((c, index) => ({
                color: "#e6e6e6",
                width: 2,
                value: index + 0.6,
                zIndex: 100
              }))
            }
          : {}),
        // Names of bar groups
        categories: options.categories
      },
      plotOptions: {
        series: {
          animation: options.animation ?? true,
          pointPadding: 0,
          groupPadding: 0
        },
        boxplot: {
          pointWidth: 30,
          grouping: false,
          shadow: false
        }
      },
      series: [{ dataLabels: { enabled: true }, data }]
    });
  };

  useEffect(() => {
    updateChart();
    return () => {
      if (chartRef.current) {
        chartRef.current.destroy();
      }
    };
  }, [chartName, chartId, data, options]);

  return <div id={chartId} />;
};

BoxPlot.propTypes = {
  chartName: PropTypes.string.isRequired,
  type: PropTypes.string,
  chartId: PropTypes.string,
  options: PropTypes.object,
  data: PropTypes.array,
  shouldUseSecondaryLabel: PropTypes.bool
};

BoxPlot.defaultProps = {
  shouldUseSecondaryLabel: false
};

export default BoxPlot;
