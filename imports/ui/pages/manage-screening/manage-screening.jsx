import React, { Component } from "react";
import PropTypes from "prop-types";
import difference from "lodash/difference";
import groupBy from "lodash/groupBy";
import capitalize from "lodash/capitalize";
import { Meteor } from "meteor/meteor";
import { withTracker } from "meteor/react-meteor-data";
import { Button } from "react-bootstrap";
import PageHeader from "../../components/page-header.jsx";
import { ScreeningAssignments } from "/imports/api/screeningAssignments/screeningAssignments";
import { Grades } from "/imports/api/grades/grades";
import { Assessments } from "/imports/api/assessments/assessments";
import { Loading } from "../../components/loading.jsx";
import { translateBenchmarkPeriod } from "/imports/api/utilities/utilities";
import ScreeningAssignment from "./screening-assignment";
import { download, isHighSchoolGrade } from "../../utilities";
import { getCSV } from "../data-admin/upload/file-upload-utils";
import ConfirmModal from "../data-admin/confirm-modal";

export class ManageScreening extends Component {
  state = {
    isInEditMode: false,
    shouldDisplayModal: false
  };

  exportScreeningDataCSV = () => {
    const data = [];
    let headers = { Fall: [], Winter: [], Spring: [] };

    const { grades, screeningAssignments } = this.props;
    const { all, ...validBenchmarkPeriods } = screeningAssignments;
    const validGrades = grades.filter(grade => grade._id !== "HS");
    Object.values(validGrades).forEach((grade, gradeIndex) => {
      const row = { Grade: grade._id };
      Object.values(validBenchmarkPeriods).forEach(benchmarkPeriod => {
        benchmarkPeriod
          .find(elem => elem.grade === grade._id)
          .assessmentIds.forEach((assessmentId, assessmentIndex) => {
            const assessment = this.props.assessments.find(elem => assessmentId === elem._id);
            const benchmarkPeriodName = capitalize(benchmarkPeriod[gradeIndex].benchmarkPeriodName);
            row[`${benchmarkPeriodName} Assessment ${assessmentIndex + 1} Number`] =
              assessment.monitorAssessmentMeasure;
            row[`${benchmarkPeriodName} Assessment ${assessmentIndex + 1} Name`] = assessment.name;
            if (
              headers[benchmarkPeriodName].indexOf(
                `${benchmarkPeriodName} Assessment ${assessmentIndex + 1} Number`
              ) === -1
            ) {
              headers[benchmarkPeriodName].push(
                `${benchmarkPeriodName} Assessment ${assessmentIndex + 1} Number`,
                `${benchmarkPeriodName} Assessment ${assessmentIndex + 1} Name`
              );
            }
          });
      });
      data.push(row);
    });

    headers = Object.values(headers).flat();
    headers.unshift("Grade");

    const hrefData = `data:application/octet-stream,${getCSV({ data, fields: headers })}`;

    download({
      filename: `screeningData_${new Date().toISOString().slice(0, 10)}.csv`,
      hrefData
    });
  };

  enableEditMode = () => {
    this.setState({
      shouldDisplayModal: false,
      isInEditMode: true
    });
  };

  showModal = () => {
    if (this.state.isInEditMode) {
      this.setState({ isInEditMode: false });
    } else {
      this.setState({ shouldDisplayModal: true });
    }
  };

  closeModal = () => {
    this.setState({ shouldDisplayModal: false });
  };

  render() {
    if (this.props.loading) {
      return <Loading message="Getting Screening Assignments" />;
    }
    const { grades } = this.props;
    const hsGrades = grades.filter(grade => isHighSchoolGrade(grade._id));
    const primaryGrades = difference(grades, hsGrades);
    return (
      <div className="conFullScreen">
        <div className="relativeWrapper">
          <Button
            className="btn btn-primary btn-success buttonInPageHeader"
            name={"exportScreeningDataCSV"}
            data-testid={"exportScreeningDataCSV"}
            onClick={this.exportScreeningDataCSV}
          >
            Export Screening Data
          </Button>
          <PageHeader
            title={"Manage Screening"}
            description={"Setup the screening assessments that are handed out to all grades for a given period."}
          />
        </div>
        <div className="container">
          <div className="card-box">
            <div className="d-flex flex-row gap-2">
              <div>
                <Button
                  className="btn btn-primary btn-success btn-xs"
                  name="editScreeningAssignments"
                  data-testid="editScreeningAssignments"
                  onClick={this.showModal}
                >
                  {this.state.isInEditMode ? "Cancel Edit Mode" : "Edit Screening Assessments"}
                </Button>
              </div>
            </div>
            {primaryGrades.map(grade => (
              <div key={grade._id} className="row">
                <div>
                  <hr />
                </div>
                <h4 className="">Grade: {grade.display}</h4>
                {this.props.periods.map(period => (
                  <ScreeningAssignment
                    key={`${grade}${period}`}
                    period={period}
                    grade={grade}
                    assessments={this.props.assessments}
                    screeningAssignments={this.props.screeningAssignments}
                    isInEditMode={this.state.isInEditMode}
                  />
                ))}
              </div>
            ))}
          </div>
          <hr />
          <div className="card-box">
            {hsGrades.map(grade => (
              <div key={grade._id} className="row">
                <h4>Grade: {grade.display}</h4>
                <ScreeningAssignment
                  key={`${grade}allPeriods`}
                  period="all"
                  grade={grade}
                  assessments={this.props.assessments}
                  screeningAssignments={this.props.screeningAssignments}
                  isInEditMode={this.state.isInEditMode}
                />
              </div>
            ))}
          </div>
        </div>
        <ConfirmModal
          showModal={this.state.shouldDisplayModal}
          bodyText={
            <div className="text-danger">
              Modifying Screening Assignment during school year will break student group history!
            </div>
          }
          confirmAction={this.enableEditMode}
          onCloseModal={this.closeModal}
        />
      </div>
    );
  }
}

ManageScreening.propTypes = {
  loading: PropTypes.bool,
  screeningAssignments: PropTypes.object,
  grades: PropTypes.array,
  assessments: PropTypes.array,
  periods: PropTypes.array
};

export default withTracker(() => {
  const saHandle = Meteor.subscribe("ScreeningAssignments");
  const gHandle = Meteor.subscribe("Grades");
  const aHandle = Meteor.subscribe("Assessments");
  const loading = !saHandle.ready() || !gHandle.ready() || !aHandle.ready();
  let screeningAssignments = {};
  let grades = [];
  let assessments = [];
  // TODO: get the below line from the db
  const periods = ["fall", "winter", "spring"];
  if (!loading) {
    screeningAssignments = ScreeningAssignments.find().fetch();
    screeningAssignments.forEach(screeningAssessment => {
      const sa = screeningAssessment;
      sa.benchmarkPeriodName = translateBenchmarkPeriod(sa.benchmarkPeriodId).name;
    });
    screeningAssignments = groupBy(screeningAssignments, "benchmarkPeriodName");
    grades = Grades.find({}, { sort: { sortorder: 1 } }).fetch();
    assessments = Assessments.find({ monitorAssessmentMeasure: { $exists: true } }, { sort: { name: 1 } }).fetch();
  }
  return { loading, screeningAssignments, grades, assessments, periods };
})(ManageScreening);
