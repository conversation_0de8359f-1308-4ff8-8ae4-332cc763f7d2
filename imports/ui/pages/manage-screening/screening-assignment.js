import React, { Component } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { Meteor } from "meteor/meteor";
import find from "lodash/find";
import every from "lodash/every";
import { translateBenchmarkPeriod, capitalizeFirstLetter } from "/imports/api/utilities/utilities";
import { isHighSchoolGrade } from "../../utilities";
import TooltipWrapper from "../../components/tooltip-wrapper";

export default class ScreeningAssignment extends Component {
  state = {};

  toggleFuzzyBox = event => {
    const { grade } = event.target.dataset;
    const { period } = event.target.dataset;
    const state = {};
    state[`fuzzyBox${grade}${period}`] = !this.state[`fuzzyBox${grade}${period}`];
    this.setState(state);
  };

  getAssessmentName(_id, index) {
    const assessment = find(this.props.assessments, { _id });
    if (!assessment) {
      return "No Name on assessment";
    }
    return (
      <div className="d-flex flex-row align-items-center gap-1" style={{ width: "100%" }}>
        <span>{index + 1}.</span>{" "}
        <small className="font-13 font-light text-nowrap">(AM {assessment.monitorAssessmentMeasure})</small>{" "}
        <TooltipWrapper text={assessment.name} />
      </div>
    );
  }

  matchesSearchString = ({ grade, period, assessmentName, monitorAssessmentMeasure = "", associatedGrades }) => {
    if (associatedGrades.indexOf(grade) < 0) {
      return false;
    }
    const searchString = this.state[`searchString${grade}${period}`];
    if (!searchString || searchString === "" || monitorAssessmentMeasure.includes(searchString)) {
      return true;
    }
    const doesMatch = [];
    searchString.split(" ").forEach(match => {
      doesMatch.push(assessmentName.toLowerCase().indexOf(match.toLowerCase()) !== -1);
    });
    return every(doesMatch);
  };

  addAssessment = event => {
    const { grade } = event.target.dataset;
    const { period } = event.target.dataset;
    const { assessmentId } = event.target.dataset;
    const screeningAssignment = find(this.props.screeningAssignments[period], { grade });
    if (!screeningAssignment) {
      Meteor.call(
        "ScreeningAssignments:insert",
        {
          grade: event.target.dataset.grade,
          benchmarkPeriodId: translateBenchmarkPeriod(capitalizeFirstLetter(event.target.dataset.period)).id,
          assessmentIds: [],
          created: {
            by: "DDP",
            on: Date.now(),
            date: new Date()
          },
          lastModified: {
            by: "DDP",
            on: Date.now(),
            date: new Date()
          }
        },
        (err, res) => {
          this.updateScreeningAssignment(res, assessmentId);
        }
      );
    } else {
      this.updateScreeningAssignment(screeningAssignment._id, assessmentId);
    }
    this.toggleFuzzyBox(event);
  };

  setSearchString = event => {
    const { grade } = event.target.dataset;
    const { period } = event.target.dataset;
    const state = {};
    state[`searchString${grade}${period}`] = this[`input${grade}${period}`].value;
    this.setState(state);
  };

  updateScreeningAssignment = (screeningAssignmentId, assessmentId) => {
    Meteor.call("ScreeningAssignments:addAssessmentId", screeningAssignmentId, assessmentId, (err, resp) => {
      if (!err && resp) {
        Alert.success("Screening Assignment updated!", { timeout: 2000 });
      }
    });
  };

  removeScreeningAssignment = event => {
    const { screeningAssignmentId, assessmentId } = event.target.dataset;
    Meteor.call("ScreeningAssignments:removeAssessmentId", screeningAssignmentId, assessmentId, (err, resp) => {
      if (!err && resp) {
        Alert.success("Screening Assignment removed!", { timeout: 2000 });
      }
    });
  };

  renderFuzzyBoxElement = () => {
    const hasFuzzyBoxEnabled = this.state[`fuzzyBox${this.props.grade._id}${this.props.period}`];
    if (!hasFuzzyBoxEnabled || !this.props.isInEditMode) {
      return null;
    }
    const filteredAssessments = hasFuzzyBoxEnabled
      ? this.props.assessments.filter(assessment =>
          this.matchesSearchString({
            grade: this.props.grade._id,
            period: this.props.period,
            assessmentName: assessment.name,
            monitorAssessmentMeasure: assessment.monitorAssessmentMeasure,
            associatedGrades: assessment.associatedGrades
          })
        )
      : this.props.assessments;
    return (
      <div className="fuzzyBox">
        <div className="fuzzyBox-header">
          Total: {filteredAssessments.length}
          <i
            className="fa fa-times pull-right text-danger"
            aria-hidden="true"
            onClick={this.toggleFuzzyBox}
            data-grade={this.props.grade._id}
            data-period={this.props.period}
            style={{
              cursor: "pointer"
            }}
          />
        </div>
        {filteredAssessments.map(assessment => {
          return (
            <div
              className="fuzzyBox-item"
              key={assessment._id}
              data-grade={this.props.grade._id}
              data-period={this.props.period}
              data-assessment-id={assessment._id}
              onClick={this.addAssessment}
            >
              AM {assessment.monitorAssessmentMeasure} - {assessment.name}
            </div>
          );
        })}
      </div>
    );
  };

  render() {
    return (
      <div
        key={`${this.props.grade._id}${this.props.period}`}
        className={`col-${isHighSchoolGrade(this.props.grade._id) ? "12" : "4"}`}
      >
        <div className="font-light">
          {capitalizeFirstLetter(
            isHighSchoolGrade(this.props.grade._id) ? "Goal Skills for Individual interventions" : this.props.period
          )}
        </div>
        {this.props.isInEditMode ? (
          <div
            className={`form-group${isHighSchoolGrade(this.props.grade._id) ? " col-4" : ""}`}
            style={{
              position: "relative"
            }}
          >
            <input
              type="text"
              className="form-control"
              placeholder="Click to add assessment"
              data-grade={this.props.grade._id}
              data-period={this.props.period}
              ref={r => {
                this[`input${this.props.grade._id}${this.props.period}`] = r;
              }}
              value={this.state[`searchString${this.props.grade._id}${this.props.period}`]}
              onChange={this.setSearchString}
              onFocus={this.toggleFuzzyBox}
            />
            {this.renderFuzzyBoxElement()}
          </div>
        ) : null}
        {this.props.screeningAssignments[this.props.period] &&
          this.props.screeningAssignments[this.props.period].map(screeningAssignment => {
            if (screeningAssignment.grade !== this.props.grade._id) return null;
            return screeningAssignment.assessmentIds.map((assessmentId, index) => (
              <div key={assessmentId + index} className="d-flex flex-row align-items-center gap-1">
                {this.props.isInEditMode ? (
                  <i
                    className="fa fa-trash red-hover"
                    aria-hidden="true"
                    onClick={this.removeScreeningAssignment}
                    data-screening-assignment-id={screeningAssignment._id}
                    data-assessment-id={assessmentId}
                  />
                ) : null}
                {this.getAssessmentName(assessmentId, index)}
              </div>
            ));
          })}
      </div>
    );
  }
}

ScreeningAssignment.propTypes = {
  screeningAssignments: PropTypes.object,
  grade: PropTypes.object,
  assessments: PropTypes.array,
  period: PropTypes.string,
  isInEditMode: PropTypes.bool
};
