import React from "react";
import MockDate from "mockdate";
import { assert } from "chai";

import CoachAccountSetup from "./coach-account-setup.jsx";
import { renderWithRouter } from "../../../../tests/helpers/testUtils";

describe("CoachAccountSetup UI", () => {
  jest.mock("../../../api/utilities/utilities", () => ({
    getCurrentSchoolYear: jest.fn(() => 2019)
  }));
  beforeAll(() => {
    MockDate.set("2018-12-20");
  });
  afterAll(() => {
    jest.restoreAllMocks();
    MockDate.reset();
  });
  describe("Render", () => {
    let coachAccountSetupComponent;
    beforeEach(() => {
      coachAccountSetupComponent = renderWithRouter(<CoachAccountSetup />);
    });
    it("render", () => {
      // Verify that the method does what we expected
      assert.isDefined(coachAccountSetupComponent, "coachAccountSetupComponent did not render");
    });
  });
});
