import { assert } from "chai";
import React from "react";
import { shallow } from "enzyme";
import DataAdminAccountSetup from "./data-admin-account-setup.jsx";

describe("AccountSetup UI", () => {
  describe("Render", () => {
    it("render", () => {
      // Verify that the method does what we expected
      const accountSetupComponent = shallow(<DataAdminAccountSetup />);
      assert.isDefined(accountSetupComponent, "accountSetupComponent did not render");
    });
  });
});
