import React, { Component } from "react";
import { Meteor } from "meteor/meteor";
import { withRouter } from "react-router-dom";
import { withTracker } from "meteor/react-meteor-data";
import PropTypes from "prop-types";
import { Button } from "react-bootstrap";
import Select from "react-select";

import Alert from "react-s-alert";
import PageHeader from "../../components/page-header.jsx";
import { getMeteorUserSync } from "/imports/api/utilities/utilities";

class ClientSetup extends Component {
  constructor(props) {
    super(props);
    this.state = {
      clientName: "",
      city: "",
      street: "",
      state: "",
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      isTestOrg: false,
      rostering: this.isUniversalDataAdmin() ? "rosterUpload" : "rosterImport",
      ssoIssuerOrgId: ""
    };
  }

  handleInputChange = event => {
    if (event.value) {
      this.setState({
        rostering: event.value
      });
    } else {
      const { target } = event;
      const value = target.type === "checkbox" ? target.checked : target.value;
      const { name } = target;

      this.setState({
        [name]: value
      });
    }
  };

  handleSubmit = () => {
    const {
      clientName,
      city,
      street,
      state,
      zip,
      firstName,
      lastName,
      email,
      phone,
      isTestOrg,
      rostering,
      ssoIssuerOrgId
    } = this.state;

    if (!clientName || clientName.length === 0) {
      Alert.error("Client Name is required");
      return;
    }
    if (!firstName || firstName.length === 0) {
      Alert.error("Primary Contact First Name is required");
      return;
    }
    if (!lastName || lastName.length === 0) {
      Alert.error("Primary Contact Last Name is required");
      return;
    }
    if (!city || city.length === 0) {
      Alert.error("City is required");
      return;
    }
    if (this.props.selfEnrollment && (!street || street.length === 0)) {
      Alert.error("Street and street no is required");
      return;
    }
    if (!state || state.length === 0) {
      Alert.error("State is required");
      return;
    }
    if (this.props.selfEnrollment && (!zip || zip.length === 0)) {
      Alert.error("ZIP code is required");
      return;
    }
    const meteorCallParams = {
      clientName,
      city,
      street,
      state,
      zip,
      firstName,
      lastName,
      email,
      phone,
      ssoIssuerOrgId
    };
    if (this.props.selfEnrollment) {
      Meteor.call("Organizations:insertSelfEnrollmentDistrict", meteorCallParams, err => {
        if (err) {
          Alert.error(err.message);
        } else {
          this.props.history.push("/");
        }
      });
      return;
    }
    Meteor.call("Organizations:insertDistrict", { ...meteorCallParams, isTestOrg, rostering }, (err, orgid) => {
      if (err) {
        Alert.error(err.message);
      } else {
        const route = `/data-admin/dashboard/${orgid}`;
        this.props.history.push(route);
      }
    });
  };

  renderRosteringMenu = () => {
    const rosterLabelByKey = {
      rosterImport: "Roster Import",
      rosterUpload: "File Upload",
      rosterEdFi: "Ed-Fi",
      rosterOR: "OneRoster"
    };
    const options = Object.entries(rosterLabelByKey).map(([value, label]) => ({ value, label }));
    const { rostering } = this.state;

    return (
      <div data-testid="rostering-select">
        <Select
          onChange={this.handleInputChange}
          classNamePrefix="react-select"
          value={{ value: rostering, label: rosterLabelByKey[rostering] }}
          options={options}
        />
      </div>
    );
  };

  renderInstructions = () => {
    const step1 = <li key={1}>Complete the form below.</li>;
    const step2 = <li key={2}>This creates a client organization (or district.)</li>;
    return this.props.selfEnrollment
      ? [
          <p key={1}>{`Here's how it works for groups of 20 students or less`}</p>,
          <ol key={2}>
            {step1}
            {step2}
            <li key={3}>The cost will be $15 per student. You can pay that once we get the students entered.</li>
          </ol>
        ]
      : [
          <p key={1}>{`Here's how it works:`}</p>,
          <ol key={2}>
            {step1}
            {step2}
          </ol>
        ];
  };

  isUniversalDataAdmin = () => {
    return this.props.userRoles.includes("universalDataAdmin");
  };

  render() {
    const isUniversalDataAdmin = this.isUniversalDataAdmin();
    return (
      <div className="conFullScreen medium-width">
        <PageHeader title={this.props.selfEnrollment ? "SpringMath self enrollment" : "Client Setup"} />
        <div className="container">
          {this.renderInstructions()}
          <div className="card-box">
            <form>
              <div className="form-group">
                <label>User Group Name (or District Name)</label>
                <input
                  onChange={this.handleInputChange}
                  type="text"
                  required="required"
                  name="clientName"
                  value={this.state.clientName}
                  className="form-control"
                />
              </div>
              {this.props.selfEnrollment || isUniversalDataAdmin ? null : (
                <div className="form-group">
                  <label>
                    <input type="checkbox" name="isTestOrg" className="m-r-10" onClick={this.handleInputChange} />
                    Is Test Organization{" "}
                    <span className="small">(Allows using custom dates and managing test data)</span>
                  </label>
                </div>
              )}
              {
                <div className="form-group">
                  <label>
                    Select rostering option for this organization
                    {this.renderRosteringMenu()}
                  </label>
                </div>
              }
              <div className="form-group">
                <label>City</label>
                <input
                  onChange={this.handleInputChange}
                  type="text"
                  required="required"
                  name="city"
                  value={this.state.city}
                  className="form-control"
                />
              </div>
              <div className="form-group">
                <label>Street & street no {this.props.selfEnrollment ? "" : "(optional)"}</label>
                <input
                  onChange={this.handleInputChange}
                  type="text"
                  required={this.props.selfEnrollment ? "required" : false}
                  name="street"
                  value={this.state.street}
                  className="form-control"
                />
              </div>
              <div className="form-group">
                <label>State</label>
                <input
                  onChange={this.handleInputChange}
                  type="text"
                  required="required"
                  name="state"
                  value={this.state.state}
                  className="form-control"
                />
              </div>
              <div className="form-group">
                <label>ZIP code {this.props.selfEnrollment ? "" : "(optional)"}</label>
                <input
                  onChange={this.handleInputChange}
                  type="text"
                  required={this.props.selfEnrollment ? "required" : false}
                  name="zip"
                  value={this.state.zip}
                  className="form-control"
                />
              </div>
              <div className="form-group">
                <fieldset>
                  <legend>{this.props.selfEnrollment ? "Your information" : "Primary Contact"}</legend>
                  <div className="form-group">
                    <label>First Name</label>
                    <input
                      onChange={this.handleInputChange}
                      type="text"
                      name="firstName"
                      value={this.state.firstName}
                      className="form-control"
                    />
                  </div>
                  <div className="form-group">
                    <label>Last Name</label>
                    <input
                      onChange={this.handleInputChange}
                      type="text"
                      name="lastName"
                      value={this.state.lastName}
                      className="form-control"
                    />
                  </div>
                  <div className="form-group">
                    <label>Email {this.props.selfEnrollment ? "" : "(optional)"}</label>
                    <input
                      onChange={this.handleInputChange}
                      type="text"
                      name="email"
                      value={this.state.email}
                      className="form-control"
                    />
                  </div>
                  {this.props.selfEnrollment ? (
                    ""
                  ) : (
                    <div className="form-group">
                      <label>Phone (optional)</label>
                      <input
                        onChange={this.handleInputChange}
                        type="text"
                        name="phone"
                        value={this.state.phone}
                        className="form-control"
                      />
                    </div>
                  )}
                  <div className="form-group">
                    <label>District SSO ID (optional)</label>
                    <input
                      onChange={this.handleInputChange}
                      type="text"
                      name="ssoIssuerOrgId"
                      value={this.state.ssoIssuerOrgId}
                      className="form-control"
                    />
                  </div>
                  {/* {this.props.selfEnrollment ? */}
                  {/* <div className="form-group"> */}
                  {/* <label>Number of students I want to work with</label> */}
                  {/* <select> */}
                  {/* {utils.range(20).map(i => <option key={i}>{i + 1}</option>)} */}
                  {/* </select> */}
                  {/* </div> : null} */}
                </fieldset>
              </div>
              <div className="form-group">
                {this.props.selfEnrollment ? (
                  <p>
                    Once you click enroll, you will be sent an onboarding email so that you can create a password for
                    the system and begin entering your students.
                  </p>
                ) : null}
                <Button href="#" className="btn btn-success" onClick={this.handleSubmit}>
                  {this.props.selfEnrollment ? "Enroll in SpringMath" : "Create Client"}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    );
  }
}

ClientSetup.propTypes = {
  selfEnrollment: PropTypes.bool,
  userRoles: PropTypes.array,
  history: PropTypes.object
};

const ClientSetupWithRouter = withRouter(ClientSetup);

export default withTracker(() => {
  const user = getMeteorUserSync();
  let userRoles = [];
  if (user) {
    userRoles = user.profile.siteAccess.map(sa => sa.role.split("arbitraryId")[1]);
  }
  return {
    userRoles
  };
})(ClientSetupWithRouter);
