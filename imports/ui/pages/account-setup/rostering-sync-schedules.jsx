import { Meteor } from "meteor/meteor";
import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import PropTypes from "prop-types";
import { withTracker } from "meteor/react-meteor-data";
import { keyBy, startCase } from "lodash";
import moment from "moment-timezone";
import Alert from "react-s-alert";

import PageHeader from "/imports/ui/components/page-header.jsx";
import { Loading } from "/imports/ui/components/loading.jsx";
import { Organizations } from "/imports/api/organizations/organizations";
import ImportHistoryModal from "../data-admin/import-history-modal";

const RosteringSyncSchedules = props => {
  const [jobsByOrgId, setJobsByOrgId] = useState(null);
  const [isFetching, setIsFetching] = useState(false);
  const [selectedOrgId, setSelectedOrgId] = useState(null);
  const [lastImportHistoryStatus, setLastImportHistoryStatus] = useState([]);

  useEffect(() => {
    const orgids = props.orgs.map(org => org._id);
    if (orgids.length) {
      setIsFetching(true);
      Meteor.call("Cron:getJobsByOrgId", (err, resp) => {
        setIsFetching(false);
        if (err) {
          return Alert.error(err.message || "There was an issue with fetching scheduled jobs");
        }
        return setJobsByOrgId(resp);
      });
    }
  }, [props.loading]);

  useEffect(() => {
    Meteor.call("RosterImports:getLatestRosterImportForOrgsWithSyncSchedule", (err, resp) => {
      if (!err) {
        setLastImportHistoryStatus(resp);
      }
    });
  }, [props.loading]);

  const getSyncScheduleData = ({ rosteringSettings }) => {
    const {
      syncSchedule: { frequency, timeZone, startDate, endDate, time }
    } = rosteringSettings;

    const startMomentDate = moment(`${startDate} ${time}`);
    const endMomentDate = moment(endDate);
    const formattedFrequency = {
      daily: startCase(frequency),
      weekly: `${startCase(frequency)} (${startMomentDate.format("dddd")})`,
      monthly: `${startCase(frequency)} (${
        startMomentDate.date() >= 28 ? "last" : `${startMomentDate.format("Do")}`
      } day of month)`
    };

    return {
      frequency: formattedFrequency[frequency],
      time: `${startMomentDate.format("LT")} (GMT ${moment()
        .tz(timeZone)
        .format("Z")})`,
      startDate: startMomentDate.format("L"),
      endDate: endMomentDate.format("L")
    };
  };

  const removeSyncSchedule = (e, orgid) => {
    e.stopPropagation();
    e.preventDefault();
    Meteor.call("Organizations:removeRosterSyncSchedule", orgid, err => {
      if (err) {
        Alert.error(err.reason || "There was an error while removing rostering sync settings", {
          timeout: 3000
        });
      } else {
        Alert.success("Rostering sync settings removed successfully.", {
          timeout: 3000
        });
      }
    });
  };

  if (props.loading || isFetching) {
    return <Loading />;
  }

  const orgsWithCronJobs = props.orgs
    .filter(org => jobsByOrgId?.[org._id])
    .sort((a, b) => a.name.localeCompare(b.name));

  const renderTableBody = () => {
    const lastImportStatusByOrgid = keyBy(lastImportHistoryStatus, "orgid");
    const statusIconClassNames = {
      completed: "fa fa-check",
      "upload failed": "fa fa-times",
      validating: "fa fa-hourglass-half"
    };

    const statusHeaderClassNames = {
      completed: "alert-success",
      "upload failed": "alert-danger",
      validating: "alert-warning"
    };
    return orgsWithCronJobs.map(org => {
      const lastImportStatusOrg = lastImportStatusByOrgid[org._id];
      const hasErrorsInLastImport = Object.keys(lastImportStatusOrg?.error || {}).length > 0;
      const { status } = jobsByOrgId[org._id] || {};
      const statusClass = {
        Running: " text-success",
        Stopped: " text-danger"
      };
      const { frequency, time, startDate, endDate } = getSyncScheduleData(org);
      return (
        <tr key={`cronDetails_${org._id}`} id={org._id} onClick={() => setSelectedOrgId(org._id)}>
          <td>
            <Link to={`/data-admin/dashboard/${org._id}`}>{org.name}</Link>
          </td>
          <td>{frequency}</td>
          <td>{time}</td>
          <td className="text-center">
            {lastImportStatusOrg ? (
              <div className="btn-group d-flex">
                <div className={`btn ${statusHeaderClassNames[lastImportStatusOrg?.status]}`}>
                  <i className={statusIconClassNames[lastImportStatusOrg?.status]} aria-hidden="true" />
                </div>
                <div className={`btn ${hasErrorsInLastImport ? "alert-danger" : "alert-success"}`}>
                  <i className={`fa fa-${hasErrorsInLastImport ? "times" : "check"}`} aria-hidden="true" />
                </div>
              </div>
            ) : (
              "N/A"
            )}
          </td>
          <td className="text-center">{startDate}</td>
          <td className="text-center">{endDate}</td>
          <td className={`text-center${statusClass[status]}`}>{status}</td>
          <td>
            <button
              type="button"
              className="btn btn-sm btn-danger"
              name="buttonDeleteRosterSyncSchedule"
              data-testid="deleteRosterSyncSchedule"
              onClick={e => removeSyncSchedule(e, org._id)}
            >
              Delete
            </button>
          </td>
        </tr>
      );
    });
  };

  return (
    <div className="conFullScreen">
      <PageHeader title="External Rostering Sync Schedules" />
      <div className="container">
        <div className="row">
          <div className="col-sm-12">
            <div className="card-box">
              {orgsWithCronJobs.length ? (
                <React.Fragment>
                  <table className="table table-condensed table-striped table-hover">
                    <thead>
                      <tr>
                        <th className="col-md-3">Organization Name</th>
                        <th className="col-md-2">Frequency</th>
                        <th className="col-md-2 text-center text-nowrap">Time</th>
                        <th className="col-md-1 text-center text-nowrap">Last Run Status</th>
                        <th className="col-md-1 text-center">Start Date</th>
                        <th className="col-md-1 text-center">End Date</th>
                        <th className="col-md-1 text-center">Status</th>
                        <th className="col-md-1"></th>
                      </tr>
                    </thead>

                    <tbody>{renderTableBody()}</tbody>
                  </table>
                  <ImportHistoryModal
                    showModal={!!selectedOrgId}
                    onCloseModal={() => setSelectedOrgId(null)}
                    org={orgsWithCronJobs.find(o => o._id === selectedOrgId) || {}}
                  />
                </React.Fragment>
              ) : (
                <div className="alert alert-warning text-center">
                  No Organization has active rostering sync schedule
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

RosteringSyncSchedules.propTypes = {
  loading: PropTypes.bool,
  orgs: PropTypes.array
};

export default withTracker(() => {
  const orgSub = Meteor.subscribe("Organizations");
  const loading = !orgSub.ready();

  let orgs = [];
  if (!loading) {
    const query = {
      isSelfEnrollee: { $ne: true },
      "rosteringSettings.syncSchedule.startDate": { $exists: true }
    };
    orgs = Organizations.find(query, {
      fields: { name: 1, "rosteringSettings.syncSchedule": 1, schoolYearBoundary: 1 }
    })
      .fetch()
      ?.map(({ _id, name, rosteringSettings, schoolYearBoundary }) => ({
        _id,
        name,
        rosteringSettings,
        schoolYearBoundary
      }));
  }
  return {
    loading,
    orgs
  };
})(RosteringSyncSchedules);
