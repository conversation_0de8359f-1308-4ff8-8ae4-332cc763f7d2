import React from "react";
import MockDate from "mockdate";
import { assert } from "chai";

import SupportAccountSetup from "./support-account-setup.jsx";
import { renderWithRouter } from "../../../../tests/helpers/testUtils";

jest.mock("../../../api/utilities/utilities", () => ({
  ...jest.requireActual("../../../api/utilities/utilities"),
  getCurrentSchoolYear: jest.fn(() => Promise.resolve(2019))
}));

describe("SupportAccountSetup UI", () => {
  beforeAll(() => {
    MockDate.set("2018-12-20");
  });
  afterAll(() => {
    jest.restoreAllMocks();
    MockDate.reset();
  });
  describe("Render", () => {
    let supportAccountSetupComponent;
    beforeEach(() => {
      supportAccountSetupComponent = renderWithRouter(<SupportAccountSetup />);
    });
    it("render", () => {
      // Verify that the method does what we expected
      assert.isDefined(supportAccountSetupComponent, "supportAccountSetupComponent did not render");
    });
  });
});
