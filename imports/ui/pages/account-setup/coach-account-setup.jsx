import { Meteor } from "meteor/meteor";
import React, { Component } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { withRouter } from "react-router-dom";

import { withTracker } from "meteor/react-meteor-data";
import { Organizations } from "/imports/api/organizations/organizations";
import { Sites } from "/imports/api/sites/sites";
import { Loading } from "../../components/loading.jsx";
import {
  capitalizeFirstLetter,
  getMeteorUserSync,
  isEmailValid,
  isIdValid,
  userIdInvalidErrorMessage
} from "/imports/api/utilities/utilities";
import ConfirmModal from "../data-admin/confirm-modal";
import { isSingleSchoolDataAdmin } from "../data-admin/utilities";

const initialState = {
  coachEmail: "",
  coachLocalId: "",
  coachFirstName: "",
  coachLastName: "",
  selectedSites: [],
  isProcessing: false,
  shouldShowNoSiteAccessModal: false,
  actionTaken: undefined,
  submittedEmail: ""
};

class CoachAccountSetup extends Component {
  state = initialState;

  componentWillUnmount() {
    this.setState(initialState);
  }

  goToDashboard = () => {
    this.props.history.push("/");
  };

  handleSendInvite = e => {
    e.preventDefault();
    if (!this.state.selectedSites.length) {
      return Alert.error("You must select at least one site before an invite email can be sent", { timeout: 5000 });
    }
    if (!isEmailValid(this.state.coachEmail)) {
      return Alert.error("You must provide a valid email address to send an email invite to", { timeout: 5000 });
    }
    if (this.state.coachLocalId.length && !isIdValid(this.state.coachLocalId, true)) {
      return Alert.error(userIdInvalidErrorMessage, {
        timeout: 5000
      });
    }
    this.setState({ isProcessing: true });
    return Meteor.call("users:getSiteAccessByEmail", this.state.coachEmail.trim(), (err, siteAccess) => {
      if (!siteAccess || siteAccess.length) {
        this.createOrUpdateUser();
      } else if (siteAccess && !siteAccess.length) {
        this.setState({ shouldShowNoSiteAccessModal: true });
      }
    });
  };

  createOrUpdateUser = () => {
    Meteor.call(
      "users:createOrUpdateCoachUser",
      {
        orgid: this.props.org._id,
        siteIds: this.state.selectedSites,
        email: this.state.coachEmail.trim(),
        localId: this.state.coachLocalId.trim(),
        firstName: this.state.coachFirstName.trim(),
        lastName: this.state.coachLastName.trim()
      },
      (error, result) => {
        if (!error) {
          // Partially reset form after adding user
          const { submittedEmail, ...formValuesInitialState } = initialState;
          this.setState(state => ({
            ...state,
            submittedEmail: this.state.coachEmail,
            ...formValuesInitialState,
            actionTaken: result.actionTaken
          }));
          this.inviteSentSuccessDiv.scrollIntoView();
        } else {
          Alert.error(error.reason || "Error creating or updating user");
        }
        return this.setState({ isProcessing: false });
      }
    );
  };

  handleInputChange = event => {
    this.setState({ [event.target.name]: event.target.value });
  };

  componentDidMount() {
    if (this.props.siteId) {
      this.setState({ selectedSites: [this.props.siteId] });
    } else {
      this.setState({ selectedSites: [] });
    }
  }

  selectSite = event => {
    const selectedSite = event.target.value;
    const currentSites = this.state.selectedSites;
    const siteIndex = currentSites.indexOf(selectedSite);
    if (siteIndex >= 0) {
      currentSites.splice(siteIndex, 1);
    } else {
      currentSites.push(event.target.value);
    }
    this.setState({ selectedSites: currentSites });
  };

  closeModal = () => {
    this.setState({ shouldShowNoSiteAccessModal: false, isProcessing: false });
  };

  getInputField = (fieldName, isRequired = true) => {
    const id = `txt${capitalizeFirstLetter(fieldName)}`;
    return (
      <input
        id={id}
        type="text"
        className="form-control"
        name={fieldName}
        onChange={this.handleInputChange}
        value={this.state[fieldName]}
        data-testid={id}
        required={isRequired}
      />
    );
  };

  render() {
    return (
      <div className="conFullScreen medium-width">
        <div className="container add-user-screen">
          <h4 className="w7 text-center">Create Coach/Administrator Account</h4>
          <div className="create-coach-text">
            <p>This page allows you to create a new coach account.</p>
            <p>Teachers can be added by using the Manage Class functionality.</p>
            <p>
              Coaches can do everything that teachers can do, plus they can view school and grade level summary data.{" "}
            </p>
            <p>
              After you complete the form below the new Coach will receive an email invitation asking them to create a
              password for SpringMath. If the user has more than one role they can click on their name in the upper
              right-hand corner of the page and use the dropdown to move between available roles.
            </p>
            <img src="/images/multipleRolesInstruction.png" height="200" width="160" />
            <p>* Coaches can Schedule Individual Interventions for individual students.</p>
            <p>* Coaches can view the School Overview.</p>
            <p>
              * Coaches can access the grade level Admin Dashboard, which includes summary notes and intervention
              statistics by class and grade.
            </p>
            <p>
              If you have a faculty member that acts both as a teacher for one or more classes and is to act as a Coach,
              the Coach record requires a unique (different) email address than the address contained in the student
              roster file.
            </p>
            <p>
              By completing the form below, the Coach or Principal will receive an email invitation to select a weblink
              (URL) in the email and to enter and verify a password for logging into SpringMath.
            </p>
          </div>
          <hr className="dark" />
          <div className="card-box">
            {this.props.loading ? (
              <Loading />
            ) : (
              <div>
                <form onSubmit={this.handleSendInvite}>
                  <div className="form-group">
                    <label htmlFor="txtDistrictName">District Name</label>
                    <input
                      id="txtDistrictName"
                      type="text"
                      className="form-control"
                      readOnly
                      value={this.props.org.name}
                    />
                  </div>
                  {!isSingleSchoolDataAdmin() ? (
                    <div className="form-group">
                      {this.state.selectedSites.length ? null : (
                        <div className="text-center text-danger">You must select at least one site</div>
                      )}
                      <label htmlFor="txtSite">Sites</label>
                      <div>
                        {this.props.sites.map(site => (
                          <div key={site._id}>
                            <label>
                              <input
                                key={site._id}
                                checked={this.state.selectedSites.includes(site._id)}
                                type="checkbox"
                                value={site._id}
                                onChange={this.selectSite}
                              />{" "}
                              {site.name}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : null}
                  <div className="form-group">
                    <label htmlFor="txtCoachEmail">Coach Email</label>
                    {this.getInputField("coachEmail")}
                  </div>
                  <div className="form-group">
                    <label htmlFor="txtCoachId">Teacher/Local ID</label>
                    <small>(optional)</small>
                    {this.getInputField("coachLocalId", false)}
                  </div>
                  <div className="form-group">
                    <label htmlFor="txtCoachFirstName">First Name</label>
                    {this.getInputField("coachFirstName")}
                  </div>
                  <div className="form-group">
                    <label htmlFor="txtCoachLastName">Last Name</label>
                    {this.getInputField("coachLastName")}
                  </div>
                  <div className="form-group">
                    <button className="btn btn-success" type="submit" disabled={this.state.isProcessing}>
                      Submit
                    </button>
                    {this.state.actionTaken ? (
                      <button className="btn btn-success pull-right" onClick={this.goToDashboard}>
                        Back to Dashboard
                      </button>
                    ) : null}
                  </div>
                </form>
                <div
                  ref={d => {
                    this.inviteSentSuccessDiv = d;
                  }}
                >
                  {this.state.actionTaken === "created" ? (
                    <div className="alert alert-success" data-testid="createdUser">
                      User with email <strong>{this.state.submittedEmail}</strong> added. Please inform the user that
                      the invite has been sent if you aren&apos;t using SSO exclusively.
                    </div>
                  ) : null}
                  {this.state.actionTaken === "updated" ? (
                    <div className="alert alert-success" data-testid="updatedUser">
                      Existing user tied to the email <strong>{this.state.submittedEmail}</strong> was updated with a
                      coach role for this organization.
                    </div>
                  ) : null}
                  {this.state.actionTaken === null ? (
                    <div className="alert alert-warning" data-testid="noActionTaken">
                      No action was taken since existing user with the email{" "}
                      <strong>{this.state.submittedEmail}</strong> already has this role.
                    </div>
                  ) : null}
                </div>
              </div>
            )}
          </div>
          <ConfirmModal
            showModal={this.state.shouldShowNoSiteAccessModal}
            confirmAction={this.createOrUpdateUser}
            onCloseModal={this.closeModal}
            bodyQuestion=""
            bodyText={
              <div>
                The user with email <strong>{this.state.coachEmail}</strong> already is in the system but is not
                assigned to this site. Would you like to assign them to this school?
              </div>
            }
            confirmText="Assign"
            cancelText="Cancel"
          />
        </div>
      </div>
    );
  }
}

CoachAccountSetup.propTypes = {
  loading: PropTypes.bool,
  org: PropTypes.object,
  history: PropTypes.object,
  sites: PropTypes.array,
  siteId: PropTypes.string
};

const CoachAccountSetupContainer = withTracker(params => {
  const curUser = getMeteorUserSync();
  const organizationsHandler = Meteor.subscribe("Organizations", params.orgid);
  let loading = !curUser || !organizationsHandler.ready();
  let org = {};
  let sites = [];
  if (!loading) {
    org = Organizations.findOne({ _id: params.orgid });
    const sitesHandler = Meteor.subscribe("Sites", org._id);
    loading = !sitesHandler.ready();
    sites = Sites.find({ orgid: params.orgid }).fetch();
  }
  return { loading, org, sites, siteId: params.siteId };
})(withRouter(CoachAccountSetup));

export default CoachAccountSetupContainer;
