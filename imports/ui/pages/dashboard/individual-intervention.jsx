import { Meteor } from "meteor/meteor";
import React, { useState, useEffect, useContext, useCallback } from "react";
import PropTypes from "prop-types";
import { useTracker } from "meteor/react-meteor-data";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import _max from "lodash/max";
import get from "lodash/get";
import { cloneDeep } from "lodash";

import { ninjalog, translateBenchmarkPeriod, calculateRoI, shouldUseDevMode } from "/imports/api/utilities/utilities";
import ProgressMonitoringChart from "../../components/pmChart";
import { Assessments } from "/imports/api/assessments/assessments";
import PrintInterventionsDropdown from "../../components/dashboard/printInterventionsDropdown";
import ScoreEntryRowCell from "../../components/score-entry/score-entry-row-cell";
import InterventionMessage from "../../components/dashboard/intervention-message";
import { ScoreEntryContext } from "../../components/score-entry/score-entry-context";
import { StudentGroupContext } from "/imports/contexts/StudentGroupContext";
import { getCurrentDateSync } from "/imports/api/helpers/getCurrentDate";
import { filterOutHistoryItemsThatDidNotLeadToIntervention } from "../../utilities";
import SkillVideoButton from "../../components/dashboard/skill-video-button";
import IncrementalRehearsalButton from "../../components/dashboard/intervention-content/incremental-rehearsal-button";
import PreviewSkillTreeModal from "./preview-skill-tree-modal";
import TooltipWrapper from "../../components/tooltip-wrapper";
import { OrganizationContext } from "../../../contexts/OrganizationContext";
import { SiteContext } from "../../../contexts/SiteContext";
import { StaticDataContext } from "../../../contexts/StaticDataContext";
import { SchoolYearContext } from "../../../contexts/SchoolYearContext";

const max = array => {
  return _max(array) || -Infinity;
};

const findMeetingTargetIndexFromScore = (score, targets) => {
  let result = -1;
  for (let i = 0; i < targets.length; i += 1) {
    if (score < targets[i]) {
      return i - 1;
    }
    result = i;
  }
  return result;
};

export const renderResultFeedback = (
  calculatedAssessmentResults,
  bmScoresObj,
  pmScoresObj,
  studentFullName,
  bmAssId,
  bmPeriodId
) => {
  // todo logic for actual feedback
  // take apart the results and look at the targets and display some nice user feedback...
  let pmScoreMeasureIndex = 1;
  if (bmScoresObj.bmAssessmentId === pmScoresObj.assessmentId) {
    pmScoreMeasureIndex = 0;
  }
  const bmTargetScores = calculatedAssessmentResults.measures[0].targetScores;
  const pmTargetScores = calculatedAssessmentResults.measures[pmScoreMeasureIndex].targetScores;

  const currentBMResultScore = parseInt(calculatedAssessmentResults.measures[0].studentResults[0].score);
  const currentPMResultScore = parseInt(
    calculatedAssessmentResults.measures[pmScoreMeasureIndex].studentResults[0].score
  );

  // eslint-disable-next-line no-restricted-globals
  if (isNaN(currentBMResultScore)) {
    return null;
  }

  const allPreviousBMScores = bmScoresObj.scores.slice(0, -1) || [0];
  const allPreviousPMScores = pmScoresObj.scores
    .filter(scoreObj => scoreObj.bmPeriodId === bmPeriodId && scoreObj.bmAssId === bmAssId)
    .map(obj => obj.score);

  const sameAsst = bmScoresObj.bmAssessmentId === pmScoresObj.assessmentId;
  const scoreTypeText = sameAsst ? "Goal" : "Intervention";
  const scoreTypeArticleText = sameAsst ? "a" : "an";
  // Moving on to next tree? (passed bm?)
  if (currentBMResultScore >= bmTargetScores[1]) {
    return (
      <div className="text-end font-light">
        <RbAlert variant="success" className="alert-success-dark m-l-10 m-r-10 text-start">
          <i className="fa fa-thumbs-o-up fa-lg fa-right" /> {studentFullName}
          &nbsp;received a{" "}
          <strong>
            <u>Goal Skill score of {currentBMResultScore}</u>
          </strong>
          ! Excellent work.
          <br />
          Save the scores to determine if {studentFullName} should move on to a new goal skill.
        </RbAlert>
      </div>
    );
  }
  // also... indicate improvement on the benchmark if so
  const improvedOnBenchmark = currentBMResultScore > max(allPreviousBMScores);
  // Moving on to a different intervention set?
  const bestTargetBefore = findMeetingTargetIndexFromScore(
    max(sameAsst ? allPreviousBMScores : allPreviousPMScores),
    pmTargetScores
  );
  const pmTargetNow = findMeetingTargetIndexFromScore(currentPMResultScore, pmTargetScores);
  if (pmTargetNow > bestTargetBefore) {
    if (pmTargetNow >= 1) {
      return (
        <div className="text-end font-light">
          <RbAlert variant="success" className="alert-success-dark m-l-10 m-r-10 text-start">
            <i className="fa fa-thumbs-o-up fa-lg fa-right" /> {studentFullName}
            &nbsp;received {scoreTypeArticleText}{" "}
            <strong>
              <u>
                {scoreTypeText} Skill score of {currentPMResultScore}
              </u>
            </strong>
            ! Excellent work. {studentFullName} will be moving on to a new Intervention Skill.
            {improvedOnBenchmark && !sameAsst ? (
              <strong> {studentFullName} also improved on the Goal Skill!</strong>
            ) : null}
            <br />
            Save the scores to continue the intervention.
          </RbAlert>
        </div>
      );
    }
    return (
      <div className="text-end font-light">
        <RbAlert variant="success" className="alert-success-dark m-l-10 m-r-10 text-start">
          <i className="fa fa-thumbs-o-up fa-lg fa-right" /> {studentFullName}
          &nbsp;received {scoreTypeArticleText}{" "}
          <strong>
            <u>
              {scoreTypeText} Skill score of {currentPMResultScore}
            </u>
          </strong>
          ! Excellent work. There will be new practice materials next time.
          {improvedOnBenchmark && !sameAsst ? (
            <strong> {studentFullName} also improved on the Goal Skill!</strong>
          ) : null}
          <br />
          Save the scores to continue the intervention for {studentFullName}.
        </RbAlert>
      </div>
    );
  }
  // Staying here?
  return (
    <div className="text-end font-light">
      <RbAlert variant="warning" className="alert-warning-dark m-l-10 m-r-10 text-start">
        <i className="fa fa-warning fa-lg fa-right" /> {studentFullName}
        &nbsp;received {scoreTypeArticleText}{" "}
        <strong>
          <u>
            {scoreTypeText} Skill score of {currentPMResultScore}
          </u>
        </strong>
        ! Great job. We will continue practicing this Intervention Skill.
        {improvedOnBenchmark ? <strong> {studentFullName} also improved on the Goal Skill!</strong> : null}
        <br />
        Save the scores to continue the intervention for {studentFullName}.
      </RbAlert>
    </div>
  );
};

const IndividualIntervention = props => {
  const scoreEntryContext = useContext(ScoreEntryContext);
  const { customDate } = useContext(SchoolYearContext);
  const { org } = useContext(OrganizationContext);
  const { isTestOrg } = org || {};
  const { siteId } = useContext(SiteContext);
  const {
    env: { CI }
  } = useContext(StaticDataContext);

  const [state, setState] = useState({
    bsModalShow: false,
    bsModalContent: "",
    bsModalClassNames: "",
    resultFeedback: null,
    calculatedAssessmentResults: null,
    isInterventionSkillGroupsModalOpen: false,
    isRecalculatingStats: false,
    isSavingScores: false,
    shouldDisplayPreviewSkillTreeModal: false,
    currentScoreStates: null
  });

  const getTimeRange = useCallback(dates => {
    const start = Math.min(...dates);
    const end = Math.max(...dates);
    return { start, end };
  }, []);

  const calculateScores = useCallback(() => {
    const { assessmentResult } = props;
    if (!siteId || !assessmentResult) {
      return;
    }
    setState(prevState => ({ ...prevState, isRecalculatingStats: true }));
    Meteor.call(
      "calculateScoreResult",
      {
        assessmentResultId: assessmentResult._id
      },
      (err, res) => {
        if (err) {
          ninjalog.error({
            msg: "calculateScoreResult -> err",
            val: err
          });
          setState(prevState => ({ ...prevState, isRecalculatingStats: false }));
        } else {
          setState(prevState => ({ ...prevState, calculatedAssessmentResults: res, isRecalculatingStats: false }));
        }
      }
    );
  }, [siteId, props.assessmentResult]);

  useEffect(() => {
    calculateScores();
  }, [calculateScores]);

  const getCurrentScores = useCallback(
    propsToUse => {
      const { assessmentId, bmAssessmentId, assessmentResult, studentInfo, getStatus } = propsToUse;
      const pmAssessmentScore = assessmentResult.scores.find(
        sc => sc.assessmentId === assessmentId && sc.studentId === studentInfo._id
      );

      const bmAssessmentScore = assessmentResult.scores.find(
        sc => sc.assessmentId === bmAssessmentId && sc.studentId === studentInfo._id
      );

      // Fix: Check for value existence more reliably
      const pmValue = pmAssessmentScore?.value;
      const bmValue = bmAssessmentScore?.value;

      return {
        pmAssessmentScore: pmValue !== null && pmValue !== undefined && pmValue !== "" ? pmValue : "",
        bmAssessmentScore: bmValue !== null && bmValue !== undefined && bmValue !== "" ? bmValue : "",
        ...(getStatus ? { pmStatus: pmAssessmentScore?.status, bmStatus: bmAssessmentScore?.status } : {})
      };
    },
    [props.assessmentResult.scores, props.assessmentId, props.bmAssessmentId, props.studentInfo._id]
  );

  useEffect(() => {
    const currentScoreStates = getCurrentScores({
      ...props,
      getStatus: true
    });

    setState(prevState => ({ ...prevState, currentScoreStates }));
  }, [
    props.assessmentResult.scores,
    props.assessmentResult._id,
    props.assessmentId,
    props.bmAssessmentId,
    props.studentInfo._id,
    getCurrentScores
  ]);

  // Additional useEffect to handle potential race conditions in CI environments
  // This will force a re-render when scores change status to COMPLETE
  useEffect(() => {
    const scores = props.assessmentResult.scores;
    const pmScore = scores.find(sc => sc.assessmentId === props.assessmentId && sc.studentId === props.studentInfo._id);
    const bmScore = scores.find(
      sc => sc.assessmentId === props.bmAssessmentId && sc.studentId === props.studentInfo._id
    );

    // Force update if both scores are complete but currentScoreStates might be stale
    if (pmScore?.status === "COMPLETE" && bmScore?.status === "COMPLETE") {
      const currentScoreStates = getCurrentScores({
        ...props,
        getStatus: true
      });
      setState(prevState => ({ ...prevState, currentScoreStates }));
    }
  }, [
    props.assessmentResult.scores.map(s => `${s._id}:${s.status}:${s.value}`).join(","),
    props.assessmentId,
    props.bmAssessmentId,
    props.studentInfo._id,
    getCurrentScores
  ]);

  const saveScores = useCallback(
    e => {
      e.preventDefault();
      scoreEntryContext.clearUnusualHighScoreFields();
      const { assessmentResult, studentInfo } = props;
      if (!siteId || !assessmentResult) {
        return;
      }
      setState(prevState => ({ ...prevState, isSavingScores: true }));
      Meteor.call(
        "saveScoreResult",
        {
          assessmentResultId: assessmentResult._id
        },
        err => {
          if (err) {
            setState(prevState => ({ ...prevState, isSavingScores: false }));
          } else {
            setState(prevState => ({ ...prevState, calculatedAssessmentResults: null, isSavingScores: false }));
            Meteor.call("assignStudentToSkillGroup", {
              siteId,
              studentId: studentInfo._id
            });
          }
        }
      );
    },
    [siteId, props.assessmentResult, props.studentInfo, scoreEntryContext]
  );

  const hideModal = useCallback(() => {
    setState(prevState => ({
      ...prevState,
      bsModalShow: false,
      bsModalContent: "",
      bsModalClassNames: "",
      bsModalIcon: ""
    }));
  }, []);

  const renderChart = useCallback(
    ({ scores, type, timeRange }) => {
      const chartOptions = {
        chartType: "line",
        title: "",
        height: 400,
        xAxisTitle: "Week",
        yAxisTitle: "Score",
        paddingTop: 10,
        marginTop: 20,
        marginRight: 10,
        timeRange
      };
      return (
        <ProgressMonitoringChart
          pmName="Progress Monitoring Scores"
          scores={scores}
          chartId={props.studentInfo._id + type}
          options={chartOptions}
          type="Chart"
          skillType={type}
        />
      );
    },
    [props.studentInfo._id]
  );

  const renderSkillGraph = useCallback(
    (pmScoresObj, bmScoresObj) => {
      const dateNowTimestamp = new Date().getTime();
      const timeRange = getTimeRange([
        ...(pmScoresObj.dates?.length ? pmScoresObj.dates : [dateNowTimestamp]),
        ...(bmScoresObj.dates?.length ? bmScoresObj.dates : [dateNowTimestamp])
      ]);
      return (
        <div className="skill-graph clearfix">
          {props.benchmarkScoreId !== props.assessmentScoreId ? (
            <div>
              <h5>
                <small>
                  <i className="fa fa-bullseye" /> Intervention Skill:
                </small>{" "}
                {pmScoresObj.skillName}
              </h5>
              {renderChart({
                scores: pmScoresObj,
                type: "PMChart",
                timeRange
              })}
            </div>
          ) : null}
          <div>
            <h5>
              <small>
                <i className="fa fa-trophy" /> Goal Skill:
              </small>{" "}
              {bmScoresObj.skillName}
              {props.studentROI ? (
                <span className="roi float-end">{`Rate of Improvement: ${props.studentROI}`}</span>
              ) : null}
            </h5>
            {renderChart({
              scores: bmScoresObj,
              type: "BMChart",
              timeRange
            })}
          </div>
        </div>
      );
    },
    [props.benchmarkScoreId, props.assessmentScoreId, props.studentROI, getTimeRange, renderChart]
  );

  const renderDebugElement = params => {
    return (
      <small className="font-13 font-light">
        {params.map((el, index) => (
          <div key={index}>
            <span>{el}</span>
            {index !== params.length ? <br /> : null}
          </div>
        ))}
      </small>
    );
  };

  const renderScoreSubmitButton = () => {
    if (Object.keys(scoreEntryContext.getUnusualHighScoreFields(props.assessmentResult._id)).length) {
      return (
        <button type="button" className="btn btn-default">
          <i className="fa fa-warning fa-right" />
          Fix Unusual High Score
        </button>
      );
    }

    // Always get fresh score states to avoid race conditions
    // Don't rely on state.currentScoreStates which might be stale
    const currentScoreStates = getCurrentScores({
      ...props,
      getStatus: true
    });
    const { pmAssessmentScore, bmAssessmentScore, bmStatus, pmStatus } = currentScoreStates;

    // Additional safeguards for CI environment edge cases
    const hasValidPmScore = pmAssessmentScore && pmAssessmentScore.toString().length > 0 && pmAssessmentScore !== "";
    const hasValidBmScore = bmAssessmentScore && bmAssessmentScore.toString().length > 0 && bmAssessmentScore !== "";
    const isPmComplete = pmStatus === "COMPLETE";
    const isBmComplete = bmStatus === "COMPLETE";

    // More robust check for both scores being complete
    const shouldShowSaveButton = hasValidPmScore && hasValidBmScore && isPmComplete && isBmComplete;

    if (shouldShowSaveButton) {
      return (
        <div className="col-sm-12">
          <button
            key="1"
            className="btn btn-success"
            data-testid="saveScoreBtn"
            disabled={state.isSavingScores}
            onClick={saveScores}
          >
            <i className="fa fa-save fa-right" />
            Save Results
          </button>
          {renderDebugElement([
            `pmAssessmentScore: ${pmAssessmentScore}`,
            `bmAssessmentScore: ${bmAssessmentScore}`,
            `bmStatus: ${bmStatus}`,
            `pmStatus: ${pmStatus}`,
            `hasValidPmScore: ${hasValidPmScore}`,
            `hasValidBmScore: ${hasValidBmScore}`,
            `isPmComplete: ${isPmComplete}`,
            `isBmComplete: ${isBmComplete}`,
            `shouldShowSaveButton: ${shouldShowSaveButton}`
          ])}
        </div>
      );
    }
    return (
      <React.Fragment>
        <button type="button" className="btn btn-default">
          <i className="fa fa-warning fa-right" />
          Enter Scores to Continue
        </button>
        {renderDebugElement([
          `pmAssessmentScore: ${pmAssessmentScore}`,
          `bmAssessmentScore: ${bmAssessmentScore}`,
          `bmStatus: ${bmStatus}`,
          `pmStatus: ${pmStatus}`
        ])}
      </React.Fragment>
    );
  };

  const renderScoreEntryRowCell = useCallback(
    ({ className, scoreType }) => {
      const {
        benchmarkAssessmentName,
        assessmentName,
        assessmentResult,
        isReadOnly,
        studentInfo,
        assessmentId,
        bmAssessmentId,
        inActiveSchoolYear
      } = props;
      const {
        assessmentTargets: [skillInstructional, skillMastery],
        benchmarkAssessmentTargets: [goalInstructional, goalMastery]
      } = assessmentResult.individualSkills;
      const targets = {
        pm: [skillInstructional, skillMastery],
        bm: [goalInstructional, goalMastery]
      };
      const pmAssessmentScoreLimit = {
        assessmentId: assessmentResult.individualSkills.assessmentId,
        limit: assessmentResult.individualSkills.assessmentTargets[1] * 5
      };
      const bmAssessmentScoreLimit = {
        assessmentId: assessmentResult.individualSkills.benchmarkAssessmentId,
        limit: assessmentResult.individualSkills.benchmarkAssessmentTargets[1] * 5
      };

      const pmAssessmentScore = assessmentResult.scores.find(
        sc => sc.assessmentId === assessmentId && sc.studentId === studentInfo._id
      );
      const bmAssessmentScore = assessmentResult.scores.find(
        sc => sc.assessmentId === bmAssessmentId && sc.studentId === studentInfo._id
      );

      const scorePostfix = scoreType === "bm" ? "bmScore" : "pmScore";
      return (
        <div className={`${className} relativeWrapper`}>
          {shouldUseDevMode(CI) ? (
            <small className="w6 debug-small">
              At: ({targets[scoreType][0]}), Above: ({targets[scoreType][1]})
            </small>
          ) : null}
          <ScoreEntryRowCell
            cellId={`${studentInfo._id}_${scorePostfix}`}
            assessmentScoreLimit={scoreType === "bm" ? bmAssessmentScoreLimit : pmAssessmentScoreLimit}
            assessmentResultId={assessmentResult._id}
            assessmentScore={scoreType === "bm" ? bmAssessmentScore : pmAssessmentScore}
            inActiveSchoolYear={inActiveSchoolYear}
            isReadOnly={isReadOnly}
            index={scoreType === "bm" ? 1 : 0}
            rowScoreCount={1}
            seleniumSelectorText={scoreType === "bm" ? "enterGoalScore" : "enterSkillScore"}
            siteId={siteId}
          />
          <label>{scoreType === "bm" ? benchmarkAssessmentName : assessmentName}</label>
        </div>
      );
    },
    [
      props.benchmarkAssessmentName,
      props.assessmentName,
      props.assessmentResult,
      props.isReadOnly,
      props.studentInfo,
      props.assessmentId,
      props.bmAssessmentId,
      siteId,
      props.inActiveSchoolYear,
      scoreEntryContext
    ]
  );

  const togglePreviewSkillTreeModal = useCallback(() => {
    setState(prevState => ({
      ...prevState,
      shouldDisplayPreviewSkillTreeModal: !prevState.shouldDisplayPreviewSkillTreeModal
    }));
  }, []);

  const {
    assessmentId,
    assessmentName,
    measureNumber,
    hasVideo,
    protocolAssessment,
    assessmentScoreId,
    benchmarkPeriodId,
    benchmarkScoreId,
    bmAssessmentId,
    compiledBMScores,
    compiledPMScores,
    feedbackPMScores,
    inActiveSchoolYear,
    interventionsAvailable,
    monitorBMAssessmentVendorId,
    monitorProtocolAssessmentVendorId,
    studentGroupName,
    studentInfo,
    studentGroupId
  } = props;
  const {
    calculatedAssessmentResults,
    isRecalculatingStats,
    bsModalShow,
    bsModalClassNames,
    bsModalContent,
    bsModalIcon
  } = state;
  // Make deep copies of these props.
  const pmFeedbackObj = cloneDeep(feedbackPMScores);
  const pmScoresObj = cloneDeep(compiledPMScores);
  const bmScoresObj = cloneDeep(compiledBMScores);

  const studentFullName = `${studentInfo.identity.name.firstName} ${studentInfo.identity.name.lastName}`;

  if (calculatedAssessmentResults) {
    const currentDate = getCurrentDateSync(customDate, isTestOrg);
    if (
      calculatedAssessmentResults.measures[1]?.studentResults[0] &&
      (pmScoresObj.scores.length > 0 || pmScoresObj.assessmentId !== bmScoresObj.bmAssessmentId)
    ) {
      const pmScore = calculatedAssessmentResults.measures[1].studentResults[0].score;
      pmScoresObj.scores.push(parseInt(pmScore));
      pmScoresObj.dates.push(currentDate?.getTime?.());
    }
    if (calculatedAssessmentResults.measures[0]?.studentResults[0]) {
      const bmScore = calculatedAssessmentResults.measures[0].studentResults[0].score;
      bmScoresObj.scores.push(parseInt(bmScore));
      bmScoresObj.dates.push(currentDate?.getTime?.());
    }
  }
  const translatedBenchmarkPeriod = translateBenchmarkPeriod(benchmarkPeriodId);
  const benchmarkPeriodName = (translatedBenchmarkPeriod && translatedBenchmarkPeriod.name) || "";

  const { pmAssessmentScore, bmAssessmentScore } = state.currentScoreStates || getCurrentScores(props);

  const shouldRenderAdditionalInfo = shouldUseDevMode(CI);
  const shouldDisplayBoostItButton =
    protocolAssessment?.ir?.measure &&
    protocolAssessment?.ir?.measure === protocolAssessment?.monitorAssessmentMeasure &&
    protocolAssessment._id === studentInfo.currentSkill.assessmentId;

  return (
    <div
      className="skill-container individual pre-intervention clearfix"
      data-benchmark-period-name={benchmarkPeriodName}
      data-student-id={studentInfo._id}
      data-testid="individual-intervention-skill"
    >
      <div className="skill-details">
        {studentInfo?.currentSkill?.assessmentId && !studentInfo?.currentSkill?.message?.dismissed ? (
          <InterventionMessage
            entityId={studentInfo._id}
            entityType="Student"
            message={studentInfo.currentSkill.message}
            name={studentInfo.identity.name}
          />
        ) : null}
        <strong>{studentFullName}</strong> is currently practicing the skill
        <h3 className="w9 d-flex gap-3 vertical-align-middle">
          {assessmentName}
          {shouldRenderAdditionalInfo ? (
            <span className="w5 font-18">{` (AM#${monitorProtocolAssessmentVendorId})`}</span>
          ) : null}
          <div>
            <SkillVideoButton measureNumber={measureNumber} skillName={assessmentName} hasVideo={hasVideo} />
          </div>
        </h3>
        <div className="d-flex justify-content-between">
          <div className="d-flex flex-row gap-1">
            <PrintInterventionsDropdown
              assessmentMeasure={monitorBMAssessmentVendorId}
              protocolMeasure={monitorProtocolAssessmentVendorId}
              interventionsAvailable={interventionsAvailable}
              assessmentId={assessmentId}
              benchmarkAssessmentId={bmAssessmentId}
              benchmarkPeriodId={benchmarkPeriodId}
              materialsType="intervention-packet"
              grade={studentInfo.grade}
              studentName={studentFullName}
              initialText="Select Activity"
              loadingText="Custom building classwide intervention materials. Just a moment please."
              groupName={studentGroupName}
              studentGroupId={studentGroupId}
            />
            {shouldUseDevMode(CI, ["LOCAL", "DEV", "QA", "STAGE"]) ? (
              <React.Fragment>
                <Button variant="outline-blue" className="skill-button" onClick={togglePreviewSkillTreeModal}>
                  <TooltipWrapper
                    text={<i className="fa fa-sitemap cursor-pointer" />}
                    tooltipText={"Individual Progress Monitoring Tree Preview"}
                    placement="top"
                    customClassName=""
                    isClickTriggerEnabled={false}
                  />
                </Button>
                {state.shouldDisplayPreviewSkillTreeModal ? (
                  <PreviewSkillTreeModal
                    showModal={state.shouldDisplayPreviewSkillTreeModal}
                    onCloseModal={togglePreviewSkillTreeModal}
                    params={{
                      grade: studentInfo?.grade || "",
                      benchmarkPeriodId: benchmarkPeriodId || "",
                      benchmarkAssessmentId: bmAssessmentId || "",
                      isPreviewOnly: true
                    }}
                  />
                ) : null}
              </React.Fragment>
            ) : null}
          </div>
          {shouldDisplayBoostItButton && (
            <div>
              <IncrementalRehearsalButton
                studentId={studentInfo._id}
                orgid={studentInfo.orgid}
                siteId={siteId}
                incrementalRehearsal={protocolAssessment.ir}
                datesOfCompletion={studentInfo?.ir?.[protocolAssessment.ir.measure]?.dates}
              />
            </div>
          )}
        </div>
      </div>
      <div className="skill-score-entry">
        {inActiveSchoolYear ? (
          <form className="form-horizontal text-center" autoComplete="off">
            <div className="container">
              {benchmarkScoreId === assessmentScoreId ? (
                <div className="form-group row">
                  {renderScoreEntryRowCell({
                    className: "col-sm-12",
                    scoreType: "bm"
                  })}
                  {renderScoreSubmitButton()}
                </div>
              ) : (
                <div className="form-group row">
                  {renderScoreEntryRowCell({
                    className: "col-sm-6",
                    scoreType: "pm"
                  })}
                  {renderScoreEntryRowCell({
                    className: "col-sm-6",
                    scoreType: "bm"
                  })}
                  {renderScoreSubmitButton()}
                </div>
              )}
            </div>
          </form>
        ) : null}
      </div>
      <div className="clearfix" />
      {!isRecalculatingStats &&
      calculatedAssessmentResults?.measures[0]?.studentResults[0] &&
      pmAssessmentScore.length &&
      bmAssessmentScore.length
        ? renderResultFeedback(
            calculatedAssessmentResults,
            bmScoresObj,
            pmFeedbackObj,
            studentFullName,
            bmAssessmentId,
            benchmarkPeriodId
          )
        : null}
      {renderSkillGraph(pmScoresObj, bmScoresObj)}
      <ButtonToolbar>
        <Modal show={bsModalShow} onHide={hideModal} dialogClassName={bsModalClassNames}>
          <Modal.Body>
            <i id="bsModalClose" className="fa fa-close fa-sm" onClick={hideModal} />
            {bsModalContent}
            <i className={bsModalIcon} />
          </Modal.Body>
        </Modal>
      </ButtonToolbar>
    </div>
  );
};

IndividualIntervention.propTypes = {
  assessmentId: PropTypes.string,
  assessmentName: PropTypes.string,
  assessmentResult: PropTypes.object,
  assessmentScoreId: PropTypes.string,
  benchmarkAssessmentName: PropTypes.string,
  benchmarkPeriodId: PropTypes.string,
  benchmarkScoreId: PropTypes.string,
  bmAssessmentId: PropTypes.string,
  compiledBMScores: PropTypes.object,
  compiledPMScores: PropTypes.object,
  feedbackPMScores: PropTypes.object,
  protocolAssessment: PropTypes.object,
  hasVideo: PropTypes.bool,
  inActiveSchoolYear: PropTypes.bool,
  interventionsAvailable: PropTypes.array,
  isFetchingSkillGroups: PropTypes.bool,
  isReadOnly: PropTypes.bool,
  measureNumber: PropTypes.string,
  monitorBMAssessmentVendorId: PropTypes.string,
  monitorProtocolAssessmentVendorId: PropTypes.string,
  preAssessmentResultId: PropTypes.string,
  refreshScroll: PropTypes.func,
  schoolYear: PropTypes.number,
  skillGroups: PropTypes.array,
  studentGroupId: PropTypes.string,
  studentGroupName: PropTypes.string,
  studentInfo: PropTypes.object,
  studentROI: PropTypes.string,
  isTestOrg: PropTypes.bool
};

const IndividualInterventionDataTracker = props => {
  const { assessmentResult, studentInfo, assessmentName } = props;
  const { studentGroup } = useContext(StudentGroupContext);

  const trackerData = useTracker(() => {
    const currentAssessmentResult = assessmentResult;

    let monitorProtocolAssessmentVendorId;
    let protocolAssessment;
    let bmAssessment;
    const studentHistory = studentInfo.history || [];
    if (studentInfo) {
      // FIXME(fmazur) - no subscription for this?
      protocolAssessment = Assessments.findOne({
        _id: currentAssessmentResult.individualSkills.assessmentId
      });
      if (currentAssessmentResult.individualSkills.benchmarkAssessmentId) {
        bmAssessment = Assessments.findOne({
          _id: currentAssessmentResult.individualSkills.benchmarkAssessmentId
        });
      }
    }

    if (protocolAssessment && bmAssessment) {
      // Skill being worked on (PM)
      monitorProtocolAssessmentVendorId = protocolAssessment.monitorAssessmentMeasure;
    }

    const { assessmentId } = currentAssessmentResult.individualSkills;
    const bmAssessmentId = currentAssessmentResult.individualSkills.benchmarkAssessmentId;
    const feedbackPMScores = {
      scores: [],
      assessmentId
    };
    const compiledScoresShell = {
      skillName: `${currentAssessmentResult.individualSkills.assessmentName}`,
      instructionalTarget: currentAssessmentResult.individualSkills.assessmentTargets[0],
      masteryTarget: currentAssessmentResult.individualSkills.assessmentTargets[1]
    };
    const compiledPMScores = {
      ...compiledScoresShell,
      scores: [],
      dates: [],
      assessmentId
    };
    const compiledBMScores = {
      ...compiledScoresShell,
      scores: [],
      dates: [],
      skillName: `${currentAssessmentResult.individualSkills.benchmarkAssessmentName}`,
      instructionalTarget: currentAssessmentResult.individualSkills.benchmarkAssessmentTargets[0],
      masteryTarget: currentAssessmentResult.individualSkills.benchmarkAssessmentTargets[1],
      bmAssessmentId
    };
    const rawBMdates = [];
    filterOutHistoryItemsThatDidNotLeadToIntervention(studentHistory)
      .sort((a, b) => a.whenEnded.on - b.whenEnded.on)
      .forEach(h => {
        if (h.assessmentId === assessmentId && h.assessmentId !== bmAssessmentId) {
          const assMeasure = h.assessmentResultMeasures.find(m => m.assessmentId === assessmentId);
          if (assMeasure && assMeasure.medianScore !== "") {
            feedbackPMScores.scores.push({
              score: Number(assMeasure.medianScore),
              bmAssId: h.benchmarkAssessmentId,
              bmPeriodId: h.benchmarkPeriodId,
              date: h.whenEnded.date
            });
            if (!h.interventions.length) {
              compiledPMScores.hasDrillDownScore = true;
            }
            compiledPMScores.scores.push(Number(assMeasure.medianScore));
            compiledPMScores.dates.push(new Date(h.whenEnded.date).getTime());
          }
        }
        if (h.benchmarkAssessmentId === bmAssessmentId) {
          const assMeasure = h.assessmentResultMeasures.find(m => m.assessmentId === bmAssessmentId);
          if (assMeasure && assMeasure.medianScore !== "") {
            compiledBMScores.scores.push(Number(assMeasure.medianScore));
            compiledBMScores.dates.push(new Date(h.whenEnded.date).getTime());
            rawBMdates.push(h.whenEnded.date);
          }
        }
      });

    const assessmentScore = currentAssessmentResult.scores.find(s => s.assessmentId === assessmentId);
    const benchmarkScore = currentAssessmentResult.scores.find(s => s.assessmentId === bmAssessmentId);

    const roi = calculateRoI(rawBMdates, compiledBMScores.scores);

    return {
      assessmentName,
      measureNumber: protocolAssessment?.monitorAssessmentMeasure,
      hasVideo: protocolAssessment?.hasVideo,
      currentAssessmentResultScores: currentAssessmentResult.scores,
      protocolAssessment,
      assessmentId,
      bmAssessmentId,
      benchmarkPeriodId: currentAssessmentResult.benchmarkPeriodId,
      assessmentScoreId: assessmentScore && assessmentScore._id,
      benchmarkScoreId: benchmarkScore && benchmarkScore._id,
      monitorProtocolAssessmentVendorId,
      monitorBMAssessmentVendorId: bmAssessment?.monitorAssessmentMeasure,
      interventionsAvailable: currentAssessmentResult.individualSkills.interventions.map(i => i.interventionAbbrv),
      feedbackPMScores,
      compiledPMScores,
      compiledBMScores,
      studentROI: roi,
      studentGroupName: get(studentGroup, "name", ""),
      assessmentResult: currentAssessmentResult
    };
  }, [assessmentResult._id, studentInfo, studentGroup]);

  return <IndividualIntervention {...props} {...trackerData} />;
};

IndividualInterventionDataTracker.propTypes = {
  assessmentResult: PropTypes.object.isRequired,
  studentInfo: PropTypes.object.isRequired,
  assessmentName: PropTypes.string.isRequired,
  studentGroupId: PropTypes.string.isRequired,
  benchmarkAssessmentName: PropTypes.string,
  inActiveSchoolYear: PropTypes.bool,
  isReadOnly: PropTypes.bool,
  refreshScroll: PropTypes.func,
  schoolYear: PropTypes.number,
  isTestOrg: PropTypes.bool
};

export default IndividualInterventionDataTracker;
