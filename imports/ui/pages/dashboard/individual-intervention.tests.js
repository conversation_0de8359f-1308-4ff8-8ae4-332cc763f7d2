import { Meteor } from "meteor/meteor";
import { assert } from "chai";
import { renderResultFeedback } from "./individual-intervention.jsx";

// helper function for children props weirdness
// possibly move to a utility file if other test scenarios could use it
function childrenArray(reactElement) {
  if (!Array.isArray(reactElement.props.children)) {
    return reactElement.props.children ? [reactElement.props.children] : [];
  }
  return reactElement.props.children;
}

// alertElementCb -> using callbacks so that it can leverage closures modified by beforeEach calls
function greenAlertBoxPresent(alertElementCb, calcAssResultsWithScore) {
  it("should display GREEN alert-success-dark alert box", () => {
    assert.match(
      alertElementCb(calcAssResultsWithScore).props.className,
      /alert-success-dark/,
      "message did not have the green alert class"
    );
  });
}
function yellowAlertBoxPresent(alertElementCb, withScores) {
  it("should display YELLOW alert-warning-dark alert box", () => {
    assert.match(
      alertElementCb(withScores).props.className,
      /alert-warning-dark/,
      "message did not have the yellow alert class"
    );
  });
}

const testBmAssId = "testBmAssId";
const testBmPeriodId = "testBmPeriodId";

// pm score shell builder
function makeTestPMScore(score) {
  return {
    bmPeriodId: testBmPeriodId,
    bmAssId: testBmAssId,
    score
  };
}

// default parameters for the renderResultFeedback function
// rename if testing other functions in this file (yes!)
function defaults() {
  return {
    calcAssResults: {
      measures: [
        {
          studentResults: [{ score: 0 }],
          targetScores: [0, 0, 0]
        },
        {
          studentResults: [{ score: 0 }],
          targetScores: [0, 0, 0]
        }
      ]
    },
    bmScoresObj: {
      scores: [0],
      bmAssessmentId: "differentAssessment"
    },
    pmScoresObj: {
      scores: [makeTestPMScore(0)],
      assessmentId: "testAssessmentId"
    }
  };
}

// get the alert child node that has the testable information from the renderResultFeedback function
function getAlertElementFromTestInput(calcAssResults, bmScoresObj, pmScoresObj, studentFullName, bmAssId, bmPeriodId) {
  const testResult = renderResultFeedback(
    calcAssResults,
    bmScoresObj,
    pmScoresObj,
    studentFullName,
    bmAssId,
    bmPeriodId
  );
  const testResultChildren = childrenArray(testResult);
  return testResultChildren.find(child => child.type.displayName === "Alert");
}

// resets the pmScoresObj and bmScoresObj between tests
function setupScoresObjParams(practicingGoalSkill) {
  const { bmScoresObj, pmScoresObj } = defaults();
  if (practicingGoalSkill) {
    bmScoresObj.bmAssessmentId = pmScoresObj.assessmentId;
  }
  return { bmScoresObj, pmScoresObj };
}

// tests all the scenarios of entering scores and crossing thresholds and leveraging existing history in parameters
// practicingGoalSkill -> if true, tests all scenarios with the goal skill and intervention skill the same (1 textbox)
//                        else tests as if the intervention skill is on its own, separated from the goal skill
function checkSkillThresholdFeedBackFacts({ practicingGoalSkill }) {
  const practicingMeasureIndex = practicingGoalSkill ? 0 : 1;
  let bmScoresObj;
  let pmScoresObj;
  beforeEach(() => {
    ({ bmScoresObj, pmScoresObj } = setupScoresObjParams(practicingGoalSkill));
  });
  // ensure goal skill was not passed by default
  const calcAssResults = () => ({
    ...defaults().calcAssResults,
    measures: [
      {
        studentResults: [
          {
            score: 0
          }
        ],
        targetScores: [10, 50, 200]
      },
      {
        studentResults: [
          {
            score: 0
          }
        ],
        targetScores: [10, 50, 200]
      }
    ]
  });
  const continuePracticingAlertBoxPresent = (alertElementCb, withScores) => {
    it("should display We will continue practicing this Intervention Skill", () => {
      const alertElementChildren = childrenArray(alertElementCb(withScores));
      const verbageNode = alertElementChildren.find(
        child => child && child.toString().match(/We will continue practicing this Intervention Skill/i)
      );
      assert.isDefined(verbageNode, "could not find continue practicing verbage");
    });
  };
  describe("and the student has not passed any target thresholds on the intervention skill", () => {
    const alertElementCb = calcAssResultsWithScore =>
      getAlertElementFromTestInput(calcAssResultsWithScore || calcAssResults(), bmScoresObj, pmScoresObj);
    describe("and the student's new score does not meet any target thresholds", () => {
      yellowAlertBoxPresent(alertElementCb);
      continuePracticingAlertBoxPresent(alertElementCb);
    });
    describe("and the student's new score passes a threshold", () => {
      const calcAssResultsWithScore = calcAssResults();
      const crossThreshold = (score, matchString) => {
        describe(`with a score of ${score}`, () => {
          // get intervention skill measure (1)
          beforeEach(() => {
            calcAssResultsWithScore.measures[practicingMeasureIndex].studentResults[0].score = score;
          });
          // const alertElement = getAlertElementFromTestInput(calcAssResultsWithScore, bmScoresObj, pmScoresObj);
          greenAlertBoxPresent(alertElementCb, calcAssResultsWithScore);
          it(`should display '${matchString}'`, () => {
            const alertElementChildren = childrenArray(alertElementCb(calcAssResultsWithScore));
            const verbageNode = alertElementChildren.find(
              child => child && child.toString().match(new RegExp(matchString, "i"))
            );
            assert.isDefined(verbageNode);
          });
        });
      };
      // test edge cases as well as normal cases. the values should be in the range of the targetscores above
      [10, 20, 13, 49].forEach(score => {
        crossThreshold(score, "There will be new practice materials next time");
      });
      [50, 100, 1000, 51].forEach(score => {
        if (practicingGoalSkill) {
          crossThreshold(score, "should move on to a new goal skill");
        } else {
          crossThreshold(score, "will be moving on to a new Intervention Skill");
        }
      });
    });
  });
  describe("and the student has passed the first threshold on the intervention skill previously", () => {
    const alertElementWithHistoryCb = withScores => {
      if (practicingGoalSkill) {
        bmScoresObj.scores.push(8);
        bmScoresObj.scores.push(10);
        // last score gets chopped off by logic, remove this if that gets changed
        bmScoresObj.scores.push(15);
      } else {
        pmScoresObj.scores.push(makeTestPMScore(8));
        pmScoresObj.scores.push(makeTestPMScore(10));
      }

      if (withScores) {
        const calcAssResultsWithScore = calcAssResults();
        // get intervention skill measure (1)
        // give it a score that also passes threshold 1
        calcAssResultsWithScore.measures[practicingMeasureIndex].studentResults[0].score = 15;
        return getAlertElementFromTestInput(
          calcAssResultsWithScore,
          bmScoresObj,
          pmScoresObj,
          "",
          testBmAssId,
          testBmPeriodId
        );
      }
      return getAlertElementFromTestInput(calcAssResults(), bmScoresObj, pmScoresObj);
    };
    describe("and the student's new score does not pass a threshold", () => {
      yellowAlertBoxPresent(alertElementWithHistoryCb, false);
      continuePracticingAlertBoxPresent(alertElementWithHistoryCb, false);
    });
    describe("and the student's new score passes the first threshold", () => {
      yellowAlertBoxPresent(alertElementWithHistoryCb, true);
      continuePracticingAlertBoxPresent(alertElementWithHistoryCb, true);
    });
  });
}

if (Meteor.isClient) {
  describe("imports/ui/pages/dashboard/individual-intervention.jsx tests", () => {
    describe("renderResultFeedback tests", () => {
      it("returns a p tag element", () => {
        const testResult = renderResultFeedback(
          defaults().calcAssResults,
          defaults().bmScoresObj,
          defaults().pmScoresObj
        );
        assert.equal(testResult.type, "div", "did not return a div element");
      });
      describe("when the intervention skill is not the same as the goal skill", () => {
        checkSkillThresholdFeedBackFacts({ practicingGoalSkill: false });
      });
      describe("when the intervention skill is the same as the goal skill", () => {
        checkSkillThresholdFeedBackFacts({ practicingGoalSkill: true });
      });
    });

    describe("renderScoreSubmitButton tests", () => {
      // Test the renderScoreSubmitButton function logic directly
      let mockScoreEntryContext;
      let mockProps;
      let mockState;

      beforeEach(() => {
        // Mock the score entry context
        mockScoreEntryContext = {
          getUnusualHighScoreFields: () => ({})
        };

        // Create mock props
        mockProps = {
          assessmentId: "pmAssessmentId",
          bmAssessmentId: "bmAssessmentId",
          studentInfo: { _id: "studentId" },
          assessmentResult: {
            _id: "assessmentResultId",
            scores: [
              {
                _id: "pmScoreId",
                assessmentId: "pmAssessmentId",
                studentId: "studentId",
                value: "",
                status: "STARTED"
              },
              {
                _id: "bmScoreId",
                assessmentId: "bmAssessmentId",
                studentId: "studentId",
                value: "",
                status: "STARTED"
              }
            ]
          }
        };

        // Create mock state
        mockState = {
          currentScoreStates: null,
          isSavingScores: false
        };
      });

      // Helper function to simulate getCurrentScores
      const getCurrentScores = propsToUse => {
        const { assessmentId, bmAssessmentId, assessmentResult, studentInfo, getStatus } = propsToUse;
        const pmAssessmentScore = assessmentResult.scores.find(
          sc => sc.assessmentId === assessmentId && sc.studentId === studentInfo._id
        );
        const bmAssessmentScore = assessmentResult.scores.find(
          sc => sc.assessmentId === bmAssessmentId && sc.studentId === studentInfo._id
        );
        return {
          pmAssessmentScore: pmAssessmentScore?.value?.length ? pmAssessmentScore?.value : "",
          bmAssessmentScore: bmAssessmentScore?.value?.length ? bmAssessmentScore?.value : "",
          ...(getStatus ? { pmStatus: pmAssessmentScore?.status, bmStatus: bmAssessmentScore?.status } : {})
        };
      };

      // Helper function to simulate the renderScoreSubmitButton logic
      const simulateRenderScoreSubmitButton = (props, state, scoreEntryContext) => {
        if (Object.keys(scoreEntryContext.getUnusualHighScoreFields(props.assessmentResult._id)).length) {
          return { type: "unusual-high-score", text: "Fix Unusual High Score" };
        }

        // Always get fresh score states to avoid race conditions
        // Don't rely on state.currentScoreStates which might be stale
        const currentScoreStates = getCurrentScores({
          ...props,
          getStatus: true
        });
        const { pmAssessmentScore, bmAssessmentScore, bmStatus, pmStatus } = currentScoreStates;

        // Additional safeguards for CI environment edge cases
        const hasValidPmScore =
          pmAssessmentScore && pmAssessmentScore.toString().length > 0 && pmAssessmentScore !== "";
        const hasValidBmScore =
          bmAssessmentScore && bmAssessmentScore.toString().length > 0 && bmAssessmentScore !== "";
        const isPmComplete = pmStatus === "COMPLETE";
        const isBmComplete = bmStatus === "COMPLETE";

        // More robust check for both scores being complete
        const shouldShowSaveButton = hasValidPmScore && hasValidBmScore && isPmComplete && isBmComplete;

        if (shouldShowSaveButton) {
          return {
            type: "save-results",
            text: "Save Results",
            className: "btn-success",
            testId: "saveScoreBtn",
            disabled: state.isSavingScores,
            debugInfo: {
              pmAssessmentScore,
              bmAssessmentScore,
              bmStatus,
              pmStatus,
              hasValidPmScore,
              hasValidBmScore,
              isPmComplete,
              isBmComplete,
              shouldShowSaveButton
            }
          };
        }

        return {
          type: "enter-scores",
          text: "Enter Scores to Continue",
          className: "btn-default",
          debugInfo: {
            pmAssessmentScore,
            bmAssessmentScore,
            bmStatus,
            pmStatus,
            hasValidPmScore,
            hasValidBmScore,
            isPmComplete,
            isBmComplete,
            shouldShowSaveButton
          }
        };
      };

      describe("when no scores are entered", () => {
        it("should display 'Enter Scores to Continue' button", () => {
          const result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);

          assert.equal(result.type, "enter-scores", "Should return enter-scores type");
          assert.equal(result.text, "Enter Scores to Continue", "Should have correct button text");
          assert.equal(result.className, "btn-default", "Should have default styling");
          assert.equal(result.debugInfo.pmAssessmentScore, "", "PM score should be empty");
          assert.equal(result.debugInfo.bmAssessmentScore, "", "BM score should be empty");
          assert.equal(result.debugInfo.pmStatus, "STARTED", "PM status should be STARTED");
          assert.equal(result.debugInfo.bmStatus, "STARTED", "BM status should be STARTED");
        });
      });

      describe("when only intervention skill score is entered", () => {
        it("should display 'Enter Scores to Continue' button", () => {
          // Update mock props to have PM score but not BM score
          mockProps.assessmentResult.scores[0].value = "25";
          mockProps.assessmentResult.scores[0].status = "COMPLETE";

          const result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);

          assert.equal(result.type, "enter-scores", "Should return enter-scores type");
          assert.equal(result.text, "Enter Scores to Continue", "Should have correct button text");
          assert.equal(result.debugInfo.pmAssessmentScore, "25", "PM score should be 25");
          assert.equal(result.debugInfo.bmAssessmentScore, "", "BM score should be empty");
          assert.equal(result.debugInfo.pmStatus, "COMPLETE", "PM status should be COMPLETE");
          assert.equal(result.debugInfo.bmStatus, "STARTED", "BM status should be STARTED");
        });
      });

      describe("when only goal skill score is entered", () => {
        it("should display 'Enter Scores to Continue' button", () => {
          // Update mock props to have BM score but not PM score
          mockProps.assessmentResult.scores[1].value = "30";
          mockProps.assessmentResult.scores[1].status = "COMPLETE";

          const result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);

          assert.equal(result.type, "enter-scores", "Should return enter-scores type");
          assert.equal(result.text, "Enter Scores to Continue", "Should have correct button text");
          assert.equal(result.debugInfo.pmAssessmentScore, "", "PM score should be empty");
          assert.equal(result.debugInfo.bmAssessmentScore, "30", "BM score should be 30");
          assert.equal(result.debugInfo.pmStatus, "STARTED", "PM status should be STARTED");
          assert.equal(result.debugInfo.bmStatus, "COMPLETE", "BM status should be COMPLETE");
        });
      });

      describe("when both scores are entered and both statuses are COMPLETE", () => {
        it("should display 'Save Results' button", () => {
          // Update mock props to have both scores and COMPLETE statuses
          mockProps.assessmentResult.scores[0].value = "25";
          mockProps.assessmentResult.scores[0].status = "COMPLETE";
          mockProps.assessmentResult.scores[1].value = "30";
          mockProps.assessmentResult.scores[1].status = "COMPLETE";

          const result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);

          assert.equal(result.type, "save-results", "Should return save-results type");
          assert.equal(result.text, "Save Results", "Should have correct button text");
          assert.equal(result.className, "btn-success", "Should have success styling");
          assert.equal(result.testId, "saveScoreBtn", "Should have correct test ID");
          assert.isFalse(result.disabled, "Button should not be disabled");
          assert.equal(result.debugInfo.pmAssessmentScore, "25", "PM score should be 25");
          assert.equal(result.debugInfo.bmAssessmentScore, "30", "BM score should be 30");
          assert.equal(result.debugInfo.pmStatus, "COMPLETE", "PM status should be COMPLETE");
          assert.equal(result.debugInfo.bmStatus, "COMPLETE", "BM status should be COMPLETE");
        });

        it("should display debug information with correct values", () => {
          // Update mock props to have both scores and COMPLETE statuses
          mockProps.assessmentResult.scores[0].value = "99";
          mockProps.assessmentResult.scores[0].status = "COMPLETE";
          mockProps.assessmentResult.scores[1].value = "0";
          mockProps.assessmentResult.scores[1].status = "COMPLETE";

          const result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);

          assert.isDefined(result.debugInfo, "Should have debug information");
          assert.equal(result.debugInfo.pmAssessmentScore, "99", "Debug should show PM score as 99");
          assert.equal(result.debugInfo.bmAssessmentScore, "0", "Debug should show BM score as 0");
          assert.equal(result.debugInfo.pmStatus, "COMPLETE", "Debug should show PM status as COMPLETE");
          assert.equal(result.debugInfo.bmStatus, "COMPLETE", "Debug should show BM status as COMPLETE");
        });
      });

      describe("when both scores are entered but statuses are not COMPLETE", () => {
        it("should display 'Enter Scores to Continue' when pm status is STARTED", () => {
          // Update mock props to have both scores but PM status is STARTED
          mockProps.assessmentResult.scores[0].value = "25";
          mockProps.assessmentResult.scores[0].status = "STARTED";
          mockProps.assessmentResult.scores[1].value = "30";
          mockProps.assessmentResult.scores[1].status = "COMPLETE";

          const result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);

          assert.equal(result.type, "enter-scores", "Should return enter-scores type");
          assert.equal(result.text, "Enter Scores to Continue", "Should have correct button text");
          assert.equal(result.debugInfo.pmStatus, "STARTED", "PM status should be STARTED");
          assert.equal(result.debugInfo.bmStatus, "COMPLETE", "BM status should be COMPLETE");
        });

        it("should display 'Enter Scores to Continue' when bm status is STARTED", () => {
          // Update mock props to have both scores but BM status is STARTED
          mockProps.assessmentResult.scores[0].value = "25";
          mockProps.assessmentResult.scores[0].status = "COMPLETE";
          mockProps.assessmentResult.scores[1].value = "30";
          mockProps.assessmentResult.scores[1].status = "STARTED";

          const result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);

          assert.equal(result.type, "enter-scores", "Should return enter-scores type");
          assert.equal(result.text, "Enter Scores to Continue", "Should have correct button text");
          assert.equal(result.debugInfo.pmStatus, "COMPLETE", "PM status should be COMPLETE");
          assert.equal(result.debugInfo.bmStatus, "STARTED", "BM status should be STARTED");
        });
      });

      describe("when there are unusual high scores", () => {
        it("should display 'Fix Unusual High Score' button", () => {
          const mockScoreEntryContextWithUnusualScores = {
            getUnusualHighScoreFields: () => ({ someField: true })
          };

          const result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContextWithUnusualScores);

          assert.equal(result.type, "unusual-high-score", "Should return unusual-high-score type");
          assert.equal(result.text, "Fix Unusual High Score", "Should have correct button text");
        });
      });

      describe("edge cases", () => {
        it("should handle empty string scores correctly", () => {
          // Reset scores to empty strings with COMPLETE status
          mockProps.assessmentResult.scores[0].value = "";
          mockProps.assessmentResult.scores[0].status = "COMPLETE";
          mockProps.assessmentResult.scores[1].value = "";
          mockProps.assessmentResult.scores[1].status = "COMPLETE";

          const result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);

          assert.equal(result.type, "enter-scores", "Should return enter-scores type when scores are empty strings");
          assert.equal(result.debugInfo.pmAssessmentScore, "", "PM score should be empty");
          assert.equal(result.debugInfo.bmAssessmentScore, "", "BM score should be empty");
        });

        it("should handle null/undefined scores correctly", () => {
          // Set scores to null/undefined
          mockProps.assessmentResult.scores[0].value = null;
          mockProps.assessmentResult.scores[0].status = "COMPLETE";
          mockProps.assessmentResult.scores[1].value = undefined;
          mockProps.assessmentResult.scores[1].status = "COMPLETE";

          const result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);

          assert.equal(result.type, "enter-scores", "Should return enter-scores type when scores are null/undefined");
          assert.equal(result.debugInfo.pmAssessmentScore, "", "PM score should be empty when null");
          assert.equal(result.debugInfo.bmAssessmentScore, "", "BM score should be empty when undefined");
        });

        it("should handle zero scores correctly", () => {
          // Set scores to "0" with COMPLETE status
          mockProps.assessmentResult.scores[0].value = "0";
          mockProps.assessmentResult.scores[0].status = "COMPLETE";
          mockProps.assessmentResult.scores[1].value = "0";
          mockProps.assessmentResult.scores[1].status = "COMPLETE";

          const result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);

          assert.equal(result.type, "save-results", "Should return save-results type when scores are zero but valid");
          assert.equal(result.debugInfo.pmAssessmentScore, "0", "PM score should be 0");
          assert.equal(result.debugInfo.bmAssessmentScore, "0", "BM score should be 0");
          assert.equal(result.debugInfo.pmStatus, "COMPLETE", "PM status should be COMPLETE");
          assert.equal(result.debugInfo.bmStatus, "COMPLETE", "BM status should be COMPLETE");
        });

        it("should always use fresh score states instead of relying on state.currentScoreStates", () => {
          // Set up state with potentially stale currentScoreStates
          const stateWithStaleScores = {
            ...mockState,
            currentScoreStates: {
              pmAssessmentScore: "",
              bmAssessmentScore: "",
              pmStatus: "STARTED",
              bmStatus: "STARTED"
            }
          };

          // But the actual props have complete scores
          mockProps.assessmentResult.scores[0].value = "15";
          mockProps.assessmentResult.scores[0].status = "COMPLETE";
          mockProps.assessmentResult.scores[1].value = "10";
          mockProps.assessmentResult.scores[1].status = "COMPLETE";

          const result = simulateRenderScoreSubmitButton(mockProps, stateWithStaleScores, mockScoreEntryContext);

          // Should use fresh data from props, not stale state
          assert.equal(result.type, "save-results", "Should return save-results type using fresh data");
          assert.equal(result.debugInfo.pmAssessmentScore, "15", "Should use fresh PM score from props");
          assert.equal(result.debugInfo.bmAssessmentScore, "10", "Should use fresh BM score from props");
          assert.equal(result.debugInfo.shouldShowSaveButton, true, "Should show save button with fresh data");
        });

        describe("CI environment race condition scenarios", () => {
          it("should handle scenario where scores are COMPLETE but values are temporarily empty", () => {
            // Simulate a race condition where status is updated but values haven't propagated yet
            mockProps.assessmentResult.scores[0].value = "";
            mockProps.assessmentResult.scores[0].status = "COMPLETE";
            mockProps.assessmentResult.scores[1].value = "";
            mockProps.assessmentResult.scores[1].status = "COMPLETE";

            const result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);

            assert.equal(
              result.type,
              "enter-scores",
              "Should return enter-scores when values are empty despite COMPLETE status"
            );
            assert.equal(result.debugInfo.hasValidPmScore, false, "Should detect invalid PM score");
            assert.equal(result.debugInfo.hasValidBmScore, false, "Should detect invalid BM score");
            assert.equal(result.debugInfo.shouldShowSaveButton, false, "Should not show save button");
          });

          it("should handle scenario where one score is valid and complete, other is complete but empty", () => {
            // Simulate partial race condition
            mockProps.assessmentResult.scores[0].value = "15";
            mockProps.assessmentResult.scores[0].status = "COMPLETE";
            mockProps.assessmentResult.scores[1].value = "";
            mockProps.assessmentResult.scores[1].status = "COMPLETE";

            const result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);

            assert.equal(result.type, "enter-scores", "Should return enter-scores when only one score is valid");
            assert.equal(result.debugInfo.hasValidPmScore, true, "Should detect valid PM score");
            assert.equal(result.debugInfo.hasValidBmScore, false, "Should detect invalid BM score");
            assert.equal(result.debugInfo.shouldShowSaveButton, false, "Should not show save button");
          });

          it("should handle scenario where values are present but status is not yet COMPLETE", () => {
            // Simulate race condition where values are updated but status hasn't propagated yet
            mockProps.assessmentResult.scores[0].value = "15";
            mockProps.assessmentResult.scores[0].status = "STARTED";
            mockProps.assessmentResult.scores[1].value = "10";
            mockProps.assessmentResult.scores[1].status = "STARTED";

            const result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);

            assert.equal(result.type, "enter-scores", "Should return enter-scores when status is not COMPLETE");
            assert.equal(result.debugInfo.hasValidPmScore, true, "Should detect valid PM score");
            assert.equal(result.debugInfo.hasValidBmScore, true, "Should detect valid BM score");
            assert.equal(result.debugInfo.isPmComplete, false, "Should detect PM not complete");
            assert.equal(result.debugInfo.isBmComplete, false, "Should detect BM not complete");
            assert.equal(result.debugInfo.shouldShowSaveButton, false, "Should not show save button");
          });

          it("should handle numeric zero values correctly", () => {
            // Test that zero is treated as a valid score
            mockProps.assessmentResult.scores[0].value = "0";
            mockProps.assessmentResult.scores[0].status = "COMPLETE";
            mockProps.assessmentResult.scores[1].value = "0";
            mockProps.assessmentResult.scores[1].status = "COMPLETE";

            const result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);

            assert.equal(result.type, "save-results", "Should return save-results for zero values");
            assert.equal(result.debugInfo.hasValidPmScore, true, "Should treat zero as valid PM score");
            assert.equal(result.debugInfo.hasValidBmScore, true, "Should treat zero as valid BM score");
            assert.equal(result.debugInfo.shouldShowSaveButton, true, "Should show save button for zero values");
          });

          it("should handle string numeric values correctly", () => {
            // Test that string numbers are handled properly
            mockProps.assessmentResult.scores[0].value = "15";
            mockProps.assessmentResult.scores[0].status = "COMPLETE";
            mockProps.assessmentResult.scores[1].value = "10";
            mockProps.assessmentResult.scores[1].status = "COMPLETE";

            const result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);

            assert.equal(result.type, "save-results", "Should return save-results for string numeric values");
            assert.equal(result.debugInfo.hasValidPmScore, true, "Should treat string number as valid PM score");
            assert.equal(result.debugInfo.hasValidBmScore, true, "Should treat string number as valid BM score");
            assert.equal(
              result.debugInfo.shouldShowSaveButton,
              true,
              "Should show save button for string numeric values"
            );
          });
        });
      });
    });

    describe("Integration tests for score state management bug", () => {
      // These tests specifically target the bug where entering both scores
      // doesn't properly update the state to show "Save Results" button
      let mockScoreEntryContext;
      let mockProps;
      let mockState;

      beforeEach(() => {
        // Mock the score entry context
        mockScoreEntryContext = {
          getUnusualHighScoreFields: () => ({})
        };

        // Create mock props that simulate the bug scenario
        mockProps = {
          assessmentId: "pmAssessmentId",
          bmAssessmentId: "bmAssessmentId",
          studentInfo: { _id: "studentId" },
          assessmentResult: {
            _id: "assessmentResultId",
            scores: [
              {
                _id: "pmScoreId",
                assessmentId: "pmAssessmentId",
                studentId: "studentId",
                value: "",
                status: "STARTED"
              },
              {
                _id: "bmScoreId",
                assessmentId: "bmAssessmentId",
                studentId: "studentId",
                value: "",
                status: "STARTED"
              }
            ]
          }
        };

        // Create mock state
        mockState = {
          currentScoreStates: null,
          isSavingScores: false
        };
      });

      // Helper function to simulate getCurrentScores with the exact logic from the component
      const getCurrentScores = propsToUse => {
        const { assessmentId, bmAssessmentId, assessmentResult, studentInfo, getStatus } = propsToUse;
        const pmAssessmentScore = assessmentResult.scores.find(
          sc => sc.assessmentId === assessmentId && sc.studentId === studentInfo._id
        );

        const bmAssessmentScore = assessmentResult.scores.find(
          sc => sc.assessmentId === bmAssessmentId && sc.studentId === studentInfo._id
        );

        // Fix: Check for value existence more reliably
        const pmValue = pmAssessmentScore?.value;
        const bmValue = bmAssessmentScore?.value;

        return {
          pmAssessmentScore: pmValue !== null && pmValue !== undefined && pmValue !== "" ? pmValue : "",
          bmAssessmentScore: bmValue !== null && bmValue !== undefined && bmValue !== "" ? bmValue : "",
          ...(getStatus ? { pmStatus: pmAssessmentScore?.status, bmStatus: bmAssessmentScore?.status } : {})
        };
      };

      // Helper function to simulate the exact renderScoreSubmitButton logic
      const simulateRenderScoreSubmitButton = (props, state, scoreEntryContext) => {
        if (Object.keys(scoreEntryContext.getUnusualHighScoreFields(props.assessmentResult._id)).length) {
          return { type: "unusual-high-score", text: "Fix Unusual High Score" };
        }

        // Always get fresh score states to avoid race conditions
        // Don't rely on state.currentScoreStates which might be stale
        const currentScoreStates = getCurrentScores({
          ...props,
          getStatus: true
        });
        const { pmAssessmentScore, bmAssessmentScore, bmStatus, pmStatus } = currentScoreStates;

        // Additional safeguards for CI environment edge cases
        const hasValidPmScore = !!(
          pmAssessmentScore &&
          pmAssessmentScore.toString().length > 0 &&
          pmAssessmentScore !== ""
        );
        const hasValidBmScore = !!(
          bmAssessmentScore &&
          bmAssessmentScore.toString().length > 0 &&
          bmAssessmentScore !== ""
        );
        const isPmComplete = pmStatus === "COMPLETE";
        const isBmComplete = bmStatus === "COMPLETE";

        // More robust check for both scores being complete
        const shouldShowSaveButton = hasValidPmScore && hasValidBmScore && isPmComplete && isBmComplete;

        if (shouldShowSaveButton) {
          return {
            type: "save-results",
            text: "Save Results",
            className: "btn-success",
            testId: "saveScoreBtn",
            disabled: state.isSavingScores,
            debugInfo: {
              pmAssessmentScore,
              bmAssessmentScore,
              bmStatus,
              pmStatus,
              hasValidPmScore,
              hasValidBmScore,
              isPmComplete,
              isBmComplete,
              shouldShowSaveButton
            }
          };
        }

        return {
          type: "enter-scores",
          text: "Enter Scores to Continue",
          className: "btn-default",
          debugInfo: {
            pmAssessmentScore,
            bmAssessmentScore,
            bmStatus,
            pmStatus,
            hasValidPmScore,
            hasValidBmScore,
            isPmComplete,
            isBmComplete,
            shouldShowSaveButton
          }
        };
      };

      describe("Bug scenario: Moving up a skill level with both scores entered", () => {
        it("should show 'Save Results' when both intervention and goal skill scores are entered with COMPLETE status", () => {
          // Simulate the expected state after entering both scores
          mockProps.assessmentResult.scores[0].value = "99"; // PM score
          mockProps.assessmentResult.scores[0].status = "COMPLETE";
          mockProps.assessmentResult.scores[1].value = "0"; // BM score
          mockProps.assessmentResult.scores[1].status = "COMPLETE";

          const result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);

          assert.equal(result.type, "save-results", "Should return save-results type");
          assert.equal(result.text, "Save Results", "Should display 'Save Results' button text");
          assert.equal(result.className, "btn-success", "Should have success styling");
          assert.equal(result.testId, "saveScoreBtn", "Should have correct test ID");
          assert.isFalse(result.disabled, "Button should not be disabled");

          // Verify the debug info shows the expected state
          assert.equal(result.debugInfo.pmAssessmentScore, "99", "PM score should be 99");
          assert.equal(result.debugInfo.bmAssessmentScore, "0", "BM score should be 0");
          assert.equal(result.debugInfo.pmStatus, "COMPLETE", "PM status should be COMPLETE");
          assert.equal(result.debugInfo.bmStatus, "COMPLETE", "BM status should be COMPLETE");
          assert.isTrue(result.debugInfo.hasValidPmScore, "Should detect valid PM score");
          assert.isTrue(result.debugInfo.hasValidBmScore, "Should detect valid BM score");
          assert.isTrue(result.debugInfo.isPmComplete, "Should detect PM is complete");
          assert.isTrue(result.debugInfo.isBmComplete, "Should detect BM is complete");
          assert.isTrue(result.debugInfo.shouldShowSaveButton, "Should show save button");
        });

        it("should show 'Enter Scores to Continue' when scores are entered but status is still STARTED (race condition)", () => {
          // Simulate the bug scenario where values are entered but status hasn't updated
          mockProps.assessmentResult.scores[0].value = "99"; // PM score
          mockProps.assessmentResult.scores[0].status = "STARTED"; // Still STARTED (bug)
          mockProps.assessmentResult.scores[1].value = "0"; // BM score
          mockProps.assessmentResult.scores[1].status = "STARTED"; // Still STARTED (bug)

          const result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);

          assert.equal(result.type, "enter-scores", "Should return enter-scores type due to STARTED status");
          assert.equal(result.text, "Enter Scores to Continue", "Should display 'Enter Scores to Continue'");
          assert.equal(result.className, "btn-default", "Should have default styling");

          // Verify the debug info shows the problematic state
          assert.equal(result.debugInfo.pmAssessmentScore, "99", "PM score should be 99");
          assert.equal(result.debugInfo.bmAssessmentScore, "0", "BM score should be 0");
          assert.equal(result.debugInfo.pmStatus, "STARTED", "PM status should be STARTED (bug)");
          assert.equal(result.debugInfo.bmStatus, "STARTED", "BM status should be STARTED (bug)");
          assert.isTrue(result.debugInfo.hasValidPmScore, "Should detect valid PM score");
          assert.isTrue(result.debugInfo.hasValidBmScore, "Should detect valid BM score");
          assert.isFalse(result.debugInfo.isPmComplete, "Should detect PM is not complete");
          assert.isFalse(result.debugInfo.isBmComplete, "Should detect BM is not complete");
          assert.isFalse(result.debugInfo.shouldShowSaveButton, "Should not show save button");
        });

        it("should show 'Enter Scores to Continue' when status is COMPLETE but values are empty (race condition)", () => {
          // Simulate another race condition where status is updated but values haven't propagated
          mockProps.assessmentResult.scores[0].value = ""; // PM score empty
          mockProps.assessmentResult.scores[0].status = "COMPLETE"; // Status updated
          mockProps.assessmentResult.scores[1].value = ""; // BM score empty
          mockProps.assessmentResult.scores[1].status = "COMPLETE"; // Status updated

          const result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);

          assert.equal(result.type, "enter-scores", "Should return enter-scores type due to empty values");
          assert.equal(result.text, "Enter Scores to Continue", "Should display 'Enter Scores to Continue'");

          // Verify the debug info shows the problematic state
          assert.equal(result.debugInfo.pmAssessmentScore, "", "PM score should be empty");
          assert.equal(result.debugInfo.bmAssessmentScore, "", "BM score should be empty");
          assert.equal(result.debugInfo.pmStatus, "COMPLETE", "PM status should be COMPLETE");
          assert.equal(result.debugInfo.bmStatus, "COMPLETE", "BM status should be COMPLETE");
          assert.isFalse(result.debugInfo.hasValidPmScore, "Should detect invalid PM score");
          assert.isFalse(result.debugInfo.hasValidBmScore, "Should detect invalid BM score");
          assert.isTrue(result.debugInfo.isPmComplete, "Should detect PM is complete");
          assert.isTrue(result.debugInfo.isBmComplete, "Should detect BM is complete");
          assert.isFalse(result.debugInfo.shouldShowSaveButton, "Should not show save button");
        });
      });

      describe("State transition scenarios", () => {
        it("should transition from 'Enter Scores' to 'Save Results' when both scores become complete", () => {
          // Initial state: no scores entered
          let result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);
          assert.equal(result.type, "enter-scores", "Should start with enter-scores");

          // Enter PM score only
          mockProps.assessmentResult.scores[0].value = "99";
          mockProps.assessmentResult.scores[0].status = "COMPLETE";
          result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);
          assert.equal(result.type, "enter-scores", "Should still be enter-scores with only PM score");

          // Enter BM score as well
          mockProps.assessmentResult.scores[1].value = "0";
          mockProps.assessmentResult.scores[1].status = "COMPLETE";
          result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);
          assert.equal(result.type, "save-results", "Should transition to save-results with both scores");
          assert.equal(result.text, "Save Results", "Should display 'Save Results'");
        });

        it("should handle partial updates correctly", () => {
          // Test various partial update scenarios
          const scenarios = [
            {
              name: "PM complete, BM started",
              pmValue: "25",
              pmStatus: "COMPLETE",
              bmValue: "",
              bmStatus: "STARTED",
              expectedType: "enter-scores"
            },
            {
              name: "PM started, BM complete",
              pmValue: "",
              pmStatus: "STARTED",
              bmValue: "30",
              bmStatus: "COMPLETE",
              expectedType: "enter-scores"
            },
            {
              name: "Both values present, PM started",
              pmValue: "25",
              pmStatus: "STARTED",
              bmValue: "30",
              bmStatus: "COMPLETE",
              expectedType: "enter-scores"
            },
            {
              name: "Both values present, BM started",
              pmValue: "25",
              pmStatus: "COMPLETE",
              bmValue: "30",
              bmStatus: "STARTED",
              expectedType: "enter-scores"
            },
            {
              name: "Both complete",
              pmValue: "25",
              pmStatus: "COMPLETE",
              bmValue: "30",
              bmStatus: "COMPLETE",
              expectedType: "save-results"
            }
          ];

          scenarios.forEach(scenario => {
            // Reset props
            mockProps.assessmentResult.scores[0].value = scenario.pmValue;
            mockProps.assessmentResult.scores[0].status = scenario.pmStatus;
            mockProps.assessmentResult.scores[1].value = scenario.bmValue;
            mockProps.assessmentResult.scores[1].status = scenario.bmStatus;

            const result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);
            assert.equal(result.type, scenario.expectedType, `${scenario.name} should return ${scenario.expectedType}`);
          });
        });
      });

      describe("Edge cases and validation", () => {
        it("should handle zero scores as valid", () => {
          mockProps.assessmentResult.scores[0].value = "0";
          mockProps.assessmentResult.scores[0].status = "COMPLETE";
          mockProps.assessmentResult.scores[1].value = "0";
          mockProps.assessmentResult.scores[1].status = "COMPLETE";

          const result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);

          assert.equal(result.type, "save-results", "Should treat zero as valid score");
          assert.equal(result.debugInfo.pmAssessmentScore, "0", "PM score should be 0");
          assert.equal(result.debugInfo.bmAssessmentScore, "0", "BM score should be 0");
          assert.isTrue(result.debugInfo.hasValidPmScore, "Should treat zero as valid PM score");
          assert.isTrue(result.debugInfo.hasValidBmScore, "Should treat zero as valid BM score");
        });

        it("should handle null and undefined values correctly", () => {
          mockProps.assessmentResult.scores[0].value = null;
          mockProps.assessmentResult.scores[0].status = "COMPLETE";
          mockProps.assessmentResult.scores[1].value = undefined;
          mockProps.assessmentResult.scores[1].status = "COMPLETE";

          const result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);

          assert.equal(result.type, "enter-scores", "Should return enter-scores for null/undefined values");
          assert.equal(result.debugInfo.pmAssessmentScore, "", "PM score should be empty for null");
          assert.equal(result.debugInfo.bmAssessmentScore, "", "BM score should be empty for undefined");
          assert.isFalse(result.debugInfo.hasValidPmScore, "Should treat null as invalid");
          assert.isFalse(result.debugInfo.hasValidBmScore, "Should treat undefined as invalid");
        });

        it("should handle string numeric values correctly", () => {
          mockProps.assessmentResult.scores[0].value = "15";
          mockProps.assessmentResult.scores[0].status = "COMPLETE";
          mockProps.assessmentResult.scores[1].value = "10";
          mockProps.assessmentResult.scores[1].status = "COMPLETE";

          const result = simulateRenderScoreSubmitButton(mockProps, mockState, mockScoreEntryContext);

          assert.equal(result.type, "save-results", "Should handle string numbers correctly");
          assert.equal(result.debugInfo.pmAssessmentScore, "15", "PM score should be string '15'");
          assert.equal(result.debugInfo.bmAssessmentScore, "10", "BM score should be string '10'");
          assert.isTrue(result.debugInfo.hasValidPmScore, "Should treat string number as valid");
          assert.isTrue(result.debugInfo.hasValidBmScore, "Should treat string number as valid");
        });
      });
    });
  });
}
