import React from "react";
import { Switch } from "react-router-dom";
import PropTypes from "prop-types";
import Layout from "../layouts/layout";
import CustomRoute from "./CustomRoute";
import {
  checkIsSupportOrUniversalCoach,
  checkHasDashboard,
  checkLoggedIn,
  setApplicationVersion
} from "../../startup/client/routeUtils";
import ClientManagement from "../pages/account-setup/client-management";
import DataAdminDashboard from "../pages/data-admin/data-admin-dashboard";
import AssessmentsDashboard from "../pages/assessments-dashboard/assessments-dashboard";
import { AppDataContext } from "./AppDataContext";
import { navbarItemsByRoleId } from "./navbarItems";
import { getMeteorUserId } from "/imports/api/utilities/utilities";

export default class SupportRoutes extends React.Component {
  state = {
    hasDashboard: null,
    userId: null
  };

  routerGroupName = "support";

  componentDidMount() {
    this.tryToGetHasDashboardValue();
  }

  componentDidUpdate() {
    if (this.state.userId !== getMeteorUserId()) {
      this.tryToGetHasDashboardValue();
    }
  }

  tryToGetHasDashboardValue = () => {
    if (getMeteorUserId()) {
      this.getHasDashboardValue();
    } else {
      setTimeout(() => {
        this.tryToGetHasDashboardValue();
      }, 100);
    }
  };

  getHasDashboardValue = () => {
    Meteor.call("users:hasDashboard", (err, hasDashboard) => {
      if (!err) {
        this.setState({ userId: getMeteorUserId(), hasDashboard });
      }
    });
  };

  render() {
    const { hasDashboard } = this.state;
    if (hasDashboard === null) {
      return null;
    }
    const tasks = [checkLoggedIn, setApplicationVersion, checkHasDashboard];
    const navbarItems = hasDashboard ? navbarItemsByRoleId.arbitraryIdsupport : [];

    return (
      <Switch>
        <CustomRoute
          path="/support-clients"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={navbarItems}
          tasks={this.props.tasks}
          render={() => (
            <AppDataContext.Consumer>
              {({ schoolYear }) => (
                <Layout content={<ClientManagement userRole={"support"} schoolYear={schoolYear} />} />
              )}
            </AppDataContext.Consumer>
          )}
        />
        <CustomRoute
          path="/support/dashboard/:orgid"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { orgid } = match.params;
            return (
              <AppDataContext.Consumer>
                {({ schoolYear }) => (
                  <Layout content={<DataAdminDashboard userRole={"support"} orgid={orgid} schoolYear={schoolYear} />} />
                )}
              </AppDataContext.Consumer>
            );
          }}
        />
        <CustomRoute
          path="/support/assessments-dashboard/:pageName?"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={navbarItems}
          tasks={tasks}
          render={({ match }) => {
            const { pageName } = match.params;
            return <Layout content={<AssessmentsDashboard pageName={pageName} pathPrefix="/support" />} />;
          }}
        />
      </Switch>
    );
  }
}

SupportRoutes.defaultProps = {
  tasks: [checkLoggedIn, setApplicationVersion, checkIsSupportOrUniversalCoach]
};

SupportRoutes.propTypes = {
  tasks: PropTypes.array
};
