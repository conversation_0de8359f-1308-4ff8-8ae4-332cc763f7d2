import { ROLE_IDS } from "/tests/cypress/support/common/constants";

export const navbarItemsByRoleId = {
  arbitraryIdsuperAdmin: [
    {
      routeName: "/clients",
      className: "fa fa-users m-r-5",
      display: "Clients Dashboard",
      priority: 0,
      navName: "clients-dashboard"
    },
    // {
    //   routeName: "/clients-sml",
    //   className: "fa fa-users m-r-5",
    //   display: "Clients Dashboard - SML",
    //   priority: 0,
    //   navName: "clients-dashboard-sml"
    // },
    {
      routeName: "/manage-screening",
      className: "fa fa-desktop m-r-5",
      display: "Manage Screening",
      priority: 1,
      navName: "manage-screening"
    },
    {
      routeName: "/class-rules",
      className: "fa fa-tree m-r-5",
      display: "Class Rules",
      priority: 1,
      navName: "class-rules"
    },
    {
      routeName: "/progress-monitoring",
      className: "fa fa-tree m-r-5",
      display: "Progress Monitoring",
      priority: 1,
      navName: "progress-monitoring"
    },
    {
      routeName: "/manage-users",
      className: "fa fa-tree m-r-5",
      display: "Manage Users",
      priority: 1,
      navName: "manage-users"
    },
    {
      routeName: "/manage-message",
      className: "fa fa-tree m-r-5",
      display: "Manage Message",
      priority: 1,
      navName: "manage-message"
    },
    {
      routeName: "/assessments-dashboard",
      className: "fa fa-tree m-r-5",
      display: "Dashboard",
      priority: 1,
      navName: "assessments-dashboard"
    },
    {
      routeName: "/rostering-schedules",
      className: "fa fa-users m-r-5",
      display: "Rostering Schedules",
      priority: 1,
      navName: "rostering-schedules"
    },
    {
      routeName: "/download-users",
      className: "fa fa-users m-r-5",
      display: "Download Users",
      priority: 1,
      navName: "download-users"
    }
  ],
  arbitraryIddownloader: [],
  arbitraryIduniversalCoach: [],
  arbitraryIduniversalDataAdmin: [
    {
      routeName: "/client-list",
      className: "fa fa-users m-r-5",
      display: "Clients Dashboard",
      priority: 0,
      navName: "client-list-dashboard"
    },
    {
      routeName: "/rostering-schedules",
      className: "fa fa-users m-r-5",
      display: "Rostering Schedules",
      priority: 1,
      navName: "rostering-schedules"
    }
  ],
  arbitraryIdsupport: [
    {
      routeName: "/support/assessments-dashboard",
      className: "fa fa-tree m-r-5",
      display: "Dashboard",
      priority: 1,
      navName: "assessments-dashboard"
    }
  ],
  arbitraryIdteacher: [],
  arbitraryIddataAdmin: [
    {
      routeName: "/data-admin/dashboard/{orgid}",
      display: "Data Admin Dashboard",
      priority: 0,
      navName: "data-admin-dashboard",
      shouldDisplay: ({ siteId, roleId }) =>
        ["none", "allSites"].includes(siteId) || [ROLE_IDS.superAdmin, ROLE_IDS.universalDataAdmin].includes(roleId)
    },
    {
      routeName: "/data-admin/manage-accounts/{orgid}",
      display: "Manage Accounts",
      priority: 1,
      navName: "data-admin-manage-accounts"
    },
    {
      routeName: "/data-admin/audit-log/{orgid}",
      display: "Audit Log",
      priority: 1,
      navName: "data-admin-audit-log",
      shouldDisplay: ({ siteId, roleId }) =>
        ["none", "allSites"].includes(siteId) || [ROLE_IDS.superAdmin, ROLE_IDS.universalDataAdmin].includes(roleId)
    },
    {
      routeName: "/data-admin/roster-import-history/{orgid}",
      display: "Import History",
      priority: 1,
      navName: "data-admin-import-history"
    },
    {
      routeName: "/data-admin/upload/{orgid}",
      display: "Import Records",
      priority: 1,
      navName: "data-admin-import-records"
    }
  ],
  arbitraryIdadmin: []
};
