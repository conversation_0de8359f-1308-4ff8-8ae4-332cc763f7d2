import React from "react";
import { withRouter } from "react-router-dom";
import ExposedRoutes from "./ExposedRoutes";
import LoggedInRoutes from "./LoggedInRoutes";
import TeacherRoutes from "./TeacherRoutes";
import DataAdminRoutes from "./DataAdminRoutes";
import SuperAdminRoutes from "./SuperAdminRoutes";
import AdminRoutes from "./AdminRoutes";
import SupportRoutes from "./SupportRoutes";
import UniversalDataAdminRoutes from "./UniversalDataAdminRoutes";

function App() {
  // TODO(fmazur) - add component to load env variables to localstorage
  return (
    <React.Fragment>
      <ExposedRoutes />
      <LoggedInRoutes />
      <TeacherRoutes />
      <DataAdminRoutes />
      <SuperAdminRoutes />
      <AdminRoutes />
      <SupportRoutes />
      <UniversalDataAdminRoutes />
    </React.Fragment>
  );
}

export default withRouter(App);
