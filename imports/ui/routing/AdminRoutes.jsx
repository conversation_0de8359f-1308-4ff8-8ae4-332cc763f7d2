import React from "react";
import PropTypes from "prop-types";
import { Switch } from "react-router-dom";
import CustomRoute from "./CustomRoute";
import {
  checkIsAdminSupportOrUniversalCoach,
  checkLoggedIn,
  setApplicationVersion
} from "../../startup/client/routeUtils";
import AdminSideNavLayout from "../layouts/side-nav-layout-admin";
import AdminView from "../pages/admin-view/admin-view";
import { AppDataContext } from "./AppDataContext";
import ProgramEvaluation from "../pages/program-evaluation/program-evaluation";
import Layout from "../layouts/layout";
import DistrictReporting from "../pages/district-reporting/district-reporting";

export default class AdminRoutes extends React.Component {
  static contextType = AppDataContext;

  navbarItems = [];

  routerGroupName = "admins";

  render() {
    return (
      <Switch>
        <CustomRoute
          path="/school-overview/:orgid/:gradeId?/:siteId?"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { siteId, orgid, gradeId } = match.params;
            return (
              <AdminSideNavLayout
                {...match.params}
                content={<AdminView gradeId={gradeId} orgid={orgid} siteId={siteId} />}
              />
            );
          }}
        />
        <CustomRoute
          path="/program-evaluation/:orgid/site/:siteId"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { siteId, orgid } = match.params;
            return <Layout {...match.params} content={<ProgramEvaluation orgid={orgid} siteId={siteId} />} />;
          }}
        />
        <CustomRoute
          path="/district-reporting/:orgid"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { orgid } = match.params;
            return (
              <Layout
                {...match.params}
                content={
                  <AppDataContext.Consumer>
                    {({ schoolYear }) => {
                      return <DistrictReporting orgid={orgid} schoolYear={schoolYear} />;
                    }}
                  </AppDataContext.Consumer>
                }
              />
            );
          }}
        />
      </Switch>
    );
  }
}

AdminRoutes.defaultProps = {
  tasks: [checkLoggedIn, setApplicationVersion, checkIsAdminSupportOrUniversalCoach]
};

AdminRoutes.propTypes = {
  tasks: PropTypes.array
};
