import React from "react";
import { Switch } from "react-router-dom";
import PropTypes from "prop-types";
import Layout from "../layouts/layout";
import LoginWithSSO from "../pages/authentication/login-with-sso";
import ClientSetup from "../pages/account-setup/client-setup";
import ResetPassword from "../pages/authentication/reset-password";
import TopNavLayout from "../layouts/information-statement-layout";
import InformationStatement from "../pages/information-statement/information-statement";
import CustomRoute from "./CustomRoute";
import { setApplicationVersion } from "../../startup/client/routeUtils";
import OnboardingWelcome from "../pages/onboarding/onboarding-welcome";
import { Login } from "../pages/authentication";

// NOTE, with ReactRouter we no longer use Accounts.onEnrollmentLink and Accounts.onResetPasswordLink, see app-config.js

export default class ExposedRoutes extends React.Component {
  navbarItems = [];

  routerGroupName = "exposed";

  render() {
    return (
      <Switch>
        <CustomRoute
          path="/login"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={() => {
            return <Layout content={<Login />} />;
          }}
        />
        <CustomRoute
          path="/login-with-sso"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={() => {
            return <Layout content={<LoginWithSSO />} />;
          }}
        />
        <CustomRoute
          path="/self-enrollment"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={() => {
            return <Layout content={<ClientSetup selfEnrollment />} />;
          }}
        />
        <CustomRoute
          path="/resetPassword/:token"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { token } = match.params;
            return <Layout content={<ResetPassword usrToken={token} />} />;
          }}
        />
        <CustomRoute
          path="/onboarding/welcome/:usrToken"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { usrToken } = match.params;
            return <Layout content={<OnboardingWelcome usrToken={usrToken} />} />;
          }}
        />
        <CustomRoute
          path="/information-statement"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={() => {
            return <TopNavLayout content={<InformationStatement />} />;
          }}
        />
      </Switch>
    );
  }
}

ExposedRoutes.defaultProps = {
  tasks: [setApplicationVersion]
};

ExposedRoutes.propTypes = {
  tasks: PropTypes.array
};
