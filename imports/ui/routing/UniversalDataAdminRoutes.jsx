import React from "react";
import { Switch } from "react-router-dom";
import PropTypes from "prop-types";
import Layout from "../layouts/layout";
import ClientSetup from "../pages/account-setup/client-setup";
import CustomRoute from "./CustomRoute";
import {
  checkIsUniversalDataAdminOrSuperAdmin,
  checkLoggedIn,
  setApplicationVersion
} from "../../startup/client/routeUtils";
import ClientManagement from "../pages/account-setup/client-management";
import DataAdminAccountSetup from "../pages/account-setup/data-admin-account-setup";
import { AppDataContext } from "./AppDataContext";
import RosteringSyncSchedules from "../pages/account-setup/rostering-sync-schedules";
import { navbarItemsByRoleId } from "./navbarItems";

export default class UniversalDataAdminRoutes extends React.Component {
  render() {
    const navbarItems = navbarItemsByRoleId.arbitraryIduniversalDataAdmin;
    const routerGroupName = "universalDataAdmins";

    return (
      <Switch>
        <CustomRoute
          path="/client-list"
          exact
          routerGroupName={routerGroupName}
          navbarItems={navbarItems}
          tasks={this.props.tasks}
          render={() => (
            <AppDataContext.Consumer>
              {({ schoolYear }) => (
                <Layout content={<ClientManagement userRole={"universalDataAdmin"} schoolYear={schoolYear} />} />
              )}
            </AppDataContext.Consumer>
          )}
        />
        <CustomRoute
          path="/client-list/setup"
          exact
          routerGroupName={routerGroupName}
          navbarItems={navbarItems}
          tasks={this.props.tasks}
          render={() => {
            return <Layout content={<ClientSetup />} />;
          }}
        />
        <CustomRoute
          path="/data-admin-account-setup/:orgid"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const { orgid } = match.params;
            return <Layout content={<DataAdminAccountSetup orgid={orgid} />} />;
          }}
        />
        <CustomRoute
          path="/rostering-schedules"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={navbarItems}
          tasks={this.props.tasks}
          render={() => {
            return <Layout content={<RosteringSyncSchedules />} />;
          }}
        />
      </Switch>
    );
  }
}

UniversalDataAdminRoutes.defaultProps = {
  tasks: [checkLoggedIn, setApplicationVersion, checkIsUniversalDataAdminOrSuperAdmin]
};

UniversalDataAdminRoutes.propTypes = {
  tasks: PropTypes.array
};
