import React from "react";
import PropTypes from "prop-types";

import { <PERSON><PERSON>, But<PERSON> } from "react-bootstrap";
import { Meteor } from "meteor/meteor";
import { getMeteorUserId } from "/imports/api/utilities/utilities";

async function hideInactivityModal() {
  if (getMeteorUserId()) {
    await Meteor.callAsync("inactivity:defibNow");
  }
}

const logoutWarningModal = props => {
  return (
    <Modal
      show={props?.user?.warn}
      onHide={hideInactivityModal}
      dialogClassName="notice"
      backdrop
      data-testid="inactivity-logout-warning-modal"
    >
      <Modal.Body>
        <i className="fa fa-2x fa-warning text-warning" />
        <h1 className="text-warning">Warning</h1>
        <p>You are about to be logged out due to inactivity.</p>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="success" onClick={hideInactivityModal}>
          Keep session active <i className="fa fa-chevron-right fa-sm fa-right" />
          <i className="fa fa-chevron-right fa-sm" />
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

logoutWarningModal.propTypes = {
  user: PropTypes.shape({
    warn: PropTypes.bool
  })
};

const version = () => `v${(typeof window.currentVersion === "function" && window.currentVersion()) || "x.x.x"}`;

const environmentType = () => {
  const environment = Meteor?.settings?.public?.ENVIRONMENT;
  if (!environment || environment === "PROD") {
    return "";
  }
  return (
    <div className="environmentType">
      <span className="w6 font-13 text-danger">{environment} </span>
    </div>
  );
};

export default { logoutWarningModal, version, environmentType };
