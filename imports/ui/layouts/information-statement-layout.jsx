import React from "react";
import PropTypes from "prop-types";

import Navigation from "./navigation/navigation.jsx";
import LoadingScreen from "../components/loading-screen/loading-screen.jsx";
import { renderFooter } from "../utilities";

const InformationStatementLayout = props => {
  return (
    <div className="wrapper">
      <Navigation />
      <main>{props.content}</main>
      {renderFooter()}
      <LoadingScreen />
    </div>
  );
};

export default InformationStatementLayout;

InformationStatementLayout.propTypes = {
  content: PropTypes.object
};
