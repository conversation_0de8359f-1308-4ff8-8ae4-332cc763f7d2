import React, { useCallback, useContext, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { getGender } from "/imports/api/utilities/utilities";
import { Meteor } from "meteor/meteor";
import Loading from "../components/loading";
import { StudentGroupContext } from "../../contexts/StudentGroupContext";
import { StudentContext } from "../../contexts/StudentContext";

const DetailHeaderText = props => {
  const { student: contextStudent } = useContext(StudentContext);
  const { studentGroup: contextStudentGroup } = useContext(StudentGroupContext);

  const student = props.student || contextStudent;
  const studentGroup = props.studentGroup || contextStudentGroup;
  const [groupStats, setGroupStats] = useState({});

  const getStats = useCallback(
    sg => {
      if (sg?._id) {
        Meteor.call("CalculateIndividualStats", sg, (err, res) => {
          if (!err) {
            setGroupStats(res);
          }
        });
      }
    },
    [studentGroup]
  );

  useEffect(() => {
    getStats(studentGroup);
  }, [getStats]);

  const getInterventionStats = () => {
    let statsToUse = groupStats;
    if (student && groupStats && groupStats.individualResults) {
      statsToUse = groupStats.individualResults.find(indRes => indRes.studentId === student._id) || groupStats;
    }
    let interventionConsistency = "N/A";
    if (statsToUse && statsToUse.interventionConsistency !== undefined) {
      // null is a factor here that's why comparing to undefined
      interventionConsistency =
        statsToUse.interventionConsistency !== null ? `${statsToUse.interventionConsistency}%` : "N/A";
    }
    const averageWeeksPerSkill = (statsToUse && statsToUse.averageWeeksPerSkill) || "N/A";
    return { interventionConsistency, averageWeeksPerSkill };
  };

  const renderStats = () => {
    if (groupStats) {
      const { interventionConsistency, averageWeeksPerSkill } = getInterventionStats();
      return (
        <span>
          <span className="intervention-stat">
            {interventionConsistency}
            <label>Weeks with Scores</label>
          </span>
          <span className="intervention-stat">
            {averageWeeksPerSkill}
            <label>Avg Weeks per Skill</label>
          </span>
        </span>
      );
    }
    return null;
  };

  if (!student || !studentGroup) {
    return (
      <header id="detail-header">
        <Loading inline={true} />
      </header>
    );
  }

  const { context: pageContext } = props;
  let headingName = "";
  let grade;
  let localId;
  let gender;
  let studentGrade;

  if (pageContext === "student-detail") {
    ({ localId } = student.identity.identification);
    gender = getGender(student.demographic.gender);
    headingName = `${student.identity.name.lastName}, ${student.identity.name.firstName}`;
    ({ studentGrade } = student);
    ({ grade } = studentGroup);
  }

  return (
    <header id="detail-header">
      <h1 className="profile-name">
        {headingName}
        <span className="profile-name-info">
          {localId && (
            <span>
              Local ID: <span className="field-value">{localId}</span>
            </span>
          )}
          {gender && (
            <span>
              Gender: <span className="field-value">{gender}</span>
            </span>
          )}
          <span>
            Grade: <span className="field-value">{grade}</span>{" "}
          </span>
          {studentGrade && (
            <span>
              Student Grade: <span className="field-value">{studentGrade}</span>{" "}
            </span>
          )}
          {props.displayStats ? renderStats() : null}
        </span>
      </h1>
    </header>
  );
};

DetailHeaderText.propTypes = {
  displayStats: PropTypes.bool,
  context: PropTypes.string,
  studentGroup: PropTypes.object,
  student: PropTypes.any
};

DetailHeaderText.defaultProps = {
  context: "student-detail",
  studentGroup: {}
};

export default DetailHeaderText;
