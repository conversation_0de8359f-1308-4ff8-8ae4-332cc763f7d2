import React, { useContext, useEffect, useRef, useMemo, useCallback } from "react";
import PropTypes from "prop-types";

import url from "url";
import selectComponentToPrint from "/imports/api/printing/selectComponentToPrint";
import Loading from "../components/loading";
import { UserContext } from "/imports/contexts/UserContext";
import { StaticDataContext } from "/imports/contexts/StaticDataContext";

function PrintLayout(props) {
  const { user } = useContext(UserContext);
  const { env } = useContext(StaticDataContext);
  const printIntervalRef = useRef(null);

  const { component } = props;

  const params = useMemo(() => {
    return url.parse(window.location.href, true).query;
  }, []);

  const Component = selectComponentToPrint(component);

  const printThisPage = useCallback(() => {
    const currentDate = new Date().toISOString().slice(0, 10);
    const pdfTitle = `${component}${params.siteId ? ` ${params.siteId}` : ""} ${currentDate}`;
    window.parent.document.querySelector("title").innerHTML = pdfTitle;
    window.document.querySelector("title").innerHTML = pdfTitle;
    const shouldPrintAllStudents = window.location.href.includes("printAllStudents");
    if (!shouldPrintAllStudents) {
      printIntervalRef.current = setInterval(() => {
        if (document.getElementsByClassName("rect5").length === 0) {
          clearInterval(printIntervalRef.current);
          setTimeout(() => {
            window.print();
            window.parent.postMessage(`printWindowClosed`, "*");
            window.parent.document.title = "SpringMath";
            if (env.METEOR_ENVIRONMENT !== "TEST") {
              window.close();
            }
          }, 1000);
        }
      }, 2000);
    }
  }, [component, params.siteId, env.METEOR_ENVIRONMENT]);

  useEffect(() => {
    printThisPage();

    return () => {
      if (printIntervalRef.current) {
        clearInterval(printIntervalRef.current);
      }
    };
  }, [printThisPage]);

  const content = useMemo(() => {
    return (
      <div className="printContainer">
        <Component {...params} />
      </div>
    );
  }, [Component, params]);

  if (user) {
    return content;
  }

  return <Loading />;
}

PrintLayout.propTypes = {
  component: PropTypes.string
};

export default PrintLayout;
