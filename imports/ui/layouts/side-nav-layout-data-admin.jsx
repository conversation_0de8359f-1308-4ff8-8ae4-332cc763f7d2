import React, { useContext, useCallback, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { cloneDeep, groupBy } from "lodash";
import { useHistory, use<PERSON>ara<PERSON>, Link } from "react-router-dom";

import { Meteor } from "meteor/meteor";
import { useTracker } from "meteor/react-meteor-data";
import Alert from "react-s-alert";
import { ListGroup } from "react-bootstrap";

import Navigation from "./navigation/navigation.jsx";
import LoadingScreen from "../components/loading-screen/loading-screen.jsx";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import * as utils from "/imports/api/utilities/utilities";
import { AppDataContext } from "../routing/AppDataContext";
import { renderFooter } from "../utilities";
import { UserContext } from "../../contexts/UserContext";
import { SiteContext } from "../../contexts/SiteContext";
import { OrganizationContext } from "../../contexts/OrganizationContext";
import { SchoolYearContext } from "../../contexts/SchoolYearContext.jsx";

function getNavHeading(headerClass, grade) {
  const title = `Grade: ${grade}`;
  const headingClass = `${headerClass} list-group-item`;
  return <span className={headingClass}>{title}</span>;
}

const DataAdminSideNavLayout = props => {
  const { navName, content } = props;
  const appDataContext = useContext(AppDataContext);
  const { orgId: orgid } = useContext(OrganizationContext);
  const { userId } = useContext(UserContext);
  const { site, siteId } = useContext(SiteContext);
  const { schoolYear } = useContext(SchoolYearContext);
  const { studentGroupId: paramsStudentGroupId, manageView = "students" } = useParams();
  const history = useHistory();
  const [studentGroupId, setStudentGroupId] = useState(paramsStudentGroupId);

  useEffect(() => {
    if (!userId) {
      history.push("/login");
    }
  }, [userId]);

  const { studentGroups, loading } = useTracker(() => {
    if (!orgid || !schoolYear) {
      return { studentGroups: [], loading: true };
    }

    const studentGroupsHandler = Meteor.subscribe("StudentGroups:PerOrg", orgid, schoolYear);
    const isLoading = !studentGroupsHandler.ready();

    let studentGroupsData = [];
    if (!isLoading) {
      const data = StudentGroups.find(
        { siteId, schoolYear },
        { fields: { name: 1, grade: 1 }, sort: { grade: 1, name: 1 } }
      ).fetch();
      studentGroupsData = data.sort((sgA, sgB) => utils.sortByGradeAndName(sgA, sgB));
    }

    return {
      studentGroups: studentGroupsData,
      loading: isLoading
    };
  }, [orgid, siteId, schoolYear]);

  useEffect(() => {
    if (paramsStudentGroupId && paramsStudentGroupId !== studentGroupId) {
      setStudentGroupId(paramsStudentGroupId);
    } else if (!paramsStudentGroupId && studentGroups.length) {
      setStudentGroupId(studentGroups[0]?._id);
    }
  }, [studentGroups, paramsStudentGroupId]);

  const isActive = useCallback(
    groupId => {
      return studentGroupId === groupId ? " active" : "";
    },
    [studentGroupId]
  );

  const getSideNavItems = useCallback(
    groups => {
      return groups.map(group => (
        <Link
          className={`${isActive(group._id)} list-group-item`}
          key={group._id}
          to={`/data-admin/manage-group/${manageView}/${orgid}/site/${siteId}/${group._id}`}
        >
          {group.name}
        </Link>
      ));
    },
    [manageView, isActive, orgid, siteId, studentGroupId]
  );

  const renderStudentGroupList = useCallback(() => {
    const studentGroupList = groupBy(studentGroups, "grade");

    const entries = Object.entries(cloneDeep(studentGroupList)).sort(([, [classA]], [, [classB]]) =>
      utils.sortByGradeAndName(classA, classB)
    );

    return entries.map(([grade, groups]) => {
      const headerClass = "list-group-header-static";
      return (
        <div key={grade}>
          {getNavHeading(headerClass, grade)}
          {getSideNavItems(groups)}
        </div>
      );
    });
  }, [studentGroups, getSideNavItems]);

  if (!site || !orgid || loading) {
    return <LoadingScreen />;
  }

  const isManageSchoolView = appDataContext?.routeName === "data-admin-manage-school";
  const isUnarchiveView = appDataContext?.routeName === "data-admin-unarchive";

  return (
    <div className="wrapper">
      <Navigation navName={navName} />
      <main>
        <aside className="side-nav">
          <div className="site-selector" data-testid="siteSelectorId">
            {site.name}
          </div>
          {!isManageSchoolView && !isUnarchiveView && (
            <ListGroup className="student-group-list">{renderStudentGroupList()}</ListGroup>
          )}
        </aside>
        {content({ studentGroupId })}
      </main>
      {renderFooter({ hasSideNav: true })}
      <LoadingScreen />
      <Alert stack={{ limit: 3 }} effect="jelly" position="top-right" offset={30} />
    </div>
  );
};

DataAdminSideNavLayout.propTypes = {
  navName: PropTypes.string,
  content: PropTypes.func
};

export default DataAdminSideNavLayout;
