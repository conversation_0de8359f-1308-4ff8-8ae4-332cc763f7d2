import React from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";

export default class ScoreDisplayRow extends React.Component {
  // Using a non-state instance variable to track mounted status
  mountedFlag = false;

  state = {
    score: this.props.score,
    isEditingScore: false,
    hasError: false
  };

  componentDidMount() {
    this.mountedFlag = true;
  }

  componentWillUnmount() {
    this.mountedFlag = false;
  }

  componentDidUpdate(prevProps, prevState) {
    if (this.state.score !== prevState.score && this.mountedFlag) {
      this.setState({ score: this.state.score });
      return true;
    }
    return false;
  }

  onValueChange = e => {
    const scoreValue = e.target.value;
    const elemTargetNextSibling = e.target.nextElementSibling;

    let hasError;

    if (scoreValue) {
      if (scoreValue[0] === "0" && scoreValue.length > 1) {
        elemTargetNextSibling.textContent = "No Leading Zeroes";
        elemTargetNextSibling.className = "help-block text-nowrap px-0 text-danger animated bounceIn";
        elemTargetNextSibling.focus();
        hasError = true;
      } else if (scoreValue[0] === "-") {
        elemTargetNextSibling.textContent = "Must Be Positive";
        elemTargetNextSibling.className = "help-block text-nowrap px-0 text-danger animated bounceIn";
        elemTargetNextSibling.focus();
        hasError = true;

        // eslint-disable-next-line no-restricted-globals
      } else if (isNaN(scoreValue)) {
        elemTargetNextSibling.textContent = "Integers Only";
        elemTargetNextSibling.className = "help-block text-nowrap px-0 text-danger animated bounceIn";
        elemTargetNextSibling.focus();
        hasError = true;
      } else {
        elemTargetNextSibling.className = "help-block d-none";
        hasError = false;
      }
    } else {
      elemTargetNextSibling.textContent = "Empty Score";
      elemTargetNextSibling.className = "help-block text-nowrap px-0 text-danger animated bounceIn";
      elemTargetNextSibling.focus();
      hasError = true;
    }

    // Set helper label / class to warning or success
    const limit = this.props.selectedSkillMasteryTarget * 5;
    if (scoreValue > limit) {
      elemTargetNextSibling.textContent = "Unusual High Score";
      elemTargetNextSibling.className = "help-block text-warning animated bounceIn";
      hasError = true;
    }

    if (this.mountedFlag) {
      this.setState({ hasError, score: scoreValue, isEditingScore: this.props.score !== scoreValue });
    }
  };

  saveStudentScores = () => {
    const { orgid, isLastSkill, selectedSkillMasteryTarget, studentId, studentGroupId, selectedSkill } = this.props;
    Meteor.call(
      "StudentGroups:updateStudentScoreInClasswideIntervention",
      {
        studentGroupId,
        assessmentId: selectedSkill.id,
        studentId,
        studentScore: this.state.score,
        masteryTarget: selectedSkillMasteryTarget,
        isLastSkill,
        orgid
      },
      error => {
        if (error) {
          Alert.error(error.reason || "There was a problem updating student score", {
            timeout: 3000
          });
        } else {
          Alert.success("Student score updated!", {
            timeout: 750
          });
          if (this.mountedFlag) {
            this.setState({ isEditingScore: false });
          }
        }
      }
    );
    return true;
  };

  getScoreText = isScoreNA => {
    if (isScoreNA) {
      return "Absent";
    }
    return (
      <span
        className={
          this.props.isEditing ? "text-left-forced d-block w5 font-14 text-grey p-l-20 cursor-not-allowed" : ""
        }
      >
        {this.props.score}
      </span>
    );
  };

  render() {
    const isScoreEmpty = !(this.state.score?.length > 0);
    const isScoreNA = this.props.score === "N/A";
    return (
      <tr className="student-item">
        <td className="student-name">
          <i className="fa fa-circle-o" />
          {` ${this.props.firstName}, ${this.props.lastName}`}
          {this.props.isEnrolled ? (
            ""
          ) : (
            <span className="font-13 font-light text-grey"> (Student no longer in group)</span>
          )}
        </td>
        <td className="screening-status">
          <ul className="screening-cols">
            <li>
              <ul className="lstScoreInputs">
                <li>
                  {this.props.canEdit && this.props.isEditing && !isScoreNA && this.props.isEnrolled ? (
                    <span>
                      <input
                        ref={r => {
                          this[this.props.studentId] = r;
                        }}
                        data-testid={`scoreDisplayRow`}
                        type="text"
                        step="1"
                        className="form-control"
                        value={this.state.score}
                        onChange={this.onValueChange}
                      />
                      <span
                        ref={r => {
                          this[`error${this.props.studentId}`] = r;
                        }}
                        className="help-block d-none"
                      />
                    </span>
                  ) : (
                    this.getScoreText(isScoreNA)
                  )}
                </li>
              </ul>
            </li>
          </ul>
        </td>
        {this.props.canEdit && this.props.isEditing ? (
          <td className="screening-status col-sm-1">
            {!isScoreNA && this.props.isEnrolled ? (
              <button
                className={`btn ${this.state.isEditingScore && "btn-success "}`}
                disabled={!this.state.isEditingScore || this.state.hasError || isScoreEmpty}
                onClick={this.saveStudentScores}
                data-testid={`saveScore_${this.props.studentId}`}
              >
                Save
              </button>
            ) : null}
          </td>
        ) : null}
      </tr>
    );
  }
}

ScoreDisplayRow.propTypes = {
  lastName: PropTypes.string.isRequired,
  firstName: PropTypes.string.isRequired,
  score: PropTypes.string.isRequired,
  canEdit: PropTypes.bool,
  isEditing: PropTypes.bool,
  isLastSkill: PropTypes.bool,
  isEnrolled: PropTypes.bool,
  selectedSkillMasteryTarget: PropTypes.number,
  selectedSkill: PropTypes.object,
  studentId: PropTypes.string,
  studentGroupId: PropTypes.string,
  orgid: PropTypes.string
};
