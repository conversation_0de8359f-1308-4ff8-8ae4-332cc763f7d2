import React from "react";
import _ from "lodash";
import PropTypes from "prop-types";
import ScoreDisplayRow from "./score-display-row";

export default class ScoreDisplay extends React.Component {
  render() {
    const results = _.sortBy(this.props.scores.assessmentResultMeasures[0].studentResults, "lastName");
    const {
      canEdit,
      selectedSkillMasteryTarget,
      selectedSkill,
      studentGroupId,
      isEditing,
      isLastSkill,
      orgid,
      currentlyEnrolledStudentIds = results.map(({ studentId }) => studentId)
    } = this.props;
    return (
      <table className="table intervention-table">
        <tbody>
          {results.map(result => (
            <ScoreDisplayRow
              key={result.studentId}
              lastName={result.firstName}
              firstName={result.lastName}
              score={result.score}
              canEdit={canEdit}
              selectedSkillMasteryTarget={selectedSkillMasteryTarget}
              selectedSkill={selectedSkill}
              studentId={result.studentId}
              isEnrolled={currentlyEnrolledStudentIds.includes(result.studentId)}
              studentGroupId={studentGroupId}
              isEditing={isEditing}
              isLastSkill={isLastSkill}
              orgid={orgid}
            />
          ))}
        </tbody>
      </table>
    );
  }
}
ScoreDisplay.propTypes = {
  scores: PropTypes.object,
  canEdit: PropTypes.bool,
  isEditing: PropTypes.bool,
  isLastSkill: PropTypes.bool,
  selectedSkillMasteryTarget: PropTypes.number,
  selectedSkill: PropTypes.object,
  studentGroupId: PropTypes.string,
  orgid: PropTypes.string,
  currentlyEnrolledStudentIds: PropTypes.array
};
