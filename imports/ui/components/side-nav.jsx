import { Meteor } from "meteor/meteor";
import React, { useState, useEffect, useContext } from "react";
import PropTypes from "prop-types";
import { Link } from "react-router-dom";
import { ListGroup } from "react-bootstrap";
import { groupBy } from "lodash";

import { renderZendeskWidget, sortByGradeAndName } from "/imports/api/utilities/utilities";
import { SiteContext } from "../../contexts/SiteContext";
import { OrganizationContext } from "../../contexts/OrganizationContext";
import { StudentGroupContext } from "../../contexts/StudentGroupContext";
import { UserContext } from "../../contexts/UserContext";
import { ROLE_IDS } from "../../../tests/cypress/support/common/constants";

function SideNav({ isAdmin, isSupport, isUniversalCoach }) {
  const { userStudentGroups, siteId, site } = useContext(SiteContext);
  const { org } = useContext(OrganizationContext);
  const { studentGroupId } = useContext(StudentGroupContext);
  const { userRoles } = useContext(UserContext);
  const [shouldDisplayZendeskWidget, setShouldDisplayZendeskWidget] = useState(false);

  useEffect(() => {
    setShouldDisplayZendeskWidget(false);

    if (!siteId || !userRoles.length) {
      return;
    }

    const isAllowedToFetchZendeskFlag = userRoles.includes(ROLE_IDS.teacher) || userRoles.includes(ROLE_IDS.admin);
    if (!isAllowedToFetchZendeskFlag) {
      return;
    }

    Meteor.call("Settings:getZendeskWidgetFlag", siteId, (err, resp) => {
      if (!err) {
        setShouldDisplayZendeskWidget(resp);
      }
    });
  }, [siteId, userRoles]);

  const isActive = groupId => {
    return studentGroupId === groupId ? " active" : "";
  };

  const getNavHeading = (headerClass, grade) => {
    const title = `Grade: ${grade}`;
    const headingClass = `${headerClass} list-group-item`;
    return <span className={headingClass}>{title}</span>;
  };

  const getSideNavItems = groups =>
    groups.map(group => (
      <Link
        className={`${isActive(group._id)} list-group-item`}
        key={group._id}
        to={`/${group.orgid}/site/${group.siteId}/student-groups/${group._id}`}
      >
        {group.name}
      </Link>
    ));

  const renderStudentGroupList = () => {
    if (!userStudentGroups || !userStudentGroups.length) {
      return null;
    }

    const studentGroupList = groupBy(userStudentGroups, "grade");
    const entries = Object.entries(studentGroupList);

    entries.sort(([, [classA]], [, [classB]]) => sortByGradeAndName(classA, classB));

    return entries.map(([grade, groups]) => {
      const headerClass = "list-group-header-static";
      return (
        <div key={grade}>
          {getNavHeading(headerClass, grade)}
          {getSideNavItems(groups)}
        </div>
      );
    });
  };

  let backToDashboardButton = null;
  if ((isAdmin || isSupport || isUniversalCoach) && userStudentGroups?.length) {
    const sortedStudentGroups = [...userStudentGroups].sort(sortByGradeAndName);
    const firstGroup = sortedStudentGroups[0];
    if (firstGroup) {
      const backToDashboardLink = `/school-overview/${firstGroup.orgid}/all/${firstGroup.siteId}`;
      backToDashboardButton = (
        <Link className="lkBck2Dashboard list-group-item" to={backToDashboardLink} key="adminLink">
          Back to Dashboard
        </Link>
      );
    }
  }

  return (
    <aside className="side-nav">
      <div className="site-selector" data-testid="siteSelectorId">
        {site?.name || "Loading..."}
      </div>
      <ListGroup className="student-group-list">
        {backToDashboardButton}
        {renderStudentGroupList()}
      </ListGroup>
      {shouldDisplayZendeskWidget && org?.name && site?.name && renderZendeskWidget(org.name, site.name)}
    </aside>
  );
}

SideNav.propTypes = {
  isAdmin: PropTypes.bool,
  isSupport: PropTypes.bool,
  isUniversalCoach: PropTypes.bool
};

export default SideNav;
