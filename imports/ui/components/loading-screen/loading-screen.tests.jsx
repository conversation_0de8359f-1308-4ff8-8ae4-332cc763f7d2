import { Meteor } from "meteor/meteor";
import { assert } from "chai";

import React from "react";
import { shallow } from "enzyme";

import LoadingScreen from "./loading-screen.jsx";

if (Meteor.isClient) {
  describe("LoadingScreen UI", () => {
    describe("Render", () => {
      it("render", () => {
        // Verify that the method does what we expected
        const loadingScreenComponent = shallow(<LoadingScreen />);
        assert.isDefined(loadingScreenComponent, "loadingScreenComponent did not render");
      });
    });
  });
}
