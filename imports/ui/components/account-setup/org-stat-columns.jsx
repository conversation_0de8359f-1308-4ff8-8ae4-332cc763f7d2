import React, { Component } from "react";
import Alert from "react-s-alert";
import PropTypes from "prop-types";

import { Loading } from "../loading";
import { getCurrentSchoolYear, getMeteorUserSync, makeCancellableMeteorCall } from "/imports/api/utilities/utilities";
import { AppDataContext } from "../../routing/AppDataContext";

export default class OrgStatColumns extends Component {
  static contextType = AppDataContext;

  activeMeteorCalls = [];

  state = {
    percentScreened: <Loading inline={true} />,
    percentScreenedIsLoaded: false,
    interventionConsistency: <Loading inline={true} />,
    averageWeeksPerSkill: <Loading inline={true} />,
    isFetching: false,
    isLoaded: false
  };

  componentDidMount = () => {
    if (this.props.studentCount <= 0) {
      this.setStats();
    }
  };

  componentWillUnmount = () => {
    this.activeMeteorCalls.forEach(cancelCall => cancelCall());
    this.activeMeteorCalls = [];
  };

  componentDidUpdate(prevProps) {
    if (!prevProps.shouldShowStats && this.props.shouldShowStats) {
      this.setStats();
    } else if (prevProps.schoolYear && prevProps.schoolYear !== this.props.schoolYear) {
      this.activeMeteorCalls.forEach(cancelCall => cancelCall());
      this.activeMeteorCalls = [];
      this.setState({ isLoaded: false, isFetching: false, percentScreenedIsLoaded: false });
    }
  }

  setStats = () => {
    if (this.props.studentCount > 0) {
      this.setState({ isFetching: true }, () => {
        this.setAverageStatsForCurrentOrg();
      });
    } else {
      this.setState({
        percentScreened: "N/A",
        percentScreenedIsLoaded: true,
        interventionConsistency: "N/A",
        averageWeeksPerSkill: "N/A",
        isLoaded: true
      });
    }
  };

  setAverageStatsForCurrentOrg = async () => {
    const orgid = this.props.org._id;
    const schoolYearContext = this.context.schoolYear || (await getCurrentSchoolYear(getMeteorUserSync(), orgid));
    const cancelCall = makeCancellableMeteorCall(
      "StudentGroups:getAverageStatsPerOrg",
      orgid,
      schoolYearContext,
      (err, res) => {
        let stats = {};
        if (err) {
          Alert.error(err.message, { timeout: 5000 });
        } else {
          stats = {
            percentScreened: res.percentScreened === "N/A" ? -1 : res.percentScreened,
            interventionConsistency:
              res.averageInterventionConsistency === "N/A" ? -1 : res.averageInterventionConsistency,
            averageWeeksPerSkill: res.averageWeeksPerSkill === "N/A" ? -1 : res.averageWeeksPerSkill
          };
          this.setState({
            percentScreened: res.percentScreened,
            interventionConsistency: res.averageInterventionConsistency,
            averageWeeksPerSkill: res.averageWeeksPerSkill,
            isFetching: false,
            isLoaded: true
          });
        }
        this.props.onStatsLoaded(orgid, stats);
      }
    );
    this.activeMeteorCalls.push(cancelCall);
  };

  formatAvarageStats = (value, precision = 0) => {
    if (this.state.isLoaded === false) {
      return <Loading inline={true} />;
    }
    if (value === "N/A") {
      return <span>{value}</span>;
    }
    const formattedValue = precision > 0 ? value.toFixed(precision) : Math.round(value).toString();
    return <span>{formattedValue}</span>;
  };

  render() {
    if (!this.state.isLoaded) {
      return (
        <td colSpan={3}>
          <div className="d-grid">
            <button className="btn btn-default" onClick={this.setStats} data-testid={`orgStats_${this.props.org.name}`}>
              {this.state.isFetching ? <Loading inline={true} /> : <span>Show stats</span>}
            </button>
          </div>
        </td>
      );
    }

    const { percentScreened, interventionConsistency, averageWeeksPerSkill } = this.state;

    const colTestId = `orgStatColumn_${this.props.org.name}`;
    return (
      <React.Fragment>
        <td className="col-md-1 text-center" data-testid={colTestId}>
          {percentScreened}
          {percentScreened === "N/A" ? "" : "%"}
        </td>
        <td className="col-md-1 text-center" data-testid={colTestId}>
          {this.formatAvarageStats(interventionConsistency)}
          {interventionConsistency === "N/A" ? "" : "%"}
        </td>
        <td className="col-md-1 text-center" data-testid={colTestId}>
          {this.formatAvarageStats(averageWeeksPerSkill, 1)}
        </td>
      </React.Fragment>
    );
  }
}

OrgStatColumns.propTypes = {
  org: PropTypes.object,
  studentCount: PropTypes.number,
  shouldShowStats: PropTypes.bool,
  onStatsLoaded: PropTypes.func,
  schoolYear: PropTypes.number
};
