import { assert } from "chai";
import React from "react";
import MockDate from "mockdate";
import ScreeningRow from "./score-entry-row.jsx";
import { renderWithRouter } from "../../../../tests/helpers/testUtils";

describe("ScreeningRow UI", () => {
  jest.mock("../../../api/utilities/utilities", () => ({
    getCurrentSchoolYear: jest.fn(() => 2019)
  }));
  beforeAll(() => {
    MockDate.set("2018-12-20");
  });
  afterAll(() => {
    jest.restoreAllMocks();
    MockDate.reset();
  });
  describe("Render", () => {
    let screeningRowComponent;
    const currentStudentScreeningStatus = {
      studentId: "testStudentId",
      allScoresEntered: true,
      allScoresCancelled: false
    };

    beforeEach(() => {
      screeningRowComponent = renderWithRouter(
        <table>
          <tbody>
            <ScreeningRow
              student={{
                identity: {
                  name: {
                    firstName: "<PERSON>",
                    lastName: "<PERSON>"
                  }
                }
              }}
              assessmentScores={[]}
              currentStudentScreeningStatus={currentStudentScreeningStatus}
            />
          </tbody>
        </table>
      );
    });

    it("render", () => {
      // Verify that the method does what we expected
      assert.isDefined(screeningRowComponent, "screeningRowComponent did not render");
    });
  });
});
