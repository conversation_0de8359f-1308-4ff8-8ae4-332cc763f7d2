import React, { useContext } from "react";
import PropTypes from "prop-types";
import moment from "moment";
import { ClassContext } from "../../pages/classContext";
import * as helpers from "./helperFunction";

import StudentName from "./StudentName.jsx";

const IndividualInterventionStudents = props => {
  const { studentGroup, groupStats } = useContext(ClassContext);
  const lastScoreUpdatedAtByStudentId = props.individualAssessmentResults.reduce((acc, result) => {
    if (!acc[result.studentId] || (result.lastScoreUpdatedAt && result.lastScoreUpdatedAt > acc[result.studentId])) {
      acc[result.studentId] = result.lastScoreUpdatedAt;
    }
    return acc;
  }, {});
  return (
    <div>
      {helpers.sortStudentsByName(props.individualInterventionStudents).map((s, i) => {
        const individualResults =
          (groupStats.extendedIndividualResults && groupStats.extendedIndividualResults.individualResults) ||
          groupStats.individualResults;
        const individualStats = individualResults && individualResults.find(stats => stats.studentId === s._id);
        const lastScoreUpdatedAtForStudent = lastScoreUpdatedAtByStudentId[s._id];
        const lastScoreUpdatedAt =
          (lastScoreUpdatedAtForStudent && moment(lastScoreUpdatedAtForStudent).format("L")) || "N/A";
        return (
          <div key={i} className="row">
            <StudentName student={s} studentGroup={studentGroup} />
            <div
              className="col-md-2 row-individual-intervention-currentAssessment"
              data-testid={`currentIntAssessmentRow_${i}`}
            >
              {s.currentSkill.assessmentName}
            </div>

            {individualStats && typeof individualStats.interventionConsistency === "number" ? (
              <div
                className={`col-md-2 text-center${individualStats.interventionConsistency < 80 ? " text-danger" : ""}`}
              >
                {`${individualStats.interventionConsistency}%`}
                <small>{`${individualStats.numberOfWeeksWithScoresEntered} of ${individualStats.numberOfWeeksActive} weeks with scores`}</small>
              </div>
            ) : (
              <div className="col-md-2 text-center">N/A</div>
            )}
            {individualStats ? (
              <div className="col-md-2 text-center">{individualStats.averageWeeksPerSkill || "N/A"}</div>
            ) : null}
            {individualStats ? <div className="col-md-2 text-center">{lastScoreUpdatedAt}</div> : null}
          </div>
        );
      })}
    </div>
  );
};

IndividualInterventionStudents.propTypes = {
  individualInterventionStudents: PropTypes.array,
  individualAssessmentResults: PropTypes.array
};

export default IndividualInterventionStudents;
