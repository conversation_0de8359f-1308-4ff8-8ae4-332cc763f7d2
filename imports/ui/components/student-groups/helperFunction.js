import Alert from "react-s-alert";

export function getStudentName(s, sortBy = "lastFirst") {
  if (s.identity && s.identity.name) {
    return sortBy === "lastFirst"
      ? `${s.identity.name.lastName}, ${s.identity.name.firstName}`
      : `${s.identity.name.firstName} ${s.identity.name.lastName}`;
  }
  return "";
}

export function sortStudentsByName(students) {
  return students && students.sort((a, b) => (getStudentName(a) > getStudentName(b) ? 1 : -1));
}

export function getStudentDispayId(s) {
  return (
    (s.identity.identification && (s.identity.identification.localId || s.identity.identification.stateId)) || undefined
  );
}

export function getStudentHref(studentId, studentGroup, section) {
  return `/${studentGroup.orgid}/site/${studentGroup.siteId}/student-groups/${studentGroup._id}/students/${studentId}${
    section ? `/${section}` : ""
  }`;
}

export function displayError(errorText, timeout = 5000) {
  Alert.error(errorText, { timeout });
}
export function getMeasureIndexesForSorting(studentRecommendationData) {
  const measureIndexesForSorting = new Set();

  studentRecommendationData.forEach(student => {
    student.recommendationReasonData.forEach((data, index) => {
      if (data.columnData.Score < data.columnData.Target) {
        measureIndexesForSorting.add(index);
      }
    });
  });

  return [...measureIndexesForSorting].sort();
}

export function sortStudentsByRecommendationData(studentRecommendationData) {
  const studentRecommendationDataCopy = [...studentRecommendationData];
  studentRecommendationDataCopy.sort((a, b) => {
    return a.studentName.localeCompare(b.studentName);
  });

  const measureIndexesForSorting = getMeasureIndexesForSorting(studentRecommendationDataCopy).reverse();
  if (!measureIndexesForSorting.length) {
    return studentRecommendationDataCopy;
  }
  studentRecommendationDataCopy.sort((a, b) => {
    const recommendationReasonDataA = a.recommendationReasonData.filter((i, index) =>
      measureIndexesForSorting.includes(index)
    );
    const recommendationReasonDataB = b.recommendationReasonData.filter((i, index) =>
      measureIndexesForSorting.includes(index)
    );

    for (let i = 0; i < recommendationReasonDataA.length; i++) {
      const { Score: scoreA, Target: instructionalTarget } = recommendationReasonDataA[i].columnData;
      const { Score: scoreB } = recommendationReasonDataB[i].columnData;

      if (scoreA !== scoreB && !(scoreA >= instructionalTarget && scoreB >= instructionalTarget)) {
        return scoreA - scoreB;
      }
    }
    return true;
  });

  studentRecommendationDataCopy.sort((a, b) => b.numberOfRecommendations - a.numberOfRecommendations);

  return studentRecommendationDataCopy;
}
