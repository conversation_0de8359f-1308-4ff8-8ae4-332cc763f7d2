import React from "react";
import PropTypes from "prop-types";
import { Link } from "react-router-dom";
import * as helpers from "./helperFunction";

const StudentName = props => {
  const studentDisplayId = helpers.getStudentDispayId(props.student);
  const gridSize = props.isAdminView ? "col-md-3" : "col-md-4 text-start";
  return (
    <div className={gridSize} data-student-grade={props.student.grade} data-testid={`individual_${props.student._id}`}>
      {
        <Link to={helpers.getStudentHref(props.student._id, props.studentGroup, "individual")}>
          {helpers.getStudentName(props.student)}
        </Link>
      }
      {studentDisplayId ? <small className="student-display-id">{studentDisplayId}</small> : null}
    </div>
  );
};

StudentName.propTypes = {
  student: PropTypes.object,
  studentGroup: PropTypes.object,
  isAdminView: PropTypes.bool
};

export default StudentName;
