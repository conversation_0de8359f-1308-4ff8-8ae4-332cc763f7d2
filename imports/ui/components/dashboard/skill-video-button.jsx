import React, { Component } from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, ModalHeader } from "react-bootstrap";
import { Link } from "react-router-dom";
import ConfirmModal from "../../pages/data-admin/confirm-modal";

export default class SkillVideoButton extends Component {
  state = {
    isVideoModalOpen: false,
    videoUrl: null,
    name: null
  };

  renderSkillVideoIcon = () => {
    const { measureNumber, hasVideo, additionalClassNames } = this.props;
    if (!hasVideo) {
      return null;
    }
    const url = `https://edspring-inta.s3.amazonaws.com/protocol-images/Skills/${measureNumber}.mp4`;
    let className = "skill-button";
    if (additionalClassNames.length) {
      className += " ";
      className += additionalClassNames;
    }
    return (
      <Button size="xs" variant="outline-blue" className={className} onClick={this.displayVideoModal(url)}>
        <i className="fa fa-video-camera cursor-pointer" />
      </Button>
    );
  };

  displayVideoModal = url => () => {
    this.setState({ isVideoModalOpen: true, videoUrl: url, skillName: this.props.skillName });
  };

  closeVideoModal = () => {
    this.setState({ isVideoModalOpen: false, videoUrl: null, skillName: null });
  };

  renderSkillVideoModal = () => {
    const { skillName, isVideoModalOpen, videoUrl } = this.state;
    return (
      <ConfirmModal
        onCloseModal={this.closeVideoModal}
        confirmAction={() => {}}
        confirmText=""
        bodyQuestion=""
        cancelText="Close"
        showModal={isVideoModalOpen}
        size="lg"
        headerText={
          <ModalHeader className="justify-content-center">
            <div className="w9">{skillName}</div>
          </ModalHeader>
        }
        customProps={{
          shouldCenterHeader: true,
          canCloseUsingBackdrop: true,
          useCustomHeader: true
        }}
        bodyText={
          <div className="text-center">
            <video className="video-js vjs-default-skin vjs-big-play-centered" controls preload="auto" width="100%">
              <source src={videoUrl} type="video/mp4" />
              <p className="vjs-no-js">
                To view this video please enable JavaScript, and consider upgrading to a web browser that{" "}
                <Link to="http://videojs.com/html5-video-support/" target="_newTab">
                  supports HTML5 video
                </Link>
              </p>
            </video>
          </div>
        }
      />
    );
  };

  render() {
    return (
      <React.Fragment>
        {this.renderSkillVideoIcon()}
        {this.state.isVideoModalOpen ? this.renderSkillVideoModal() : null}
      </React.Fragment>
    );
  }
}

SkillVideoButton.propTypes = {
  measureNumber: PropTypes.string,
  skillName: PropTypes.string,
  hasVideo: PropTypes.bool,
  additionalClassNames: PropTypes.string
};

SkillVideoButton.defaultProps = {
  additionalClassNames: ""
};
