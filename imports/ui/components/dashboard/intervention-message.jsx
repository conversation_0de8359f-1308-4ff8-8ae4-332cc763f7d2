import React, { Component } from "react";
import PropTypes from "prop-types";
import isObject from "lodash/isObject";

import { getResultsMessageFromCode } from "/imports/api/utilities/utilities";
import * as helpers from "../student-groups/helperFunction";

class InterventionMessage extends Component {
  dismissCurrentSkillMessage() {
    const type = this.props.entityType; // 'StudentGroup' or 'Student'
    const { entityId, shouldDismissEndIntervention, studentGroupId } = this.props;
    if (shouldDismissEndIntervention) {
      Meteor.call(
        "endCurrentIndividualIntervention",
        {
          studentId: entityId,
          studentGroupId
        },
        err => {
          if (err) {
            helpers.displayError(`There was an error: ${err.message}`);
          }
        }
      );
    } else {
      Meteor.call("DismissCurrentSkillMessage", entityId, type, err => {
        if (err) {
          throw new Meteor.Error(403, "Failed to dismiss message");
        }
      });
    }
  }

  render() {
    const { message } = this.props;
    const resultsMessage = getResultsMessageFromCode(message?.messageCode, this.props.name, this.props.season);
    let resultsMessageText = resultsMessage;
    let resultsMessageType;
    if (isObject(resultsMessage) && resultsMessage.text) {
      resultsMessageText = resultsMessage.text;
      resultsMessageType = resultsMessage.type;
    }
    return (
      <div
        className={`conInterventionMessageNotice ${
          resultsMessageType === "warning" ? "conInterventionMessageWarning" : ""
        }`}
      >
        <div className="conInterventionMessageNotice-Heading">
          <h2>
            {resultsMessageText}
            {message && message.additionalStudentsAddedToInterventionQueue ? (
              <div>New students are eligible for individual interventions. Please visit the Students page.</div>
            ) : null}
          </h2>
          <button className="btnNoticeAction btnStartScreening btn" onClick={() => this.dismissCurrentSkillMessage()}>
            <i className="fa fa-close" data-testid="dismissInterventionMessage" />
          </button>
        </div>
      </div>
    );
  }
}
export default InterventionMessage;

InterventionMessage.propTypes = {
  message: PropTypes.object,
  entityId: PropTypes.string,
  entityType: PropTypes.string,
  name: PropTypes.object,
  season: PropTypes.string,
  shouldDismissEndIntervention: PropTypes.bool,
  studentGroupId: PropTypes.string
};

InterventionMessage.defaultProps = {
  shouldDismissEndIntervention: false
};
