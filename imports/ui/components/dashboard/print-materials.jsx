import { Meteor } from "meteor/meteor";
import React, { Component } from "react";
import { with<PERSON>outer } from "react-router-dom";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { Loading } from "/imports/ui/components/loading";
import { downloadPdf } from "/imports/ui/utilities";

class PrintMaterials extends Component {
  constructor(props) {
    super(props);
    this.state = {
      printMaterialsStatus: "NOT_ATTEMPTED",
      printMaterials: "",
      printMaterialsQueue: []
    };
    this.inDropdown = !!props.getCurrentlyPrinting;
  }

  componentDidUpdate(prevProps) {
    if (this.props.selectedSkillAssessmentId !== prevProps.selectedSkillAssessmentId) {
      this.resetToDefaultState();
    }
  }

  requestPrintMaterials = () => {
    const assessmentIds = [this.props.assessmentId];
    const {
      benchmarkPeriodId,
      materialsType, // 'intervention-packet' or 'assessment'
      materialsPart = "", // empty for all parts, "instructions" or "practice"
      interventionType = "", // 'CW', 'CCC', 'GP', '' for assessment,
      assessmentMeasure, // measure number
      protocolMeasure,
      studentName = "",
      groupName = "",
      mayTakeLong
    } = this.props;

    this.setState({
      printMaterialsStatus: "FETCHING"
    });
    if (mayTakeLong) {
      Alert.info("Generating all materials for this intervention may take up to a minute. Do not exit this web page.", {
        timeout: 120000
      });
    }
    Meteor.call(
      "printMaterials:triggerGenerateAndGetLink",
      {
        protocolType: interventionType,
        assessmentIds,
        assessmentMeasureIds: [assessmentMeasure],
        protocolMeasureIds: [protocolMeasure],
        studentGrade: this.props.grade,
        studentName,
        studentGroupId: this.props.match?.params?.studentGroupId,
        payloadType: materialsType,
        benchmarkPeriodId,
        groupName,
        materialsPart
      },
      (error, resp) => {
        Alert.closeAll(); // for closing long waiting notice
        if (error) {
          Alert.error(`${error.error}: ${error.reason}`, { timeout: 5000 });
          this.setState({
            printMaterialsStatus: "ERROR"
          });
        } else if (this.state.printMaterialsStatus === "FETCHING") {
          const parts = Array.from(resp.matchAll(/"part\d+"([^"]+)/gi));
          if (parts.length) {
            this.setState({
              printMaterialsStatus: "SUCCESS",
              printMaterialsQueue: parts.map(part => part[1])
            });
          } else {
            this.setState({
              printMaterialsStatus: "SUCCESS",
              printMaterials: resp
            });
          }
        }
      }
    );
  };

  selectPrintMaterialsInDropdown = () => {
    if (this.inDropdown) {
      this.props.setCurrentlyPrinting(this.props.printOptionKey);
    }
  };

  resetErrorState = () => {
    if (this.state.printMaterialsStatus === "ERROR") {
      this.resetToDefaultState();
    }
  };

  resetToDefaultState = () => {
    this.setState({
      hasPrinted: false,
      printMaterialsStatus: "NOT_ATTEMPTED"
    });
    if (this.inDropdown) {
      this.props.setCurrentlyPrinting(false);
    }
  };

  resetState = () => {
    this.props.setCurrentlyPrinting(false);
  };

  handleDownloadPdf = () => {
    const fileName = `springmath-assessment${this.props.assessmentId}.pdf`;
    downloadPdf(this.state.printMaterials, fileName);
    if (this.inDropdown) {
      this.props.setCurrentlyPrinting(false);
    }
    this.resetToDefaultState();
  };

  handleDownloadPdfQueue = (index, title) => () => {
    const fileName = `springmath-assessment${this.props.assessmentId}_${title}.pdf`;
    downloadPdf(this.state.printMaterialsQueue[index], fileName);
  };

  renderPrintAssessmentsButton = () => {
    const buttonClass = `btn btn-success white-space-normal`;
    const itemClass = this.inDropdown ? "menuitem print-materials-link" : `btn btn-primary invert white-space-normal`;
    if (this.state.printMaterialsStatus === "NOT_ATTEMPTED") {
      return (
        <span
          className={itemClass}
          onClick={this.inDropdown ? this.selectPrintMaterialsInDropdown : this.requestPrintMaterials}
        >
          <i className="fa fa-file-text-o fa-left" />
          {`${this.inDropdown ? " " : ""}${this.props.initialText}`}
        </span>
      );
    }
    if (this.state.printMaterialsStatus === "SUCCESS") {
      if (this.state.printMaterialsQueue.length) {
        return (
          <React.Fragment>
            <span className={buttonClass} onClick={this.handleDownloadPdfQueue(0, "student-materials")}>
              <i className="fa fa-file-text-o fa-left" />
              View Student Materials
            </span>{" "}
            <span className={buttonClass} onClick={this.handleDownloadPdfQueue(1, "answer-key")}>
              <i className="fa fa-file-text-o fa-left" />
              View Answer Key
            </span>
          </React.Fragment>
        );
      }
      return this.handleDownloadPdf();
    }
    if (this.state.printMaterialsStatus === "ERROR") {
      return (
        <span className="btn btn-danger" onClick={this.resetErrorState}>
          <i className="fa fa-file-text-o fa-left" />
          Printing error, click to reset
        </span>
      );
    }
    return <p>There was a problem loading assessments</p>;
  };

  renderResetButton = () => {
    if (!this.inDropdown || !this.props.getCurrentlyPrinting() || this.state.printMaterialsStatus !== "SUCCESS") {
      return null;
    }
    return (
      <span className="btn btn-danger" onClick={this.resetState}>
        <i className="fa fa-file-text-o fa-left" />
        Reset
      </span>
    );
  };

  componentDidMount() {
    if (
      this.inDropdown &&
      this.state.printMaterialsStatus === "NOT_ATTEMPTED" &&
      this.props.getCurrentlyPrinting() === this.props.printOptionKey
    ) {
      this.requestPrintMaterials();
    }
  }

  render() {
    if (this.props.loading) return <Loading />;
    return (
      <div
        className={`w9 text-center ${
          this.inDropdown && !this.props.getCurrentlyPrinting() ? "print-materials-item" : "print-materials-button"
        } ${this.inDropdown && this.props.getCurrentlyPrinting() && this.props.isVertical ? "col-12" : ""}`}
        data-testid="printAssessmentsButton"
      >
        {this.state.printMaterialsStatus === "FETCHING" ? (
          <Loading message={this.props.loadingText} inline />
        ) : (
          <React.Fragment>
            {this.renderPrintAssessmentsButton()}
            {this.renderResetButton()}
          </React.Fragment>
        )}
      </div>
    );
  }
}

PrintMaterials.defaultProps = {
  mayTakeLong: false
};

PrintMaterials.propTypes = {
  assessmentId: PropTypes.string,
  assessmentMeasure: PropTypes.string,
  benchmarkPeriodId: PropTypes.string,
  grade: PropTypes.string,
  initialText: PropTypes.string,
  interventionType: PropTypes.string,
  loading: PropTypes.bool,
  loadingText: PropTypes.string,
  materialsType: PropTypes.string,
  materialsPart: PropTypes.string,
  printOptionKey: PropTypes.string,
  protocolMeasure: PropTypes.string,
  studentName: PropTypes.string,
  groupName: PropTypes.string,
  setCurrentlyPrinting: PropTypes.func,
  getCurrentlyPrinting: PropTypes.func,
  mayTakeLong: PropTypes.bool,
  selectedSkillAssessmentId: PropTypes.string,
  isVertical: PropTypes.bool,
  match: PropTypes.object
};

export default withRouter(PrintMaterials);
