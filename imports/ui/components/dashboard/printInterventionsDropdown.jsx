import { Meteor } from "meteor/meteor";
import React, { Component } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { DropdownButton, Dropdown } from "react-bootstrap";
import { Loading } from "/imports/ui/components/loading";
import { downloadPdf } from "/imports/ui/utilities";

function convertIdToName(id) {
  switch (id) {
    case "CCC":
      return "Cover, Copy, Compare";
    case "GP":
      return "Guided Practice";
    case "TT":
      return "Timed Trial";
    case "RC":
      return "Response Cards";
    case "B":
      return "Bingo";
    case "FIB":
      return "Fill-In Bingo";
    case "SRG":
      return "Scatter and Rearrange Game";
    case "IR":
      return "Incremental Rehearsal";
    case "BAG":
      return "Before and After Game";
    default:
      return null;
  }
}

export default class PrintInterventionsDropdown extends Component {
  constructor(props) {
    super(props);
    this.handleDropDownActivitySelect = this.handleDropDownActivitySelect.bind(this);
    this.state = {
      fetchingPrintMaterials: false,
      printMaterialsStatus: "NOT_ATTEMPTED",
      printMaterials: ""
    };
  }

  handleDropDownActivitySelect(eventKey, event) {
    event.preventDefault();
    const aid = this.props.assessmentMeasure; // NOTE(fmazur) - monitorAssessmentMeasure from benchmarkAssessmentId
    const pid = this.props.protocolMeasure; // NOTE(fmazur) - monitorAssessmentMeasure from assessmentId
    const assessmentIds = [this.props.assessmentId, this.props.benchmarkAssessmentId];
    const { benchmarkPeriodId, groupName, studentGroupId, grade: studentGrade, studentName = "" } = this.props;

    Meteor.call(
      "printMaterials:triggerGenerateAndGetLink",
      {
        protocolType: eventKey,
        assessmentIds,
        assessmentMeasureIds: [aid],
        protocolMeasureIds: [pid],
        studentGrade,
        studentName,
        studentGroupId,
        payloadType: "intervention-packet",
        benchmarkPeriodId,
        groupName
      },
      (err, resp) => {
        if (err) {
          Alert.error(`${err.error}: ${err.reason}`, { timeout: 3000 });
          this.setState({
            fetchingPrintMaterials: false,
            printMaterialsStatus: "ERROR"
          });
        } else {
          this.setState({
            fetchingPrintMaterials: false,
            printMaterialsStatus: "SUCCESS",
            printMaterials: resp
          });
        }
      }
    );
    this.setState({
      fetchingPrintMaterials: true
    });
  }

  renderDropDown() {
    if (this.props.protocolMeasure && this.props.protocolMeasure.length > 0) {
      return (
        <DropdownButton
          variant="primary"
          className="invert"
          onSelect={this.handleDropDownActivitySelect}
          title={this.props.initialText}
          id="ddbActivity"
        >
          {this.props.interventionsAvailable.map(intervention => (
            <Dropdown.Item key={intervention} eventKey={intervention}>
              {convertIdToName(intervention)}
            </Dropdown.Item>
          ))}
        </DropdownButton>
      );
    }
    return <Loading inline={true} />;
  }

  handleDownloadPdf = () => {
    const fileName = `springmath-assessment${this.props.assessmentId}.pdf`;
    downloadPdf(this.state.printMaterials, fileName);
    this.setState({ fetchingPrintMaterials: false, printMaterialsStatus: "NOT_ATTEMPTED", printMaterials: "" });
  };

  render() {
    return (
      <div>
        {this.state.fetchingPrintMaterials ? (
          <Loading message="Custom building intervention activity packets. Just a moment please." inline />
        ) : (
          <div>{this.state.printMaterialsStatus === "SUCCESS" ? this.handleDownloadPdf() : this.renderDropDown()}</div>
        )}
      </div>
    );
  }
}

PrintInterventionsDropdown.propTypes = {
  assessmentId: PropTypes.string,
  assessmentMeasure: PropTypes.string,
  benchmarkAssessmentId: PropTypes.string,
  benchmarkPeriodId: PropTypes.string,
  grade: PropTypes.string,
  interventionsAvailable: PropTypes.array,
  protocolMeasure: PropTypes.string,
  studentGroupId: PropTypes.string,
  studentName: PropTypes.string,
  groupName: PropTypes.string,
  initialText: PropTypes.string
};
