import React, { useState, useContext, useCallback, useEffect, useRef, useMemo } from "react";
import PropTypes from "prop-types";
import { intersection, uniq } from "lodash";

import PrintInterventionsDropdown from "./printInterventionsDropdown";
import PrintMaterials from "./print-materials";
import PrintMultipleMaterials from "./print-multiple-materials";
import { shouldUseDevMode } from "../../../api/utilities/utilities";
import { StaticDataContext } from "../../../contexts/StaticDataContext";
import { AppDataContext } from "../../routing/AppDataContext";

export function GroupedAssessmentResults(props) {
  const {
    env: { CI }
  } = useContext(StaticDataContext);
  const assessmentResult = props.assessmentResults[0];
  const isSingleStudent = props.assessmentResults.length === 1;
  const { assessmentId } = assessmentResult.scores[0];
  const { groupedAssessmentsCollapsedContext, setContext } = useContext(AppDataContext);
  const contextKey = useMemo(() => `${assessmentId}_${props.instructionalLevel}_${props.type}`, [
    assessmentId,
    props.instructionalLevel,
    props.type
  ]);
  const [isCollapsed, setIsCollapsed] = useState(groupedAssessmentsCollapsedContext?.[contextKey] ?? true);
  const shouldRenderAdditionalInfo = shouldUseDevMode(CI);
  const contextRef = useRef(groupedAssessmentsCollapsedContext);

  useEffect(() => {
    if (!(contextKey in (groupedAssessmentsCollapsedContext || {}))) {
      setContext({
        groupedAssessmentsCollapsedContext: {
          ...groupedAssessmentsCollapsedContext,
          [contextKey]: true
        }
      });
    }
  }, [contextKey, groupedAssessmentsCollapsedContext, setContext]);

  useEffect(() => {
    contextRef.current = groupedAssessmentsCollapsedContext;
  }, [groupedAssessmentsCollapsedContext]);

  const updateCollapsedState = useCallback(
    newCollapsedState => {
      setIsCollapsed(newCollapsedState);
      setContext({
        groupedAssessmentsCollapsedContext: {
          ...contextRef.current,
          [contextKey]: newCollapsedState
        }
      });
    },
    [setContext, contextKey]
  );

  const getAssessmentMeasureForGroupedAssessments = skillAssessmentIdsInSmallGroup => {
    const { assessmentMeasures } =
      props.groupedAssessments.find(ga =>
        ga.assessmentIds?.find(gaId => skillAssessmentIdsInSmallGroup.includes(gaId))
      ) || {};
    return assessmentMeasures;
  };

  const getCurrentAssessmentForSingleStudent = () => {
    return {
      measureNumber: props.measureNumberByAssessmentId[assessmentId],
      assessmentId,
      monitorProtocols:
        assessmentResult.individualSkills.interventions.map(intervention => intervention.interventionAbbrv) || []
    };
  };

  const getMostDifficultAssessmentInSmallGroup = () => {
    let skillAssessmentIdsInSmallGroup = props.assessmentResults.map(a => a.scores[0].assessmentId);
    if (isSingleStudent) {
      const { currentSkill } = props.studentsById[assessmentResult.studentId];
      if (
        !getAssessmentMeasureForGroupedAssessments([currentSkill.benchmarkAssessmentId])?.includes(
          props.measureNumberByAssessmentId[currentSkill.assessmentId]
        )
      ) {
        skillAssessmentIdsInSmallGroup = uniq([
          ...skillAssessmentIdsInSmallGroup,
          currentSkill.assessmentId,
          currentSkill.benchmarkAssessmentId
        ]);
      }
    }
    const assessmentIdByMeasure = {};
    const measureNumbersInSmallGroup = skillAssessmentIdsInSmallGroup.map(aId => {
      const measure = props.measureNumberByAssessmentId[aId];
      assessmentIdByMeasure[measure] = aId;
      return measure;
    });
    const assessmentMeasures = getAssessmentMeasureForGroupedAssessments(skillAssessmentIdsInSmallGroup);
    let mostDifficultAssessment = {};
    assessmentMeasures?.forEach(groupedMeasure => {
      if (measureNumbersInSmallGroup.includes(groupedMeasure)) {
        mostDifficultAssessment = {
          measureNumber: groupedMeasure,
          assessmentId: assessmentIdByMeasure[groupedMeasure],
          monitorProtocols:
            props.groupedAssessments.find(ga => ga.monitorInterventionProtocolsByMeasure[groupedMeasure])
              ?.monitorInterventionProtocolsByMeasure[groupedMeasure] || []
        };
      }
    });
    return mostDifficultAssessment;
  };

  const toggle = e => {
    const { type, className, parentElement, tagName } = e.target;
    if (type === "button" || className.includes("btn") || parentElement.className.includes("btn") || tagName === "A") {
      return;
    }
    updateCollapsedState(!isCollapsed);
  };

  const renderStudentList = () => {
    return (
      <ul className="list-inline m-10 m-t-0 m-b-0">
        {props.assessmentResults.map(ii => {
          const student = props.studentsById[ii.studentId];
          const studentFullName = `${student.identity.name.firstName} ${student.identity.name.lastName}`;
          return (
            <li key={ii.studentId} className="small text-black w4">
              {studentFullName}{" "}
              <small className="w3">
                ({ii?.individualSkills?.assessmentName ? `${ii.individualSkills.assessmentName}` : ""})
              </small>
            </li>
          );
        })}
      </ul>
    );
  };

  const fetchAssessmentResultForBenchmarkPeriodId = aId => {
    return props.assessmentResults.find(
      ar => ar.individualSkills.assessmentId === aId || ar.individualSkills.benchmarkAssessmentId === aId
    );
  };

  const getMaterialRequestItems = () => {
    return props.assessmentResults.map(({ assessmentIds, studentId, grade }) => {
      const uniqueAssessmentIds = [...new Set(assessmentIds)];
      const student = props.studentsById[studentId];
      const studentName = `${student.identity.name.firstName} ${student.identity.name.lastName}`;
      return {
        assessmentIds: uniqueAssessmentIds,
        measureNumbers: uniqueAssessmentIds.map(aId => props.measureNumberByAssessmentId[aId]),
        studentName,
        grade
      };
    });
  };

  const isIndividualIntervention = props.type === "individualIntervention";

  const { skillName = assessmentResult.individualSkills.assessmentName } =
    props.groupedAssessments.find(ga => ga.assessmentIds?.includes(assessmentId)) || {};
  const skill = assessmentResult.individualSkills;

  const mostDifficultAssessmentInSmallGroup = isSingleStudent
    ? getCurrentAssessmentForSingleStudent()
    : getMostDifficultAssessmentInSmallGroup();

  let { measureNumber, assessmentId: measureAssessmentId } = mostDifficultAssessmentInSmallGroup;
  const { monitorProtocols } = mostDifficultAssessmentInSmallGroup;

  if (
    !props.groupedAssessments?.length ||
    !measureNumber ||
    !measureAssessmentId ||
    props.type === "drillDownAssessment"
  ) {
    // NOTE(fmazur) - DrillDown has same assessmentId as benchmarkAssessmentId
    // NOTE(fmazur) - IndividualIntervention has assessmentId for current skill and benchmarkAssessmentId for current goal skill
    measureNumber = props.measureNumberByAssessmentId[assessmentId];
    measureAssessmentId =
      props.type === "individualIntervention" ? assessmentId : assessmentResult.individualSkills.benchmarkAssessmentId;
  }
  const goalSkillName = assessmentResult?.individualSkills?.benchmarkAssessmentName || "";

  const { benchmarkPeriodId = "" } =
    fetchAssessmentResultForBenchmarkPeriodId(measureAssessmentId) ||
    fetchAssessmentResultForBenchmarkPeriodId(
      props.type === "individualIntervention" ? assessmentId : assessmentResult.individualSkills.benchmarkAssessmentId
    ) ||
    {};

  let parsedInterventions = monitorProtocols;
  parsedInterventions = props.assessmentResults.reduce((intersected, ar) => {
    const interventions = ar.individualSkills.interventions.map(intervention => intervention.interventionAbbrv) || [];
    return intersection(intersected, interventions);
  }, parsedInterventions);

  const groupedAssessmentName =
    props.assessmentResults.length > 1 && isIndividualIntervention ? skillName : skill.assessmentName;
  const shouldDisplayInterventionDropdown = !!(isIndividualIntervention && parsedInterventions.length);

  const renderInterventionLevelLabel = label => {
    return label?.length && props.type !== "drillDownAssessment" ? <small>({label})</small> : null;
  };

  const renderGenerateProgressMonitoringAssessmentButton = (bmId, assessmentName) => {
    if (isSingleStudent) {
      return null;
    }
    return (
      <PrintMultipleMaterials
        materialRequestItems={getMaterialRequestItems()}
        interventionType=""
        materialsType="assessment"
        benchmarkPeriodId={bmId}
        studentGroupId={assessmentResult.studentGroupId}
        assessmentName={assessmentName}
        initialText="Generate Progress Monitoring Assessments"
        loadingText="Generating assessments"
      />
    );
  };

  let studentName;
  if (isSingleStudent) {
    const student = props.studentsById[assessmentResult.studentId];
    studentName = `${student.identity.name.firstName} ${student.identity.name.lastName}`;
  }

  return (
    <div className="panel panel-info">
      <div
        className="panel-heading d-flex justify-content-between align-items-center"
        onClick={toggle}
        role="button"
        data-testid={`individual_grouped_section`}
      >
        <div>
          <i className={`m-r-10 fa fa-chevron-${isCollapsed ? "down" : "up"}`} />
          <strong>{groupedAssessmentName}</strong> {renderInterventionLevelLabel(props.instructionalLevel)}
          {shouldRenderAdditionalInfo ? <small className="w3">{` (AM#${measureNumber})`}</small> : ""}
        </div>
        <div className="d-flex">
          {shouldDisplayInterventionDropdown ? (
            <PrintInterventionsDropdown
              assessmentMeasure={props.measureNumberByAssessmentId[props.benchmarkAssessmentId] || measureNumber}
              protocolMeasure={measureNumber}
              interventionsAvailable={parsedInterventions}
              assessmentId={measureAssessmentId}
              benchmarkAssessmentId={props.benchmarkAssessmentId}
              benchmarkPeriodId={assessmentResult.benchmarkPeriodId}
              materialsType="intervention-packet"
              grade={assessmentResult.grade}
              studentName={isSingleStudent ? studentName : undefined}
              initialText={isSingleStudent ? "Select Activity" : "Select Small Group Activity"}
              loadingText="Custom building classwide intervention materials. Just a moment please."
              groupName={props.studentGroupName}
              studentGroupId={assessmentResult.studentGroupId}
            />
          ) : null}
          {isIndividualIntervention ? (
            renderGenerateProgressMonitoringAssessmentButton(benchmarkPeriodId, groupedAssessmentName)
          ) : (
            <PrintMaterials
              assessmentMeasure={measureNumber}
              assessmentId={measureAssessmentId}
              protocolMeasure={measureNumber}
              interventionType=""
              materialsType="assessment"
              benchmarkPeriodId={benchmarkPeriodId}
              grade={props.assessmentResults[0].grade}
              initialText="Generate Assessment"
              loadingText="Generating custom assessment. Just a moment please."
            />
          )}
        </div>
      </div>
      {shouldRenderAdditionalInfo ? (
        <div className="alert-info m-0 w6" style={{ color: "#31708f", paddingLeft: "15px" }}>
          <small>
            {goalSkillName}
            <span className="w4">
              {props.measureNumberByAssessmentId[props.benchmarkAssessmentId]
                ? ` (AM#${props.measureNumberByAssessmentId[props.benchmarkAssessmentId]})`
                : ""}
            </span>
          </small>
        </div>
      ) : null}
      <div className="panel-body p-0">
        {isCollapsed
          ? renderStudentList()
          : props.assessmentResults.map(ii =>
              isIndividualIntervention ? props.renderIndividualIntervention(ii) : props.renderFollowUpAssessment(ii)
            )}
      </div>
    </div>
  );
}

GroupedAssessmentResults.propTypes = {
  assessmentResults: PropTypes.array,
  groupedAssessments: PropTypes.array,
  measureNumberByAssessmentId: PropTypes.object,
  renderFollowUpAssessment: PropTypes.func,
  renderIndividualIntervention: PropTypes.func,
  studentGroupName: PropTypes.string,
  benchmarkAssessmentId: PropTypes.string,
  instructionalLevel: PropTypes.string,
  studentsById: PropTypes.object,
  type: PropTypes.string
};
