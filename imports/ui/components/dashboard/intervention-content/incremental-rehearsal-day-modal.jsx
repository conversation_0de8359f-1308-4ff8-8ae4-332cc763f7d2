import React, { Component } from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";

export default class IncrementalRehearsalDayModal extends Component {
  close = () => {
    this.props.closeModal();
  };

  render() {
    const { day, incrementalRehearsal, isCompleted } = this.props;

    return (
      <Modal show={this.props.showModal} onHide={this.close} dialogClassName="modal-lg" backdrop="static">
        <ModalHeader>
          <h3 className="w9">
            Incremental Rehearsal: {incrementalRehearsal.name} - Day {day}
          </h3>
        </ModalHeader>

        <ModalBody>
          <div className="modal-container">
            <div className="row">
              <p>
                <strong>Incremental rehearsal</strong> is a quick but powerful intervention to help children master
                basic facts.
              </p>
              <p>
                It will only take you 3 extra minutes per day, but will help your students more easily master difficult
                facts.
              </p>
              <p>When you start classwide intervention, you will see the incremental rehearsal button.</p>
              <ol className="ms-3">
                <li>First you will show a 1-min video lesson teaching a specific “unknown” fact for the day.</li>
                <li>
                  Then you click on the button <strong>View Slides</strong> which will download a slide deck to your
                  computer.
                </li>
                <li>
                  Next, project the slide deck onto the board at the front of the room and have children chorally
                  respond to each slide.
                </li>
                <li>On the first slide, you will have them practice answering the unknown correctly three times.</li>
                <li>Then advance the deck and signal for students to answer aloud as you go. That’s it.</li>
                <li>
                  The next time you click on <strong>Use</strong>, SpringMath will provide a new video targeting a new
                  unknown and then provide continued practice of recently learned facts along with the daily unknown
                  fact.
                </li>
              </ol>
              <p>You should do incremental rehearsal after you’ve conducted classwide math intervention for the day.</p>
              <p>
                Please note, you can preview the lesson and materials by clicking <strong>Preview</strong> but once you
                have selected <strong>Use</strong>, SpringMath will consider that session complete and move you to new
                materials the following day.
              </p>
              <p>
                There is no need to print any materials or record anything to use this 3-min add-on to classwide
                intervention.
              </p>
            </div>
          </div>
        </ModalBody>

        <ModalFooter className="d-flex justify-content-center">
          {!isCompleted && (
            <Button variant="success" onClick={this.props.onUse}>
              Use
            </Button>
          )}
          <Button variant="info" onClick={this.props.onPreview}>
            Preview
          </Button>
          <Button variant="default" onClick={this.close}>
            Close
          </Button>
        </ModalFooter>
      </Modal>
    );
  }
}

IncrementalRehearsalDayModal.defaultProps = {
  onUse: () => {},
  onPreview: () => {},
  isCompleted: false
};

IncrementalRehearsalDayModal.propTypes = {
  showModal: PropTypes.bool,
  closeModal: PropTypes.func,
  onUse: PropTypes.func,
  onPreview: PropTypes.func,
  incrementalRehearsal: PropTypes.object,
  day: PropTypes.number,
  isCompleted: PropTypes.bool
};
