import React, { Component } from "react";
import PropTypes from "prop-types";
import { DropdownButton, Dropdown } from "react-bootstrap";
import PrintMaterials from "../print-materials";

export default class ClasswideInterventionPrintOptions extends Component {
  constructor(props) {
    super(props);
    this.state = {
      currentlyPrinting: false
    };
    this.dropdownTitle = this.props.dropdownTitle || "Create Intervention Materials";
  }

  getPrintingComponents = () => {
    const { isVertical, selectedSkillAssessmentId, interventionType } = this.props;
    const printMaterialProps = {
      ...this.props.printMaterialProps,
      setCurrentlyPrinting: this.setCurrentlyPrinting,
      getCurrentlyPrinting: this.getCurrentlyPrinting,
      isVertical
    };

    return {
      [interventionType]: (
        <PrintMaterials
          {...printMaterialProps}
          interventionType={interventionType}
          printOptionKey={interventionType}
          materialsType="intervention-packet"
          initialText="Generate All Materials"
          loadingText="Building All Materials"
          selectedSkillAssessmentId={selectedSkillAssessmentId}
        />
      ),
      [`${interventionType}_instructions`]: (
        <PrintMaterials
          {...printMaterialProps}
          interventionType={interventionType}
          printOptionKey={`${interventionType}_instructions`}
          materialsType="intervention-packet"
          materialsPart="instructions"
          initialText="Generate Teacher Materials"
          loadingText="Building Teacher Materials"
          selectedSkillAssessmentId={selectedSkillAssessmentId}
        />
      ),
      [`${interventionType}_practice`]: (
        <PrintMaterials
          {...printMaterialProps}
          interventionType={interventionType}
          printOptionKey={`${interventionType}_practice`}
          materialsType="intervention-packet"
          materialsPart="practice"
          initialText="Generate Student Materials and Answer Key"
          loadingText="Building Student Materials and Answer Key"
          selectedSkillAssessmentId={selectedSkillAssessmentId}
        />
      )
    };
  };

  setCurrentlyPrinting = printKey => {
    this.setState({ currentlyPrinting: printKey });
  };

  getCurrentlyPrinting = () => this.state.currentlyPrinting;

  render() {
    const printingComponents = this.getPrintingComponents();
    if (this.state.currentlyPrinting) {
      return printingComponents[this.state.currentlyPrinting];
    }
    const { variant, dropdownTitle, dropdownOptions, printMaterialProps, interventionType } = this.props;
    return (
      <DropdownButton
        variant={variant || "primary"}
        title={this.dropdownTitle}
        id={dropdownTitle ? `ddbActivity${this.dropdownTitle.replace(/\s/g, "")}` : "ddbActivity"}
        {...dropdownOptions}
        data-testid={`${interventionType}-measure-${printMaterialProps.assessmentMeasure}`}
      >
        {Object.keys(printingComponents).map(printKey => (
          <Dropdown.Item
            key={printKey}
            eventKey={printKey}
            data-testid={`create-intervention-materials-options-${interventionType}`}
            onClick={e => e.stopPropagation()} // Disable closing dropdown on selecting item
          >
            {printingComponents[printKey]}
          </Dropdown.Item>
        ))}
      </DropdownButton>
    );
  }
}

ClasswideInterventionPrintOptions.propTypes = {
  printMaterialProps: PropTypes.object,
  selectedSkillAssessmentId: PropTypes.string,
  dropdownTitle: PropTypes.string,
  interventionType: PropTypes.string,
  className: PropTypes.string,
  dropdownOptions: PropTypes.object,
  variant: PropTypes.string,
  isVertical: PropTypes.bool
};
