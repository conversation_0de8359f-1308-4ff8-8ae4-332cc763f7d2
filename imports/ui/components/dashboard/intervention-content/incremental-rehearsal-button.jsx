import React, { Component } from "react";
import PropTypes from "prop-types";
import { DropdownButton, Dropdown } from "react-bootstrap";
import { get, range } from "lodash";
import Alert from "react-s-alert";

import { getMeteorUserSync } from "/imports/api/utilities/utilities";
import { getCurrentDate } from "/imports/api/helpers/getCurrentDate";

import IncrementalRehearsalDayModal from "./incremental-rehearsal-day-modal";
import IncrementalRehearsalVideoModal from "./incremental-rehearsal-video-modal";

export default class IncrementalRehearsalButton extends Component {
  constructor(props) {
    super(props);

    this.state = {
      isDayModalOpen: false,
      isVideoModalOpen: false,
      day: null,
      isCompleted: null,
      type: null,
      currentDate: new Date() // Default fallback
    };
  }

  async componentDidMount() {
    await this.loadCurrentDate();
  }

  async componentDidUpdate(prevProps) {
    // Reload current date if orgid changes
    if (prevProps.orgid !== this.props.orgid) {
      await this.loadCurrentDate();
    }
  }

  loadCurrentDate = async () => {
    try {
      const meteorUser = getMeteorUserSync();
      const customDate = get(meteorUser, "profile.customDate");
      const currentDate = await getCurrentDate(customDate, this.props.orgid);
      this.setState({ currentDate });
    } catch (error) {
      console.error("Error loading current date:", error);
      // Keep the default fallback date
    }
  };

  openDayModal = (day, isCompleted) => () => {
    this.setState({ isDayModalOpen: true, day, isCompleted });
  };

  closeDayModal = () => {
    this.setState({ isDayModalOpen: false, day: null });
  };

  handleUse = () => {
    const { studentGroupId, studentId, incrementalRehearsal, siteId } = this.props;
    const methodName = studentId
      ? "Students:useIncrementalRehearsalForDay"
      : "StudentGroups:useIncrementalRehearsalForDay";
    const methodParams = { measureNumber: incrementalRehearsal.measure, day: this.state.day };

    if (studentId) {
      methodParams.studentId = studentId;
      methodParams.siteId = siteId;
    } else {
      methodParams.studentGroupId = studentGroupId;
    }
    Meteor.call(methodName, methodParams, err => {
      if (err) {
        Alert.error("Problem with using Incremental Rehearsal");
      } else {
        this.setState({ isVideoModalOpen: true, isDayModalOpen: false, type: "use" });
      }
    });
  };

  handlePreview = () => {
    this.setState({ isVideoModalOpen: true, isDayModalOpen: false, type: "preview" });
  };

  closeVideoModal = () => {
    this.setState({ isVideoModalOpen: false, day: null });
  };

  renderDropdownDayOptions = () => {
    const { maxVideos = 0, maxSlides = 0 } = this.props.incrementalRehearsal;
    const MAX_NUMBER_OF_DAYS = Math.max(maxVideos, maxSlides, 20);
    const NUMBER_OF_PAST_DAYS = 5;
    const { currentDate } = this.state;
    const startOfDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate());
    const startOfDayTimestamp = Math.floor(startOfDay.getTime() / 1000);
    const numberOfCompletedDays = this.props.datesOfCompletion.length;
    const shouldDisplayNewDay = !this.props.datesOfCompletion.find(timestamp => timestamp >= startOfDayTimestamp);
    const maxDay = Math.min(numberOfCompletedDays + (shouldDisplayNewDay ? 1 : 0), MAX_NUMBER_OF_DAYS);
    const minDay = Math.max(0, maxDay - NUMBER_OF_PAST_DAYS - 1);
    return range(maxDay, minDay, -1).map(day => {
      const isCompleted = day <= numberOfCompletedDays;
      return (
        <Dropdown.Item
          key={day}
          onClick={this.openDayModal(day, isCompleted)}
          className={`${isCompleted ? "text-black-50" : ""}`}
        >
          <i className="fa fa-file-text-o fa-left" /> Day {day} Material
        </Dropdown.Item>
      );
    });
  };

  render() {
    return (
      <React.Fragment>
        <DropdownButton variant="warning" className="invert ms-auto" title="Boost It">
          {this.renderDropdownDayOptions()}
        </DropdownButton>
        <IncrementalRehearsalDayModal
          showModal={this.state.isDayModalOpen}
          closeModal={this.closeDayModal}
          incrementalRehearsal={this.props.incrementalRehearsal}
          day={this.state.day}
          isCompleted={this.state.isCompleted}
          onUse={this.handleUse}
          onPreview={this.handlePreview}
        />
        {this.state.isVideoModalOpen && (
          <IncrementalRehearsalVideoModal
            showModal={this.state.isVideoModalOpen}
            closeModal={this.closeVideoModal}
            incrementalRehearsal={this.props.incrementalRehearsal}
            day={this.state.day}
            type={this.state.type}
          />
        )}
      </React.Fragment>
    );
  }
}

IncrementalRehearsalButton.defaultProps = {
  incrementalRehearsal: {},
  datesOfCompletion: []
};

IncrementalRehearsalButton.propTypes = {
  studentGroupId: PropTypes.string,
  studentId: PropTypes.string,
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  incrementalRehearsal: PropTypes.object,
  datesOfCompletion: PropTypes.array
};
