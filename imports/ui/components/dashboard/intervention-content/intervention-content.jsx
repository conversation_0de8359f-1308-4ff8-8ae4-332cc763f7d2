import React, { Component } from "react";
import PropTypes from "prop-types";
import get from "lodash/get";

import { withTracker } from "meteor/react-meteor-data";
import { Assessments } from "/imports/api/assessments/assessments";
import { Loading } from "../../loading.jsx";
import InterventionMessage from "../intervention-message.jsx";
import { calculateClasswideROI } from "/imports/api/utilities/utilities";
import PrintMaterials from "../print-materials";
import ClasswideInterventionPrintOptions from "./classwide-intervention-print-options";
import { isHighSchoolGrade } from "../../../utilities";
import IncrementalRehearsalButton from "./incremental-rehearsal-button";

class InterventionContent extends Component {
  constructor(props) {
    super(props);
    this.hideModal = this.hideModal.bind(this);
    this.state = {
      bsModalShow: false,
      bsModalContent: "",
      bsModalClassNames: "",
      loadingPrintDocument: false
    };
  }

  componentDidUpdate(prevProps) {
    if (!this.props.loading && this.props.loading !== prevProps.loading) {
      this.props.refreshScroll();
    }
  }

  // eslint-disable-next-line camelcase
  UNSAFE_componentWillReceiveProps(nextProps) {
    const thisPropSkill = this.props.studentGroup.currentClasswideSkill;
    const nextPropSkill = nextProps.studentGroup.currentClasswideSkill;
    if (
      thisPropSkill.assessmentId !== nextPropSkill.assessmentId &&
      thisPropSkill.assessmentResultId !== nextPropSkill.assessmentResultId
    ) {
      this.showModal("Congratulations! Your class is ready to move up a skill!", true);
    } else if (
      thisPropSkill.assessmentId === nextPropSkill.assessmentId &&
      thisPropSkill.assessmentResultId !== nextPropSkill.assessmentResultId
    ) {
      this.showModal(
        "You will continue to work on this skill next week. Keep at it! Most classes reach mastery within 2-3 weeks of high-quality practice.",
        false
      );
    }
  }

  showModal(message, passedIntervention) {
    this.setState({
      bsModalShow: true,
      bsModalContent: message,
      bsModalClassNames: `notice notice-${passedIntervention ? "success" : "warning"}`,
      bsModalIcon: `noticeIcon fa fa-2x fa-${passedIntervention ? "thumbs-o-up" : "warning"}`
    });
  }

  hideModal() {
    this.setState({
      bsModalShow: false,
      bsModalContent: "",
      bsModalClassNames: "",
      bsModalIcon: ""
    });
  }

  getPrintMaterialProps = () => ({
    assessmentMeasure: this.props.monitorAssessmentVendorId,
    assessmentId:
      this.props.selectedSkillAssessmentId ||
      (this.props.studentGroup && this.props.studentGroup.currentClasswideSkill.assessmentId),
    benchmarkPeriodId: this.props.benchmarkPeriodId,
    protocolMeasure: this.props.monitorAssessmentVendorId,
    grade: this.props.studentGroup.grade,
    groupName: this.props.studentGroup.name
  });

  wasClasswideInterventionCompleted = () =>
    this.props &&
    this.props.studentGroup &&
    this.props.studentGroup.currentClasswideSkill &&
    !this.props.studentGroup.currentClasswideSkill.assessmentId &&
    this.props.studentGroup.currentClasswideSkill.message &&
    !this.props.studentGroup.currentClasswideSkill.message.dismissed &&
    !this.props.isPrinting;

  shouldShowInterventionMessage = () =>
    this.props.studentGroup.currentClasswideSkill &&
    this.props.studentGroup.currentClasswideSkill.message &&
    !this.props.studentGroup.currentClasswideSkill.message.dismissed &&
    !this.props.isPrinting;

  render() {
    const interventionTitle = this.props.assessmentName || "";
    // Show classwide complete message if they have a currentSkill without an assessmentId
    const showClasswideComplete = this.wasClasswideInterventionCompleted();
    // Classwide
    if (!this.props.loading) {
      if (this.props.studentGroup) {
        const printMaterialProps = this.getPrintMaterialProps();
        const isHighSchoolGroup = isHighSchoolGrade(this.props.studentGroup.grade);
        const isKindergarten = this.props.studentGroup.grade === "K";

        if (this.props.isPrinting) {
          return (
            <h4 className="w9 p-l-1">
              {this.props.classwideROI ? (
                <span className="roi">{`Classwide Rate of Improvement: ${this.props.classwideROI}`}</span>
              ) : null}
              {interventionTitle}
            </h4>
          );
        }

        const currentClasswideSkill = get(this, "props.studentGroup.currentClasswideSkill", {});
        const currentClasswideSkillMessage = currentClasswideSkill?.message || {};
        // Class median is below instructional target
        const { assessment, studentGroup, isAdditionalSkill, selectedSkillAssessmentId } = this.props;
        const isDisplayingAdditionalSkillFirstTime =
          isAdditionalSkill &&
          !studentGroup.additionalHistory?.map(h => h.assessmentId).includes(selectedSkillAssessmentId);
        const hasAcquisitionLevelIntervention =
          currentClasswideSkill?.assessmentId === selectedSkillAssessmentId &&
          (currentClasswideSkillMessage?.messageCode === "2" || isDisplayingAdditionalSkillFirstTime);
        const shouldDisplayBoostItButton =
          assessment?.ir?.measure &&
          assessment?.ir?.grades?.includes(studentGroup?.grade) &&
          assessment._id === studentGroup.currentClasswideSkill.assessmentId;

        return (
          <div className="intervention-content classwide">
            {this.shouldShowInterventionMessage() ? (
              <InterventionMessage
                entityType="StudentGroup"
                entityId={this.props.studentGroup._id}
                message={currentClasswideSkillMessage}
              />
            ) : null}
            <p>
              Your class is currently in classwide intervention. Complete intervention activities weekly and enter
              progress monitoring scores weekly.{" "}
              {isKindergarten && (
                <a
                  id="classwide-materials-link"
                  rel="noopener noreferrer"
                  target="_blank"
                  href="https://s3.amazonaws.com/springmath/pdf/faq/Kindergarten%20Classwide%20Preparation%2012-16-2020.pdf"
                >
                  Click here to prepare for classwide intervention.
                </a>
              )}
            </p>
            <h4 className="w9">
              {this.props.classwideROI ? (
                <span className="roi">{`Classwide Rate of Improvement: ${this.props.classwideROI}`}</span>
              ) : null}
              {interventionTitle}
            </h4>
            <h5>Create Intervention Materials to View or Print</h5>
            <div className="print-materials-buttons-row d-flex gap-2">
              {isHighSchoolGroup ? (
                <React.Fragment>
                  <PrintMaterials
                    {...printMaterialProps}
                    interventionType="CW"
                    materialsType="intervention-packet"
                    initialText="Classwide Intervention"
                    loadingText="Building 15m Materials"
                    selectedSkillAssessmentId={this.props.selectedSkillAssessmentId}
                  />
                </React.Fragment>
              ) : (
                <ClasswideInterventionPrintOptions
                  printMaterialProps={printMaterialProps}
                  selectedSkillAssessmentId={this.props.selectedSkillAssessmentId}
                  interventionType={"CW"}
                />
              )}
              {hasAcquisitionLevelIntervention && (
                <ClasswideInterventionPrintOptions
                  printMaterialProps={printMaterialProps}
                  selectedSkillAssessmentId={this.props.selectedSkillAssessmentId}
                  interventionType={"GP-CW"}
                  dropdownTitle={"Create Skill Acquisition Materials"}
                />
              )}
              {isHighSchoolGroup && (
                <PrintMaterials
                  {...printMaterialProps}
                  interventionType="HI"
                  materialsType="intervention-packet"
                  initialText="Boost It"
                  loadingText="Building Materials"
                  mayTakeLong={true}
                  selectedSkillAssessmentId={this.props.selectedSkillAssessmentId}
                />
              )}
              {shouldDisplayBoostItButton && (
                <IncrementalRehearsalButton
                  studentGroupId={studentGroup._id}
                  orgid={studentGroup.orgid}
                  incrementalRehearsal={assessment.ir}
                  datesOfCompletion={studentGroup?.ir?.[assessment.ir.measure]?.dates}
                />
              )}
            </div>
          </div>
        );
      }
      if (showClasswideComplete) {
        return (
          <div className="intervention-content classwide">
            <InterventionMessage
              entityType="StudentGroup"
              entityId={this.props.studentGroup._id}
              message={this.props.studentGroup.currentClasswideSkill.message}
            />
            <small>
              We recommend that you visit the students page to schedule a couple students for individual interventions.
            </small>
          </div>
        );
      }
    }
    return <Loading message="Loading..." inline />;
  }
}

InterventionContent.propTypes = {
  progressMonitoringInfo: PropTypes.object,
  studentGroup: PropTypes.object,
  benchmarkPeriodId: PropTypes.string,
  monitorAssessmentVendorId: PropTypes.string,
  isAdditionalSkill: PropTypes.bool,
  loading: PropTypes.bool,
  isPrinting: PropTypes.bool,
  classwideROI: PropTypes.string,
  refreshScroll: PropTypes.func,
  assessment: PropTypes.object,
  assessmentName: PropTypes.string,
  selectedSkillAssessmentId: PropTypes.string
};

export default withTracker(params => {
  let assessmentName = "";
  let monitorAssessmentVendorId;

  let assessment;
  let roi = "";
  const { selectedSkillAssessmentId, studentGroup, isAdditionalSkill = false } = params || {};
  if (selectedSkillAssessmentId) {
    assessment = Assessments.findOne({ _id: selectedSkillAssessmentId });
  } else if (studentGroup?.currentClasswideSkill) {
    assessment = Assessments.findOne({
      _id: studentGroup.currentClasswideSkill?.assessmentId
    });
  }
  const { name, monitorAssessmentMeasure, _id } = assessment || {};
  if (assessment) {
    assessmentName = name;
    monitorAssessmentVendorId = monitorAssessmentMeasure;
    roi = calculateClasswideROI(studentGroup, null, _id);
  }
  const loading =
    studentGroup?.currentClasswideSkill?.assessmentId && !assessment && studentGroup?.currentClasswideSkill?.targets;
  return {
    assessment,
    assessmentName,
    monitorAssessmentVendorId,
    isAdditionalSkill,
    loading,
    classwideROI: roi
  };
})(InterventionContent);

export { InterventionContent as PureInterventionContent }; // testing
