import { Meteor } from "meteor/meteor";
import React, { Component } from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import { Link } from "react-router-dom";

const incrementalRehearsalBaseUrl = Meteor.settings.public.INCREMENTAL_REHEARSAL_BASE_URL || "";

export default class IncrementalRehearsalVideoModal extends Component {
  close = () => {
    this.props.closeModal();
  };

  render() {
    const { day, incrementalRehearsal, type } = this.props;
    const { maxVideos = 20, maxSlides = 20 } = this.props.incrementalRehearsal;
    let slidesUrl = "";
    let videoUrl = "";
    if (incrementalRehearsalBaseUrl) {
      const incrementalRehearsalSkillBaseUrl = `${incrementalRehearsalBaseUrl}/${incrementalRehearsal.name}`;
      slidesUrl = `${incrementalRehearsalSkillBaseUrl}/${incrementalRehearsal.name}_Day-${day}.pdf`;
      videoUrl = `${incrementalRehearsalSkillBaseUrl}/${incrementalRehearsal.name}_Video-${day}.mp4`;
    }

    return (
      <Modal show={this.props.showModal} onHide={this.close} dialogClassName="modal-lg modal-xl" backdrop="static">
        <ModalHeader>
          <h3 className="w9">
            Incremental Rehearsal: {incrementalRehearsal.name} - Day {day}
            {type === "preview" && " (Preview)"}
          </h3>
        </ModalHeader>

        <ModalBody>
          <div className="modal-container">
            <div className="row">
              {videoUrl && day <= maxVideos ? (
                <video
                  className="video-js vjs-default-skin vjs-big-play-centered"
                  controls
                  preload="auto"
                  width="100%"
                  height="auto"
                >
                  <source src={videoUrl} type="video/mp4" />
                  <p className="vjs-no-js">
                    To view this video please enable JavaScript, and consider upgrading to a web browser that{" "}
                    <Link to="http://videojs.com/html5-video-support/" target="_newTab">
                      supports HTML5 video
                    </Link>
                  </p>
                </video>
              ) : null}
            </div>
          </div>
        </ModalBody>

        <ModalFooter className="d-flex justify-content-center">
          {slidesUrl && day <= maxSlides ? (
            <a className="btn btn-success" href={slidesUrl} target="_blank" rel="noreferrer">
              View Slides
            </a>
          ) : null}
          <Button variant="default" onClick={this.close}>
            Close
          </Button>
        </ModalFooter>
      </Modal>
    );
  }
}

IncrementalRehearsalVideoModal.propTypes = {
  showModal: PropTypes.bool,
  closeModal: PropTypes.func,
  incrementalRehearsal: PropTypes.object,
  day: PropTypes.number,
  type: PropTypes.string
};
