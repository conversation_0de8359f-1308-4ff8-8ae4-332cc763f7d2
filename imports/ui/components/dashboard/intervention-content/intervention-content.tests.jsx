import { Meteor } from "meteor/meteor";
import { assert } from "chai";
import React from "react";
import { mount } from "enzyme";
import { PureInterventionContent } from "./intervention-content.jsx";
import InterventionMessage from "../intervention-message.jsx";

if (Meteor.isClient) {
  describe("InterventionContent UI", () => {
    describe("Render", () => {
      let interventionContentComponent;
      beforeEach(() => {
        interventionContentComponent = mount(<PureInterventionContent studentGroup={{ currentClasswideSkill: {} }} />);
      });

      it("renders the primary component", () => {
        // Verify that the method does what we expected
        assert.isOk(interventionContentComponent.length, "interventionContentComponent did not render");
      });
    });

    describe("InterventionMessage", () => {
      const studentGroupWithMessageToDisplay = {
        _id: "studentGroupId",
        currentClasswideSkill: {
          message: {
            code: "99",
            dismissed: false
          }
        }
      };
      it("is rendered when the StudentGroup has a currentClasswideSkill with a message that has not been dismissed", () => {
        const interventionContentComponent = mount(
          <PureInterventionContent studentGroup={studentGroupWithMessageToDisplay} />
        );
        const messageComponent = interventionContentComponent.find(InterventionMessage);
        assert.isOk(messageComponent.length);
      });
      it("is not rendered when the StudentGroup has a currentClasswideSkill with a message that has been dismissed", () => {
        const interventionContentComponent = mount(
          <PureInterventionContent studentGroup={studentGroupWithMessageToDisplay} />
        );
        const messageComponent = interventionContentComponent.find(InterventionMessage);
        assert.isOk(messageComponent.length);
      });
    });
  });
}
