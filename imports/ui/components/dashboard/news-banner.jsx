import React, { Component } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { debounce } from "lodash";
import TooltipWrapper from "../tooltip-wrapper";
import { getZendeskUrl, isOnPrintPage } from "../../utilities";

export default class NewsBanner extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isExpanded: false,
      isOverflowing: false,
      isExpandable: false
    };
    this.formRef = React.createRef();
    this.inputRef = React.createRef();
  }

  debouncedCheckIsOverflowing = debounce(() => this.checkIsOverflowing("resize"), 250);

  componentDidMount() {
    window.addEventListener("resize", this.debouncedCheckIsOverflowing);
    this.checkIsOverflowing("mount");
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevState.isExpanded !== this.state.isExpanded || prevState.isOverflowing !== this.state.isOverflowing) {
      this.checkIsOverflowing();
    }
  }

  componentWillUnmount() {
    window.removeEventListener("resize", this.debouncedCheckIsOverflowing);
  }

  checkIsOverflowing = (initialState = null) => {
    const element = document.querySelector(`#messageContent_${this.props.message._id || "newMessage"}`);
    const isOverflowing = element?.scrollWidth > element?.clientWidth;
    if (initialState === "mount" || initialState === "resize") {
      this.setState({ isExpandable: isOverflowing });
    }
    this.setState({ isOverflowing });
  };

  expandBanner = () => {
    if (this.state.isExpandable) {
      this.setState(state => ({ isExpanded: !state.isExpanded }));
    }
  };

  openLearnMoreLinkInNewTab = e => {
    e.stopPropagation();
    const win = window.open(this.props.message.learnMoreUrl, "_blank");
    win.focus();
  };

  openZendesk = e => {
    e.stopPropagation();
    getZendeskUrl(({ action, jwt, error } = {}) => {
      if (error) {
        Alert.error(error);
      } else {
        this.inputRef.current.value = jwt;
        this.formRef.current.action = action;
        this.formRef.current.submit();
      }
    });
  };

  render() {
    const isPrinting = isOnPrintPage();
    if (isPrinting) {
      return null;
    }

    const { isExpanded, isOverflowing, isExpandable } = this.state;
    const {
      messageContent,
      messageColor,
      messageTextColor,
      buttonColor,
      buttonIconColor,
      isLearnMoreActive,
      isSupportLinkActive,
      _id
    } = this.props.message;
    const newsBannerId = `news-banner-${_id}`;

    return (
      <div className="flex-container conNewsBannerContainer">
        <div
          className={`conNewsBanner${isExpanded ? " expanded" : ""}${isExpandable ? " cursor-pointer" : ""}`}
          style={{
            background: messageColor,
            border: messageColor
          }}
          data-testid={newsBannerId}
          onClick={this.expandBanner}
        >
          <div className="conNewsBanner-Heading flex-container gap-1" style={{ color: messageTextColor }}>
            <div className="iconCallout">
              {isOverflowing || isExpanded ? (
                <i className={`fa fa-chevron-${isExpanded ? "up" : "down"}`} />
              ) : (
                <i className="fa fa-bullhorn" />
              )}
            </div>
            <p id={`messageContent_${_id || "newMessage"}`} style={{ color: messageTextColor }}>
              {messageContent}
            </p>
            {isLearnMoreActive ? (
              <button
                className={`btn btn-xs`}
                style={{ background: buttonColor, backgroundColor: buttonColor }}
                data-testid={`${newsBannerId}-learn-more-btn`}
                onClick={this.openLearnMoreLinkInNewTab}
              >
                <TooltipWrapper
                  tooltipText="Learn More"
                  text={<i className="fa fa-info-circle" style={{ color: buttonIconColor }} />}
                  customClassName={""}
                />
              </button>
            ) : null}
            {isSupportLinkActive ? (
              <button
                className={`btn btn-xs`}
                style={{ background: buttonColor, backgroundColor: buttonColor }}
                data-testid={`${newsBannerId}-support-btn`}
                onClick={this.openZendesk}
              >
                <TooltipWrapper
                  tooltipText="Reach out to Support"
                  text={<i className="fa fa-support" style={{ color: buttonIconColor }} />}
                  customClassName={""}
                />
              </button>
            ) : null}
          </div>
        </div>
        <form ref={this.formRef} action={""} method="post" target="_blank">
          <input ref={this.inputRef} type="hidden" name="jwt"></input>
        </form>
      </div>
    );
  }
}

NewsBanner.propTypes = {
  message: PropTypes.object,
  closeNewsBanner: PropTypes.func,
  dense: PropTypes.bool
};

NewsBanner.defaultProps = {
  dense: false
};
