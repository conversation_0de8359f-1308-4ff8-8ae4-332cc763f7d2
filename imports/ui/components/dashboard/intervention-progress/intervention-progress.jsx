import React, { Component } from "react";
import PropTypes from "prop-types";

import { getMidYearGoalPosition } from "/imports/api/helpers/getMidYearGoalPosition";
import SkillVideoButton from "../skill-video-button";

function renderLabel(skill, canGoToSkill) {
  if (canGoToSkill) {
    return <label className="clickable-skill">{skill.name}</label>;
  }
  return <label>{skill.name}</label>;
}

const MidYearGoalItem = () => (
  <div className="skill-item-mid-year-goal">
    <div>
      <label>
        <div>Mid-Year Goal</div>
      </label>
    </div>
  </div>
);

const EndYearGoalItem = () => (
  <div className="skill-item-mid-year-goal skill-item-end-year-goal">
    <div>
      <label>
        <div>End of Year Goal</div>
        <div className="subtitle">First grade challenge skills</div>
      </label>
    </div>
  </div>
);

// Used on Classwide Intervention Score Entry Page
export default class InterventionProgress extends Component {
  renderSkills() {
    const { skillList, grade, defaultSkillListLength = skillList.length } = this.props;
    const midYearGoalPosition = getMidYearGoalPosition(grade);
    const endYearGoalPosition = defaultSkillListLength < skillList.length ? defaultSkillListLength : null;
    const availableVideos = skillList.filter(s => s.hasVideo).map(s => s.measureNumber);
    return skillList.map((skill, i) => {
      const hasVideo = availableVideos.includes(skill.measureNumber);
      const currentSkill = skill.complete && skill.active === true;
      const completedSkill = skill.complete && !skill.active;
      const canGoToSkill = currentSkill || completedSkill;
      const skillItemClass = !skill.selected ? "skill-item" : "skill-item active";
      return (
        <React.Fragment key={skill.id}>
          {i === midYearGoalPosition && <MidYearGoalItem />}
          {i === endYearGoalPosition && <EndYearGoalItem />}
          <div
            className={skillItemClass}
            data-testid={currentSkill ? "currentSkill" : `${skillItemClass}-${i}`}
            onClick={canGoToSkill ? () => this.props.setSelectedAssessment(i, skill.id) : null}
          >
            <SkillVideoButton measureNumber={skill.measureNumber} skillName={skill.name} hasVideo={hasVideo} />
            <i className={completedSkill ? "fa fa-check" : null} />
            <i className={!skill.complete ? "fa fa-circle-o" : null} />
            <i className={currentSkill ? "fa fa-circle" : null} />
            {renderLabel(skill, canGoToSkill)}
          </div>
        </React.Fragment>
      );
    });
  }

  render() {
    return (
      <div className="d-none d-lg-block col-md-3 float-end">
        <div className="skill-progress">
          <h6 className="w9">Intervention Progress</h6>
          <div className="skill-map">
            <div className="intervention-dots">{this.renderSkills()}</div>
          </div>
        </div>
      </div>
    );
  }
}

InterventionProgress.propTypes = {
  skillList: PropTypes.array,
  defaultSkillListLength: PropTypes.number,
  setSelectedAssessment: PropTypes.func,
  grade: PropTypes.string
};
