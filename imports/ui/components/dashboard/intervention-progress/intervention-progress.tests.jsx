import { assert } from "chai";

import React from "react";
import TestUtils from "react-dom/test-utils";

import InterventionProgress from "./intervention-progress.jsx";

describe("InterventionProgress UI", () => {
  describe("Render", () => {
    let interventionProgressComponent;

    beforeEach(() => {
      interventionProgressComponent = TestUtils.renderIntoDocument(<InterventionProgress skillList={[]} />);
    });

    it("render", () => {
      // Verify that the method does what we expected
      assert.isDefined(interventionProgressComponent, "interventionProgressComponent did not render");
    });
  });
});
