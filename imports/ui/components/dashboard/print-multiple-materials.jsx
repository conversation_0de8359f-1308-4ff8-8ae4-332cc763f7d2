import { Meteor } from "meteor/meteor";
import React, { Component } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import J<PERSON>Z<PERSON> from "jszip";
import { saveAs } from "file-saver";

import { Loading } from "/imports/ui/components/loading";
import { downloadPdf } from "/imports/ui/utilities";

function requestMaterialsForStudent({
  protocolType,
  assessmentIds,
  assessmentMeasureIds,
  protocolMeasureIds,
  studentGrade,
  studentName,
  studentGroupId,
  payloadType,
  benchmarkPeriodId,
  groupName,
  materialsPart
}) {
  return new Promise((resolve, reject) => {
    Meteor.call(
      "printMaterials:triggerGenerateAndGetLink",
      {
        protocolType,
        assessmentIds,
        assessmentMeasureIds,
        protocolMeasureIds,
        studentGrade,
        studentName,
        studentGroupId,
        payloadType,
        benchmarkPeriodId,
        groupName,
        materialsPart
      },
      (error, resp) => {
        if (error) {
          return reject(error);
        }
        return resolve(resp);
      }
    );
  });
}

export default class PrintMultipleMaterials extends Component {
  constructor(props) {
    super(props);
    this.state = {
      printMaterialsStatus: "NOT_ATTEMPTED",
      printMaterialsQueue: []
    };
  }

  requestPrintMultipleMaterials = () => {
    const {
      benchmarkPeriodId,
      materialsType, // 'intervention-packet' or 'assessment'
      materialsPart = "", // empty for all parts, "instructions" or "practice"
      interventionType = "", // 'CW', 'CCC', 'GP', '' for assessment,
      materialRequestItems,
      groupName = "",
      mayTakeLong,
      studentGroupId
    } = this.props;

    this.setState({
      printMaterialsStatus: "FETCHING"
    });
    if (mayTakeLong) {
      Alert.info("Generating all materials may take up to a minute. Do not exit this web page.", {
        timeout: 120000
      });
    }

    const materialRequestItemPromises = materialRequestItems.map(
      ({ assessmentIds, measureNumbers, studentName, grade }) =>
        requestMaterialsForStudent({
          protocolType: interventionType,
          assessmentIds,
          assessmentMeasureIds: measureNumbers,
          protocolMeasureIds: [],
          studentGrade: grade,
          studentName,
          studentGroupId,
          payloadType: materialsType,
          benchmarkPeriodId,
          groupName,
          materialsPart
        })
    );

    Promise.all(materialRequestItemPromises)
      .then(resp => {
        Alert.closeAll();
        if (this.state.printMaterialsStatus === "FETCHING") {
          this.setState({
            printMaterialsStatus: "SUCCESS",
            printMaterialsQueue: resp
          });
        }
      })
      .catch(error => {
        Alert.closeAll();
        Alert.error(`${error.error}: ${error.reason}`, { timeout: 5000 });
        this.setState({
          printMaterialsStatus: "ERROR"
        });
      });
  };

  resetErrorState = () => {
    if (this.state.printMaterialsStatus === "ERROR") {
      this.resetToDefaultState();
    }
  };

  resetToDefaultState = () => {
    this.setState({
      hasPrinted: false,
      printMaterialsStatus: "NOT_ATTEMPTED"
    });
  };

  handleDownloadPdf = () => {
    const fileName = `springmath-assessments_${this.props.materialRequestItems[0].studentName}.pdf`;
    downloadPdf(this.state.printMaterialsQueue[0], fileName);
    this.resetToDefaultState();
  };

  handleDownloadZip = () => {
    const fileName = `springmath-assessments-${this.props.assessmentName}.zip`;
    const zip = new JSZip();
    this.state.printMaterialsQueue.forEach((pdfContent, index) => {
      const pdfName = `springmath-assessments_${this.props.materialRequestItems[index].studentName}.pdf`;
      zip.file(pdfName, pdfContent, { base64: true });
    });
    zip
      .generateAsync({
        type: "blob",
        compression: "DEFLATE",
        compressionOptions: {
          level: 6
        }
      })
      .then(content => {
        saveAs(content, fileName);
      });
    this.resetToDefaultState();
  };

  renderPrintAssessmentsButton = () => {
    const itemClass = "btn btn-primary btn-xs invert white-space-normal";
    if (this.state.printMaterialsStatus === "NOT_ATTEMPTED") {
      return (
        <span
          className={itemClass}
          style={{ width: "150px", fontSize: "12px", lineHeight: "1.4" }}
          onClick={this.requestPrintMultipleMaterials}
        >
          {this.props.initialText}
        </span>
      );
    }
    if (this.state.printMaterialsStatus === "SUCCESS") {
      return this.state.printMaterialsQueue.length > 1 ? this.handleDownloadZip() : this.handleDownloadPdf();
    }
    if (this.state.printMaterialsStatus === "ERROR") {
      return (
        <span className="btn btn-danger" onClick={this.resetErrorState}>
          <i className="fa fa-file-text-o fa-left" />
          Printing error, click to reset
        </span>
      );
    }
    return <p>There was a problem loading assessments</p>;
  };

  render() {
    if (this.props.loading) return <Loading />;

    return (
      <div className="m-l-5 w9 text-center print-materials-button" data-testid="printMultipleAssessmentsButton">
        {this.state.printMaterialsStatus === "FETCHING" ? (
          <Loading message={this.props.loadingText} inline />
        ) : (
          this.renderPrintAssessmentsButton()
        )}
      </div>
    );
  }
}

PrintMultipleMaterials.defaultProps = {
  mayTakeLong: false
};

PrintMultipleMaterials.propTypes = {
  assessmentName: PropTypes.string,
  benchmarkPeriodId: PropTypes.string,
  initialText: PropTypes.string,
  interventionType: PropTypes.string,
  loading: PropTypes.bool,
  loadingText: PropTypes.string,
  materialRequestItems: PropTypes.array,
  materialsType: PropTypes.string,
  materialsPart: PropTypes.string,
  printOptionKey: PropTypes.string,
  groupName: PropTypes.string,
  mayTakeLong: PropTypes.bool,
  studentGroupId: PropTypes.string
};
