import React from "react";
import PropTypes from "prop-types";

function renderCheckboxIfEnabled(enabled, studentId, addStudent, checkedStudents) {
  return (
    enabled && (
      <input
        type="checkbox"
        checked={checkedStudents.includes(studentId)}
        onChange={addStudent}
        data-student-id={studentId}
        data-testid="scheduleInterventionCheckbox"
      />
    )
  );
}

const individualInterventionStudentList = ({
  addStudent,
  recommendationData,
  schedulingEnabled,
  scheduledStudentIds,
  checkedStudents,
  screeningData
}) => {
  // props will have a map of students to measures/reasons they are in the list
  const addStudentFunc = e => {
    const isChecked = e.currentTarget.checked;
    const studentId = e.target.getAttribute("data-student-id");
    addStudent(isChecked, studentId);
  };

  const renderScreeningData = (studentRecommendationData, studentScreeningData) => {
    return studentScreeningData.length ? (
      <React.Fragment>
        <div className="row">
          <div key="-1" className="col-md-8" />
          {Object.keys(studentScreeningData[0].columnData).map((columnDataName, j) => (
            <div key={j} className="col-md-2 text-center recommendation-header">
              <small>{columnDataName}</small>
            </div>
          ))}
        </div>
        {studentScreeningData.map((reasonData, k) => (
          <div key={k} className="row">
            <div className="col-md-8 reasonTitle" title={reasonData.reasonTitle}>
              {reasonData.reasonTitle}
            </div>
            {Object.keys(reasonData.columnData).map((columnDataName, j) => {
              const className = `col-md-2 text-center ${
                reasonData.showRed && columnDataName === "Score" ? "reasonRed" : ""
              }`;
              return (
                <div key={j} className={className}>
                  {reasonData.columnData[columnDataName]}
                </div>
              );
            })}
          </div>
        ))}
      </React.Fragment>
    ) : (
      <div className="row">No screening data available</div>
    );
  };

  const recommendationDataSortedByWorstPerformance =
    recommendationData && recommendationData.length ? recommendationData : [];

  function getStudentScreeningDataByStudentId() {
    return recommendationDataSortedByWorstPerformance.reduce((dataByStudentId, rd) => {
      const { studentId } = rd;
      // eslint-disable-next-line no-param-reassign
      dataByStudentId[studentId] = screeningData
        ? Object.values(screeningData).map((measureData, i) => {
            const reasonTitle = measureData.assessmentName || `Measure ${i + 1}`;
            const studentResult = measureData.screeningResults[studentId];
            return {
              reasonTitle,
              columnData: {
                Score: (studentResult && studentResult.score) || "N/A",
                Target: measureData.cutoffTarget
              },
              showRed: !studentResult || !studentResult.score || studentResult.score < measureData.cutoffTarget
            };
          })
        : rd.recommendationReasonData;
      return dataByStudentId;
    }, {});
  }

  const studentScreeningDataByStudentId = getStudentScreeningDataByStudentId();

  return (
    <div className="student-cards">
      {recommendationDataSortedByWorstPerformance.map((rd, i) =>
        rd ? (
          <div
            key={i}
            className="individual-recommendation-card"
            data-student-grade={rd.grade}
            data-testid={`interventionCard_${rd.studentName}`}
          >
            {scheduledStudentIds && scheduledStudentIds.some(sId => sId === rd.studentId) ? (
              <i className="fa fa-check fa-2d text-success scheduled" />
            ) : (
              renderCheckboxIfEnabled(schedulingEnabled, rd.studentId, addStudentFunc, checkedStudents)
            )}
            <span className="student-name">{rd.studentName}</span>
            {i < 2 && (
              <div className="important-dog-ear">
                <i className="fa fa-star important-dog-ear-icon" />
              </div>
            )}
            <div className="container">{renderScreeningData(rd, studentScreeningDataByStudentId[rd.studentId])}</div>
          </div>
        ) : (
          <div key={i}>Student no longer available</div>
        )
      )}
    </div>
  );
};

individualInterventionStudentList.propTypes = {
  recommendationData: PropTypes.array,
  schedulingEnabled: PropTypes.bool,
  addStudent: PropTypes.func,
  scheduledStudentIds: PropTypes.array,
  checkedStudents: PropTypes.array,
  screeningData: PropTypes.object
};

export default individualInterventionStudentList;
