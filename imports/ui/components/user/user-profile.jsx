import React, { useEffect, useState } from "react";
import Al<PERSON> from "react-s-alert";
import { useHistory } from "react-router-dom";
import { Collapse } from "react-bootstrap";
import { difference, startsWith, uniq } from "lodash";
import PropTypes from "prop-types";

import {
  getMeteorUserSync,
  passwordRegex,
  passwordSpecMsg,
  useDebouncedEffect
} from "/imports/api/utilities/utilities";

import PageHeader from "../page-header";
import Loading from "../loading";
import MfaModal from "../../pages/authentication/mfa-modal";

function UserProfile(props) {
  const [loading, setLoading] = useState(true);
  const [userData, setUserData] = useState(null);
  const [openCollapsed, setOpenCollapsed] = useState({});
  const [openCollapsedOrgs, setOpenCollapsedOrgs] = useState(false);
  const [oldPassword, setOldPassword] = useState("");
  const [password, setPassword] = useState("");
  const [passwordConfirm, setPasswordConfirm] = useState("");
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [isPasswordValid, setIsPasswordValid] = useState(true);
  const [doesPasswordMatch, setDoesPasswordMatch] = useState(false);
  const [isPasswordChangeRequired, setIsPasswordChangeRequired] = useState(
    localStorage.getItem("isPasswordChangeRequired") === "true" || false
  );
  const [shouldDisplayMfaModal, setShouldDisplayMfaModal] = useState(false);
  const history = useHistory();

  const getUserData = () => {
    setLoading(true);
    Meteor.call("user:getUserData", props.userId, (err, resp) => {
      if (!err) {
        setUserData(resp);
      } else {
        Alert.error("Error fetching user data");
      }
      setLoading(false);
    });
  };

  useEffect(() => {
    getUserData();
  }, []);

  const toggleSchoolPermissionsRow = () => {
    const siteNames = Object.keys(userData.studentGroupsBySitesPermissions);
    const toggledState = !Object.values(openCollapsed)[0];
    const missingSites = difference(siteNames, Object.keys(openCollapsed)).reduce((a, site) => {
      // eslint-disable-next-line no-param-reassign
      a[site] = toggledState;
      return a;
    }, {});
    const newState = Object.keys(openCollapsed).reduce((a, key) => {
      // eslint-disable-next-line no-param-reassign
      a[key] = toggledState;
      return a;
    }, missingSites);
    setOpenCollapsed(newState);
  };

  useDebouncedEffect(
    () => {
      if (!password.length && !passwordConfirm.length) {
        setIsPasswordValid(true);
        setDoesPasswordMatch(true);
      }

      if (password.length) {
        const isValid = !!passwordRegex.test(password);
        setIsPasswordValid(isValid);
      }

      if (password.length && passwordConfirm.length) {
        return setDoesPasswordMatch(password === passwordConfirm);
      }
      return setDoesPasswordMatch(true);
    },
    [password, passwordConfirm],
    1000
  );

  const changePassword = () => {
    if (password !== passwordConfirm) {
      setDoesPasswordMatch(false);
      return;
    }

    if (!passwordRegex.test(password)) {
      setIsPasswordValid(false);
      return;
    }
    setIsPasswordValid(true);
    Meteor.call("changeUserPassword", oldPassword, password, err => {
      if (err) {
        return Alert.error(
          err.reason === "Incorrect password" ? "Incorrect old password" : err.reason || "Failed to change password"
        );
      }
      Meteor.call("user:onPasswordChange");
      Meteor.call("user:sendChangedPasswordEmail");
      setOldPassword("");
      setPassword("");
      setPasswordConfirm("");
      localStorage.removeItem("isPasswordChangeRequired");
      setIsPasswordChangeRequired(false);
      Alert.success("Successfully updated password.");
      return history.push("/logout");
    });
  };

  const disableMFA = () => {
    Meteor.call("user:disableMFA", props.userId, error => {
      if (error) {
        Alert.error(error.reason);
      } else {
        Alert.success("MFA disabled successfully");
        getUserData();
      }
    });
  };

  const displaySetupMfaModal = () => {
    setShouldDisplayMfaModal(true);
  };

  const closeSetupMfaModal = () => {
    setShouldDisplayMfaModal(false);
    getUserData();
  };

  const renderProfileContent = () => {
    const {
      email,
      teacherId,
      roles,
      memberSince,
      loginData,
      lastPasswordChange,
      studentGroupsBySitesPermissions,
      organizationNames,
      isSSOOnlyOrg,
      isDataAdmin,
      isMFAEnabled
    } = userData;
    const teacherOrCoachRoles = roles.filter(r => startsWith(r, "Teacher - ") || startsWith(r, "Coach - "));
    const isChangePasswordButtonDisabled =
      !password.length || !passwordConfirm.length || !oldPassword.length || !doesPasswordMatch || !isPasswordValid;
    let lastPasswordChangeString = lastPasswordChange && new Date(lastPasswordChange).toLocaleString();
    if (!lastPasswordChangeString) {
      lastPasswordChangeString = loginData.lastPasswordChange
        ? new Date(loginData.lastPasswordChange).toLocaleString()
        : "N/A";
    }
    const isViewingAsDifferentUser = userData._id !== Meteor.userId();

    return (
      <div className={props.isModal ? "" : "col-lg-8 offset-lg-2"}>
        <div className="card">
          <div className="card-body">
            <div className="row">
              <div className="col-sm-3">
                <p className="m-0">Organization{organizationNames.length ? "s" : ""}</p>
              </div>
              <div className={`col-sm-9${organizationNames.length > 1 ? " cursor-pointer" : ""}`}>
                {organizationNames.length > 1 ? (
                  <div>
                    <p className="m-0 w6" onClick={() => setOpenCollapsedOrgs(!openCollapsedOrgs)}>
                      <i className={`fa fa-chevron-${openCollapsedOrgs ? "down" : "up"}`} /> Assigned Organizations
                    </p>
                    <Collapse in={openCollapsedOrgs}>
                      <ul className="m-0 list-unstyled">
                        {organizationNames.sort().map((orgname, index) => (
                          <li key={index}>
                            <p className="mb-0 m-l-15 w4">{orgname}</p>
                          </li>
                        ))}
                      </ul>
                    </Collapse>
                  </div>
                ) : (
                  organizationNames[0]
                )}
              </div>
            </div>

            <hr />
            <div className="row">
              <div className="col-sm-3">
                <p className="m-0">Email</p>
              </div>
              <div className="col-sm-9">{email}</div>
            </div>

            {teacherId ? (
              <React.Fragment>
                <hr />
                <div className="row">
                  <div className="col-sm-3">
                    <p className="m-0">Teacher ID</p>
                  </div>
                  <div className="col-sm-9">{teacherId}</div>
                </div>
              </React.Fragment>
            ) : null}

            <hr />
            <div className="row">
              <div className="col-sm-3">
                <p className="m-0">Role(s)</p>
              </div>
              <div className="col-sm-9">
                <ul className="m-0 list-unstyled">
                  {uniq(roles)
                    .sort()
                    .map((role, index) => (
                      <li key={index} className="w4">
                        <div className="m-0 text-grey">{role}</div>
                      </li>
                    ))}
                </ul>
              </div>
            </div>

            {teacherOrCoachRoles.length ? (
              <React.Fragment>
                <hr />
                <div className="row">
                  <div className="col-sm-3 cursor-pointer" onClick={toggleSchoolPermissionsRow}>
                    <p className="m-0">Schools/Classes Permissions</p>
                  </div>
                  <div className="col-sm-9">
                    {Object.entries(studentGroupsBySitesPermissions).map(([siteName, groupNames]) => {
                      return (
                        <div key={siteName}>
                          <div
                            className="m-0 w5 cursor-pointer"
                            onClick={() => setOpenCollapsed({ ...openCollapsed, [siteName]: !openCollapsed[siteName] })}
                          >
                            <i className={`fa fa-chevron-${openCollapsed[siteName] ? "down" : "up"}`} /> {siteName}
                          </div>
                          {groupNames.flat(1).length ? (
                            <Collapse in={openCollapsed[siteName]}>
                              <ul className="m-0 list-unstyled">
                                {groupNames
                                  .flat(1)
                                  .sort()
                                  .map((groupName, index) => (
                                    <li key={index}>
                                      <p className="mb-0 m-l-15 w4">{groupName}</p>
                                    </li>
                                  ))}
                              </ul>
                            </Collapse>
                          ) : null}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </React.Fragment>
            ) : null}

            <hr />
            <div className="row">
              <div className="col-sm-3">
                <p className="mb-0">Member Since</p>
              </div>
              <div className="col-sm-9">
                <div className="m-0 w4">{memberSince}</div>
              </div>
            </div>

            <hr />
            <div className="row">
              <div className="col-sm-3">
                <p className="mb-0">Login Count</p>
              </div>
              <div className="col-sm-9">
                <div className="m-0 w4">{loginData.loginCount || "N/A"}</div>
              </div>
            </div>

            <hr />
            <div className="row">
              <div className="col-sm-3">
                <p className="mb-0">Last Login</p>
              </div>
              <div className="col-sm-9">
                <div className="m-0 w4">
                  {loginData.lastLogin ? new Date(loginData.lastLogin).toLocaleString() : "N/A"}
                </div>
              </div>
            </div>

            <hr />
            <div className="row">
              <div className="col-sm-3">
                <p className="m-0">Last Failed Login</p>
              </div>
              <div className="col-sm-9">
                <div className="m-0">
                  {loginData.lastFailedLogin ? new Date(loginData.lastFailedLogin).toLocaleString() : "N/A"}
                </div>
              </div>
            </div>

            <hr />
            <div className="row">
              <div className="col-sm-3">
                <p className="m-0">Last Password Change</p>
              </div>
              <div className="col-sm-9">
                <div className="m-0">{lastPasswordChangeString}</div>
              </div>
            </div>

            {(isDataAdmin || !isSSOOnlyOrg) && !props.isModal ? (
              <React.Fragment>
                <hr />
                <div className="row">
                  <div className="col-sm-3">
                    <p className="m-0">Change Password</p>
                  </div>
                  <div className="col-sm-9">
                    {isPasswordChangeRequired && <div className="alert alert-danger">Password change is required</div>}
                    <div className="p-0 col-sm-5 d-grid gap-2">
                      <input
                        className="form-control"
                        type={"password"}
                        placeholder="Old password"
                        onChange={e => setOldPassword(e.target.value)}
                        value={oldPassword}
                      />
                      <div className="input-group">
                        <input
                          className="form-control"
                          type={isPasswordVisible ? "text" : "password"}
                          placeholder="New password"
                          onChange={e => setPassword(e.target.value)}
                          value={password}
                          aria-describedby="basic-addon2"
                        />
                        <i
                          className={`input-group-text fa ${isPasswordVisible ? "fa-eye" : "fa-eye-slash"}`}
                          onClick={() => setIsPasswordVisible(!isPasswordVisible)}
                          id="basic-addon2"
                        />
                      </div>
                      <input
                        className="form-control"
                        type={isPasswordVisible ? "text" : "password"}
                        placeholder="Confirm new password"
                        onChange={e => setPasswordConfirm(e.target.value)}
                        value={passwordConfirm}
                      />
                      <button
                        className={`btn btn-success${isChangePasswordButtonDisabled ? " cursor-not-allowed" : ""}`}
                        onClick={changePassword}
                        disabled={password !== passwordConfirm || isChangePasswordButtonDisabled}
                      >
                        Save
                      </button>
                    </div>
                    {!doesPasswordMatch && password.length && passwordConfirm.length ? (
                      <p className="m-0 warningMessage">Password does not match.</p>
                    ) : null}
                    {!isPasswordValid && password.length ? (
                      <p className="m-0 warningMessage" dangerouslySetInnerHTML={{ __html: passwordSpecMsg }} />
                    ) : null}
                  </div>
                </div>
              </React.Fragment>
            ) : null}

            <React.Fragment>
              <hr />
              <div className="row">
                <div className="col-sm-3">
                  <p className="m-0">Is MFA linked</p>
                </div>
                <div className="col-sm-9">
                  <div className="m-0">
                    {isMFAEnabled ? "Yes" : "No"}
                    {isMFAEnabled ? (
                      <button className="btn btn-danger m-l-15" onClick={disableMFA}>
                        Unlink
                      </button>
                    ) : (
                      <button
                        className="btn btn-success m-l-15"
                        onClick={displaySetupMfaModal}
                        disabled={isViewingAsDifferentUser}
                      >
                        Link
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </React.Fragment>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="conFullScreen">
      <PageHeader title={userData?.fullName || "Loading..."} />
      <div className={props.isModal ? "" : "container"}>
        {loading || !getMeteorUserSync() ? <Loading /> : renderProfileContent()}
      </div>
      {shouldDisplayMfaModal && (
        <MfaModal showModal={shouldDisplayMfaModal} closeModal={closeSetupMfaModal} isMFAEnabled={false} />
      )}
    </div>
  );
}

UserProfile.propTypes = {
  userId: PropTypes.string,
  isModal: PropTypes.bool
};

UserProfile.defaultProps = {
  isModal: true
};

export default UserProfile;
