import React from "react";
import * as ReactDOM from "react-dom";
import { Overlay } from "react-bootstrap";
import PropTypes from "prop-types";

const DISTANCE_FROM_BOTTOM = 50; // overlay will be hidden if user scrolls within this value to the element bottom

export default class ScrollIndicator extends React.Component {
  constructor(props) {
    super(props);
    this.overlayAnchor = React.createRef();
    this.state = {
      showIndicator: false,
      uniqKey: ""
    };
  }

  componentDidMount() {
    this.handleScroll();
  }

  componentDidUpdate(prevProps, prevState) {
    if (!this.props.wait && this.props.uniqKey !== prevState.uniqKey) {
      this.setState({ uniqKey: this.props.uniqKey });
      this.handleScroll();
    }
  }

  handleScroll = () => {
    const elem = this.getTarget();
    if (Math.abs(elem.scrollHeight - (elem.offsetHeight + elem.scrollTop)) < DISTANCE_FROM_BOTTOM) {
      // scrolled to the container bottom
      this.setState({ showIndicator: false });
    } else if (!this.state.showIndicator) {
      this.setState({ showIndicator: true });
    }
  };

  getTarget = () => {
    // eslint-disable-next-line react/no-find-dom-node
    let target = ReactDOM.findDOMNode(this.overlayAnchor.current);
    if (target && this.props.targetSelector) {
      target = target.querySelector(this.props.targetSelector) || target;
    }
    return target;
  };

  render() {
    return (
      <React.Fragment>
        <div className="scrollableContainer" ref={this.overlayAnchor} onScroll={this.handleScroll}>
          {this.props.children}
        </div>
        {this.state.showIndicator ? (
          <Overlay show placement="bottom" container={this.props.container} target={this.getTarget}>
            {this.props.indicatorComponent}
          </Overlay>
        ) : null}
      </React.Fragment>
    );
  }
}

ScrollIndicator.propTypes = {
  targetSelector: PropTypes.string,
  children: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.node), PropTypes.node]),
  indicatorComponent: PropTypes.object.isRequired,
  container: PropTypes.object.isRequired,
  wait: PropTypes.bool,
  uniqKey: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
};
