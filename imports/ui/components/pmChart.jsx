import React, { Component } from "react";
import PropTypes from "prop-types";
import Highcharts from "highcharts/highstock";
import { inRange } from "lodash";
import {
  getXAxisDateTimeLabelFormats,
  getPlotLine,
  getTooltipTimeLabelFormats,
  dayInMilliseconds,
  weekInMilliseconds,
  getTimeRange
} from "../utilities";

export default class ProgressMonitoringChart extends Component {
  // When the DOM is ready, create the chart.
  componentDidMount() {
    this.updateChart();
  }

  // Update the chart if the component is updated
  componentDidUpdate() {
    this.updateChart();
  }

  //  Destroy chart before unmount.
  componentWillUnmount() {
    this.chart.destroy();
  }

  updateChart() {
    const { options } = this.props;
    const scoresData = [];
    this.props.scores.scores.forEach((score, index) => {
      if (this.props.scores.hasDrillDownScore && index === 0) {
        scoresData.push({
          x: this.props.scores.dates[index],
          y: score,
          name: `Drill-down - ${new Date(this.props.scores.dates[index]).toISOString().slice(0, 10)}`
        });
      } else {
        scoresData.push({ x: this.props.scores.dates[index], y: score });
      }
    });
    let firstScoreTimestamp = new Date().valueOf();
    if (scoresData.length) {
      [{ x: firstScoreTimestamp }] = scoresData;
    }
    const { instructionalTarget, masteryTarget } = this.props.scores;

    const dates = scoresData.map(score => score.x);
    dates.push(options.timeRange.start);
    dates.push(options.timeRange.end);
    const updatedTimeRange = getTimeRange(dates);

    // Fix blank graph edge case when hidden target scores are out of time range
    if (!inRange(firstScoreTimestamp, updatedTimeRange.start, updatedTimeRange.end + 1)) {
      firstScoreTimestamp = updatedTimeRange.start;
    }

    this.chart = new Highcharts[this.props.type || "Chart"](this.props.chartId, {
      credits: {
        enabled: false
      },
      chart: {
        height: options.height || "",
        type: options.chartType || "line",
        marginRight: options.marginRight || 20,
        marginTop: options.marginTop || 20
      },
      accessibility: {
        enabled: false
      },
      title: {
        text: options.title || "",
        x: -50
      },
      plotOptions: {
        line: {
          color: "#2693D5",
          lineWidth: 5,
          marker: {
            radius: 8
          }
        }
      },
      legend: {
        enabled: false
      },
      series: [
        {
          data: scoresData,
          animation: {
            duration: 0
          },
          name: this.props.scores.skillName,
          marker: {
            enabled: true,
            radius: 8
          }
        },

        // Include hidden data points for the target scores so that the Y-Axis will auto scale
        {
          name: "Target",
          type: "scatter",
          marker: {
            enabled: false
          },
          data: [
            [firstScoreTimestamp, instructionalTarget],
            [firstScoreTimestamp, masteryTarget]
          ],
          enableMouseTracking: false
        }
      ],
      tooltip: {
        dateTimeLabelFormats: getTooltipTimeLabelFormats(),
        split: true
      },
      xAxis: {
        type: "datetime",
        dateTimeLabelFormats: getXAxisDateTimeLabelFormats(),
        title: {
          text: options.xAxisTitle || ""
        },
        ordinal: false,
        minTickInterval: dayInMilliseconds,
        tickPixelInterval: 150,
        tickLength: 0,
        min: updatedTimeRange.start,
        max: updatedTimeRange.end,
        minRange: weekInMilliseconds
      },
      yAxis: {
        title: {
          text: options.yAxisTitle || ""
        },
        plotLines: [getPlotLine(masteryTarget, "Mastery"), getPlotLine(instructionalTarget, "Instructional")]
      }
    });
  }

  // Create the div which the chart will be rendered to.
  render() {
    return (
      <div
        id={this.props.chartId}
        className="chart"
        data-mastery-target={this.props.scores.masteryTarget}
        data-instructional-target={this.props.scores.instructionalTarget}
        data-testid={this.props.skillType === "PMChart" ? "intervention-skill-graph" : "goal-skill-graph"}
      />
    );
  }
}

ProgressMonitoringChart.propTypes = {
  type: PropTypes.string,
  scores: PropTypes.object,
  chartId: PropTypes.string,
  options: PropTypes.object,
  skillType: PropTypes.string
};
