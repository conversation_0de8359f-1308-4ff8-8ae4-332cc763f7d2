import React, { Component, createRef } from "react";
import { Tooltip } from "bootstrap";
import PropTypes from "prop-types";

class TooltipWrapper extends Component {
  constructor(props) {
    super(props);
    this.textRef = createRef();
    this.tooltip = null;
  }

  componentDidMount() {
    const { isClickTriggerEnabled, offset } = this.props;
    this.tooltip = new Tooltip(this.textRef.current, {
      customClass: "tooltip-wrapper",
      trigger: `hover focus${isClickTriggerEnabled ? " click" : ""}`,
      ...(offset ? { offset } : {})
    });

    document.addEventListener("click", this.handleOutsideClick, true);
  }

  componentWillUnmount() {
    if (this.tooltip) {
      this.tooltip.dispose();
    }

    document.removeEventListener("click", this.handleOutsideClick, true);
  }

  handleOutsideClick = event => {
    if (
      this.tooltip?.tip &&
      !this.tooltip?.tip.contains(event.target) &&
      this.textRef.current &&
      !this.textRef.current.contains(event.target)
    ) {
      this.tooltip.hide();
    }
  };

  render() {
    const { text, tooltipText, placement, customClassName } = this.props;

    return (
      <div
        ref={this.textRef}
        className={customClassName}
        data-bs-toggle="tooltip"
        data-bs-placement={placement || "top"}
        title={tooltipText || text}
      >
        {text}
      </div>
    );
  }
}

TooltipWrapper.propTypes = {
  text: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  tooltipText: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  placement: PropTypes.string,
  offset: PropTypes.array,
  maxWidth: PropTypes.string,
  customClassName: PropTypes.string,
  isClickTriggerEnabled: PropTypes.bool
};

TooltipWrapper.defaultProps = {
  customClassName: "text-overflow",
  isClickTriggerEnabled: true
};

export default TooltipWrapper;
