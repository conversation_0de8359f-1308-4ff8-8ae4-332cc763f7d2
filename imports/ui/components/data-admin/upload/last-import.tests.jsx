import React from "react";
import MockDate from "mockdate";
import { assert } from "chai";

import { LastImportComponent } from "./last-import.jsx";
import { renderWithRouter } from "../../../../../tests/helpers/testUtils";

jest.mock("../../../../api/utilities/utilities", () => ({
  ...jest.requireActual("../../../../api/utilities/utilities"),
  getCurrentSchoolYear: jest.fn(() => 2019),
}));

describe("LastImport UI", () => {
  beforeAll(() => {
    MockDate.set("2018-12-20");
  });
  afterAll(() => {
    jest.restoreAllMocks();
    MockDate.reset();
  });
  describe("Render", () => {
    let lastImportContentComponent;
    beforeEach(() => {
      lastImportContentComponent = renderWithRouter(
        <LastImportComponent lastRosterImport={{ _id: "lastRosterImportId" }} />
      );
    });
    it("render", () => {
      assert.isDefined(lastImportContentComponent, "lastImportContentComponent did not render");
    });
  });
});
