import { Meteor } from "meteor/meteor";
import React, { Component } from "react";
import PropTypes from "prop-types";
import moment from "moment";
import { Link } from "react-router-dom";
import isEmpty from "lodash/isEmpty";
import get from "lodash/get";
import { Loading } from "../../loading.jsx";
import { getFormattedStudentGroupErrors } from "../../../pages/data-admin/upload/file-upload-utils";

export class LastImportComponent extends Component {
  state = {
    loading: false,
    siteCount: 0,
    studentGroupCount: 0,
    futureUserCount: 0,
    studentCount: 0
  };

  componentDidMount() {
    const { orgid, lastRosterImport } = this.props;
    this.setState({ loading: true });
    Meteor.call(
      "RosterImports:getLastImportSummaryData",
      { orgid, rosterImportId: lastRosterImport._id },
      (err, resp) => {
        if (!err) {
          this.setState({ ...resp });
        }
        this.setState({ loading: false });
      }
    );
  }

  renderStatistics = statusText => {
    const { lastRosterImport } = this.props;
    const { siteCount, futureUserCount, studentGroupCount, studentCount, loading } = this.state;
    if (lastRosterImport) {
      const prettyDate = moment(lastRosterImport.started.date).format("dddd, MMMM Do YYYY");
      const recordsProposed = lastRosterImport.itemCount;
      const lis = [
        <div key="1" className="animated pulse">
          <i className="fa fa-plus text-success" /> {siteCount} Sites added/updated
        </div>,
        <div key="2" className="animated pulse">
          <i className="fa fa-plus text-success" /> {futureUserCount} Teachers added/updated
        </div>,
        <div key="3" className="animated pulse">
          <i className="fa fa-plus text-success" /> {studentGroupCount} Classes added/updated
        </div>,
        <div key="4" className="animated pulse">
          <i className="fa fa-plus text-success" /> {studentCount} Students added/updated
        </div>
      ];
      return (
        <ul>
          <li>{statusText}</li>
          <li>{prettyDate}</li>
          <li>
            <i className="fa fa-chevron-up" /> {recordsProposed} Data Rows
          </li>
          {loading ? (
            <li>
              <Loading inline />
            </li>
          ) : (
            lis.map((li, index) => <li key={index}>{li}</li>)
          )}
        </ul>
      );
    }
    return null;
  };

  toggleFileUploadError = () => {
    const { orgid, lastRosterImport } = this.props;
    const { isHidden = false } = this.props.lastRosterImport.error;
    Meteor.call("toggleRosterImportsError", { rosterImportId: lastRosterImport._id, orgid, isHidden }, error => {
      if (error) {
        console.log(`toggleRosterImportsError error: ${error}`);
      }
    });
  };

  getErrorDetails = () => {
    const lastImportError = this.props.lastRosterImport.error;
    let displayErrors =
      lastImportError && Object.keys(lastImportError).length
        ? lastImportError.clientMessage || lastImportError.errorMessage
        : "Failed for unknown reason";
    const hasGroupErrors = !isEmpty(get(this.props.lastRosterImport, "studentGroupErrors"));
    if (hasGroupErrors) {
      displayErrors += "\n\n";
      displayErrors += getFormattedStudentGroupErrors(this.props.lastRosterImport.studentGroupErrors);
    }
    const hasUniqueEmailErrors = !isEmpty(get(this.props.lastRosterImport, "uniqueEmailErrors"));

    if (hasUniqueEmailErrors) {
      displayErrors += "\n\n";
      displayErrors += this.props.lastRosterImport.uniqueEmailErrors.join("\n");
    }

    return displayErrors;
  };

  render() {
    const { props } = this;
    const status = props.lastRosterImport && props.lastRosterImport.status;
    if (status) {
      switch (status.toLowerCase()) {
        case "completed":
          return <div>{this.renderStatistics("Complete")}</div>;
        case "pending":
          return <div>{this.renderStatistics(<Loading inline message="Pending" cssClasses="import-pending" />)}</div>;
        case "error":
          return <p className="text-center text-danger">Error</p>;
        case "upload failed": {
          const errorDetails = this.getErrorDetails();
          const newLinesCount = errorDetails.match(/\n/g) ? errorDetails.match(/\n/g).length : 0;
          const rows = Math.min(newLinesCount + 5, 40);
          const { isHidden } = props.lastRosterImport.error || false;
          return (
            <div className="small m-t-10">
              <div className="d-grid">
                <button className="btn btn-danger btn-xs" type="button" onClick={this.toggleFileUploadError}>
                  Upload Failed <i className={`fa fa-caret-${isHidden ? "up" : "down"}`} />
                </button>
              </div>
              <textarea
                disabled
                className={`form-control alert alert-danger m-0 ${isHidden ? "collapse" : ""}`}
                rows={rows}
                value={errorDetails}
              />
            </div>
          );
        }
        case "processing":
          return (
            <div>{this.renderStatistics(<Loading inline message="Processing" cssClasses="import-processing" />)}</div>
          );
        case "error triggering processing":
          return <span>error triggering processing - See Administrator</span>;
        default:
          return null;
      }
    }
    return (
      <div>
        <ul>
          <li>{`Your organization doesn't have any data uploaded yet.`}</li>
          <li>
            <Link to="upload" className="btn btn-success">
              UPLOAD DATA <i className="fa fa-upload" />
            </Link>
          </li>
        </ul>
      </div>
    );
  }
}

LastImportComponent.propTypes = {
  orgid: PropTypes.string,
  loading: PropTypes.bool,
  lastRosterImport: PropTypes.object,
  siteCount: PropTypes.number,
  futureUserCount: PropTypes.number,
  studentGroupCount: PropTypes.number,
  studentCount: PropTypes.number
};

export default LastImportComponent;
