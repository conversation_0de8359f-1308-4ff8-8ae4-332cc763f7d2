import React from "react";
import PropTypes from "prop-types";
import { withTracker } from "meteor/react-meteor-data";
import { Link } from "react-router-dom";
import { ListGroup } from "react-bootstrap";
import { Grades } from "/imports/api/grades/grades";

const isActive = (href, path) => (path.includes(href) ? " active" : "");

const gradesSideNav = props => {
  const grades = props.grades.map(grade => ({
    href: `/sampleAssessments/${grade.display}`,
    text: `Grade ${grade.display}`
  }));

  return (
    <aside className="side-nav">
      <ListGroup className="student-group-list">
        {grades.map((gi, index) => (
          <Link className={`${isActive(gi.href, props.currentPath)} list-group-item`} to={gi.href} key={index}>
            {gi.text}
          </Link>
        ))}
      </ListGroup>
    </aside>
  );
};

gradesSideNav.propTypes = {
  currentPath: PropTypes.string,
  grades: PropTypes.array
};

/** ****************************************************************
 // Data Container
 ***************************************************************** */
export default withTracker(() => {
  const gradesHandle = Meteor.subscribe("Grades");
  let grades = [];
  if (gradesHandle.ready()) {
    grades = Grades.find({ _id: { $ne: "HS" } }, { sort: { sortorder: 1 }, fields: { display: 1 } }).fetch();
  }

  return {
    grades
  };
})(gradesSideNav);
