import React, { Component } from "react";
import PropTypes from "prop-types";

import Highcharts from "highcharts/highstock";

export default class AdminProgressCircleChart extends Component {
  // When the DOM is ready, create the chart.
  componentDidMount() {
    this.updateChart();
  }

  // Update the chart if the component is updated
  componentDidUpdate() {
    this.updateChart();
  }

  shouldComponentUpdate(nextProps) {
    return nextProps.shown !== this.props.shown || nextProps.percentComplete !== this.props.percentComplete;
  }

  //  Destroy chart before unmount.
  componentWillUnmount() {
    this.chart.destroy();
  }

  getChartText() {
    const percentText = this.props.percentText || "Completed";
    const text =
      this.props.percentComplete !== null && this.props.percentComplete >= 0
        ? `<span class="percent">${this.props.percentComplete}%</span> ${percentText}`
        : `<span class="percent">N/A</span>`;
    return `<div class="circleChartTitle">${text}</div>`;
  }

  updateChart() {
    const chartFontSize = this.props.isPrinting ? "12px" : "14px";
    this.chart = new Highcharts.Chart(this.props.chartId, {
      title: {
        text: this.getChartText(),
        useHTML: true,
        verticalAlign: "middle",
        style: { fontSize: chartFontSize, textAlign: "center" },
        y: 5
      },
      subtitle: {
        text: `Grade ${this.props.grade}`,
        verticalAlign: "bottom",
        style: {
          fontSize: chartFontSize,
          fontWeight: "bold",
          textAlign: "center"
        },
        y: 10
      },
      chart: {
        type: "pie",
        backgroundColor: "transparent",
        margin: [5, -8, 0, -8],
        spacingTop: 0,
        spacingBottom: 0,
        spacingLeft: 0,
        spacingRight: 0
      },
      accessibility: {
        enabled: false
      },
      plotOptions: {
        pie: {
          center: ["50%", "40%"],
          dataLabels: { enabled: false },
          size: "100%",
          shadow: false
        },
        series: {
          states: {
            hover: {
              enabled: false,
              halo: {
                size: 0
              }
            }
          }
        }
      },
      series: [
        {
          borderColor: "transparent",
          data: [
            {
              y: this.props.percentComplete,
              color: "#005bb5",
              tooltipDisabled: true
            },
            {
              y: 100 - this.props.percentComplete,
              color: "#ededed",
              tooltipDisabled: true
            }
          ],
          size: "100%",
          innerSize: "85%"
        }
      ],
      tooltip: { enabled: false },
      credits: { enabled: false },
      exporting: { enabled: false }
    });
  }
  // Create the div which the chart will be rendered to.

  render() {
    return <div id={this.props.chartId} className="circle-chart" />;
  }
}

AdminProgressCircleChart.propTypes = {
  chartId: PropTypes.string,
  grade: PropTypes.node,
  percentComplete: PropTypes.node,
  percentText: PropTypes.string,
  shown: PropTypes.bool,
  isPrinting: PropTypes.bool
};
