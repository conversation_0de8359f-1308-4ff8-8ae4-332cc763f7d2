import React, { Component } from "react";
import PropTypes from "prop-types";
import { get } from "lodash";

import QuickInfoContainer from "./quick-info-container.jsx";

import { getCurrentEnrolledGrade } from "/imports/api/students/utils";
import * as utils from "/imports/api/utilities/utilities";
import { AppDataContext } from "../../routing/AppDataContext";
import { getMeteorUserSync } from "/imports/api/utilities/utilities";

export default class ClasswideQuickInfoSection extends Component {
  static contextType = AppDataContext;

  constructor(props) {
    super(props);

    this.state = {
      expanded: false
    };
  }

  toggle = () => {
    this.setState({ expanded: !this.state.expanded });
  };

  getQuickInfoData = (positiveInfoData, negativeInfoData) => {
    const groupNamesWithNegativeInfoData = negativeInfoData.map(n => n.name);
    const filteredPositiveInfoData = positiveInfoData.filter(p => !groupNamesWithNegativeInfoData.includes(p.name));
    return [...negativeInfoData, ...filteredPositiveInfoData];
  };

  render() {
    if (!this.props.data || !this.props.data.length) return null;

    const classwideStudentGroupIds = this.props.studentGroups.reduce((a, c) => {
      if (c.currentClasswideSkill) {
        a.push(c._id);
      }
      return a;
    }, []);

    const positiveInfoData = [];
    const negativeInfoData = [];
    const { schoolYear } = this.context;
    const isPriorYear = schoolYear && schoolYear !== utils.getCurrentSchoolYear(getMeteorUserSync());

    const classwideInfoMessages = {
      goodProgress: averageWeeksPerSkill =>
        `Progress is fantastic. This class is progressing at ${averageWeeksPerSkill} weeks per skill. We'd recommend asking this teacher what's working and if they have any tips for others!`,
      goodInterventionConsistency:
        "This class has excellent intervention consistency! This means that they have been entering weekly progress monitoring scores for all of their students, great job.",
      stuckOnSkill: "This class has been on one skill for over 4 weeks. It might be worth checking in with them.",
      lowInterventionConsistency:
        "This class has low intervention consistency. This means scores aren't being entered in SpringMath each week. We would recommend checking with them to make sure the scores can be entered.",
      scoresNotImproving:
        "The scores for most of the students in this class are not improving. It might be a good idea to check in with them."
    };

    this.props.data
      .filter(groupStats => {
        const currentStudentGroup = this.props.studentGroups.find(sg => sg._id === groupStats.studentGroupId);
        const currentClasswideSkill = get(currentStudentGroup, "currentStudentGroup.currentClasswideSkill");
        const isInterventionComplete =
          groupStats.numberOfSkillsPracticed === groupStats.numberOfSkillsInClasswideTree ||
          (isPriorYear && currentClasswideSkill && !currentClasswideSkill.assessmentId);
        return classwideStudentGroupIds.includes(groupStats.studentGroupId) && !isInterventionComplete;
      })
      .forEach(stats => {
        const messageInitialState = {
          name: stats.studentGroupName,
          href: `/${this.props.orgid}/site/${this.props.siteId}/student-groups/${stats.studentGroupId}`
        };
        const isLingering = stats.numberOfWeeksPracticingCurrentSkill && stats.numberOfWeeksPracticingCurrentSkill > 4;
        if (!isLingering && stats.averageWeeksPerSkill && stats.averageWeeksPerSkill <= 2) {
          positiveInfoData.push({
            ...messageInitialState,
            message: classwideInfoMessages.goodProgress(stats.averageWeeksPerSkill)
          });
        }
        // build lingering 4 weeks or more on a skill messages
        if (isLingering) {
          negativeInfoData.push({
            ...messageInitialState,
            message: classwideInfoMessages.stuckOnSkill,
            warning: true
          });
        }
        // build positive intervention consistency messages
        if (stats.interventionConsistency && stats.interventionConsistency >= 95) {
          positiveInfoData.push({
            ...messageInitialState,
            message: classwideInfoMessages.goodInterventionConsistency
          });
        }
        // build negative intervention consistency messages
        // make sure we actually have some classwide intervention history first
        const curStudentGroup = this.props.studentGroups.find(sg => sg._id === stats.studentGroupId);
        const curStudentGroupClasswideHistory =
          curStudentGroup && curStudentGroup.history && curStudentGroup.history.some(h => h.type === "classwide");
        if (
          typeof stats.interventionConsistency === "number" &&
          stats.interventionConsistency < 80 &&
          curStudentGroupClasswideHistory
        ) {
          negativeInfoData.push({
            ...messageInitialState,
            message: classwideInfoMessages.lowInterventionConsistency,
            warning: true
          });
        }
        // 75% of students have scores lower after 2 week than at the beggining of Classwide Intervention SPRIN-925
        if (stats.worseScoresRatio && stats.worseScoresRatio > 75) {
          negativeInfoData.push({
            ...messageInitialState,
            message: classwideInfoMessages.scoresNotImproving,
            warning: true
          });
        }
      });

    if (!positiveInfoData.length && !negativeInfoData.length) {
      return null;
    }
    const quickInfoData = this.getQuickInfoData(positiveInfoData, negativeInfoData);
    if (!quickInfoData.length) {
      return null;
    }

    return (
      <section className="quickInfoSection quickInfoClasswideSection" data-testid="quickInfoClasswideSection">
        <h2>Summary Notes for {getCurrentEnrolledGrade(this.props.grade)}</h2>
        <QuickInfoContainer quickInfoData={quickInfoData} containerExpanded={this.state.expanded} />
        {quickInfoData.length > 4 && (
          <button className="btn btn-link btn-center" onClick={this.toggle}>
            {this.state.expanded ? "Show Less" : "Show More"}
          </button>
        )}
      </section>
    );
  }
}

ClasswideQuickInfoSection.propTypes = {
  data: PropTypes.array,
  grade: PropTypes.string,
  siteId: PropTypes.string,
  orgid: PropTypes.string,
  studentGroups: PropTypes.array
};
