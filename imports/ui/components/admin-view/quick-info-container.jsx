import React from "react";
import PropTypes from "prop-types";
import { Link } from "react-router-dom";

const quickInfoContainer = props => (
  <ul className={`quick-info-container ${props.containerExpanded ? "" : "collapsed"}`}>
    {props.quickInfoData.map((quickInfo, i) => (
      <li key={i}>
        <Link to={quickInfo.href} className={quickInfo.warning ? "text-danger" : ""}>
          {quickInfo.name}
        </Link>
        : {quickInfo.message}
      </li>
    ))}
  </ul>
);

quickInfoContainer.propTypes = {
  containerExpanded: PropTypes.bool,
  quickInfoData: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string.isRequired,
      href: PropTypes.string.isRequired,
      message: PropTypes.string.isRequired,
      warning: PropTypes.bool
    })
  ).isRequired
};

export default quickInfoContainer;
