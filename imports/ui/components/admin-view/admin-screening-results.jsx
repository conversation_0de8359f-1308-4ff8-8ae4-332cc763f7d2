import React, { Component } from "react";
import PropTypes from "prop-types";
import { PercentMeetingTargetChart } from "../../pages/admin-view/graphs/percent-meeting-target-chart.jsx";
import { getFormattedSchoolYear } from "/imports/api/utilities/utilities";
import BarGraph from "../../pages/district-reporting/bar-graph";
import { colors } from "../../../api/constants";

function renderSeasonalChangeText(seasonalChange) {
  if (seasonalChange && seasonalChange > 0) {
    return `, an increase of ${seasonalChange}%.`;
  }
  if (seasonalChange && seasonalChange < 0) {
    return `, a decrease of ${Math.abs(seasonalChange)}%.`;
  }
  return null;
}

function renderBulletText(seasonalData) {
  if (!seasonalData || !seasonalData.summaryData || !seasonalData.name) {
    return null;
  }
  if (seasonalData.summaryData.numberOfStudentsAssessed) {
    return (
      <li key={seasonalData.periodId} className={seasonalData.name.toLowerCase()}>
        {`In ${seasonalData.name} ${getFormattedSchoolYear(seasonalData.schoolYear)}`}
        ,&nbsp;
        <span className="schoolPercentage">{`${seasonalData.summaryData.y}%`}</span>
        &nbsp;
        {`of ${seasonalData.summaryData.name} At/Above Instructional Target`}
        {seasonalData.summaryData.seasonalChange
          ? renderSeasonalChangeText(seasonalData.summaryData.seasonalChange)
          : "."}
      </li>
    );
  }
  return null;
}

export default class AdminScreeningResults extends Component {
  renderGraph() {
    const chartContext = this.props.grade === "ALL" ? "Grade" : "Teacher";
    const chartName = `MeetsTargetBy${chartContext}`;
    const chartId = `${chartName}_${this.props.grade}`;
    const chartOptions = {
      chartType: "line",
      title: "",
      height: 400,
      xAxisTitle: chartContext,
      yAxisTitle: "Median Score",
      marginTop: 10,
      marginRight: 10
    };
    // Filter out scores from seasons without any data yet
    const filteredTargetScoreData = this.props.targetScoreData.filter(
      sc => sc.data && sc.data.some(d => d.numberOfStudentsAssessed)
    );
    const isProgramEvaluation = this.props.type === "evaluation";

    const colorBySeasonName = {
      fall: isProgramEvaluation ? colors.orange : "#f9bf68",
      winter: isProgramEvaluation ? colors.darkBlue : "#70a1d9",
      spring: isProgramEvaluation ? colors.steelBlue30 : "#aee57c"
    };

    if (filteredTargetScoreData.length > 0) {
      if (isProgramEvaluation) {
        return (
          <BarGraph
            chartName={"Percent of Students At Or Above the Instructional Target by Season"}
            data={filteredTargetScoreData.map(f => ({
              ...f,
              color: colorBySeasonName[f.name.toLowerCase()],
              data: f.data.map(d => ({ ...d, y: d.y || 0, isNA: !(d.y || d.y === 0) }))
            }))}
            chartId={chartId}
            options={{
              ...chartOptions,
              height: filteredTargetScoreData[0].data.length * 55,
              categoryOffset: -10,
              yAxisTitle: "% At/Above Instructional Target",
              isLegendEnabled: true,
              customHeightOffset: 135
            }}
          />
        );
      }
      return (
        <PercentMeetingTargetChart
          data={filteredTargetScoreData}
          chartName={chartName}
          type="Column"
          chartId={chartId}
          options={chartOptions}
        />
      );
    }
    return <h3 className="stamped">No screening results have been completed yet.</h3>;
  }

  render() {
    const isEvaluation = this.props.type === "evaluation";
    const summaryElements = (
      <ul className="lstScreeningWindowSummary">
        {this.props.targetScoreData.map(seasonalData => renderBulletText(seasonalData))}
      </ul>
    );
    return (
      <div className="conScreeningResults">
        {isEvaluation ? null : <h2>Screening Results (Percent of Students Meeting Target)</h2>}
        <p>
          Screenings are snapshots of student math comprehension at a given time. Screening allows you to identity
          student groups at-risk, commit resources to their improvement, and review their progress.
        </p>

        {isEvaluation ? (
          <ul>
            <li>
              Did the percent of students who met the instructional target on all of the screening measures increase by
              season?
            </li>
            {summaryElements}
          </ul>
        ) : (
          summaryElements
        )}

        {this.renderGraph()}
      </div>
    );
  }
}

AdminScreeningResults.propTypes = {
  targetScoreData: PropTypes.array,
  grade: PropTypes.string,
  type: PropTypes.string
};

AdminScreeningResults.defaultProps = {
  type: ""
};
