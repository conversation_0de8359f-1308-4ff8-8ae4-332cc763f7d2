import React, { useState, useEffect, useContext } from "react";
import { Meteor } from "meteor/meteor";
import { useTracker } from "meteor/react-meteor-data";
import PropTypes from "prop-types";
import Alert from "react-s-alert";

import { getCurrentSchoolYear, getMeteorUserSync, ninjalog } from "/imports/api/utilities/utilities";
import { Sites } from "/imports/api/sites/sites";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { AssessmentResults } from "/imports/api/assessmentResults/assessmentResults";

import Loading from "../../components/loading";
import ClasswideInterventionTable from "../../pages/admin-view/classwide-intervention-table";
import { SchoolYearContext } from "/imports/contexts/SchoolYearContext";
import SkillGroupsMessage from "../../pages/dashboard/skill-groups-message";
import InterventionSkillGroupsModal from "../../pages/dashboard/intervention-skill-groups-modal";

function SchoolOverviewDetails(props) {
  const [state, setState] = useState({
    schoolDemographics: null,
    allClasswideStats: null,
    groupedClasswideStats: {},
    isInterventionSkillGroupsModalOpen: false,
    skillGroups: [],
    isFetchingSkillGroups: true,
    studentGroupEnrollments: [],
    isDataFetching: false
  });

  const openInterventionSkillGroupsModal = () => {
    setState(prev => ({ ...prev, isInterventionSkillGroupsModalOpen: true }));
  };

  const closeInterventionSkillGroupsModal = () => {
    setState(prev => ({ ...prev, isInterventionSkillGroupsModalOpen: false }));
  };

  // Handle classwide stats updates
  useEffect(() => {
    let isMounted = true;

    if (props.studentGroups?.length) {
      setState(prev => ({ ...prev, isDataFetching: true }));
      const studentGroupIds = props.studentGroups.map(sg => sg._id);

      Meteor.call("calculateClasswideStatsForMultipleGroups", studentGroupIds, false, (err, result) => {
        if (!isMounted) return;

        if (err) {
          ninjalog.error({
            msg: `error getting classwide intervention stats: ${err}`,
            context: "admin"
          });
          setState(prev => ({ ...prev, isDataFetching: false }));
        } else {
          const { allClasswideResults, studentGroupEnrollments } = result;
          const groupedClasswideStats = {};
          props.grades.forEach(grade => {
            groupedClasswideStats[grade] = allClasswideResults.filter(
              ({ studentGroupId }) => props.studentGroups.find(sg => sg._id === studentGroupId).grade === grade
            );
          });
          setState(prev => ({
            ...prev,
            allClasswideStats: allClasswideResults,
            studentGroupEnrollments,
            groupedClasswideStats,
            isDataFetching: false
          }));
        }
      });
    }

    return () => {
      isMounted = false;
    };
  }, [props.studentGroups?.length, props.siteId]);

  // Handle skill groups updates
  useEffect(() => {
    let isMounted = true;

    if (props.studentGroups?.length) {
      const { siteId, schoolYear, orgid } = props.studentGroups[0];

      Meteor.call("getSkillGroupAssignments", { siteId, schoolYear, orgid }, (err, resp) => {
        if (!isMounted) return;

        if (!err) {
          setState(prev => ({ ...prev, skillGroups: resp }));
        } else {
          Alert.error("Error getting skill groups");
        }
        setState(prev => ({ ...prev, isFetchingSkillGroups: false }));
      });
    }

    return () => {
      isMounted = false;
    };
  }, [props.studentGroups?.length, props.siteId]);

  if (props.loading) {
    return <Loading />;
  }

  return (
    <React.Fragment>
      {!props.isPrinting && (
        <SkillGroupsMessage
          skillGroups={state.skillGroups}
          isFetchingSkillGroups={state.isFetchingSkillGroups}
          openInterventionSkillGroupsModal={openInterventionSkillGroupsModal}
        />
      )}
      <div className={!props.isPrinting ? "conOverviewContent" : null}>
        <section className="classwideInterventionSection" data-testid="classwideInterventionSection">
          <h2 data-testid="classwideInterventionSectionHeader">
            <i className="fa fa-users" /> Classwide Interventions
          </h2>
          <ClasswideInterventionTable
            allClasswideStats={state.allClasswideStats}
            studentGroups={props.studentGroups}
            assessmentResults={props.assessmentResults}
            currentBMPeriod={props.currentBMPeriod}
            studentGroupEnrollments={state.studentGroupEnrollments}
            loading={!state.allClasswideStats}
            isSchoolOverview
            isHighSchool={props.isHighSchool}
          />
        </section>
        {state.skillGroups.length ? (
          <InterventionSkillGroupsModal
            showModal={state.isInterventionSkillGroupsModalOpen}
            closeModal={closeInterventionSkillGroupsModal}
            skillGroups={state.skillGroups}
          />
        ) : null}
      </div>
    </React.Fragment>
  );
}

SchoolOverviewDetails.propTypes = {
  assessmentResults: PropTypes.array,
  currentBMPeriod: PropTypes.object,
  grades: PropTypes.array,
  headerTitle: PropTypes.string,
  isHighSchool: PropTypes.bool,
  isPrinting: PropTypes.bool,
  loading: PropTypes.bool,
  orgid: PropTypes.string,
  percentMeetingTargetByGradeAndSeason: PropTypes.array,
  schoolwidePercentScreened: PropTypes.object,
  siteId: PropTypes.string,
  siteName: PropTypes.string,
  studentGroups: PropTypes.array,
  user: PropTypes.object
};

SchoolOverviewDetails.defaultProps = {
  isHighSchool: false
};

function SchoolOverviewDetailsWithTracker(params) {
  const { orgid, siteId, grades } = params;
  const [resolvedSchoolYear, setResolvedSchoolYear] = useState(params.schoolYear);

  // Handle async school year resolution
  useEffect(() => {
    const resolveSchoolYear = async () => {
      if (!params.schoolYear) {
        const user = getMeteorUserSync();
        if (user && orgid) {
          const currentSchoolYear = await getCurrentSchoolYear(user, orgid);
          setResolvedSchoolYear(currentSchoolYear);
        }
      } else {
        setResolvedSchoolYear(params.schoolYear);
      }
    };

    resolveSchoolYear();
  }, [orgid, params.schoolYear]);

  const trackerData = useTracker(() => {
    if (!resolvedSchoolYear) {
      return { loading: true };
    }

    const user = getMeteorUserSync();
    const studentGroupsHandle = Meteor.subscribe("StudentGroups:PerSite", orgid, siteId, resolvedSchoolYear);
    const sitesHandle = Meteor.subscribe("Sites", orgid, siteId);
    const orgHandle = Meteor.subscribe("Organizations", orgid);

    const loading = !sitesHandle.ready() && !studentGroupsHandle.ready() && !orgHandle.ready();
    let site = {};
    let studentGroups = [];
    let assessmentResults = [];
    let finalLoading = true;

    if (!loading) {
      site = Sites.findOne({ _id: siteId });
      studentGroups = StudentGroups.find({ siteId, grade: { $in: grades } }).fetch();
      const studentGroupIds = studentGroups.map(sg => sg._id);
      const assessmentResultsSub = Meteor.subscribe(
        "AssessmentResults:IndividualByStudentGroupIds",
        studentGroupIds,
        resolvedSchoolYear,
        { $in: grades }
      );
      finalLoading = !assessmentResultsSub.ready();
      if (!finalLoading) {
        assessmentResults = AssessmentResults.find({
          studentGroupId: { $in: studentGroupIds }
        }).fetch();
      }
    }

    return {
      siteId,
      assessmentResults,
      orgid,
      loading: finalLoading,
      site,
      studentGroups,
      user,
      schoolYear: resolvedSchoolYear
    };
  }, [resolvedSchoolYear, orgid, siteId, grades]);

  return <SchoolOverviewDetails {...params} {...trackerData} />;
}

const SchoolOverviewDetailsWithContext = props => {
  const { schoolYear } = useContext(SchoolYearContext) || {};
  return <SchoolOverviewDetailsWithTracker {...props} schoolYear={schoolYear} />;
};

export default SchoolOverviewDetailsWithContext;
