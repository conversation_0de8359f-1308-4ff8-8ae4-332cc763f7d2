import React from "react";
import PropTypes from "prop-types";
import moment from "moment";
import { get } from "lodash";

import * as helpers from "../student-groups/helperFunction.js";
import CalculateAsOfDate from "./calculateAsOfDate.jsx";
import StudentName from "../student-groups/StudentName.jsx";

const IndividualInterventionStudentsAdmin = props => (
  <div>
    {helpers.sortStudentsByName(props.individualInterventionStudents).map((student, i) => {
      const individualResults =
        (props.groupStats.extendedIndividualResults && props.groupStats.extendedIndividualResults.individualResults) ||
        props.groupStats.individualResults;
      const individualStats = individualResults && individualResults.find(stats => stats.studentId === student._id);

      const lastScoreUpdatedAt = getLastScoreUpdatedAt(props.assessmentResults, student);

      return (
        <div key={i} className="row rowIndvStudents">
          <StudentName student={student} studentGroup={props.studentGroup} isAdminView={true} />
          <div className="col-md-2 rowIndvStudents-currentAssessment">{student.currentSkill.assessmentName}</div>
          <div className="col-md-2">{lastScoreUpdatedAt}</div>
          {individualStats && typeof individualStats.interventionConsistency === "number" ? (
            <div
              className={`col-md-2 rowIndvStudents-consistency${
                individualStats.interventionConsistency < 80 ? " text-danger" : ""
              }`}
            >
              {individualStats.interventionConsistency}%
              <small>{`${individualStats.numberOfWeeksWithScoresEntered} of ${individualStats.numberOfWeeksActive} weeks with scores`}</small>
            </div>
          ) : (
            <div className="col-md-2">N/A</div>
          )}
          {individualStats && individualStats.averageWeeksPerSkill ? (
            <div className="col-md-1">{individualStats.averageWeeksPerSkill}</div>
          ) : (
            <div className="col-md-1">N/A</div>
          )}
          {individualStats ? (
            <div className="col-md-2 input-group-date">
              <CalculateAsOfDate
                {...props}
                group={props.studentGroup}
                student={student}
                type="individual"
                benchmarkPeriodId={student.currentSkill && student.currentSkill.benchmarkPeriodId}
              />
            </div>
          ) : null}
        </div>
      );
    })}
  </div>
);

function getLastScoreUpdatedAt(assessmentResults, student) {
  const assessmentResult = assessmentResults.find(ar => ar.studentId === student._id && ar.status === "OPEN");
  let lastScoreUpdatedAt = assessmentResult && assessmentResult.lastScoreUpdatedAt;

  if (!lastScoreUpdatedAt) {
    const previousAssessmentResult = assessmentResults.find(
      ar => ar._id === assessmentResult?.previousAssessmentResultId
    );
    lastScoreUpdatedAt = previousAssessmentResult && previousAssessmentResult.lastScoreUpdatedAt;

    if (!lastScoreUpdatedAt) {
      const lastCompletedIndividualIntervention =
        student.history && student.history.find(ar => ar.type === "individual");
      lastScoreUpdatedAt = get(lastCompletedIndividualIntervention, "whenEnded.on");
    }
  }
  return (lastScoreUpdatedAt && moment(lastScoreUpdatedAt).format("L")) || "N/A";
}

IndividualInterventionStudentsAdmin.propTypes = {
  individualInterventionStudents: PropTypes.array,
  groupStats: PropTypes.object,
  studentGroup: PropTypes.object,
  updateStats: PropTypes.func,
  assessmentResults: PropTypes.array
};

export default IndividualInterventionStudentsAdmin;
