import React, { Component } from "react";
import PropTypes from "prop-types";

import Highcharts from "highcharts/highstock";
import { translateBenchmarkPeriod } from "/imports/api/utilities/utilities.js";

export default class ScreeningResultsSummaryChart extends Component {
  // When the DOM is ready, create the chart.
  componentDidMount() {
    this.renderBenchmarkHistoryGraph();
  }

  // Update the chart if the component is updated
  componentDidUpdate() {
    this.renderBenchmarkHistoryGraph();
  }

  //  Destroy chart before unmount.
  componentWillUnmount() {
    this.chart && this.chart.destroy();
  }

  renderBenchmarkHistoryGraph() {
    const measureCategories =
      this.props.historyMeasures && this.props.historyMeasures.map((hm, i) => `Measure ${i + 1}: ${hm.assessmentName}`);
    const measuresPercentMeetingTarget =
      this.props.historyMeasures && this.props.historyMeasures.map(hm => hm.percentMeetingTarget);
    const color =
      this.props.historyMeasures && translateBenchmarkPeriod(this.props.historyMeasures[0].benchmarkPeriodId).color;
    this.charts = this.charts || [];
    this.charts.push(
      new Highcharts[this.props.type || "Chart"](this.props.chartId, {
        credits: {
          enabled: false
        },
        chart: {
          type: "column",
          marginRight: 100
        },
        accessibility: {
          enabled: false
        },
        title: {
          text: ""
        },
        legend: {
          enabled: false
        },
        series: [
          {
            name: "PercentMeetingTarget",
            data: measuresPercentMeetingTarget
          }
        ],
        xAxis: {
          categories: measureCategories,
          labels: {
            step: 1
          }
        },
        yAxis: {
          title: {
            text: "% At/Above Instructional Target"
          },
          max: 100,
          min: 0
        },
        plotOptions: {
          column: {
            maxPointWidth: 100,
            minPointLength: 3
          },
          series: {
            color
          }
        }
      })
    );
  }
  render() {
    return <div id={this.props.chartId} className="chart" />;
  }
}
