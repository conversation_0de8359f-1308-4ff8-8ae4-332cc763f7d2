import React, { useState, useCallback, useContext } from "react";
import PropTypes from "prop-types";

import IndividualRecommendation from "./individual-recommendation";
import { isSML } from "/imports/api/utilities/utilities";
import { StudentGroupContext } from "../../../contexts/StudentGroupContext";
import { UserContext } from "../../../contexts/UserContext";
import { OrganizationContext } from "../../../contexts/OrganizationContext";

function classDoesNotMeetTarget(medianScore) {
  return medianScore < 50 && <i className="fa fa-exclamation" />;
}

function IndividualProblem(props) {
  const { studentGroup } = useContext(StudentGroupContext);
  const { user } = useContext(UserContext);
  const { orgId } = useContext(OrganizationContext);
  const [selectedAssessment, setSelectedAssessment] = useState(0);

  const handleClick = useCallback(assessmentIndex => {
    setSelectedAssessment(assessmentIndex);
  }, []);

  const percentOfStudentsMeetingTarget = useCallback(() => {
    return props.assessmentResults.classwideResults.percentMeetingTarget;
  }, [props.assessmentResults.classwideResults.percentMeetingTarget]);

  const renderMeasureResults = useCallback(() => {
    return props.assessmentResults.measures.map((measureResults, index) => (
      <li
        key={index}
        className={selectedAssessment === index ? "active" : ""}
        data-toggle="tooltip"
        data-placement="top"
        title={measureResults.measureName}
        onClick={() => handleClick(index)}
      >
        <h2 className="w7 text-center">
          {measureResults.percentMeetingTarget}%{classDoesNotMeetTarget(measureResults.percentMeetingTarget)}
        </h2>
        <h4 className="data-sub">Measure {index + 1}</h4>
      </li>
    ));
  }, [props.assessmentResults.measures, selectedAssessment, handleClick]);

  const getClassroomPerformanceSummary = useCallback(() => {
    if (props.onlyOneStudentTookAndFailedBenchmark) {
      return (
        <React.Fragment>
          <p>
            Because there is only 1 student in your class and they would benefit from intervention we recommend doing an
            <span className="cw-underline"> individual intervention.</span>
          </p>
        </React.Fragment>
      );
    }
    return props.classwideEnabled ? (
      <p>
        <span className="text-success">
          {`${props.assessmentResults.classwideResults.percentMeetingTarget}% of students are meeting all requirements.`}
        </span>
      </p>
    ) : (
      <p>
        <em>{percentOfStudentsMeetingTarget()}% of your class is meeting the target.</em>
        &nbsp; See below for a list of students in need of intervention to benefit from grade-level instruction.
      </p>
    );
  }, [
    props.onlyOneStudentTookAndFailedBenchmark,
    props.classwideEnabled,
    props.assessmentResults.classwideResults.percentMeetingTarget,
    percentOfStudentsMeetingTarget
  ]);

  return (
    <div>
      <div className="screening-results item">
        <div className="classwide-result">
          <h3 className="w7">Classroom Performance</h3>
          {getClassroomPerformanceSummary()}
          {isSML(orgId) || props.isPrinting ? null : <div className="big-stats">{renderMeasureResults()}</div>}
        </div>
      </div>
      <IndividualRecommendation
        selectedAssessment={selectedAssessment}
        assessmentResults={props.assessmentResults}
        students={props.students}
        currentBenchmarkPeriodId={props.currentBenchmarkPeriod._id}
        classwideEnabled={props.classwideEnabled}
        scheduledStudentIds={props.scheduledStudentIds}
        studentGroup={studentGroup}
        isInCurrentSchoolYear={props.isInCurrentSchoolYear}
        user={user}
        onlyOneStudentTookAndFailedBenchmark={props.onlyOneStudentTookAndFailedBenchmark}
        firstClasswideInterventionCreatedAt={props.firstClasswideInterventionCreatedAt}
        isClasswideInterventionComplete={props.isClasswideInterventionComplete}
        isPrinting={props.isPrinting}
      />
    </div>
  );
}

IndividualProblem.propTypes = {
  assessmentResults: PropTypes.object,
  classwideEnabled: PropTypes.bool,
  currentBenchmarkPeriod: PropTypes.object,
  scheduledStudentIds: PropTypes.array,
  students: PropTypes.array,
  isInCurrentSchoolYear: PropTypes.bool,
  onlyOneStudentTookAndFailedBenchmark: PropTypes.bool,
  isPrinting: PropTypes.bool,
  firstClasswideInterventionCreatedAt: PropTypes.number,
  isClasswideInterventionComplete: PropTypes.bool
};

export default IndividualProblem;
