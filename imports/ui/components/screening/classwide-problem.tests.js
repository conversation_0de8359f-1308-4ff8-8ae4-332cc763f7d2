import React from "react";
import { expect } from "chai";
import { shallow } from "enzyme";
import { PureClasswideProblem } from "./classwide-problem.jsx";

describe("ClasswideProblem Component", () => {
  it("should show a link to kindergarten materials when the group is grade K", () => {
    const assessmentResults = {
      grade: "K",
      classwideResults: {
        percentAtRisk: 30
      },
      measures: []
    };
    const wrapper = shallow(<PureClasswideProblem assessmentResults={assessmentResults} />);
    expect(wrapper.find("#classwide-materials-link").length).to.equal(1);
  });
  it("should not show a link to kindergarten materials when the group is grade 05", () => {
    const assessmentResults = {
      grade: "05",
      classwideResults: {
        percentAtRisk: 30
      },
      measures: []
    };
    const wrapper = shallow(<PureClasswideProblem assessmentResults={assessmentResults} />);
    expect(wrapper.find("#classwide-materials-link").length).to.equal(0);
  });
});
