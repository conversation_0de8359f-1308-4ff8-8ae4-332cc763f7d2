import React, { Component } from "react";
import PropTypes from "prop-types";
import _ from "lodash";

import Highcharts from "highcharts/highstock";

export default class ClassWideScreeningMeasureScoresChart extends Component {
  // When the DOM is ready, create the chart.
  componentDidMount() {
    this.updateChart();
  }

  shouldComponentUpdate(nextProps) {
    // Only update the chart when the user tabs to a different graph,
    // not when they interact with the page (checkboxes, etc)
    return (
      this.props &&
      this.props.measureResults &&
      this.props.measureResults.assessmentId !== nextProps.measureResults.assessmentId
    );
  }

  componentDidUpdate() {
    this.updateChart();
  }

  //  Destroy chart before unmount.
  componentWillUnmount() {
    try {
      this.chart.destroy();
    } catch (e) {
      // nothing
    }
  }

  updateChart() {
    const studentCategories = [];
    const sortedStudentScoreValues = [];

    // Sort the studentResults first so that the name and score stay linked until we pull them apart for the graph
    let sortedStudentResults = this.props.measureResults.studentResults.sort((a, b) => {
      return Number(b.score) - Number(a.score);
    });

    sortedStudentResults = _.filter(sortedStudentResults, result => {
      return result.status !== "CANCELLED";
    });

    const [instructionalTarget, masteryTarget] = this.props.measureResults.targetScores;

    sortedStudentResults.forEach(studentResult => {
      const studentName = `${studentResult.firstName.substr(0, 1)}. ${studentResult.lastName}`;
      sortedStudentScoreValues.push({
        name: studentName,
        color: studentResult.score >= instructionalTarget ? "#21CBB3" : "#F3A537",
        y: Number(studentResult.score)
      });
      studentCategories.push(studentName);
    });

    if (document.getElementById(this.props.chartId)) {
      this.chart = new Highcharts[this.props.type || "Chart"](this.props.chartId, {
        credits: {
          enabled: false
        },
        chart: {
          type: "column",
          marginRight: 100
        },
        accessibility: {
          enabled: false
        },
        plotOptions: {
          column: {
            minPointLength: 3
          }
        },
        title: {
          text: ""
        },
        legend: {
          enabled: false
        },
        series: [
          {
            name: "Score",
            data: sortedStudentScoreValues
          },
          // Include one hidden data point for the target score so that the Y-Axis will auto scale
          {
            name: "Target",
            type: "scatter",
            marker: {
              enabled: false
            },
            data: [instructionalTarget, masteryTarget],
            enableMouseTracking: false
          }
        ],
        xAxis: {
          categories: studentCategories,
          labels: {
            rotation: -80,
            step: 1
          }
        },
        yAxis: {
          title: {
            text: "Score"
          },
          plotLines: [
            {
              value: masteryTarget,
              color: "#21BA6E",
              dashStyle: "Dash",
              width: 2,
              label: {
                text: `Mastery Target (${masteryTarget})`,
                align: "right",
                style: {
                  color: "#555"
                },
                x: +80
              }
            },
            {
              value: instructionalTarget,
              color: "#F4D03F",
              dashStyle: "Dash",
              width: 2,
              label: {
                text: `Instructional Target (${instructionalTarget})`,
                align: "right",
                style: {
                  color: "#555"
                },
                x: +80
              }
            }
          ]
        }
      });
    }
  }

  //  Create the div which the chart will be rendered to.
  render() {
    return <div id={this.props.chartId} />;
  }
}
ClassWideScreeningMeasureScoresChart.propTypes = {
  measureResults: PropTypes.object.isRequired,
  type: PropTypes.string,
  chartId: PropTypes.string
};
