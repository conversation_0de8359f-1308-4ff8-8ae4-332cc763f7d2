import React, { Component } from "react";
import PropTypes from "prop-types";

import MeasureResultsDetail from "./measure-results-detail";
import IndividualRecommendation from "./individual-recommendation";

export default class ClasswideProblem extends Component {
  constructor() {
    super();
    this.state = {
      selectedAssessment: 0
    };
  }

  handleClick(assessmentIndex) {
    this.setState({ selectedAssessment: assessmentIndex });
  }

  renderMeasureResults() {
    return this.props.assessmentResults.measures.map((measureResults, index) => (
      <li
        key={index}
        className={this.state.selectedAssessment === index ? "active" : ""}
        data-toggle="tooltip"
        data-placement="top"
        title={measureResults.assessmentName}
        onClick={this.handleClick.bind(this, index)}
      >
        <h2 className="w7 text-center">
          {measureResults.percentMeetingTarget < 50 ? (
            <i className="fa-fail">{measureResults.percentMeetingTarget}%</i>
          ) : (
            <i>{measureResults.percentMeetingTarget}%</i>
          )}
        </h2>
        <h4 className="data-sub">Measure {index + 1}</h4>
      </li>
    ));
  }

  getKindergartenMaterialsLink = () => (
    <p>
      Some additional materials will help you complete your class wide interventions.
      <br />
      <a
        id="classwide-materials-link"
        rel="noopener noreferrer"
        target="_blank"
        href="https://s3.amazonaws.com/springmath/pdf/faq/Kindergarten%20Classwide%20Preparation%2012-16-2020.pdf"
      >
        <i className="fa fa-file-o" />
        {`  Click here to prepare for classwide intervention.`}
      </a>
    </p>
  );

  getClasswideResultText = () => {
    if (this.props.isClasswideInterventionComplete) {
      return null;
    }
    if (this.props.isClasswideInterventionProgressed) {
      return <p>The classwide intervention has already been started.</p>;
    }
    return (
      <p>
        We <span className="cw-underline">recommend classwide intervention</span> which will improve performance for all
        of your students and help get this class on track to reach mastery.
      </p>
    );
  };

  getScreeningSummaryText = () => {
    if (this.props.isClasswideInterventionProgressed) {
      return (
        <h5>
          Next Steps: <strong>Keep practicing the classwide intervention.</strong>
        </h5>
      );
    }
    if (this.props.isClasswideInterventionComplete) {
      return (
        <h4 className="w9 mt-4">
          Next Steps: If some students still need help talk with your coach about scheduling individual interventions
          for them.
        </h4>
      );
    }
    return (
      <div>
        <h5>
          Next Steps: <strong>Performing Class Wide Interventions</strong>
        </h5>
        <p>Skill packets will be provided to help your students practice essential math skills.</p>
        {this.props.assessmentResults.grade === "K" ? this.getKindergartenMaterialsLink() : null}
        <p>{`As you complete skills you will receive new packets and be able to view your class' skill progress.`}</p>
      </div>
    );
  };

  getMeasureIndexList = () => {
    return [...Array(this.props.assessmentResults.measures.length).keys()];
  };

  getMeasureResultsDetail = () => {
    if (this.props.isPrinting) {
      return this.getMeasureIndexList().map(num => {
        return (
          <div
            key={`chart_${num}`}
            className="page-break-after screening-page-graph"
            data-testid={`classwide-screening-graph_${num}`}
          >
            <MeasureResultsDetail assessmentResults={this.props.assessmentResults} selectedMeasureIndex={num} />
          </div>
        );
      });
    }

    return (
      <MeasureResultsDetail
        assessmentResults={this.props.assessmentResults}
        selectedMeasureIndex={this.state.selectedAssessment}
      />
    );
  };

  render() {
    return (
      <div>
        <div className="screening-results item">
          <div className="classwide-result">
            <h3 className="w7">Classroom Performance</h3>
            <p>
              <span className="cw-underline">
                {100 - this.props.assessmentResults.classwideResults.percentAtRisk}% of your class reached the target on
                all of the screening assessments.
              </span>
            </p>
            {this.getClasswideResultText()}
            {this.props.isPrinting ? null : <div className="big-stats">{this.renderMeasureResults()}</div>}
          </div>
        </div>
        <div className="intervention-recommendation">
          <div id="classwide-screening-results">
            {this.getMeasureResultsDetail()}
            {this.props.isPrinting ? null : <hr />}
          </div>
          <div id="classwide-screening-summary">{this.getScreeningSummaryText()}</div>
        </div>
        {this.props.isClasswideInterventionComplete && (
          <IndividualRecommendation
            assessmentResults={this.props.assessmentResults}
            currentBenchmarkPeriodId={this.props.currentBenchmarkPeriod?._id}
            scheduledStudentIds={this.props.scheduledStudentIds}
            students={this.props.students}
            classwideEnabled={this.props.classwideEnabled}
            studentGroup={this.props.studentGroup}
            isInCurrentSchoolYear={this.props.isInCurrentSchoolYear}
            user={this.props.user}
            onlyOneStudentTookAndFailedBenchmark={this.props.onlyOneStudentTookAndFailedBenchmark}
            isClasswideInterventionComplete={this.props.isClasswideInterventionComplete}
            isPrinting={this.isPrinting}
            noIndividualScreeningResults={this.props.noIndividualScreeningResults}
          />
        )}
      </div>
    );
  }
}

ClasswideProblem.propTypes = {
  assessmentResults: PropTypes.object,
  isClasswideInterventionComplete: PropTypes.bool,
  isClasswideInterventionProgressed: PropTypes.bool,
  currentBenchmarkPeriod: PropTypes.object,
  scheduledStudentIds: PropTypes.array,
  students: PropTypes.array,
  classwideEnabled: PropTypes.bool,
  studentGroup: PropTypes.object,
  isInCurrentSchoolYear: PropTypes.bool,
  user: PropTypes.object,
  onlyOneStudentTookAndFailedBenchmark: PropTypes.bool,
  isPrinting: PropTypes.bool,
  noIndividualScreeningResults: PropTypes.bool
};

export { ClasswideProblem as PureClasswideProblem };
