import React from "react";
import PropTypes from "prop-types";
import ClassWideScreeningMeasureScoresChart from "./screening-measure-scores-chart.jsx";
import { isOnPrintPage } from "../../utilities";

export default function measureResultsDetail(props) {
  const isPrinting = isOnPrintPage();
  const currentAssessmentMeasure = props.assessmentResults.measures[props.selectedMeasureIndex];
  const { percentMeetingTarget } = currentAssessmentMeasure;

  const showPercentMeetingTarget = () => {
    if (isPrinting) {
      return percentMeetingTarget < 50 ? (
        <i className="measure-fail">{percentMeetingTarget}% passed</i>
      ) : (
        <i>{percentMeetingTarget}% passed</i>
      );
    }
    return null;
  };

  return (
    <div>
      <h5 data-testid={`measure-results-detail-name_${props.selectedMeasureIndex}`}>
        Measure {props.selectedMeasureIndex + 1}: <strong>{currentAssessmentMeasure.assessmentName}</strong>
        {isPrinting ? ", " : null}
        {showPercentMeetingTarget()}
      </h5>
      <p>{`Your students' screening scores compared to the target score.`}</p>
      <div className="class-performance">
        <ClassWideScreeningMeasureScoresChart
          type="Chart"
          chartId={`classWideMeasureScoresChart_${props.selectedMeasureIndex}`}
          measureResults={props.assessmentResults.measures[props.selectedMeasureIndex]}
        />
      </div>
    </div>
  );
}

measureResultsDetail.propTypes = {
  selectedMeasureIndex: PropTypes.number,
  assessmentResults: PropTypes.object
};
