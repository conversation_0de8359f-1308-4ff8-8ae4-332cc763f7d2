import React, { Component } from "react";
import { Meteor } from "meteor/meteor";
import { with<PERSON>outer } from "react-router-dom";
import PropTypes from "prop-types";

class ScreeningWindowStarted extends Component {
  constructor(props) {
    super(props);

    this.startNewScreening = this.startNewScreening.bind(this);
  }

  startNewScreening() {
    const { siteId } = this.props.studentGroup;
    const { grade } = this.props.studentGroup;
    const { orgid } = this.props.studentGroup;
    const studentGroupId = this.props.studentGroup._id;
    const opts = {
      studentGroupId,
      siteId,
      grade,
      orgid
    };
    Meteor.call("setUpNewScreeningPeriod", opts, (error, results) => {
      if (error) {
        console.log("error: ", error);
      }
      if (results) {
        this.props.history.push(`/${orgid}/site/${siteId}/student-groups/${studentGroupId}/screening/form`);
      }
    });
  }

  render() {
    return (
      <div className="conScreeningNotice screeningWindowStarted">
        <div className="conScreeningNotice-Heading clearfix">
          {this.props.screeningContinues ? (
            <button
              className="btnNoticeAction btnStartScreening btn btn-xs"
              onClick={() => {
                this.props.history.push(
                  `/${this.props.studentGroup.orgid}/site/${this.props.studentGroup.siteId}/student-groups/${this.props.studentGroup._id}/screening/form`
                );
              }}
            >
              Continue Screening
            </button>
          ) : (
            <button className="btnNoticeAction btnStartScreening btn btn-xs" onClick={this.startNewScreening}>
              Start Screening
            </button>
          )}
          <div className="iconCallout">
            <i className="fa fa-bullhorn" />
          </div>

          {this.props.screeningContinues ? (
            <h2>It looks like your class has not completed {this.props.screeningSeason} screening.</h2>
          ) : (
            <h2>{`It's time to start ${this.props.screeningSeason} screening!`}</h2>
          )}
        </div>
      </div>
    );
  }
}

ScreeningWindowStarted.propTypes = {
  screeningContinues: PropTypes.bool,
  studentGroup: PropTypes.object,
  history: PropTypes.object,
  screeningSeason: PropTypes.string
};

export default withRouter(ScreeningWindowStarted);
