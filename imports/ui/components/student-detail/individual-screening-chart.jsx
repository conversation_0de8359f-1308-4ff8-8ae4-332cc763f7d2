import React, { useEffect, useRef } from "react";
import PropTypes from "prop-types";

import Highcharts from "highcharts/highstock";
import { isOnPrintPage } from "../../utilities";

export function IndividualScreeningChart({ chartId, measureScores, bmScores }) {
  const chartRef = useRef(null);

  const updateChart = () => {
    const isPrinting = isOnPrintPage();
    const measureCategories =
      bmScores && bmScores.assessmentResultMeasures.map((hm, i) => `Measure ${i + 1}: ${hm.assessmentName}`);
    const instructionalTargets = bmScores && bmScores.assessmentResultMeasures.map(hm => hm.targetScores[0]);
    const masteryTargets = bmScores && bmScores.assessmentResultMeasures.map(hm => hm.targetScores[1]);
    chartRef.current = new Highcharts.Chart(chartId, {
      credits: {
        enabled: false
      },
      chart: {
        type: "column",
        height: isPrinting ? "370px" : "400px"
      },
      accessibility: {
        enabled: false
      },
      title: {
        text: ""
      },
      legend: {
        enabled: true,
        ...(isPrinting ? { itemStyle: { fontSize: "10px" } } : {}),
        itemHiddenStyle: {
          color: "#ccc",
          textDecoration: "none"
        }
      },
      series: [
        ...measureScores,
        {
          type: "scatter",
          name: "Mastery Target",
          data: masteryTargets,
          showInLegend: false,
          dataLabels: {
            enabled: true,
            style: {
              fontWeight: "normal"
            }
          },
          marker: {
            symbol: "url(/images/mastery-target.png)",
            width: 100,
            height: 2
          },
          tooltip: {
            headerFormat: "",
            pointFormat: "{series.name}: <b>{point.y}<b>"
          }
        },
        {
          type: "scatter",
          name: "Instructional Target",
          data: instructionalTargets,
          showInLegend: false,
          dataLabels: {
            enabled: true,
            style: {
              fontWeight: "normal"
            }
          },
          marker: {
            symbol: "url(/images/instructional-target.png)",
            width: 100,
            height: 2
          },
          tooltip: {
            headerFormat: "",
            pointFormat: "{series.name}: <b>{point.y}</b>"
          }
        }
      ],
      xAxis: {
        categories: measureCategories,
        labels: {
          step: 1
        }
      },
      yAxis: {
        title: {
          text: ""
        },
        gridLineDashStyle: "Dot",
        gridLineColor: "#95A0A6",
        min: 0,
        endOnTick: false // Fix yAxis labels going over 100 when series y = 100
      },
      plotOptions: {
        column: {
          maxPointWidth: 100,
          minPointLength: 3
        },
        scatter: {
          dataLabels: {
            enabled: true,
            formatter() {
              return `${this.series.name} (${this.point.y})`;
            }
          }
        }
      }
    });
  };

  useEffect(() => {
    // Ensure the DOM element exists before creating the chart
    const chartElement = document.getElementById(chartId);
    if (chartElement && bmScores && measureScores) {
      updateChart();
    }

    // TODO(fmazur) - investigate why HMR crashes app on destroy
    return () => {
      try {
        if (chartRef.current && typeof chartRef.current.destroy === "function") {
          chartRef.current.destroy();
        }
        // eslint-disable-next-line no-empty
      } catch (error) {}
    };
  }, [measureScores, bmScores, chartId]);

  return <div id={chartId} className="student-detail-chart" data-testid={chartId} />;
}

IndividualScreeningChart.propTypes = {
  chartId: PropTypes.string,
  bmScores: PropTypes.object,
  measureScores: PropTypes.array
};
