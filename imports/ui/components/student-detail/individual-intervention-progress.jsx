import React, { useContext, useEffect, useMemo, useState } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { cloneDeep, findLast, get, isObject, uniq } from "lodash";
import { Meteor } from "meteor/meteor";
import IndividualSkillList from "./individual-skill-list.jsx";
import IndividualGoalSkillList from "./individual-goal-skill-list.jsx";
import { Rules } from "/imports/api/rules/rules";
import ClasswidePMChart from "./student-detail-PM-Chart.jsx";
import {
  translateBenchmarkPeriod,
  calculateRoI,
  getPageBreakClassForEverySecondElementByIndex
} from "/imports/api/utilities/utilities";
import { ScreeningAssignments } from "/imports/api/screeningAssignments/screeningAssignments";
import { Assessments } from "/imports/api/assessments/assessments";
import IndividualGraphTitle from "./individual-graph-title";
import { getTimeRange, isOnPrintPage } from "../../utilities";
import ConfirmModal from "../../pages/data-admin/confirm-modal";
import Loading from "../loading";
import { StudentContext } from "../../../contexts/StudentContext";
import { StudentGroupContext } from "../../../contexts/StudentGroupContext";
import { UserContext } from "../../../contexts/UserContext";

// eslint-disable-next-line max-statements
export default function IndividualInterventionProgress(props) {
  const { student: contextStudent = {} } = useContext(StudentContext);
  const { studentGroup: contextStudentGroup } = useContext(StudentGroupContext);
  const { userSiteAccess, user } = useContext(UserContext);

  // State for student and studentGroup based on original file logic
  const [student, setStudent] = useState(contextStudent);
  const [studentGroup, setStudentGroup] = useState(contextStudentGroup);

  // Update student and studentGroup when context changes
  useEffect(() => {
    setStudent(contextStudent);
  }, [contextStudent]);

  useEffect(() => {
    setStudentGroup(contextStudentGroup);
  }, [contextStudentGroup]);

  const activeBMPeriodIds = useMemo(() => getActiveBMPeriods(student?.history), [student]);
  const skillHistoryHierarchy = useMemo(() => getHistoryHierarchy(student, studentGroup, activeBMPeriodIds), [
    student,
    studentGroup,
    activeBMPeriodIds
  ]);

  // Define helper functions first
  const getGoalAndInterventionScore = studentData => {
    const { assessmentResultMeasures, assessmentId, benchmarkAssessmentId, interventions } = studentData
      ?.history?.[0] || {
      assessmentResultMeasures: []
    };
    const goalSkillResultMeasure = assessmentResultMeasures.find(arm => arm.assessmentId === benchmarkAssessmentId);
    const interventionSkillResultMeasure = assessmentResultMeasures.find(arm => arm.assessmentId === assessmentId);
    const goalSkillScoreValue = goalSkillResultMeasure ? goalSkillResultMeasure.studentScores[0] : undefined;
    let interventionSkillScoreValue;
    if (
      assessmentId !== benchmarkAssessmentId &&
      assessmentResultMeasures.length === 2 &&
      interventions &&
      interventions.length > 0
    ) {
      interventionSkillScoreValue = interventionSkillResultMeasure
        ? interventionSkillResultMeasure.studentScores[0]
        : undefined;
    }
    return { goalSkillScore: goalSkillScoreValue, interventionSkillScore: interventionSkillScoreValue };
  };

  const { goalSkillScore, interventionSkillScore } = useMemo(() => getGoalAndInterventionScore(student), [student]);
  const [selectedGoalAssessmentId, setSelectedGoalAssessmentId] = useState(null);
  const [selectedAssessmentToDisplay, setSelectedAssessmentToDisplay] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isClearScoresModalOpen, setIsClearScoresModalOpen] = useState(false);
  const [isUpdatingScores, setIsUpdatingScores] = useState(false);
  const [initialGoalSkillScore, setInitialGoalSkillScore] = useState(goalSkillScore);
  const [initialInterventionSkillScore, setInitialInterventionSkillScore] = useState(interventionSkillScore);
  const [newGoalSkillScore, setNewGoalSkillScore] = useState(goalSkillScore);
  const [newInterventionSkillScore, setNewInterventionSkillScore] = useState(interventionSkillScore);
  const [shouldShowSaveIndividualScores, setShouldShowSaveIndividualScores] = useState(false);
  const [isEditScoresModalOpen, setIsEditScoresModalOpen] = useState(false);

  // Add scoreHelperTimeoutHandle ref for timeout management
  const scoreHelperTimeoutHandle = React.useRef(null);

  const updateDisplayedScores = () => {
    const { goalSkillScore: newGoalScore, interventionSkillScore: newInterventionScore } = getGoalAndInterventionScore(
      student
    );
    setInitialGoalSkillScore(newGoalScore);
    setInitialInterventionSkillScore(newInterventionScore);
    setNewGoalSkillScore(newGoalScore);
    setNewInterventionSkillScore(newInterventionScore);
  };

  useEffect(() => {
    updateDisplayedScores();
  }, [student]);

  // Define helper functions
  const toggleEditingScores = () => {
    setIsEditing(prev => !prev);
  };

  const toggleEditingScoresModal = () => {
    setIsEditScoresModalOpen(prev => !prev);
  };

  const confirmActionEditModal = () => {
    toggleEditingScoresModal();
    toggleEditingScores();
  };

  const handleEditScoresButton = () => {
    if (isEditing) {
      toggleEditingScores();
    } else {
      toggleEditingScoresModal();
    }
  };

  const shouldShowSaveIndividualScoresButton = ({
    newGoalSkillScore: checkGoalScore = newGoalSkillScore,
    newInterventionSkillScore: checkInterventionScore = newInterventionSkillScore,
    areScoresUnusuallyHigh = false
  }) => {
    const hasGoalSkillScoreChanged = initialGoalSkillScore !== checkGoalScore;
    const hasInterventionSkillScoreChanged = initialInterventionSkillScore !== checkInterventionScore;
    const isGoalSkillScoreValid = isScoreValid(checkGoalScore);
    const isInterventionSkillScoreValid = isScoreValid(checkInterventionScore);
    const isDiagnostic = !initialGoalSkillScore;

    const isAnyScoreEmpty =
      (initialGoalSkillScore !== undefined && checkGoalScore === "") ||
      (initialInterventionSkillScore !== undefined && checkInterventionScore === "");
    const didAnyScoreChange =
      (isGoalSkillScoreValid && hasGoalSkillScoreChanged) ||
      (isInterventionSkillScoreValid && hasInterventionSkillScoreChanged);
    const isDiagnosticScoreValid = isInterventionSkillScoreValid && hasInterventionSkillScoreChanged && isDiagnostic;
    const shouldShowSaveIndividualScoresValue =
      !areScoresUnusuallyHigh && !isAnyScoreEmpty && (didAnyScoreChange || isDiagnosticScoreValid);

    setShouldShowSaveIndividualScores(shouldShowSaveIndividualScoresValue);
  };

  const checkUnusuallyHighScores = ({
    newGoalSkillScore: checkGoalScore = newGoalSkillScore,
    newInterventionSkillScore: checkInterventionScore = newInterventionSkillScore
  }) => {
    // TODO: Need to get completedAssessmentResults from context or props
    // For now, using placeholder logic to avoid errors
    const mockTargets = { assessmentTargets: [10, 20], benchmarkAssessmentTargets: [15, 30] };
    const isUnusuallyHighIndividualInterventionScore = checkInterventionScore > mockTargets.assessmentTargets[1] * 5;
    const isUnusuallyHighGoalSkillScore = checkGoalScore > mockTargets.benchmarkAssessmentTargets[1] * 5;
    return { isUnusuallyHighGoalSkillScore, isUnusuallyHighIndividualInterventionScore };
  };

  const onValueChange = scoreName => e => {
    e.preventDefault();
    clearTimeout(scoreHelperTimeoutHandle.current);
    const elemTarget = e.target;
    const newScore = e.target.value;
    elemTarget.nextElementSibling.className = "help-block invisible";
    const parsedScore = parseInt(newScore);
    let isUnusuallyHighScore = false;
    const newScoreObject = { [scoreName]: newScore };

    if (parsedScore >= 0 || newScore === "") {
      const { isUnusuallyHighGoalSkillScore, isUnusuallyHighIndividualInterventionScore } = checkUnusuallyHighScores(
        newScoreObject
      );
      isUnusuallyHighScore = isUnusuallyHighGoalSkillScore || isUnusuallyHighIndividualInterventionScore;
      if (
        (scoreName === "newInterventionSkillScore" && isUnusuallyHighIndividualInterventionScore) ||
        (scoreName === "newGoalSkillScore" && isUnusuallyHighGoalSkillScore)
      ) {
        elemTarget.nextElementSibling.className = "help-block text-warning text-center animated bounceIn";
      }
      // Update the appropriate state based on scoreName
      if (scoreName === "newGoalSkillScore") {
        setNewGoalSkillScore(newScore);
      } else if (scoreName === "newInterventionSkillScore") {
        setNewInterventionSkillScore(newScore);
      }
    }
    shouldShowSaveIndividualScoresButton({ ...newScoreObject, areScoresUnusuallyHigh: isUnusuallyHighScore });
  };

  const renderInterventionSkillRow = () => {
    return initialInterventionSkillScore !== undefined ? (
      <React.Fragment>
        <hr className="col-sm-9" />
        <label className="col-sm-7">Intervention Skill score</label>
        <div className="col-sm-2 input-no-arrows">
          <input
            className="form-control"
            type="text"
            data-testid="individualInterventionEditStudent"
            value={newInterventionSkillScore}
            onChange={onValueChange("newInterventionSkillScore")}
          />
          <span className="help-block text-center invisible">Unusual High Score</span>
        </div>
      </React.Fragment>
    ) : null;
  };

  const renderGoalSkillRow = () => {
    return initialGoalSkillScore !== undefined ? (
      <React.Fragment>
        <hr className="col-sm-9" />
        <label className="col-sm-7">Goal Skill score</label>
        <div className="col-sm-2 input-no-arrows">
          <input
            className="form-control"
            type="text"
            data-testid="individualInterventionEditGoalStudent"
            value={newGoalSkillScore}
            onChange={onValueChange("newGoalSkillScore")}
          />
          <span className="help-block text-center invisible">Unusual High Score</span>
        </div>
        <hr className="col-sm-9" />
      </React.Fragment>
    ) : null;
  };

  const selectGraphToDisplay = (benchmarkAssessmentId, assessmentId, isGoalSkill) => {
    if (isGoalSkill) {
      setSelectedAssessmentToDisplay(null);
      setSelectedGoalAssessmentId(benchmarkAssessmentId);
    } else {
      setSelectedGoalAssessmentId(assessmentId);
    }
  };

  const getGraphableGoals = () => {
    return skillHistoryHierarchy.filter(sh => sh.studentScores && sh.studentScores.length > 0);
  };

  const renderAllSkills = graphableSkills => {
    if (!graphableSkills.length) {
      return null;
    }
    return (
      <div>
        {graphableSkills.map((skill, index) => (
          <div
            className={getPageBreakClassForEverySecondElementByIndex(index, true)}
            key={`skill_CHART_${student._id}_${index}`}
          >
            <IndividualGraphTitle skill={skill} goalSkill={false} />
            <ClasswidePMChart
              scores={skill}
              chartId={`skill_CHART_${student._id}_${index}`}
              type="chart"
              pmName="Progress Monitoring Scores"
              studentName={`${student.identity.name.firstName} ${student.identity.name.lastName}`}
              studentGroup={studentGroup}
            />
          </div>
        ))}
      </div>
    );
  };

  const renderAllGoals = (graphableGoals, goalSkillSectionClassName = "", startFromFirstIndex = false) => {
    if (!graphableGoals.length) {
      return null;
    }
    return (
      <div className={goalSkillSectionClassName}>
        {graphableGoals.map((goal, index) => {
          const goalCopy = cloneDeep(goal);
          goalCopy.active = false;
          return (
            <div
              className={getPageBreakClassForEverySecondElementByIndex(index, startFromFirstIndex)}
              key={`goal_CHART_${student._id}_${index}`}
            >
              <IndividualGraphTitle skill={goalCopy} goalSkill={true} />
              <ClasswidePMChart
                scores={goalCopy}
                chartId={`goal_CHART_${student._id}_${index}`}
                type="chart"
                pmName="Progress Monitoring Scores"
                studentName={`${student.identity.name.firstName} ${student.identity.name.lastName}`}
                studentGroup={studentGroup}
              />
            </div>
          );
        })}
      </div>
    );
  };

  const getDatesFromScores = (goal, skill) => {
    if (!goal || !goal.studentScores) {
      return null;
    }
    const goalDates = goal.studentScores.map(date => (isObject(date) ? date.x : date[0]));
    let skillDates = [];
    if (skill) {
      skillDates = skill.studentScores.map(date => (isObject(date) ? date.x : date[0]));
    }
    const selectedScoreDates = [...skillDates, ...goalDates];

    return selectedScoreDates.length ? getTimeRange(selectedScoreDates) : null;
  };

  const clearScores = () => {
    if (!student.history.length) {
      return;
    }
    setIsUpdatingScores(true);
    Meteor.call(
      "clearLastIndividualInterventionScores",
      {
        studentGroupId: studentGroup._id,
        studentId: student._id,
        orgid: studentGroup.orgid
      },
      err => {
        if (!err) {
          const studentName = student.identity.name;
          Alert.success(`Successfully cleared score(s) for: ${studentName.lastName}, ${studentName.firstName}`);
          setIsUpdatingScores(false);
          if (isEditing) {
            toggleEditingScores();
          }
        }
      }
    );
  };

  const showSelectedScores = event => {
    event.preventDefault();
    // TODO: Implement showSelectedScores functionality
  };

  const toggleClearScoresModal = () => {
    setIsClearScoresModalOpen(prev => !prev);
  };

  const saveNewScores = () => {
    setIsUpdatingScores(true);
    toggleEditingScores();
    Meteor.call(
      "editLastIndividualInterventionScores",
      {
        studentGroupId: studentGroup._id,
        orgid: studentGroup.orgid,
        studentId: student._id,
        newGoalScoreValue: parseInt(newGoalSkillScore),
        newInterventionScoreValue: parseInt(newInterventionSkillScore)
      },
      (err, { wereScoresEdited, student: updatedStudent, studentGroup: updatedStudentGroup }) => {
        const studentName = student.identity.name;
        if (!err && wereScoresEdited) {
          Alert.success(`Successfully edited score(s) for: ${studentName.lastName}, ${studentName.firstName}`, {
            timeout: 2000
          });
          setIsUpdatingScores(false);
          setStudent(updatedStudent);
          setStudentGroup(updatedStudentGroup);
        } else {
          Alert.error(`There was an error editing score(s) for: ${studentName.lastName}, ${studentName.firstName}`, {
            timeout: 2000
          });
        }
      }
    );
  };

  const renderEditScoreRows = () => {
    return isEditing ? (
      <div className="row mt-1 mb-1 p-l-1">
        {renderInterventionSkillRow()}
        {renderGoalSkillRow()}
      </div>
    ) : null;
  };

  const getSubjectName = () => {
    // TODO: Need to get userPrivilegedRole from context
    return "teacher"; // Default to teacher for now
  };

  const renderConfirmModal = () => {
    let modalDateClearScore = [];
    const scoreDate = student?.history?.[0]?.whenEnded?.on;
    if (scoreDate) {
      modalDateClearScore = new Date(scoreDate).toDateString().split(" ");
    }
    return (
      <ConfirmModal
        showModal={isClearScoresModalOpen}
        onCloseModal={toggleClearScoresModal}
        confirmAction={() => {
          clearScores();
        }}
        headerText="Are you sure you want to clear score(s) for selected graph point(s)?"
        bodyText={<strong>WARNING:</strong>}
        bodyQuestion={`This action will permanently delete the scores for ${modalDateClearScore[1]}-${
          modalDateClearScore[2]
        } and cannot be undone.
                Confirm with the ${getSubjectName()} that they have a paper copy of student score(s) or are willing to
                re-administer the Intervention`}
        confirmText="Yes, clear scores"
        isWarning={true}
      />
    );
  };

  const renderEditConfirmationModal = () => {
    return (
      <ConfirmModal
        showModal={isEditScoresModalOpen}
        onCloseModal={toggleEditingScoresModal}
        confirmAction={confirmActionEditModal}
        headerText="Are you sure you want to edit scores for selected graph point?"
        bodyQuestion={`Before editing scores, it is recommended to verify that the ${getSubjectName()} has access to the paper tests since all changes that you save are final.`}
        confirmText="Acknowledged"
        isWarning={true}
      />
    );
  };

  const renderNoGraphableSkillsNotice = (graphableGoals, graphableSkills) => {
    if (!graphableGoals.length && !graphableSkills.length && isOnPrintPage()) {
      return (
        <div className="alert alert-info text-center">
          No graphable intervention skills or goal skills for this student found
        </div>
      );
    }
    return null;
  };

  const renderIndividualInterventionSkill = ({
    selectedSkill,
    selectedGoalInterventionSkillList,
    timeRange,
    student: studentData,
    selectedGoal,
    selectedGoalAssessmentId: goalAssessmentId
  }) => {
    if (!selectedSkill?.studentScores?.length) {
      return null;
    }

    const assessmentIdPriorToInterventionTimestamps = getDrillDownItemsPriorToIntervention(
      studentData.history,
      goalAssessmentId
    ).map(item => item.whenEnded.on);
    const parsedStudentScores = (
      selectedSkill?.studentScores?.map((sr, index) => {
        if (
          index === 0 &&
          !assessmentIdPriorToInterventionTimestamps.includes(sr.x) &&
          sr.name.includes("Drill-down")
        ) {
          return { x: sr.x };
        }
        return sr;
      }) || []
    ).filter(s => "y" in s);

    return (
      <div className="row">
        <div className="col-md-9 print-clear">
          <div>
            <IndividualGraphTitle skill={selectedSkill} goalSkill={false} />
            <ClasswidePMChart
              scores={{ ...selectedSkill, studentScores: parsedStudentScores }}
              chartId={`skill_CHART_${studentData._id}`}
              type="chart"
              pmName="Progress Monitoring Scores"
              studentName={`${studentData.identity.name.firstName} ${studentData.identity.name.lastName}`}
              timeRange={timeRange}
              scoresClickable={false} // TODO: Get userPrivilegedRole from context
              showSelectedScores={showSelectedScores}
              studentGroup={studentGroup}
            />
          </div>
        </div>
        <div className="col-md-3 print-display">
          <IndividualSkillList
            skillList={selectedGoalInterventionSkillList}
            setSelectedAssessment={selectGraphToDisplay}
            selectedGoal={selectedGoal}
            selectedSkill={selectedSkill}
          />
        </div>
      </div>
    );
  };

  if (!skillHistoryHierarchy || !skillHistoryHierarchy.length > 0) {
    return null;
  }
  // If the user selected a goal skill, filter the intervention skill list to only include those that were done
  // in support of the selected goal skill
  const activeBenchmarkAssessmentId =
    findLast(skillHistoryHierarchy, sh => sh.active)?.goalAssessmentId ||
    findLast(skillHistoryHierarchy, sh => sh.complete)?.goalAssessmentId;

  // Preselect the active benchmarkAssessment if they have something to show, otherwise pick the last complete one
  let currentSelectedGoalAssessmentId;
  const currentCompletedSkillMatchingBenchmarkSkill = findLast(
    skillHistoryHierarchy,
    sh => sh.goalAssessmentId === activeBenchmarkAssessmentId
  );
  if (selectedGoalAssessmentId) {
    currentSelectedGoalAssessmentId = selectedGoalAssessmentId;
  } else if (
    currentCompletedSkillMatchingBenchmarkSkill?.interventions ||
    currentCompletedSkillMatchingBenchmarkSkill?.active
  ) {
    currentSelectedGoalAssessmentId = activeBenchmarkAssessmentId;
  } else {
    currentSelectedGoalAssessmentId = findLast(skillHistoryHierarchy, sh => sh.complete)?.goalAssessmentId;
  }
  const selectedGoalInterventionSkillList = skillHistoryHierarchy.find(
    sh => sh.goalAssessmentId === currentSelectedGoalAssessmentId
  )?.interventions;
  const selectedGoal = skillHistoryHierarchy.find(sh => sh.goalAssessmentId === currentSelectedGoalAssessmentId);
  // Pick which intervention graph to display
  let selectedSkillAssessmentId;
  if (selectedAssessmentToDisplay) {
    selectedSkillAssessmentId = selectedAssessmentToDisplay;
  } else if (selectedGoalInterventionSkillList && selectedGoalInterventionSkillList.find(skill => skill.active)) {
    selectedSkillAssessmentId = selectedGoalInterventionSkillList.find(skill => skill.active).assessmentId;
  } else if (selectedGoalInterventionSkillList && selectedGoalInterventionSkillList.length) {
    // Select the last one in the list -- should be most recent performed
    selectedSkillAssessmentId =
      selectedGoalInterventionSkillList[selectedGoalInterventionSkillList.length - 1].assessmentId;
  }

  const assessmentIdsWithDrillDownPriorToInterventionAndInterventions = uniq([
    ...getDrillDownItemsPriorToIntervention(student.history, currentSelectedGoalAssessmentId).map(d => d.assessmentId),
    ...student.history
      .filter(h => h.benchmarkAssessmentId === currentSelectedGoalAssessmentId && h.interventions.length)
      .map(h => h.assessmentId)
  ]);
  const filteredGoalSkillList =
    selectedGoalInterventionSkillList?.filter(skill =>
      assessmentIdsWithDrillDownPriorToInterventionAndInterventions.includes(skill.assessmentId)
    ) || [];
  const selectedSkill =
    filteredGoalSkillList.find(skill => skill.assessmentId === selectedSkillAssessmentId) ||
    filteredGoalSkillList[filteredGoalSkillList.length - 1];
  const selectedGoalHasScores = selectedGoal && selectedGoal.studentScores && selectedGoal.studentScores.length > 0;

  const timeRange = getDatesFromScores(selectedGoal, selectedSkill);
  const graphableGoals = getGraphableGoals();
  const graphableSkills = [].concat(...graphableGoals.map(goal => goal.interventions || []));
  graphableSkills.sort(
    (a, b) =>
      a.active - b.active ||
      a.studentScores[a.studentScores.length - 1].x - b.studentScores[b.studentScores.length - 1].x
  );

  const className = !props.isFirstSection ? "page-break-before" : "";
  const shouldAddPageBreakForGoalSkillSection =
    (props.printAllIndividualInterventionGraphs && graphableSkills?.length > 0) ||
    (!props.printAllIndividualInterventionGraphs && selectedSkill);
  const goalSkillSectionClassName = shouldAddPageBreakForGoalSkillSection ? "page-break-before" : "";
  const shouldAddPageBreakAfterFirstGoalSkill = !shouldAddPageBreakForGoalSkillSection && props.isFirstSection;

  const canManageScores = userSiteAccess.find(
    sa =>
      sa.role === "arbitraryIdsuperAdmin" ||
      sa.role === "arbitraryIduniversalDataAdmin" ||
      (sa.role === "arbitraryIddataAdmin" && user?.profile.orgid === studentGroup.orgid)
  );

  return (
    <div data-testid="individual-intervention-progress-section" className={className}>
      <h3>Individual Intervention Progress</h3>
      {renderNoGraphableSkillsNotice(graphableGoals, graphableSkills)}
      {props.printAllIndividualInterventionGraphs
        ? renderAllSkills(graphableSkills)
        : renderIndividualInterventionSkill({
            selectedSkill,
            selectedGoalInterventionSkillList: filteredGoalSkillList,
            timeRange,
            student,
            selectedGoal,
            selectedGoalAssessmentId: currentSelectedGoalAssessmentId
          })}
      <br />
      {props.printAllIndividualInterventionGraphs ? (
        renderAllGoals(graphableGoals, goalSkillSectionClassName, shouldAddPageBreakAfterFirstGoalSkill)
      ) : (
        <div className={`row ${goalSkillSectionClassName}`}>
          <div className="col-md-9 print-clear">
            <IndividualGraphTitle skill={selectedGoal} goalSkill={true} />
            {selectedGoalHasScores ? (
              <ClasswidePMChart
                scores={selectedGoal}
                chartId={`goal_CHART_${student._id}`}
                type="chart"
                pmName="Progress Monitoring Scores"
                studentName={`${student.identity.name.firstName} ${student.identity.name.lastName}`}
                timeRange={timeRange}
                scoresClickable={false} // TODO: Get userPrivilegedRole from context
                showSelectedScores={showSelectedScores}
                studentGroup={studentGroup}
              />
            ) : (
              <h5 className="stamped">No new scores have been recorded since screening.</h5>
            )}
          </div>
          <div className="col-md-3 print-display">
            <IndividualGoalSkillList
              goalSkillList={skillHistoryHierarchy}
              setSelectedAssessment={selectGraphToDisplay}
              selectedGoal={selectedGoal}
              activeBMPeriodIds={activeBMPeriodIds}
            />
          </div>
        </div>
      )}
      {canManageScores && student.history?.length ? (
        <React.Fragment>
          <div className="row">
            <div className="col-md-9">
              {isUpdatingScores ? (
                <div className="pull-right">
                  <Loading inline={true} message="Updating scores..." />
                </div>
              ) : (
                <div className="pull-right mb-1">
                  {shouldShowSaveIndividualScores ? (
                    <button className="btn btn-success" onClick={saveNewScores}>
                      Save
                    </button>
                  ) : null}{" "}
                  <button className="btn btn-primary" onClick={handleEditScoresButton}>
                    {isEditing ? "Cancel Editing Scores" : "Edit Scores"}
                  </button>{" "}
                  <button className="btn btn-danger" onClick={toggleClearScoresModal}>
                    Clear Scores
                  </button>
                </div>
              )}
            </div>
          </div>
          {renderEditScoreRows()}
          {renderConfirmModal()}
          {renderEditConfirmationModal()}
        </React.Fragment>
      ) : null}
    </div>
  );
}

IndividualInterventionProgress.propTypes = {
  printAllIndividualInterventionGraphs: PropTypes.bool.isRequired,
  isFirstSection: PropTypes.bool,
  studentGroup: PropTypes.object,
  student: PropTypes.object
};

export function getDrillDownItemsPriorToIntervention(studentHistory, selectedGoalAssessmentId) {
  const itemsPriorToIntervention = [];
  let filteredItems = studentHistory;
  if (selectedGoalAssessmentId) {
    filteredItems = studentHistory.filter(h => h.benchmarkAssessmentId === selectedGoalAssessmentId);
  }
  filteredItems.forEach((item, index) => {
    if (item?.interventions.length && filteredItems[index + 1] && !filteredItems[index + 1]?.interventions.length) {
      itemsPriorToIntervention.push(filteredItems[index + 1]);
    }
  });
  return itemsPriorToIntervention;
}

export function getScoreAndDate(historyItem, type) {
  const assessmentId = type === "benchmark" ? historyItem.benchmarkAssessmentId : historyItem.assessmentId;
  const arm = historyItem.assessmentResultMeasures.find(am => am.assessmentId === assessmentId);
  const point = {
    x: historyItem.whenEnded.on,
    y: arm && arm.medianScore
  };
  if (historyItem.interventions && !historyItem.interventions.length) {
    point.name = `Drill-down - ${new Date(historyItem.whenEnded.on).toISOString().slice(0, 10)}`;
  }
  return [point];
}

function getRoiValue(scores) {
  const goalScores = scores.map(score => score[1]);
  const goalDates = scores.map(score => new Date(score[0]));
  return calculateRoI(goalDates, goalScores);
}

function updateGoalSkillsData(skillHistoryHierarchy = []) {
  // skillHistoryHierarchy and studentScores already sorted - prepared for graph so order will always be the same
  const allStudentScores = get(skillHistoryHierarchy, "skillHistoryHierarchy[0].studentScores[0]", []);
  const [firstGoalSkillTimestamp, firstGoalSkillScore] = allStudentScores;

  return skillHistoryHierarchy.map(goalSkill => {
    // eslint-disable-next-line no-param-reassign
    goalSkill.drillDownOnlyScore =
      typeof goalSkill.drillDownOnlyScore !== "undefined"
        ? goalSkill.drillDownOnlyScore
        : !goalSkill.interventions?.length;
    if (firstGoalSkillTimestamp && typeof firstGoalSkillScore !== "undefined") {
      const studentScores = goalSkill.studentScores ? goalSkill.studentScores : [];
      return {
        ...goalSkill,
        studentScores,
        roi: getRoiValue(studentScores, firstGoalSkillScore, firstGoalSkillTimestamp)
      };
    }
    return goalSkill;
  });
}

export function getSkillHistoryHierarchy(allActivities, goalSkillContainerSeed, currentSkill) {
  // activities get parsed into displayed scores
  const unsortedSkillHistoryHierarchy = allActivities.reduce((a, c) => {
    const goalSkillContainer = a.find(gsc => gsc.goalAssessmentId === c.benchmarkAssessmentId);
    if (!goalSkillContainer) {
      return a;
    }
    if (!goalSkillContainer.masteryTarget) {
      // eslint-disable-next-line prefer-destructuring
      goalSkillContainer.masteryTarget = c.benchmarkAssessmentTargets[1];
    }
    if (!goalSkillContainer.instructionalTarget) {
      // eslint-disable-next-line prefer-destructuring
      goalSkillContainer.instructionalTarget = c.benchmarkAssessmentTargets[0];
    }
    // need to add goalSkill?
    if (c.assessmentId === c.benchmarkAssessmentId || c.assessmentResultMeasures.length > 1) {
      if (!goalSkillContainer.studentScores) {
        goalSkillContainer.studentScores = [];
      }
      goalSkillContainer.studentScores.unshift(getScoreAndDate(c, "benchmark"));
      goalSkillContainer.studentScores = goalSkillContainer.studentScores.flat(1);
      goalSkillContainer.drillDownOnlyScore = !c.interventions.length;
    }
    // need to add intervention score?
    if (c.benchmarkAssessmentId !== c.assessmentId) {
      if (!goalSkillContainer.interventions) {
        goalSkillContainer.interventions = [];
      }
      let interventionContainer = goalSkillContainer.interventions.find(
        intervention => intervention.assessmentId === c.assessmentId
      );
      if (!interventionContainer) {
        const active = currentSkill && !currentSkill.whenEnded && currentSkill.assessmentId === c.assessmentId;
        interventionContainer = {
          active,
          complete: !active,
          drillDownOnlyScore: !c.interventions.length,
          assessmentId: c.assessmentId,
          masteryTarget: c.assessmentTargets[1],
          instructionalTarget: c.assessmentTargets[0],
          assessmentName: c.assessmentName
        };
        goalSkillContainer.interventions.unshift(interventionContainer);
      }
      if (!interventionContainer.studentScores) {
        interventionContainer.studentScores = [];
      }
      interventionContainer.studentScores.unshift(getScoreAndDate(c));
      interventionContainer.studentScores = interventionContainer.studentScores.flat(1);
      interventionContainer.drillDownOnlyScore = interventionContainer.drillDownOnlyScore && !c.interventions.length;
    }
    return a;
  }, goalSkillContainerSeed);
  return unsortedSkillHistoryHierarchy.sort((a, b) => (a.sortOrder < b.sortOrder ? -1 : 1));
}

export function getFoundationalSkillData({
  student,
  benchmarkPeriodId,
  rootRuleAssessments,
  screeningAssessmentIds,
  groupBenchmarkScores = []
}) {
  // Get All activities to process
  let allActivities = JSON.parse(JSON.stringify(student.history || []));
  allActivities = allActivities.filter(
    activity => activity.type !== "benchmark" && activity.benchmarkPeriodId === benchmarkPeriodId
  );
  // Make the skeleton structures for the goalSkills
  const goalSkillContainerSeed = rootRuleAssessments
    .filter(rule => screeningAssessmentIds.includes(rule.attributeValues.assessmentId))
    .map(rule => {
      const assessmentName = get(Assessments.findOne(rule.attributeValues.assessmentId), "name", rule.name) || "N/A";
      const active =
        student.currentSkill &&
        !student.currentSkill.whenEnded &&
        student.currentSkill.benchmarkAssessmentId === rule.attributeValues.assessmentId;
      const groupBenchmarkRuleScores = groupBenchmarkScores.find(
        item => item.assessmentId === rule.attributeValues.assessmentId
      );
      const hasCompletedSkillWithIndividualInterventionOrDrillDown = allActivities.some(
        h => h.benchmarkAssessmentId === rule.attributeValues.assessmentId
      );
      const hasCompletedFirstSkillWithScreening =
        screeningAssessmentIds?.[0] === rule.attributeValues.assessmentId &&
        groupBenchmarkRuleScores?.studentResults?.some(
          result => result.studentId === student._id && result.meetsTarget
        );
      const skillData = {
        goalAssessmentId: rule.attributeValues.assessmentId,
        benchmarkPeriodId: translateBenchmarkPeriod(rule.attributeValues.benchmarkPeriod).id,
        assessmentName,
        active,
        complete:
          (hasCompletedSkillWithIndividualInterventionOrDrillDown || hasCompletedFirstSkillWithScreening) && !active,
        sortOrder: screeningAssessmentIds.indexOf(rule.attributeValues.assessmentId)
      };
      if (groupBenchmarkRuleScores) {
        const [instructionalTarget, masteryTarget] = groupBenchmarkRuleScores.targetScores;
        return { ...skillData, instructionalTarget, masteryTarget };
      }
      return skillData;
    });
  return { allActivities, goalSkillContainerSeed };
}

export function getActiveBMPeriods(studentHistory = []) {
  return studentHistory.reduceRight((a, c) => {
    if (c.benchmarkPeriodId && a.indexOf(c.benchmarkPeriodId) < 0) {
      a.push(c.benchmarkPeriodId);
    }
    return a;
  }, []);
}

function getHistoryHierarchy(student, studentGroup, activeBMPeriodIds) {
  let skillHistoryHierarchy = [];
  // Build the final skillHistoryHierarchy by looping through each of the periods
  // in which the student was active in individual interventions
  const groupBenchmarkScores = [];
  const groupBenchmarkHistory = studentGroup.history
    ? studentGroup.history.filter(
        item => item.type === "benchmark" && activeBMPeriodIds.includes(item.benchmarkPeriodId)
      )
    : [];
  if (groupBenchmarkHistory.length) {
    groupBenchmarkHistory.map(item => groupBenchmarkScores.push(...item.assessmentResultMeasures));
  }

  activeBMPeriodIds.forEach(activeBMPeriodId => {
    const rootRuleAssessments = Rules.find({
      "attributeValues.grade": studentGroup.grade,
      "attributeValues.benchmarkPeriod": translateBenchmarkPeriod(activeBMPeriodId).label
    })
      .fetch()
      .filter(rule => rule._id === rule.rootRuleId);
    // Get the screening Assignments so that we can order the Goal Skills properly
    const screeningAssessmentIds = ScreeningAssignments.findOne({
      grade: student.grade,
      benchmarkPeriodId: activeBMPeriodId
    });
    const { allActivities, goalSkillContainerSeed } = getFoundationalSkillData({
      student,
      benchmarkPeriodId: activeBMPeriodId,
      rootRuleAssessments,
      screeningAssessmentIds: (screeningAssessmentIds && screeningAssessmentIds.assessmentIds) || [],
      groupBenchmarkScores
    });

    const hasCorrectSkillHistory = goalSkillContainerSeed.some(gs => gs.active || gs.complete);
    if (hasCorrectSkillHistory) {
      let newSkillHistoryHierarchy = getSkillHistoryHierarchy(
        allActivities,
        goalSkillContainerSeed,
        student.currentSkill
      );
      newSkillHistoryHierarchy = updateGoalSkillsData(newSkillHistoryHierarchy);
      skillHistoryHierarchy = [...skillHistoryHierarchy, ...newSkillHistoryHierarchy];
    }
  });
  return skillHistoryHierarchy;
}

function isScoreValid(score) {
  return parseInt(score) >= 0;
}
