import get from "lodash/get";
import { Assessments } from "/imports/api/assessments/assessments.js";

const createStudentScoreContainer = (students, stuResult) => {
  const currentStudent = students.find(stu => stu._id === stuResult.studentId);
  let name;
  let lastName;
  if (currentStudent) {
    name = `${currentStudent.identity.name.firstName} ${currentStudent.identity.name.lastName}`;
    lastName = currentStudent.identity.name.lastName;
  } else {
    name = `${stuResult.firstName} ${stuResult.lastName}`;
    lastName = stuResult.lastName;
  }
  return {
    studentId: stuResult.studentId,
    lastName,
    name,
    data: []
  };
};

const findStudentScore = (allStudentsScores, stuResult) =>
  allStudentsScores.find(stuScoreContainer => stuScoreContainer.studentId === stuResult.studentId);

const sortByLastNameHelper = (a, b) => {
  let sortResult = 0;
  if (a.lastName < b.lastName) {
    sortResult = -1;
  } else if (a.lastName > b.lastName) {
    sortResult = 1;
  }
  return sortResult;
};

const generateAbsentStudentsEmptyScores = (assResultMeasure, groupSkill) => {
  const absentStudentsScores = [];
  // Add null score to students that were absent during intervention to display the graph correctly
  if (assResultMeasure.studentResults.length !== groupSkill.enrolledStudentIds.length) {
    const studentsWithoutScores = groupSkill.enrolledStudentIds.filter(
      sid => !assResultMeasure.studentResults.find(result => result.studentId === sid)
    );

    studentsWithoutScores.forEach(sid => {
      absentStudentsScores.push({
        studentId: sid,
        score: null
      });
    });
  }
  return absentStudentsScores;
};

const addAllStudentsScores = (assResultMeasure, students, date, allStudentsScores) => {
  assResultMeasure.studentResults.forEach(stuResult => {
    if (!findStudentScore(allStudentsScores, stuResult)) {
      allStudentsScores.push(createStudentScoreContainer(students, stuResult));
    }
    findStudentScore(allStudentsScores, stuResult).data.unshift([
      date,
      stuResult.score ? parseInt(stuResult.score) : null
    ]);
  });
};

const findAssessmentResultMeasure = groupSkill =>
  groupSkill.assessmentResultMeasures.find(arm => arm.assessmentId === groupSkill.assessmentId);

const findStudentResult = (assResultMeasure, student) =>
  assResultMeasure && student && assResultMeasure.studentResults.find(sr => sr.studentId === student._id);

const isCurrentSkill = (groupSkill, rule) => groupSkill.assessmentId === rule.assessmentId;

const isSkillActive = (studentGroup, rule) =>
  // This rule is active if it is the same as the one that the group is currently practicing
  studentGroup.currentClasswideSkill && rule.assessmentId === studentGroup.currentClasswideSkill.assessmentId;

const getScoresHistoryForGradeLevelSkill = async (
  rule,
  studentGroup,
  classwideActivities,
  student,
  students,
  isAdditionalSkillList
) => {
  const active = isSkillActive(studentGroup, rule);
  let complete = false;

  const classMedianScores = [];
  const studentScores = [];
  const allStudentsScores = [];
  let instructionalTarget = null;
  let masteryTarget = null;
  // Need to loop through groupActivity and compile scores
  classwideActivities
    .filter(groupSkill => isCurrentSkill(groupSkill, rule))
    .forEach(filteredGroupSkill => {
      complete = complete || isCurrentSkill(filteredGroupSkill, rule);

      const date = filteredGroupSkill.whenEnded && filteredGroupSkill.whenEnded.on;
      const assResultMeasure =
        filteredGroupSkill.assessmentResultMeasures && findAssessmentResultMeasure(filteredGroupSkill);

      if (assResultMeasure) {
        const classScore = assResultMeasure && assResultMeasure.medianScore;
        classMedianScores.unshift([date, classScore]);
        instructionalTarget =
          instructionalTarget || (assResultMeasure.targetScores && assResultMeasure.targetScores[0]);
        masteryTarget = masteryTarget || (assResultMeasure.targetScores && assResultMeasure.targetScores[1]);

        assResultMeasure.studentResults = [
          ...assResultMeasure.studentResults,
          ...generateAbsentStudentsEmptyScores(assResultMeasure, filteredGroupSkill)
        ];

        // If we were not given a student we should compile all the students results
        if (!student) {
          addAllStudentsScores(assResultMeasure, students, date, allStudentsScores);
        } else {
          // otherwise only compile the selected students results
          const studentResult = findStudentResult(assResultMeasure, student);
          const studentScore = studentResult && parseInt(studentResult.score);
          studentScores.unshift([date, studentScore]);
        }
      } else {
        // find the targets anyway
        instructionalTarget = get(filteredGroupSkill, "targets[0]", 0);
        masteryTarget = get(filteredGroupSkill, "targets[1]", 0);
      }
    });
  allStudentsScores.sort((a, b) => sortByLastNameHelper(a, b));

  const assessment = await Assessments.findOneAsync({ _id: rule.assessmentId });
  const assessmentName = assessment ? assessment.name : "N/A";
  return {
    id: rule.assessmentId,
    key: rule.assessmentId,
    name: assessmentName,
    instructionalTarget,
    masteryTarget,
    complete,
    active,
    classMedianScores,
    studentScores,
    allStudentsScores,
    isAdditional: isAdditionalSkillList
  };
};

const getSkillHistory = async (
  allActivities,
  gradeLevelRules,
  studentGroup,
  students,
  student,
  isAdditionalSkillList = false
) => {
  if (allActivities && studentGroup.currentClasswideSkill) {
    allActivities.unshift(studentGroup.currentClasswideSkill);
  }
  const classwideActivities = allActivities.filter(activity => activity.type !== "benchmark");

  return Promise.all(
    (gradeLevelRules?.skills || []).map(rule =>
      getScoresHistoryForGradeLevelSkill(
        rule,
        studentGroup,
        classwideActivities,
        student,
        students,
        isAdditionalSkillList
      )
    )
  );
};

export default getSkillHistory;
