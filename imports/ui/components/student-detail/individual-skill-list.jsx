import React, { Component } from "react";
import PropTypes from "prop-types";
import { orderBy } from "lodash";
import { getIconClassName, getLabelClasses } from "./skill-list-styling";

export default class IndividualSkillList extends Component {
  renderSkills() {
    const skillList = this.props.skillList || [];
    return (
      <div>
        <div className="row">
          <div className="col-md-12 intervention-dots">
            {skillList && skillList.length ? (
              <div className="skill-list-title">
                Interventions
                <label>&nbsp;for {this.props.selectedGoal.assessmentName} Goal Skill</label>
              </div>
            ) : null}
            <div className="intervention-skill-list">
              {skillList && skillList.length
                ? orderBy(skillList, "studentScores.0.x", "desc").map((skillInfo, i) =>
                    this.renderIndividualSkillList(skillInfo, i)
                  )
                : null}
            </div>
          </div>
        </div>
      </div>
    );
  }

  renderIndividualSkillList(skillInfo, i) {
    let onclick = () => {};
    if (skillInfo.studentScores && skillInfo.studentScores.length) {
      onclick = () => {
        this.props.setSelectedAssessment(null, skillInfo.assessmentId, false);
      };
    }
    const interventionSkillIcon = getIconClassName(skillInfo);
    const isSelectedSkill =
      this.props.selectedSkill && this.props.selectedSkill.assessmentId === skillInfo.assessmentId;
    return (
      <div key={i} className="skill-item individual-skill-item clearfix" onClick={onclick}>
        <i className={interventionSkillIcon} />
        <label className={getLabelClasses(skillInfo, isSelectedSkill)}>{skillInfo.assessmentName}</label>
      </div>
    );
  }

  render() {
    return (
      <div className="skill-progress">
        <div className="skill-map">
          <div className="intervention-dots">{this.renderSkills()}</div>
        </div>
      </div>
    );
  }
}

IndividualSkillList.propTypes = {
  selectedGoal: PropTypes.object,
  setSelectedAssessment: PropTypes.func,
  selectedSkill: PropTypes.object,
  skillList: PropTypes.array
};
