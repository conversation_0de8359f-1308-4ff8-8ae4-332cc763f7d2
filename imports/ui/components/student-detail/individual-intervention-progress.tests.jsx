import { assert } from "chai";

import {
  getSkillHistoryHierarchy,
  getActiveBMPeriods,
  getFoundationalSkillData,
  getScoreAndDate,
  getDrillDownItemsPriorToIntervention
} from "./individual-intervention-progress.jsx";
import { Assessments } from "/imports/api/assessments/assessments";

const scoreDate = new Date();
const benchmarkPeriodId = "8S52Gz5o85hRkECgq";
const student = {
  currentSkill: {
    benchmarkPeriodId,
    benchmarkAssessmentId: "BENCHMARK_1",
    assessmentId: "INTERVENTION_1",
    type: "individual",
    benchmarkAssessmentTargets: [1, 2, 3],
    assessmentTargets: [1, 2, 3],
    assessmentResultMeasures: [],
    interventions: []
  },
  history: [
    {
      benchmarkPeriodId,
      benchmarkAssessmentId: "BENCHMARK_1",
      assessmentId: "INTERVENTION_1",
      type: "individual",
      benchmarkAssessmentTargets: [1, 2, 3],
      assessmentTargets: [1, 2, 3],
      whenEnded: {
        date: scoreDate,
        on: scoreDate.getTime()
      },
      assessmentResultMeasures: [
        {
          assessmentId: "INTERVENTION_1",
          medianScore: 5
        }
      ],
      interventions: [
        {
          interventionId: "56attvmgjtrjMsFif",
          interventionLabel: "Intervention Adviser - Cover Copy and Compare",
          interventionAbbrv: "CCC"
        }
      ]
    }
  ]
};
const rootRuleAssessments = [
  {
    attributeValues: {
      assessmentId: "BENCHMARK_2",
      benchmarkPeriod: "fall-period"
    }
  },
  {
    attributeValues: {
      assessmentId: "BENCHMARK_1",
      benchmarkPeriod: "fall-period"
    }
  },
  {
    attributeValues: {
      assessmentId: "BENCHMARK_3",
      benchmarkPeriod: "fall-period"
    }
  }
];

const assessments = [
  {
    _id: "BENCHMARK_1",
    name: "BENCHMARK_1_NAME"
  },
  {
    _id: "BENCHMARK_2",
    name: "BENCHMARK_2_NAME"
  },
  {
    _id: "BENCHMARK_3",
    name: "BENCHMARK_3_NAME"
  }
];

const groupBenchmarkScores = [
  {
    assessmentId: "BENCHMARK_1",
    targetScores: [10, 20]
  },
  {
    assessmentId: "BENCHMARK_2",
    targetScores: [30, 40]
  },
  {
    assessmentId: "BENCHMARK_3",
    targetScores: [50, 60]
  }
];
const screeningAssessmentIds = ["BENCHMARK_1", "BENCHMARK_2", "BENCHMARK_3"];

describe("getActiveBMPeriods helper function tests", () => {
  it("should return an empty array with no student history ", () => {
    const result = getActiveBMPeriods([]);
    assert.equal(result.length, 0, "should be an empty array");
  });
  it("array should be length of one", () => {
    const result = getActiveBMPeriods([{ benchmarkPeriodId: "FALL_ID" }, { benchmarkPeriodId: "FALL_ID" }]);
    assert.equal(result.length, 1, "should have a length of one");
  });
  it("should be an array of length two", () => {
    const result = getActiveBMPeriods([{ benchmarkPeriodId: "FALL_ID" }, { benchmarkPeriodId: "WINTER_ID" }]);
    assert.equal(result.length, 2, "should have a length of two");
  });
});

describe("getFoundationalSkillData helper function tests", () => {
  beforeAll(() => {
    assessments.forEach(async assessment => Assessments.insertAsync(assessment));
  });
  afterAll(async () => {
    await Assessments.removeAsync({});
  });
  it("should return an object", () => {
    const result = getFoundationalSkillData({
      student: {},
      benchmarkPeriodId: "FALL_ID",
      rootRuleAssessments: [],
      screeningAssessmentIds: []
    });
    assert.equal(typeof result, "object", "should be an object");
    assert.equal(result.allActivities.length, 0, "allActivities should be an array of length 0");
    assert.equal(result.goalSkillContainerSeed.length, 0, "goalSkillContainerSeed should be an array of length 0");
  });
  describe("when building an object", () => {
    let result;
    beforeEach(() => {
      result = getFoundationalSkillData({
        student,
        benchmarkPeriodId,
        rootRuleAssessments,
        screeningAssessmentIds
      });
    });
    describe("allActivities", () => {
      it("should have a length of 1", () => {
        assert.equal(result.allActivities.length, 1, "Should have a length of 1");
      });
    });

    describe("goalSkillContainerSeed", () => {
      it("should have the same length as the number of benchmarkAssessments", () => {
        assert.equal(
          result.goalSkillContainerSeed.length,
          rootRuleAssessments.length,
          `should be of length ${rootRuleAssessments.length}`
        );
      });

      it("should have a sortOrder that matches the screeningAssessmentIds array order", () => {
        const { sortOrder } = result.goalSkillContainerSeed.find(goal => goal.goalAssessmentId === "BENCHMARK_2");
        assert.equal(sortOrder, 1, "should have a sortOrder of 1");
      });

      it("should contain mastery and instructional target if those are available in studentGroup history", () => {
        const { goalSkillContainerSeed } = getFoundationalSkillData({
          student,
          benchmarkPeriodId,
          rootRuleAssessments,
          screeningAssessmentIds,
          groupBenchmarkScores
        });

        goalSkillContainerSeed.forEach(goalSkill => {
          assert.property(goalSkill, "masteryTarget", "The mastery target was not found");
          assert.property(goalSkill, "instructionalTarget", "The instructional target was not found");
        });
      });
    });
  });
});

describe("getSkillHistoryHierarchy helper function tests", () => {
  beforeAll(() => {
    assessments.forEach(async assessment => Assessments.insertAsync(assessment));
  });
  afterAll(async () => {
    await Assessments.removeAsync({});
  });
  describe("with no current student history", () => {
    it("should return an empty array", () => {
      const result = getSkillHistoryHierarchy([], []);
      assert.equal(result.length, 0, "should be an empty array");
    });
  });

  describe("with test data consisting of three goal assessments", () => {
    let result;
    beforeAll(() => {
      const foundation = getFoundationalSkillData({
        student,
        benchmarkPeriodId,
        rootRuleAssessments,
        screeningAssessmentIds
      });
      result = getSkillHistoryHierarchy(
        foundation.allActivities,
        foundation.goalSkillContainerSeed,
        student.currentSkill
      );
    });

    it("should return a skillHistoryHierarchy of three items", () => {
      assert.equal(result.length, 3, "Should have three items in the skillHistoryHierarchy for each goalAssessment");
    });
    it("the first skillHistoryHierarchy should have a sortOrder of 0", () => {
      assert.equal(result[0].sortOrder, 0, "The first item should have a sortOrder of 0");
    });
    it("the first skillHistoryHierarchy should have interventions with a length greater than 0", () => {
      assert.equal(
        result[0].interventions.length > 0,
        true,
        "The interventions on the first item should have length greater than 0"
      );
    });
    it("should return follow-up(drill-down) and use appropriate name", () => {
      const newFoundation = getFoundationalSkillData({
        student: { ...student, history: [{ ...student.history[0], interventions: [] }] },
        benchmarkPeriodId,
        rootRuleAssessments,
        screeningAssessmentIds
      });
      result = getSkillHistoryHierarchy(
        newFoundation.allActivities,
        newFoundation.goalSkillContainerSeed,
        student.currentSkill
      );

      const resultInterventions = result.find(mr => mr.interventions).interventions;
      expect(resultInterventions.length).toEqual(1);
      expect(resultInterventions[0].studentScores[0].name.includes("Drill-down")).toBe(true);
    });
  });
});

describe("getScoreAndDate helper function tests", () => {
  it("should return an object with y and name properties", () => {
    const result = getScoreAndDate(student.history[0]);
    const scoreIndex = 0;
    assert.equal(result[scoreIndex].y, 5, "should have a score of 5");
    assert.equal(typeof result[scoreIndex].x, "number", "should have a date as a timestamp");
  });
});

describe("getDrillDownItemsPriorToIntervention", () => {
  const goalSkillId = "goalSkillId";
  const otherGoalSkillId = "otherGoalSkillId";
  const defaultStudentHistory = [
    {
      benchmarkAssessmentId: goalSkillId,
      interventions: [],
      whenEnded: {
        on: 1678970024882
      }
    },
    {
      benchmarkAssessmentId: goalSkillId,
      interventions: [{}, {}],
      whenEnded: {
        on: 1678797063094
      }
    },
    {
      benchmarkAssessmentId: goalSkillId,
      interventions: [{}, {}, {}],
      whenEnded: {
        on: 1678796778483
      }
    },
    {
      benchmarkAssessmentId: goalSkillId,
      interventions: [],
      whenEnded: {
        on: 1678796730959
      }
    },
    {
      benchmarkAssessmentId: goalSkillId,
      interventions: [],
      whenEnded: {
        on: 1678796705307
      }
    },
    {
      benchmarkAssessmentId: otherGoalSkillId,
      interventions: [{}, {}],
      whenEnded: {
        on: 1678796576764
      }
    },
    {
      benchmarkAssessmentId: otherGoalSkillId,
      interventions: [],
      whenEnded: {
        on: 1678796560085
      }
    }
  ];

  it("should return empty array if no drill-down history items prior to intervention exist", () => {
    const studentHistory = [];
    const selectedGoalAssessmentId = "";

    const result = getDrillDownItemsPriorToIntervention(studentHistory, selectedGoalAssessmentId);
    expect(result).toEqual([]);
  });

  it("should only return items that are prior to intervention while using student history", () => {
    const result = getDrillDownItemsPriorToIntervention(defaultStudentHistory, goalSkillId);
    expect(result).toEqual([
      {
        benchmarkAssessmentId: goalSkillId,
        interventions: [],
        whenEnded: {
          on: 1678796730959
        }
      }
    ]);
  });

  it("should only return items that are prior to intervention for multiple drill-downs prior to screening within one goal skill", () => {
    const studentHistory = [
      {
        benchmarkAssessmentId: goalSkillId,
        interventions: [{}, {}],
        whenEnded: {
          on: 1678970044882
        }
      },
      ...defaultStudentHistory
    ];
    const result = getDrillDownItemsPriorToIntervention(studentHistory, goalSkillId);
    expect(result).toEqual([
      {
        benchmarkAssessmentId: goalSkillId,
        interventions: [],
        whenEnded: {
          on: 1678970024882
        }
      },
      {
        benchmarkAssessmentId: goalSkillId,
        interventions: [],
        whenEnded: {
          on: 1678796730959
        }
      }
    ]);
  });
});
