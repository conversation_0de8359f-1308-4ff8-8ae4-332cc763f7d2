import getSkillHistory from "./getSkillHistory";
import { Assessments } from "/imports/api/assessments/assessments";

const assessment1Id = "testAssessmentId1";
const assessment1Date = new Date(1528190617478);
const assessment1Name = "testAssessmentName1";
const assessment1Score = "27";
const assessment2Id = "testAssessmentId2";
const assessment2Name = "testAssessmentName2";
const assessment2Score = "12";
const assessment3Id = "testAssessmentId3";
const assessment3Date = new Date(1529190617478);
const assessment3Name = "testAssessmentName3";
const assessment3Score = "1";
const assessment4Score = "16";

const student1Id = "testStudent1";
const student2Id = "testStudent2";

const students = [
  {
    _id: student1Id,
    identity: {
      name: { firstName: "TestFirstName1", lastName: "TestLastName1" }
    }
  }
];

const assessments = [
  {
    _id: assessment1Id,
    name: assessment1Name
  },
  {
    _id: assessment2Id,
    name: assessment2Name
  },
  {
    _id: assessment3Id,
    name: assessment3Name
  }
];

const gradeLevelRules = {
  _id: "test_class_wide_rule_1",
  orgid: "test_organization_id",
  name: "Grade 3 Class Wide Intervention",
  description: "Grade 3 Class Wide Intervention",
  grade: "03",
  skills: [
    {
      assessmentId: assessment1Id,
      assessmentName: assessment1Name,
      interventions: []
    },
    {
      assessmentId: assessment2Id,
      assessmentName: assessment2Name,
      interventions: []
    },
    {
      assessmentId: assessment3Id,
      assessmentName: assessment3Name,
      interventions: []
    }
  ],
  enabled: true,
  ruleDefinitionId: "test_rule_definition",
  conditionTypeId: "INTAClassWideDecisionTree",
  created: {
    by: "TEST",
    date: Date.parse("2016-10-17T15:25:17.836+0000"),
    on: 1476717917836.0
  },
  lastModified: {
    by: "TEST",
    date: Date.parse("2016-10-17T15:25:17.836+0000"),
    on: 1476717917836.0
  }
};

const groupHistory = [
  {
    type: "classwide",
    whenEnded: { date: assessment1Date, on: assessment1Date.toISOString() },
    assessmentId: assessment1Id,
    targets: [32, 64, 300],
    assessmentResultMeasures: [
      {
        assessmentId: assessment1Id,
        assessmentName: assessment1Name,
        studentResults: [
          { studentId: student1Id, score: assessment1Score },
          { studentId: student2Id, score: assessment2Score }
        ],
        targetScores: [32, 64, 300]
      }
    ],
    enrolledStudentIds: [student1Id, student2Id]
  },
  {
    type: "classwide",
    whenEnded: { date: assessment3Date, on: assessment3Date.toISOString() },
    assessmentId: assessment1Id,
    targets: [32, 64, 300],
    assessmentResultMeasures: [
      {
        assessmentId: assessment1Id,
        assessmentName: assessment1Name,
        studentResults: [
          { studentId: student1Id, score: assessment3Score },
          { studentId: student2Id, score: assessment2Score }
        ],
        targetScores: [32, 64, 300]
      }
    ],
    enrolledStudentIds: [student1Id, student2Id]
  },
  {
    type: "classwide",
    whenEnded: { date: assessment3Date, on: assessment3Date.toISOString() },
    assessmentId: assessment2Id,
    targets: [32, 64, 300],
    assessmentResultMeasures: [
      {
        assessmentName: assessment2Name,
        assessmentId: assessment2Id,
        studentResults: [
          { studentId: student1Id, score: assessment3Score },
          { studentId: student2Id, score: assessment4Score }
        ],
        targetScores: [32, 64, 300]
      }
    ],
    enrolledStudentIds: [student1Id, student2Id]
  }
];
describe("getSkillHistory", () => {
  beforeAll(async () => {
    await Promise.all(assessments.map(assessment => Assessments.insertAsync(assessment)));
  });

  afterAll(async () => {
    await Assessments.removeAsync({});
  });
  it("should return 3 skills, 2 with scores for the whole group, no scores for individual student, when no student id is passed", async () => {
    const studentGroup = {};
    const student = null;

    const skillHistory = await getSkillHistory(groupHistory, gradeLevelRules, studentGroup, students, student);

    expect(skillHistory.length).toEqual(3);
    expect(skillHistory[0].id).toEqual(assessment1Id);
    expect(skillHistory[0].key).toEqual(assessment1Id);
    expect(skillHistory[0].name).toEqual(assessment1Name);
    expect(skillHistory[0].complete).toEqual(true);
    expect(skillHistory[0].classMedianScores.length).toEqual(2);
    expect(skillHistory[0].studentScores.length).toEqual(0);
    expect(skillHistory[0].allStudentsScores.length).toEqual(2);
    expect(skillHistory[0].allStudentsScores[0].data[0][0]).toEqual(assessment3Date.toISOString());
    expect(skillHistory[0].allStudentsScores[0].data[0][1]).toEqual(parseInt(assessment3Score));
    expect(skillHistory[0].allStudentsScores[1].data[0][0]).toEqual(assessment3Date.toISOString());
    expect(skillHistory[0].allStudentsScores[1].data[0][1]).toEqual(parseInt(assessment2Score));

    expect(skillHistory[1].id).toEqual(assessment2Id);
    expect(skillHistory[1].key).toEqual(assessment2Id);
    expect(skillHistory[1].name).toEqual(assessment2Name);
    expect(skillHistory[1].complete).toEqual(true);
    expect(skillHistory[1].classMedianScores.length).toEqual(1);
    expect(skillHistory[1].studentScores.length).toEqual(0);
    expect(skillHistory[1].allStudentsScores.length).toEqual(2);
    expect(skillHistory[1].allStudentsScores[0].data[0][0]).toEqual(assessment3Date.toISOString());
    expect(skillHistory[1].allStudentsScores[0].data[0][1]).toEqual(parseInt(assessment3Score));
    expect(skillHistory[1].allStudentsScores[1].data[0][0]).toEqual(assessment3Date.toISOString());
    expect(skillHistory[1].allStudentsScores[1].data[0][1]).toEqual(parseInt(assessment4Score));

    expect(skillHistory[2].id).toEqual(assessment3Id);
    expect(skillHistory[2].studentScores.length).toEqual(0);
  });
  it("should return 3 skills, 2 with scores for a particular student", async () => {
    const studentGroup = {};
    const student = { _id: student1Id };

    const skillHistory = await getSkillHistory(groupHistory, gradeLevelRules, studentGroup, students, student);
    expect(skillHistory.length).toEqual(3);
    expect(skillHistory[0].id).toEqual(assessment1Id);
    expect(skillHistory[0].key).toEqual(assessment1Id);
    expect(skillHistory[0].name).toEqual(assessment1Name);
    expect(skillHistory[0].complete).toEqual(true);
    expect(skillHistory[0].classMedianScores.length).toEqual(2);
    expect(skillHistory[0].studentScores.length).toEqual(2);
    expect(skillHistory[0].studentScores[0][0]).toEqual(assessment3Date.toISOString());
    expect(skillHistory[0].studentScores[0][1]).toEqual(parseInt(assessment3Score));
    expect(skillHistory[0].studentScores[1][0]).toEqual(assessment1Date.toISOString());
    expect(skillHistory[0].studentScores[1][1]).toEqual(parseInt(assessment1Score));
    expect(skillHistory[0].allStudentsScores.length).toEqual(0);
    expect(skillHistory[1].id).toEqual(assessment2Id);

    expect(skillHistory[1].key).toEqual(assessment2Id);
    expect(skillHistory[1].name).toEqual(assessment2Name);
    expect(skillHistory[1].complete).toEqual(true);
    expect(skillHistory[1].classMedianScores.length).toEqual(1);
    expect(skillHistory[1].studentScores.length).toEqual(1);
    expect(skillHistory[1].studentScores[0][0]).toEqual(assessment3Date.toISOString());
    expect(skillHistory[1].studentScores[0][1]).toEqual(parseInt(assessment3Score));
    expect(skillHistory[1].allStudentsScores.length).toEqual(0);

    expect(skillHistory[2].id).toEqual(assessment3Id);
    expect(skillHistory[2].studentScores.length).toEqual(0);
  });
  it("should add missing student empty score if student is enrolled but not in the studentResults array", async () => {
    const studentGroup = {
      currentClasswideSkill: {
        assessmentId: assessment1Id,
        assessmentName: assessment1Name,
        interventions: [],
        targets: [32, 64, 300],
        whenStarted: {
          on: 1490798782027.0,
          date: Date.parse("2017-03-29T14:46:22.027+0000")
        }
      }
    };
    const student = null;

    const groupHistoryForThisTest = [
      {
        type: "classwide",
        whenEnded: {
          date: assessment1Date,
          on: assessment1Date.toISOString()
        },
        assessmentId: assessment1Id,

        assessmentResultMeasures: [
          {
            assessmentId: assessment1Id,
            assessmentName: assessment1Name,
            studentResults: [{ studentId: student1Id, score: assessment1Score }]
          }
        ],
        enrolledStudentIds: [student1Id, student2Id]
      },
      {
        type: "classwide",
        whenEnded: {
          date: assessment3Date,
          on: assessment3Date.toISOString()
        },
        assessmentId: assessment2Id,

        assessmentResultMeasures: [
          {
            assessmentName: assessment2Name,
            assessmentId: assessment2Id,
            studentResults: [
              { studentId: student1Id, score: assessment3Score },
              { studentId: student2Id, score: assessment4Score }
            ]
          }
        ],
        enrolledStudentIds: [student1Id, student2Id]
      }
    ];

    const skillHistory = await getSkillHistory(
      groupHistoryForThisTest,
      gradeLevelRules,
      studentGroup,
      students,
      student
    );
    expect(skillHistory.length).toEqual(3);
    expect(skillHistory[0].id).toEqual(assessment1Id);
    expect(skillHistory[0].key).toEqual(assessment1Id);
    expect(skillHistory[0].name).toEqual(assessment1Name);
    expect(skillHistory[0].complete).toEqual(true);
    expect(skillHistory[0].classMedianScores.length).toEqual(1);
    expect(skillHistory[0].studentScores.length).toEqual(0);
    expect(skillHistory[0].allStudentsScores.length).toEqual(2);
    expect(skillHistory[0].allStudentsScores[0].lastName).toEqual(students[0].identity.name.lastName);
    expect(skillHistory[0].allStudentsScores[0].name).toEqual(
      `${students[0].identity.name.firstName} ${students[0].identity.name.lastName}`
    );
    expect(skillHistory[0].allStudentsScores[0].data[0][0]).toEqual(assessment1Date.toISOString());
    expect(skillHistory[0].allStudentsScores[0].data[0][1]).toEqual(parseInt(assessment1Score));
    expect(skillHistory[0].allStudentsScores[1].data[0][0]).toEqual(assessment1Date.toISOString());
    expect(skillHistory[0].allStudentsScores[1].data[0][1]).toEqual(null);

    expect(skillHistory[1].id).toEqual(assessment2Id);
    expect(skillHistory[1].key).toEqual(assessment2Id);
    expect(skillHistory[1].name).toEqual(assessment2Name);
    expect(skillHistory[1].complete).toEqual(true);
    expect(skillHistory[1].classMedianScores.length).toEqual(1);
    expect(skillHistory[1].studentScores.length).toEqual(0);
    expect(skillHistory[1].allStudentsScores.length).toEqual(2);
    expect(skillHistory[1].allStudentsScores[0].data[0][0]).toEqual(assessment3Date.toISOString());
    expect(skillHistory[1].allStudentsScores[0].data[0][1]).toEqual(parseInt(assessment3Score));
    expect(skillHistory[1].allStudentsScores[1].data[0][0]).toEqual(assessment3Date.toISOString());
    expect(skillHistory[1].allStudentsScores[1].data[0][1]).toEqual(parseInt(assessment4Score));

    expect(skillHistory[2].id).toEqual(assessment3Id);
    expect(skillHistory[2].studentScores.length).toEqual(0);
  });
});
