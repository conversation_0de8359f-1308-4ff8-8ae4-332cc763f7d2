import React, { Component } from "react";
import PropTypes from "prop-types";
import moment from "moment/moment";

import { StudentContext } from "/imports/contexts/StudentContext.jsx";

import { Loading } from "../loading.jsx";
import GroupChangeEntry from "./GroupChangeEntry";
import GroupLeaveEntry from "./GroupLeaveEntry";
import ActivityItem from "./activity-item";
import { useContextOrProps } from "../../utilities";

function compileStudentScores(allClasswideScores, individualInterventionScores = [], otherResults = []) {
  const compiledScores = [...allClasswideScores, ...individualInterventionScores, ...otherResults];
  compiledScores.sort((a, b) => b.whenEnded.on - a.whenEnded.on);
  return compiledScores;
}

export default class StudentLog extends Component {
  static contextType = StudentContext;

  renderEnrollments = () => {
    const enrollments = [...this.props.studentEnrollments].sort((a, b) => a.created.on - b.created.on);
    const enrollmentHistory = [];
    const iterator = enrollments.length + 1;
    if (enrollments.length > 1) {
      const enrollmentsToPair = [];
      for (let i = 0; i <= iterator; i++) {
        const currentEnrollment = enrollments.shift();
        if (currentEnrollment) {
          enrollmentsToPair.push(currentEnrollment);
        }
        const { firstName } = this.context.student.identity.name;
        if (enrollmentsToPair.length === 2) {
          const newEnrollmentTimestamp = enrollmentsToPair[1].created.on;
          const previousGroupEnrollment = enrollmentsToPair[0];
          const newGroupEnrollment = enrollmentsToPair[1];
          const previousGroup = this.props.otherStudentGroups.find(
            group => group._id === previousGroupEnrollment.studentGroupId
          );
          const newGroup = this.props.otherStudentGroups.find(group => group._id === newGroupEnrollment.studentGroupId);
          if (previousGroup && newGroup) {
            const enrollmentChangeDate = moment(newGroupEnrollment.created.date).format("MMM DD");
            enrollmentHistory.push(
              <GroupChangeEntry
                key={newEnrollmentTimestamp}
                firstName={firstName}
                previousGroupName={previousGroup.name}
                newGroupName={newGroup.name}
                enrollmentChangeDate={enrollmentChangeDate}
                wasInterventionClosed={!!newGroupEnrollment.wasIndividualInterventionClosed}
              />
            );
          }
          enrollmentsToPair.shift();
        } else if (i === iterator && !enrollmentsToPair[0].isActive) {
          const groupLeaveEnrollment = enrollmentsToPair[0];
          const groupName = this.props.otherStudentGroups.find(
            group => group._id === groupLeaveEnrollment.studentGroupId
          ).name;
          enrollmentHistory.push(
            <GroupLeaveEntry
              key={groupLeaveEnrollment.lastModified.on}
              groupLeaveEnrollment={groupLeaveEnrollment}
              firstName={firstName}
              groupName={groupName}
            />
          );
        }
      }
    }
    return enrollmentHistory;
  };

  renderScores() {
    const studentScores = compileStudentScores(
      this.props.allClasswideScores.reduce((a, c) => {
        if (c.type === "benchmark") {
          if (!a.find(el => el.type === "benchmark" && el.benchmarkPeriodId === c.benchmarkPeriodId)) {
            a.push(c);
          }
        } else {
          a.push(c);
        }
        return a;
      }, []),
      this.props.individualInterventionScores,
      this.props.otherResults
    );
    return studentScores
      .map(score => {
        const isScoreFromOtherEnrollment = !score.enrolledStudentIds;
        const student = useContextOrProps({ componentInstance: this, property: "student", verificationGetPath: "_id" });
        if (isScoreFromOtherEnrollment || score.enrolledStudentIds.includes(student._id)) {
          return (
            <ActivityItem score={score} schoolYear={this.props.schoolYear} student={student} key={score.whenEnded.on} />
          );
        }
        return null;
      })
      .filter(score => score);
  }

  renderHistory = () => {
    const history = [...this.renderScores(), ...this.renderEnrollments()];
    history.sort((a, b) => parseInt(b.key) - parseInt(a.key));
    return history;
  };

  render() {
    if (this.props.allClasswideScores) {
      const history = this.renderHistory();

      if (history.length) {
        return <ul className="activity">{history}</ul>;
      }
      return <div className="alert alert-info text-center">No data found for this student</div>;
    }
    return <Loading />;
  }
}

const scoreShape = {
  assessmentResultMeasures: PropTypes.arrayOf(
    PropTypes.shape({
      studentResults: PropTypes.arrayOf(
        PropTypes.shape({
          assessmentName: PropTypes.string,
          studentId: PropTypes.string
        })
      )
    })
  ),
  score: PropTypes.number,
  whenEnded: PropTypes.shape({
    date: PropTypes.instanceOf(Date),
    on: PropTypes.number
  }).isRequired
};

StudentLog.propTypes = {
  allClasswideScores: PropTypes.arrayOf(PropTypes.shape(scoreShape)),
  individualInterventionScores: PropTypes.arrayOf(PropTypes.shape(scoreShape)),
  studentEnrollments: PropTypes.array,
  schoolYear: PropTypes.number,
  otherResults: PropTypes.array,
  otherStudentGroups: PropTypes.array,
  student: PropTypes.any
};

export { StudentLog as PureStudentLog, compileStudentScores }; // for testing
