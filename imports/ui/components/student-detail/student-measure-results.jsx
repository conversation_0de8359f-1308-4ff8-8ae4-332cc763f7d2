import React from "react";
import PropTypes from "prop-types";

const StudentMeasureResults = ({ data, student }) => {
  const score = data.studentResults.find(sr => sr.studentId === student._id);
  return score && score.status === "COMPLETE" ? (
    <p>
      {student.identity.name.firstName}
      's score on {data.assessmentName} was <strong>{score.score}</strong>.
    </p>
  ) : (
    <p>
      {student.identity.name.firstName}
      's class was assessed on {data.assessmentName}, but {student.identity.name.firstName} was{" "}
      <strong>absent or unavailable</strong>.
    </p>
  );
};

StudentMeasureResults.propTypes = {
  data: PropTypes.object,
  student: PropTypes.object
};

export default StudentMeasureResults;
