import { render } from "enzyme/build/index";

export const groupChangeEnrollments = [
  {
    _id: "firstGroupEnrollmentId",
    created: {
      date: new Date("2018-01-10T10:44:07.305Z"),
      on: 1515581047305
    },
    studentGroupId: "previousGroupId",
    grade: "03"
  },
  {
    _id: "secondGroupEnrollmentId",
    created: {
      date: new Date("2018-01-13T10:44:17.305Z"),
      on: 1515840257305
    },
    studentGroupId: "newGroupId",
    grade: "03"
  }
];
export const groupLeaveEnrollment = [
  {
    _id: "thirdGroupEnrollmentId",
    created: {
      date: new Date("2018-01-30T10:44:27.305Z"),
      on: 1517309067305
    },
    lastModified: {
      date: new Date("2018-01-30T10:44:27.306Z"),
      on: 1517309067306
    },
    studentGroupId: "leftGroupId"
  }
];
export const previousGroup = {
  _id: "previousGroupId",
  name: "previousGroupName",
  created: { on: 1516358647305 }
};
export const newGroup = {
  _id: "newGroupId",
  name: "newGroupName",
  created: { on: 1516358657305 }
};
export const leftGroup = {
  _id: "leftGroupId",
  name: "leftGroupName",
  created: { on: 1516358667305 }
};
export const otherStudentGroups = [leftGroup, newGroup, previousGroup];
export const student = {
  _id: "testStudentId",
  identity: {
    name: {
      firstName: "TestUserFirstName"
    }
  }
};
export const allClasswideScores = [
  {
    assessmentResultMeasures: [
      {
        assessmentName: "testAssessmentName",
        studentResults: [
          {
            studentId: "testStudentId",
            score: 10,
            status: "COMPLETE"
          }
        ]
      }
    ],
    whenEnded: {
      date: new Date("2018-01-19T10:34:07.305Z"),
      on: 1516358047305
    },
    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
    type: "benchmark"
  },
  {
    assessmentResultMeasures: [
      {
        assessmentName: "testAssessmentName",
        studentResults: [
          {
            studentId: "testStudentId",
            status: "CANCELLED",
            score: 10
          }
        ]
      }
    ],
    whenEnded: {
      date: new Date("2018-01-17T10:10:47.305Z"),
      on: 1516183847305
    },
    type: "benchmark",
    benchmarkPeriodId: "nEsbWokBWutTZFkTh"
  }
];
export const individualInterventionScores = [
  {
    assessmentResultMeasures: [
      {
        assessmentName: "testAssessmentName",
        studentResults: [
          {
            studentId: "testStudentId",
            score: 10,
            status: "COMPLETE"
          }
        ]
      }
    ],
    score: 10,
    type: "individual",
    interventions: [{}],
    whenEnded: {
      date: new Date("2018-01-18T10:57:27.305Z"),
      on: 1516273047305
    }
  },
  {
    assessmentResultMeasures: [
      {
        assessmentName: "testAssessmentName",
        studentResults: [
          {
            studentId: "testStudentId"
          }
        ]
      }
    ],
    score: 10,
    type: "individual",
    interventions: [{}],
    whenEnded: {
      date: new Date("2018-01-15T10:24:07.305Z"),
      on: 1516011847305
    }
  }
];
export const otherEnrollmentsResults = [
  {
    studentGroupId: "previousGroupId",
    type: "classwide",
    whenEnded: {
      date: new Date("2018-01-12T10:44:07.305Z"),
      on: 1515753847305
    },
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    assessmentResultMeasures: [
      {
        assessmentName: "classwideAssessment 1",
        studentResults: [
          {
            studentId: "testStudentId",
            score: 0,
            status: "COMPLETE"
          },
          {
            studentId: "someOtherStudentScore",
            score: 10,
            status: "COMPLETE"
          }
        ]
      }
    ]
  },
  {
    studentGroupId: "previousGroupId",
    type: "benchmark",
    whenEnded: {
      on: 1515667447305,
      date: new Date("2018-01-11T10:44:07.305Z")
    },
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    assessmentResultMeasures: [
      {
        assessmentName: "benchmarkAssessment 1",
        studentResults: [
          {
            studentId: "testStudentId",
            score: 0,
            status: "COMPLETE"
          },
          {
            studentId: "someOtherStudentScore",
            score: 10,
            status: "COMPLETE"
          }
        ]
      },
      {
        assessmentName: "benchmarkAssessment 2",
        studentResults: [
          {
            studentId: "someOtherStudentScore",
            score: 10,
            status: "COMPLETE"
          },
          {
            studentId: "testStudentId",
            score: 0,
            status: "COMPLETE"
          }
        ]
      }
    ]
  }
];
export const enrollments = [...groupChangeEnrollments, ...groupLeaveEnrollment];

export function verifyRenderedText(timeline, result) {
  timeline.forEach((event, index) => {
    const historyText = render(result[index]);
    event.forEach(dayText => {
      expect(historyText.text()).toMatch(dayText);
    });
  });
}
