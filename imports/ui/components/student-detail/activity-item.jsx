import React from "react";
import PropTypes from "prop-types";
import moment from "moment/moment";

import StudentMeasureResults from "./student-measure-results";
import * as utils from "/imports/api/utilities/utilities";

export function getActivityTitle(activityScore, schoolYear) {
  switch (activityScore.type) {
    case "benchmark":
      return `${utils.translateBenchmarkPeriod(activityScore.benchmarkPeriodId).title} ${utils.getFormattedSchoolYear(
        schoolYear
      )} Screening`;
    case "classwide":
      return "Classwide Intervention";
    case "individual":
      if (activityScore.interventions && activityScore.interventions.length) {
        return "Individual Intervention";
      }
      return "Diagnostic Assessment";
    default:
      return "Activity";
  }
}

const ActivityItem = ({ score, schoolYear, student }) => (
  <li className="activity-item page-break-avoid">
    <span className="activity-date">{moment(score.whenEnded.date).format("MMM DD")}</span>
    <h5>
      <span>{getActivityTitle(score, schoolYear)}</span>
    </h5>
    {score.assessmentResultMeasures.map(arm => (
      <StudentMeasureResults data={arm} student={student} key={arm.assessmentName} />
    ))}
  </li>
);

ActivityItem.propTypes = {
  score: PropTypes.object,
  schoolYear: PropTypes.number,
  student: PropTypes.object
};

export default ActivityItem;
