import { assert } from "chai";

import { getIconClassName, getLabelClasses } from "./skill-list-styling";

describe("imports/ui/components/student-detail/skill-list-styling.jsx tests", () => {
  describe("getIconClassName helper function tests", () => {
    it("should return a check icon when the skill is complete", () => {
      const result = getIconClassName({ complete: true });
      assert.equal(result, "fa fa-check", "should be a check icon");
    });

    it("should return an active open circle when it is a drilldown", () => {
      const result = getIconClassName({
        complete: true,
        drillDownOnlyScore: true
      });
      assert.equal(result, "fa fa-circle-o active", "should be a check icon");
    });

    it("should return a solid circle when it is active but not complete", () => {
      const result = getIconClassName({
        complete: false,
        active: true,
        drillDownOnlyScore: false
      });
      assert.equal(result, "fa fa-circle", "should be a check icon");
    });

    it("should return an open grey circle without the active class if it is not complete nor active", () => {
      const result = getIconClassName({
        complete: false,
        active: false,
        drillDownOnlyScore: false
      });
      assert.equal(result, "fa fa-circle-o", "should be a check icon");
    });

    it("should return active if it is active and contains studentScores", () => {
      const result = getLabelClasses({ active: true, studentScores: [1, 2, 3] }, false);
      assert.equal(result, "active", "should be active");
    });

    it("should return active if it is active and contains interventions", () => {
      const result = getLabelClasses({ active: true, interventions: [5, 7, 9] }, false);
      assert.equal(result, "active", "should be active");
    });

    it("should return active and selected if it is a selected skill", () => {
      const result = getLabelClasses({ active: true, studentScores: [1, 1] }, true);
      assert.equal(result, "active selected", "should be active");
    });

    it("should return active if the skill is complete and it has scores", () => {
      const result = getLabelClasses({ complete: true, studentScores: [1, 2, 3] }, false);
      assert.equal(result, "active", "should be active");
    });
  });
});
