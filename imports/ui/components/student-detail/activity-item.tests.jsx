import { getActivityTitle } from "./activity-item";

describe("imports/ui/components/student-detail/activity-item.jsx tests", () => {
  describe("getActivityTitle", () => {
    it("should return 'Classwide Intervention' for a score of type 'classwide'", () => {
      const activityScore = {
        type: "classwide"
      };
      const schoolYear = 2019;
      const title = getActivityTitle(activityScore, schoolYear);

      expect(title).toBe("Classwide Intervention");
    });

    it("should return 'Diagnostic Assessment' for a score of type 'individual' without any interventions", () => {
      const activityScore = {
        type: "individual",
        interventions: []
      };
      const schoolYear = 2019;
      const title = getActivityTitle(activityScore, schoolYear);

      expect(title).toBe("Diagnostic Assessment");
    });

    it("should return 'Individual Intervention' for a score of type 'individual' with some interventions", () => {
      const activityScore = {
        type: "individual",
        interventions: [{}, {}]
      };
      const schoolYear = 2019;
      const title = getActivityTitle(activityScore, schoolYear);

      expect(title).toBe("Individual Intervention");
    });

    it("should return 'Fall 2018-19 Screening' for a score of type 'benchmark' from the Fall screening 2019", () => {
      const activityScore = {
        type: "benchmark",
        benchmarkPeriodId: "fall-period"
      };
      const schoolYear = 2019;
      const title = getActivityTitle(activityScore, schoolYear);

      expect(title).toBe("Fall 2018-19 Screening");
    });

    it("should return 'Spring 2016-17 Screening' for a score of type 'benchmark' from the Spring screening 2017", () => {
      const activityScore = {
        type: "benchmark",
        benchmarkPeriodId: "spring-period"
      };
      const schoolYear = 2017;
      const title = getActivityTitle(activityScore, schoolYear);

      expect(title).toBe("Spring 2016-17 Screening");
    });
  });
});
