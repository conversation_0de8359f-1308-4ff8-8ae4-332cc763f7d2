import { Meteor } from "meteor/meteor";
import React, { Component, useState, useEffect, useRef } from "react";
import PropTypes from "prop-types";
import _ from "lodash";
import { useTracker } from "meteor/react-meteor-data";
import Alert from "react-s-alert";

import { Loading } from "../loading.jsx";
import ClasswideSkillList from "./classwide-skill-list.jsx";
import { Rules } from "/imports/api/rules/rules";
import { OrganizationContext } from "/imports/contexts/OrganizationContext.jsx";
import ClasswidePMChart from "./student-detail-PM-Chart.jsx";
import { calculateClasswideROI, MAX_SKILLS_FROM_NEXT_GRADE } from "/imports/api/utilities/utilities";
import ScoreDisplay from "../score-display/score-display";
import GraphTitle from "./GraphTitle";
import ConfirmModal from "../../pages/data-admin/confirm-modal";
import getSkillsHistory from "./getSkillHistory";

class ClasswideInterventionProgress extends Component {
  static contextType = OrganizationContext;

  constructor(props) {
    super(props);
    this.state = {
      selectedClasswideAssessment: this.getActiveOrLastPracticedIndex() || null,
      classwideROI: props.classwideROI,
      studentROI: props.studentROI,
      shouldShowStudentsScores: props.shouldShowStudentsScores || false,
      displayInterventionScores: false,
      selectedDate: undefined,
      isClearScoresModalOpen: false,
      isEditScoresModalOpen: false,
      assessmentResults: null,
      isFetchingAbsentScores: false
    };
    this.selectGraphToDisplay = this.selectGraphToDisplay.bind(this);
  }

  componentDidUpdate(prevProps) {
    if (!this.props.loading && this.props.loading !== prevProps.loading) {
      this.props.refreshScroll();
      this.setState({ classwideROI: this.props.classwideROI, studentROI: this.props.studentROI });
    }
    if (this.props.skillList.length !== prevProps.skillList.length) {
      this.setState({
        selectedClasswideAssessment: this.getActiveOrLastPracticedIndex(),
        selectedDate: undefined
      });
    }
    if (
      this.props.skillList.length &&
      prevProps.skillList.length &&
      this.props.skillList[this.props.skillList.length - 1].allStudentsScores.length !==
        prevProps.skillList[prevProps.skillList.length - 1].allStudentsScores.length
    ) {
      this.setState({
        selectedClasswideAssessment: this.getLastCompleteSkillIndex()
      });
    }
    if (
      this.props.userPrivilegedRole &&
      !this.state.isFetchingAbsentScores &&
      (this.state.assessmentResults === null ||
        this.props.studentGroup?.history?.length !== prevProps.studentGroup?.history?.length)
    ) {
      this.setState({ isFetchingAbsentScores: true });
      Meteor.call(
        "ManageScores:AssessmentResultsAbsentScores",
        this.props.studentGroup._id,
        this.props.studentGroup.orgid,
        (err, resp) => {
          if (!err) {
            this.setState({ assessmentResults: resp, isFetchingAbsentScores: false });
          }
        }
      );
    }
  }

  selectGraphToDisplay(assessmentIndex) {
    // only allow selecting skills that have history or at least got filled with target information
    const skill = this.props.skillList[assessmentIndex];
    if (skill && skill.masteryTarget) {
      const studentGroup = _.cloneDeep(this.props.studentGroup);
      studentGroup.currentClasswideSkill.assessmentId = skill.id;
      const classwideROI = calculateClasswideROI(studentGroup);
      const studentROI = this.props.student ? calculateClasswideROI(studentGroup, this.props.student) : null;
      this.setState(
        () => ({
          selectedClasswideAssessment: assessmentIndex,
          classwideROI,
          studentROI,
          selectedDate: undefined,
          displayInterventionScores: false
        }),
        () => {
          this.props.setIsEditing(false);
        }
      );
    }
  }

  clearScores = () => {
    const assessmentIndex = this.getSkillIndexToDisplay();
    const skill = this.props.skillList[assessmentIndex];
    const studentGroupId = this.props.studentGroup._id;
    const assessmentId = skill.id;
    const orgid = this.context.orgId || this.props.studentGroup.orgid;

    Meteor.call("clearLastClasswideInterventionScores", { studentGroupId, assessmentId, orgid }, err => {
      if (err) {
        Alert.error(err.message, {
          timeout: 3000
        });
      } else {
        Alert.success("Scores cleared successfully", {
          timeout: 3000
        });
      }
    });
  };

  toggleStudentsScoresVisibility = () => {
    this.setState(state => ({ ...state, shouldShowStudentsScores: !this.state.shouldShowStudentsScores }));
  };

  getLastCompleteSkillIndex = () => {
    return this.props.skillList.filter(skill => skill.complete === true).length - 1;
  };

  getSkillIndexToDisplay = () => {
    let skillIndexToDisplay;
    if (this.state.selectedClasswideAssessment !== null) {
      skillIndexToDisplay = this.state.selectedClasswideAssessment;
    } else {
      skillIndexToDisplay = this.getActiveOrLastPracticedIndex();
    }
    // If students are all done with classwide, default to the last one when entering page
    if (skillIndexToDisplay < 0 || skillIndexToDisplay >= this.props.skillList.length) {
      skillIndexToDisplay = this.props.skillList.length - 1;
    }
    return skillIndexToDisplay;
  };

  getActiveOrLastPracticedIndex() {
    if (Number.isInteger(this.props.selectedSkillIndex) && this.props.selectedSkillIndex >= 0) {
      return this.props.selectedSkillIndex;
    }
    const activeSkillIndex = this.props.skillList.findIndex(skill => skill.active === true);
    if (activeSkillIndex < 0) {
      return _.findLastIndex(this.props.skillList, "complete");
    }
    if (activeSkillIndex > 0 && !this.wasSkillPracticed(activeSkillIndex)) {
      return activeSkillIndex - 1;
    }
    return activeSkillIndex;
  }

  wasSkillPracticed(lastPracticedSkillIndex) {
    return (
      this.props.skillList[lastPracticedSkillIndex].allStudentsScores.length ||
      this.props.skillList[lastPracticedSkillIndex].studentScores.length
    );
  }

  showSelectedScores = (event, isClick = false) => {
    event.preventDefault();
    const isEditing =
      this.props.isEditing &&
      (!isClick || (isClick && this.props.totalNumberOfSkills - 1 === this.state.selectedClasswideAssessment));

    this.setState(
      () => ({
        displayInterventionScores: true,
        selectedDate: event.target.x
      }),
      () => {
        this.props.setIsEditing(isEditing);
      }
    );
  };

  hideSelectedScores = event => {
    event.preventDefault();
    this.setState(
      () => ({
        displayInterventionScores: false
      }),
      () => {
        this.props.setIsEditing(false);
      }
    );
  };

  toggleEditingScoresModal = () => {
    this.setState(state => ({ ...state, isEditScoresModalOpen: !this.state.isEditScoresModalOpen }));
  };

  toggleEditingScores = () => {
    this.props.setIsEditing(!this.props.isEditing);
  };

  confirmActionEditModal = () => {
    this.toggleEditingScoresModal();
    this.toggleEditingScores();
  };

  handleEditScoresButton = () => {
    if (this.props.isEditing) {
      this.toggleEditingScores();
    } else {
      this.toggleEditingScoresModal();
    }
  };

  displayScoresForSelectedDate = selectedDate => {
    const studentGroupHistory = [
      ...(this.props.studentGroup.additionalHistory || []),
      ...(this.props.studentGroup.history || [])
    ];
    const selectedHistoryScore = studentGroupHistory.find(result => result.whenEnded.on === selectedDate);
    let scores = selectedHistoryScore;
    if (this.props.userPrivilegedRole && this.state.assessmentResults && selectedHistoryScore) {
      const assessmentResultScores =
        this.state.assessmentResults?.find(ar => ar._id === selectedHistoryScore.assessmentResultId)?.scores || [];
      const updatedAssessmentResultMeasure = {
        ...selectedHistoryScore.assessmentResultMeasures[0],
        studentResults: [
          ...(selectedHistoryScore.assessmentResultMeasures[0]?.studentResults || []),
          ...assessmentResultScores.filter(ars => ars.status === "CANCELLED").map(ars => ({ ...ars, score: ars.value }))
        ]
      };
      scores = { ...selectedHistoryScore, assessmentResultMeasures: [updatedAssessmentResultMeasure] };
    }
    if (!scores) {
      return null;
    }
    const selectedSkill = this.props.skillList[this.state.selectedClasswideAssessment];
    const masteryTarget = selectedSkill ? selectedSkill.masteryTarget : 0;
    const orgid = this.context.orgId || this.props.studentGroup.orgid;
    return (
      <ScoreDisplay
        scores={scores}
        selectedSkillMasteryTarget={masteryTarget}
        canEdit={!!this.props.userPrivilegedRole}
        selectedSkill={selectedSkill}
        studentGroupId={this.props.studentGroup._id}
        isEditing={this.props.isEditing}
        isLastSkill={this.props.totalNumberOfSkills - 1 === this.state.selectedClasswideAssessment}
        orgid={orgid}
        currentlyEnrolledStudentIds={this.props.students.filter(s => s.isActive).map(s => s._id)}
      />
    );
  };

  toggleClearScoresModal = () => {
    this.setState(state => ({ ...state, isClearScoresModalOpen: !this.state.isClearScoresModalOpen }));
  };

  getSubjectName = () => {
    return this.props.userPrivilegedRole === "arbitraryIddataAdmin" ? "teacher" : "customer";
  };

  render() {
    const chartOptions = {
      chartType: "line",
      title: "",
      height: 400,
      xAxisTitle: "",
      yAxisTitle: "Score",
      marginTop: 50,
      marginRight: 175
    };
    const { loading, skillList } = this.props;
    const { selectedClasswideAssessment } = this.state;
    if (!loading && skillList && skillList.length > 0 && selectedClasswideAssessment !== null) {
      const { student, userPrivilegedRole } = this.props;
      const {
        selectedDate,
        classwideROI,
        studentROI,
        shouldShowStudentsScores,
        displayInterventionScores,
        isClearScoresModalOpen,
        isEditScoresModalOpen
      } = this.state;
      let name = "";
      let studentId = "";
      if (student) {
        name = `${student.identity.name.firstName} ${student.identity.name.lastName}`;
        studentId = student._id;
      }
      const skillIndexToDisplay = this.getSkillIndexToDisplay();
      const skillToDisplay = skillList[skillIndexToDisplay];
      if (student) {
        skillToDisplay.allStudentsScores = skillToDisplay.allStudentsScores.filter(s => s.studentId === student._id);
      }
      const isLastSkill = skillIndexToDisplay === skillList.length - 1 && skillToDisplay.classMedianScores.length;
      let selectedScoreDate = selectedDate;
      const shouldDisplayInterventionScores = displayInterventionScores;
      if (isLastSkill && !selectedScoreDate) {
        [selectedScoreDate] = skillToDisplay.classMedianScores[skillToDisplay.classMedianScores.length - 1];
      }
      const isLastSkillSelected =
        skillIndexToDisplay === skillList.length - 1 &&
        skillToDisplay.classMedianScores.length &&
        selectedScoreDate === skillToDisplay.classMedianScores[skillToDisplay.classMedianScores.length - 1][0];
      let modalDateClearScore = 0;
      if (selectedScoreDate) {
        modalDateClearScore = new Date(selectedScoreDate).toDateString().split(" ");
      }
      return (
        <div data-testid="classwide-intervention-progress-section">
          {userPrivilegedRole ? null : <h3>Classwide Intervention Progress</h3>}
          <section>
            <div className="row">
              <div className="col-md-9 print-clear">
                <GraphTitle skill={skillToDisplay} classwideROI={classwideROI} studentROI={studentROI} />
                <ClasswidePMChart
                  scores={skillToDisplay}
                  chartId={`classwide_PM_CHART_${studentId}`}
                  type="chart"
                  options={chartOptions}
                  pmName="Progress Monitoring Scores"
                  studentName={name}
                  shouldShowStudentsScores={shouldShowStudentsScores}
                  showSelectedScores={this.showSelectedScores}
                  hideSelectedScores={this.hideSelectedScores}
                  selectedScoreDate={selectedScoreDate}
                  scoresClickable={true}
                  displayInterventionScores={shouldDisplayInterventionScores}
                  isSuperAdminOrUniversalDataAdminOrDataAdmin={!!userPrivilegedRole}
                  studentGroup={this.props.studentGroup}
                />
                {!student ? (
                  <div className="pull-right mt-4 mb-1">
                    <br />
                    <br />
                    {!student && userPrivilegedRole && isLastSkillSelected ? (
                      <React.Fragment>
                        <button className="btn btn-primary" onClick={this.handleEditScoresButton}>
                          {this.props.isEditing ? "Cancel Editing Scores" : "Edit Scores"}
                        </button>{" "}
                        <button
                          className="btn btn-danger"
                          onClick={this.toggleClearScoresModal}
                          data-testid="cwi-clear-scores-btn"
                        >
                          Clear Scores
                        </button>
                      </React.Fragment>
                    ) : null}{" "}
                    <button className="btn btn-primary" onClick={this.toggleStudentsScoresVisibility}>
                      {shouldShowStudentsScores ? "Hide Students scores" : "Show Students scores"}
                    </button>
                  </div>
                ) : null}
              </div>

              {this.props.shouldShowSkillTreeProgress && (
                <ClasswideSkillList
                  skillList={skillList}
                  setSelectedAssessment={this.selectGraphToDisplay}
                  selectedSkillIndex={selectedClasswideAssessment}
                />
              )}
            </div>
            {shouldDisplayInterventionScores && (
              <div className="row">
                <div className="col-md-9 print-clear">{this.displayScoresForSelectedDate(selectedScoreDate)}</div>
              </div>
            )}
          </section>
          <ConfirmModal
            showModal={isClearScoresModalOpen}
            onCloseModal={this.toggleClearScoresModal}
            confirmAction={this.clearScores}
            headerText="Are you sure you want to clear scores for selected graph point?"
            bodyText={<strong>WARNING:</strong>}
            bodyQuestion={`This action will permanently delete the scores for ${modalDateClearScore[1]}-${
              modalDateClearScore[2]
            } and cannot be undone.
                Confirm with the ${this.getSubjectName()} that they have a paper copy of all students' scores or are willing to
                re-administer the Classwide Intervention`}
            confirmText="Yes, clear scores"
            isWarning={true}
          />
          <ConfirmModal
            showModal={isEditScoresModalOpen}
            onCloseModal={this.toggleEditingScoresModal}
            confirmAction={this.confirmActionEditModal}
            headerText="Are you sure you want to edit scores for selected graph point?"
            bodyQuestion={`Before editing scores, it is recommended to verify that the ${this.getSubjectName()} has access to the paper tests since all changes that you save are final.`}
            confirmText="Acknowledged"
            isWarning={true}
          />
        </div>
      );
    }
    return <Loading />;
  }
}

ClasswideInterventionProgress.propTypes = {
  loading: PropTypes.bool,
  goalSkillList: PropTypes.array, // TODO: arrayOf
  skillList: PropTypes.array, // TODO: arrayOf
  totalNumberOfSkills: PropTypes.number,
  student: PropTypes.object, // TODO: shape
  students: PropTypes.array,
  studentGroup: PropTypes.object,
  classwideROI: PropTypes.string,
  studentROI: PropTypes.string,
  refreshScroll: PropTypes.func,
  userPrivilegedRole: PropTypes.string,
  shouldShowSkillTreeProgress: PropTypes.bool,
  shouldShowStudentsScores: PropTypes.bool,
  selectedSkillIndex: PropTypes.number,
  selectedSkillAssessmentId: PropTypes.string,
  isEditing: PropTypes.bool,
  setIsEditing: PropTypes.func
};

// Data Container
const ClasswideInterventionProgressContainer = ({
  student,
  studentGroup,
  students,
  userPrivilegedRole,
  shouldShowSkillTreeProgress = true,
  selectedSkillAssessmentId
}) => {
  const isEditingRef = useRef(false);

  const [skillList, setSkillList] = useState([]);
  const [totalNumberOfSkills, setTotalNumberOfSkills] = useState(0);
  const [studentROI, setStudentROI] = useState("N/A");
  const [classwideROI, setClasswideROI] = useState("N/A");
  const [skillIndex, setSkillIndex] = useState(-1);
  const [isFetchingData, setIsFetchingData] = useState(false);
  const [isEditing, setIsEditing] = useState(isEditingRef.current);

  const { loading } = useTracker(() => {
    const additionalGradeForRules = studentGroup.grade === "K" ? "01" : null;
    const rulesSub = Meteor.subscribe("GradeLevelRulesByStudentGroup", studentGroup._id, additionalGradeForRules);
    const assessmentSub = Meteor.subscribe("AssessmentsForGrade", studentGroup.grade);

    return {
      loading: !rulesSub.ready() || !assessmentSub.ready()
    };
  }, [studentGroup._id, studentGroup.grade]);

  // Handle async skill list generation
  useEffect(() => {
    setIsFetchingData(true);
    const generateSkillList = async () => {
      if (loading) return;

      const additionalGradeForRules = studentGroup.grade === "K" ? "01" : null;
      const gradeLevelRules = Rules.findOne({ grade: studentGroup.grade });
      const additionalGradeLevelRules = additionalGradeForRules
        ? Rules.findOne({ grade: additionalGradeForRules })
        : undefined;

      // Get All activities
      const studentGroupHistory = JSON.parse(JSON.stringify(studentGroup.history));
      const additionalStudentGroupHistory = JSON.parse(JSON.stringify(studentGroup.additionalHistory || []));

      const defaultSkillList = await getSkillsHistory(studentGroupHistory, gradeLevelRules, studentGroup, students);
      const isAdditionalSkillList = true;

      const additionalSkillListFull = await getSkillsHistory(
        additionalStudentGroupHistory,
        additionalGradeLevelRules,
        studentGroup,
        students,
        undefined,
        isAdditionalSkillList
      );

      const additionalSkillList = (additionalSkillListFull || []).slice(0, MAX_SKILLS_FROM_NEXT_GRADE);
      const combinedSkillList = [...(defaultSkillList || []), ...additionalSkillList];

      let filteredSkillList = combinedSkillList;
      setTotalNumberOfSkills(combinedSkillList.length);

      if (userPrivilegedRole) {
        filteredSkillList = combinedSkillList.filter(
          skill => skill.complete === true && skill.allStudentsScores.length
        );
      }

      setSkillList(filteredSkillList);

      let currentSkillIndex = -1;
      if (selectedSkillAssessmentId) {
        currentSkillIndex = filteredSkillList.findIndex(skillElem => skillElem.id === selectedSkillAssessmentId);
        if (currentSkillIndex === -1) {
          currentSkillIndex = filteredSkillList.findIndex(
            skillElem => skillElem.id === studentGroup.currentClasswideSkill?.assessmentId
          );
        }
      }
      setSkillIndex(currentSkillIndex);

      const skill = filteredSkillList[currentSkillIndex !== -1 ? currentSkillIndex : filteredSkillList.length - 1];
      let assessmentId;
      if (skill?.masteryTarget) {
        assessmentId = skill.id;
      }

      const newStudentROI = student ? calculateClasswideROI(studentGroup, student, assessmentId) : null;
      const newClasswideROI = calculateClasswideROI(studentGroup, null, assessmentId);

      setStudentROI(newStudentROI);
      setClasswideROI(newClasswideROI);
      setIsFetchingData(false);
    };

    generateSkillList();
  }, [loading, studentGroup, students, userPrivilegedRole, selectedSkillAssessmentId, student]);

  const setIsEditingValue = isEditingValue => {
    isEditingRef.current = isEditingValue;
    setIsEditing(isEditingValue);
  };

  if (loading || isFetchingData) {
    return <Loading />;
  }

  return (
    <ClasswideInterventionProgress
      loading={loading}
      student={student}
      skillList={skillList}
      totalNumberOfSkills={totalNumberOfSkills}
      studentGroup={studentGroup}
      students={students}
      classwideROI={classwideROI}
      isEditing={isEditing}
      setIsEditing={setIsEditingValue}
      studentROI={studentROI}
      userPrivilegedRole={userPrivilegedRole}
      shouldShowSkillTreeProgress={shouldShowSkillTreeProgress}
      selectedSkillIndex={skillIndex}
    />
  );
};

ClasswideInterventionProgressContainer.propTypes = {
  student: PropTypes.object,
  studentGroup: PropTypes.object.isRequired,
  students: PropTypes.array.isRequired,
  userPrivilegedRole: PropTypes.string,
  shouldShowSkillTreeProgress: PropTypes.bool,
  selectedSkillAssessmentId: PropTypes.string
};

export default ClasswideInterventionProgressContainer;
