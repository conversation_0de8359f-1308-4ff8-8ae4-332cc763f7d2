import React, { createContext } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { useTracker } from "meteor/react-meteor-data";
import { uniq } from "lodash";

function buildUserContext(meteorUser) {
  if (!meteorUser) {
    return {
      user: null,
      userId: null,
      userOrgId: null,
      userRoles: [],
      currentSiteAccess: {},
      userSiteAccess: []
    };
  }

  const profile = meteorUser.profile || {};
  const siteAccessList = Array.isArray(profile.siteAccess) ? profile.siteAccess.filter(sa => sa.isActive) : [];

  return {
    // TODO(fmazur) - remove maybe when components use direct context props
    user: meteorUser,
    userId: meteorUser._id || null,
    userOrgId: profile.orgid || null,
    // TODO(fmazur) - decide whether we use id or shorter name getRoleName helper or something like this exists
    userRoles: uniq(siteAccessList.map(sa => sa.role)),
    currentSiteAccess: siteAccessList[0] || {},
    userSiteAccess: siteAccessList
  };
}

export const UserContext = createContext(buildUserContext());

export const UserContextProvider = ({ children }) => {
  const userContext = useTracker(() => {
    const meteorUser = Meteor.user();
    const url = window.location.pathname;
    if (Meteor.loggingOut() || url.includes("logout") || url.includes("login")) {
      return buildUserContext(null);
    }
    return buildUserContext(meteorUser);
  }, []);

  return <UserContext.Provider value={userContext}>{children}</UserContext.Provider>;
};

UserContextProvider.propTypes = {
  children: PropTypes.node
};
