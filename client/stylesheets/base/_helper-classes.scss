/* ===========
Helper classes
=============*/
.p-0 {
    padding: 0px !important;
}
.p-t-0 {
    padding-top: 0px !important;
}
.p-t-10 {
    padding-top: 10px !important;
}
.p-t-15 {
    padding-top: 15px !important;
}
.p-b-0 {
    padding-bottom: 0px !important;
}
.p-b-10 {
    padding-bottom: 10px !important;
}
.p-l-30 {
    padding-left: 30px !important;
}
.p-l-20 {
    padding-left: 20px !important;
}

.m-0 {
    margin: 0px !important;
}

.m-5 {
    margin: 5px;
}

.m-10 {
    margin: 10px;
}
.m-r-5 {
    margin-right: 5px;
}
.m-r-10 {
    margin-right: 10px;
}
.m-r-15 {
    margin-right: 15px !important;
}
.m-r-25 {
    margin-right: 25px;
}
.m-l-5 {
    margin-left: 5px;
}
.m-l-10 {
    margin-left: 10px;
}
.m-l-15 {
    margin-left: 15px;
}
.m-t-5 {
    margin-top: 5px !important;
}
.m-t-0 {
    margin-top: 0px;
}
.m-t-10 {
    margin-top: 10px !important;
}
.m-t-15 {
    margin-top: 15px !important;
}
.m-t-20 {
    margin-top: 20px !important;
}
.m-t-30 {
    margin-top: 30px !important;
}
.m-t-40 {
    margin-top: 40px !important;
}
.m-t-50 {
    margin-top: 50px !important;
}
.m-t-60 {
    margin-top: 60px !important;
}
.m-t-70 {
    margin-top: 70px !important;
}
.m-b-0 {
    margin-bottom: 0px !important;
}
.m-b-5 {
    margin-bottom: 5px;
}
.m-b-10 {
    margin-bottom: 10px;
}
.m-b-15 {
    margin-bottom: 15px;
}
.m-b-20 {
    margin-bottom: 20px;
}
.m-b-25 {
    margin-bottom: 25px;
}
.m-b-30 {
    margin-bottom: 30px !important;
}
.m-b-40 {
    margin-bottom: 40px !important;
}
.w-xs {
    min-width: 80px;
}
.w-sm {
    min-width: 95px;
}
.w-md {
    min-width: 110px;
}
.w-lg {
    min-width: 140px;
}
.m-h-50 {
    min-height: 50px;
}
.l-h-34 {
    line-height: 34px !important;
}
.font-light {
    font-weight: 300;
}
.font-normal {
    font-weight: normal;
}
.font-13 {
    font-size: 13px !important;
}
.font-14 {
    font-size: 14px;
}
.font-18 {
    font-size: 18px;
}
.wrapper-md {
    padding: 20px;
}
.pull-in {
    margin-left: -20px;
    margin-right: -20px;
}
.b-0 {
    border: none !important;
}
.no-border {
    border: none;
}
.bx-s-0 {
    box-shadow: none !important;
}
.bx-shadow {
    -moz-box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
}
.mx-box {
    max-height: 380px;
    min-height: 380px;
}
.thumb-sm {
    height: 32px;
    width: 32px;
}
.thumb-md {
    height: 48px;
    width: 48px;
}
.thumb-lg {
    height: 88px;
    width: 88px;
}
.btn-center{
    display: block;
    margin: auto;
}

.no-caret > .btn::after {
    display: none;
}

.d-flex{
    display: flex;
}

.flex-grow-1 {
    flex-grow: 1 !important;
}

.text-left-forced {
    text-align: left !important;
}

.outline {
    color: #000;
    text-shadow: -1px 0 white, 0 1px white, 1px 0 white, 0 -1px white;
    -webkit-font-smoothing: antialiased;
}

.vertical-align-middle {
    vertical-align: middle;
}
