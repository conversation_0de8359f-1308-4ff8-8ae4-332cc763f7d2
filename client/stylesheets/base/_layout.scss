.wrapper {
  bottom: 0;
  left: 0;
  overflow: auto;
  position: absolute;
  right: 0;
  top: 0;

  .nav-support {
    /* background: #DFEBF2; */
    /* padding-top: 52px; */
  }
}

main {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 53px;


}

.workspace-container {
  background: #F8F7F3;
  bottom: 0;
  left: 215px;
  overflow: hidden;
  padding: 5px 20px;
  position: absolute;
  right: 0;
  top: 0;
  display: flex;
  flex-direction: column;

  .student-group-header {
    border-bottom: 1px solid #ccc;

    .student-group-nav {
      display: -webkit-flex; /* Safari */
      display: flex;

      -webkit-flex-direction: row; /* Safari */
      flex-direction:         row;

      -webkit-flex-wrap: nowrap; /* Safari */
      flex-wrap:         nowrap;

      -webkit-justify-content: center; /* Safari */
      justify-content:         center;

    }/*-- /.workspace-container --*/
  }/*-- /.student-group-header --*/
}/*-- /.student-group-nav --*/

.card-box-wrapper {
  background-clip: padding-box;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 3px;
  overflow: auto;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  margin-bottom: 25px;
  box-shadow: 0 0 4px 0 rgba(0,0,0,0.36);

  & > div {
    display: flex;
    flex-direction: column;
    flex-grow: inherit;
    min-height: min-content;
    overflow-x: hidden;
    overflow-y: auto;
  }
}

.container {
    width: 95%;
    max-width: 95%;
    padding: 0;
}

.container-alt {
    margin-left: auto;
    margin-right: auto;
    padding-left: 15px;
    padding-right: 15px;
}


.footer {
    background-color: #f9f9f9;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    bottom: 0px;
    color: #58666e;
    text-align: left !important;
    padding: 20px 30px;
    position: absolute;
    right: 0;
    left: 240px;
}


footer {
    bottom: 3px;
    color: rgba(76, 86, 103, 0.35);
    font-size: .7em;
    position: fixed;
}

.footer-with-side-nav {
  width: calc(100% - 255px);
  margin: 0 20px 0 235px
}

.footer-without-side-nav {
  width: calc(100% - 40px);
  margin: 0 20px;
}
