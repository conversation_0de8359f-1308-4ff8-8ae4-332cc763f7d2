@import "{}/client/stylesheets/base/colors";

/* Modals being used as Notice Alerts */
.notice {
  background: #ffffff;

  .modal-content {
    background: transparent;
    text-align: center;
    font-size: 1.7em;

    i.fa {
      &.noticeIcon {
        display: block;
        margin-top: 20px;
      }

      &#bsModalClose {
        display: block;
        font-size: 0.7em;
        position: absolute;
        right: -25px;
        top: -28px;
      }
    }
  }

  &.notice-danger {
    /* Red */
    background-color: #f2dede;
    border: 1px solid #ebccd1;
    color: #a94442;
  }
  &.notice-success {
    /* Green */
    background-color: #dff0d8;
    border: 1px solid #d6e9c6;
    color: #3c763d;
  }
  &.notice-warning {
    /* Yellow */
    background-color: #fbf1ba;
    border-color: #f9e1b1;
    color: #8a6d3b;
  }
}

.row {
  margin-right: -10px;
  margin-left: -10px;
}

.row.row-margin-fixed {
  margin-right: 0px;
  margin-left: 0px;
}

.col-lg-1,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-md-1,
.col-md-10,
.col-md-11,
.col-md-12,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-sm-1,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9 {
  padding-left: 10px;
  padding-right: 10px;
}
.breadcrumb {
  background-color: transparent;
  margin-bottom: 15px;
  margin-top: 5px;
}

.dropdown-toggle::after {
  vertical-align: 0.155em;
}

.dropdown-menu {
  padding: 4px 0;
  border: 0;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.26);
}

.dropdown-menu > a {
  padding: 6px 20px;
  line-height: 1.25rem;
}

.dropdown-header {
  font-size: 0.775rem;
}

.dropdown-hover {
  position: relative;
  display: inline-block;
  &:hover .dropdown-hover-content {
    display: block;
  }
}
.dropdown-hover-content {
  display: none;
  position: absolute;
  padding: 5px;
  min-width: 230px;
  z-index: 1;
  //-webkit-animation: dropdownOpen 0.3s ease-out;
  //-o-animation: dropdownOpen 0.3s ease-out;
  //animation: dropdownOpen 0.3s ease-out;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.26);
  background: #fff;
  > button {
    margin-bottom: 2px;
    background-color: #4fd1c1 !important;
  }
}

code {
  color: #5d9cec;
  border-radius: 4px;
}
code,
pre {
  background-color: #f4f8fb;
}
.bg-empty {
  background: transparent !important;
}
.bg-success {
  background-color: #009886 !important;
}
.bg-info {
  background-color: #3ddcf7 !important;
}
.bg-warning {
  background-color: #ffaa00 !important;
}
.bg-danger {
  background-color: #ef5350 !important;
}
.bg-muted {
  background-color: #f5f5f5 !important;
}
.bg-white {
  background-color: #ffffff !important;
}
.text-white {
  color: #ffffff;
}
.text-danger {
  color: #ef5350 !important;
}
.text-muted {
  color: #98a6ad;
}
.text-primary {
  color: #f9cd48;
}
.text-warning {
  color: #ffaa00;
}
.text-success {
  color: #009886 !important;
}
.text-info {
  color: #3ddcf7;
}
.text-dark {
  color: #4c5667 !important;
}
.form-control {
  -moz-border-radius: 2px;
  -moz-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  -webkit-border-radius: 2px;
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  background-color: #fafafa;
  border-radius: 2px;
  border: 1px solid #ddd;
  box-shadow: none;
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
}
.form-control:focus {
  background: #ffffff;
  border: 1px solid #e0e0e0;
  box-shadow: none;
}
.label {
  color: #ffffff !important;
}
.label-primary {
  background-color: #f9cd48;
}
.label-success {
  background-color: #009886;
}
.label-info {
  background-color: #3ddcf7;
}
.label-warning {
  background-color: #ffaa00;
}
.label-danger {
  background-color: #ef5350;
}
.label-dark {
  background: #4f595b;
}
.label-inverse {
  background-color: #4c5667;
}
.btn-link-no-padding {
  padding: 0 !important;
}
.btn-link-underline {
  text-decoration: underline;
}
.badge {
  font-weight: 600;
  padding: 3px 5px;
  font-size: 12px;
  margin-top: 1px;
}
.badge-xs {
  font-size: 9px;
}
.badge-xs,
.badge-sm {
  -webkit-transform: translate(0, -2px);
  -ms-transform: translate(0, -2px);
  -o-transform: translate(0, -2px);
  transform: translate(0, -2px);
}
.badge-primary {
  background-color: #f9cd48;
}
.badge-success {
  background-color: #009886;
}
.badge-info {
  background-color: #3ddcf7;
}
.badge-warning {
  background-color: #ffaa00;
}
.badge-danger {
  background-color: #ef5350;
}
.badge-inverse {
  background-color: #4c5667;
}
/* Pagination/ Pager */
.pagination > li:first-child > a,
.pagination > li:first-child > span {
  border-bottom-left-radius: 3px;
  border-top-left-radius: 3px;
}
.pagination > li:last-child > a,
.pagination > li:last-child > span {
  border-bottom-right-radius: 3px;
  border-top-right-radius: 3px;
}
.pagination > li > a,
.pagination > li > span {
  color: #636e7b;
}
.pagination > li > a:hover,
.pagination > li > span:hover,
.pagination > li > a:focus,
.pagination > li > span:focus {
  background-color: #e4e7ea;
}
.pagination-split li {
  margin-left: 5px;
  display: inline-block;
  float: left;
}
.pagination-split li:first-child {
  margin-left: 0;
}
.pagination-split li a {
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
}
.pagination > .active > a,
.pagination > .active > span,
.pagination > .active > a:hover,
.pagination > .active > span:hover,
.pagination > .active > a:focus,
.pagination > .active > span:focus {
  background-color: #005bb5;
  border-color: #005bb5;
}
.pager .disabled > a,
.pager .disabled > a:focus,
.pager .disabled > a:hover,
.pager .disabled > span {
  opacity: 0.6;
}
.pager li > a,
.pager li > span {
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  color: #4c5667;
}

.nav.nav-tabs + .tab-content {
  background: #ffffff;
  margin-bottom: 20px;
  padding: 20px;
}
.tabs-vertical-env {
  margin-bottom: 30px;
}
.tabs-vertical-env .tab-content {
  background: #ffffff;
  display: table-cell;
  margin-bottom: 30px;
  padding: 30px;
  vertical-align: top;
}
.tabs-vertical-env .nav.tabs-vertical {
  display: table-cell;
  min-width: 120px;
  vertical-align: top;
  width: 150px;
}
.tabs-vertical-env .nav.tabs-vertical li.active > a {
  background-color: #ffffff;
  border: 0;
}
.tabs-vertical-env .nav.tabs-vertical li > a {
  color: #333333;
  text-align: center;
  font-family: "Roboto", sans-serif;
  font-weight: 500;
  white-space: nowrap;
}
.nav.nav-tabs > li.active > a {
  background-color: #ffffff;
  border: 0;
}
.nav.nav-tabs > li > a {
  background-color: transparent;
  border-radius: 0;
  border: none;
  color: #333333 !important;
  cursor: pointer;
  line-height: 50px;
  font-weight: 500;
  padding: 0px 20px;
  font-family: "Roboto", sans-serif;
}
.nav.nav-tabs > li > a:hover {
  color: #f9cd48 !important;
}
.nav.tabs-vertical > li > a {
  background-color: transparent;
  border-radius: 0;
  border: none;
  color: #333333 !important;
  cursor: pointer;
  line-height: 50px;
  padding: 0px 20px;
}
.nav.tabs-vertical > li > a:hover {
  color: #f9cd48 !important;
}
.tab-content {
  box-shadow: none;
  color: #777777;
}
.nav.nav-tabs > li:last-of-type a {
  margin-right: 0px;
}
.nav.nav-tabs {
  border-bottom: 0;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}
.navtab-custom li {
  margin-bottom: -2px;
}
.navtab-custom li a {
  border-top: 2px solid transparent !important;
}
.navtab-custom li.active a {
  border-top: 2px solid #f9cd48 !important;
}
.nav-tab-left.navtab-custom li a {
  border: none !important;
  border-left: 2px solid transparent !important;
}
.nav-tab-left.navtab-custom li.active a {
  border-left: 2px solid #f9cd48 !important;
}
.nav-tab-right.navtab-custom li a {
  border: none !important;
  border-right: 2px solid transparent !important;
}
.nav-tab-right.navtab-custom li.active a {
  border-right: 2px solid #f9cd48 !important;
}
.nav-tabs.nav-justified > .active > a,
.nav-tabs.nav-justified > .active > a:hover,
.nav-tabs.nav-justified > .active > a:focus,
.tabs-vertical-env .nav.tabs-vertical li.active > a {
  border: none;
}
.nav-tabs > li.active > a,
.nav-tabs > li.active > a:focus,
.nav-tabs > li.active > a:hover,
.tabs-vertical > li.active > a,
.tabs-vertical > li.active > a:focus,
.tabs-vertical > li.active > a:hover {
  color: #337ab7 !important;
}
/* Dropcap */
.dropcap {
  font-size: 3.1em;
}
.dropcap,
.dropcap-circle,
.dropcap-square {
  display: block;
  float: left;
  font-weight: 400;
  line-height: 36px;
  margin-right: 6px;
  text-shadow: none;
}
/* Modal */
.modal .modal-dialog .modal-content {
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  border-color: #dddddd;
  border-radius: 2px;
  box-shadow: none;
  padding: 30px;
}
.modal .modal-dialog .modal-content .modal-header {
  border-bottom-width: 2px;
  margin: 0;
  padding: 0;
  padding-bottom: 15px;
}
.modal .modal-dialog .modal-content .modal-body {
  padding: 20px 0;
}
.modal .modal-dialog .modal-content .modal-footer {
  padding: 0;
  padding-top: 15px;
}
.modal-full {
  width: 98%;
}
.modal-content .nav.nav-tabs + .tab-content {
  margin-bottom: 0px;
}
.modal-content .panel-group {
  margin-bottom: 0px;
}
.modal-content .panel {
  border-top: none;
}
/* Custom-modal */
.modal-demo {
  background-color: #fff;
  width: 600px;
  -webkit-border-radius: 4px;
  border-radius: 4px;
  -moz-border-radius: 4px;
  background-clip: padding-box;
  display: none;
}
.modal-demo .close {
  position: absolute;
  top: 15px;
  right: 25px;
  color: #eeeeee;
}
.custom-modal-title {
  padding: 15px 25px 15px 25px;
  line-height: 22px;
  font-size: 18px;
  background-color: #f9cd48;
  color: #ffffff;
  text-align: left;
  margin: 0px;
}
.custom-modal-text {
  padding: 20px;
}
.custombox-modal-flash .close,
.custombox-modal-rotatedown .close {
  top: 20px;
  z-index: 9999;
}
.tabs-vertical-env .tab-content {
  margin-bottom: 0px;
}
.table > thead > tr > td.middle-align,
.table > tbody > tr > td.middle-align {
  vertical-align: middle;
}
.borderless td,
.borderless th {
  border: none;
}
.legendLabel {
  padding-left: 10px !important;
}
/* Alerts */
.alert-success {
  background-color: rgba(0, 152, 134, 0.25) !important;
  border-color: rgba(0, 152, 134, 0.5) !important;
  color: #009886;
}
.alert-success-dark {
  background-color: #dff0d8 !important;
  border-color: #d6e9c6 !important;
  color: #3c763d;
}
.alert-success .alert-link {
  color: #009886;
}
.alert-info {
  background-color: rgba(61, 220, 247, 0.2) !important;
  border-color: rgba(61, 220, 247, 0.5) !important;
  color: #3ddcf7;
}
.alert-info .alert-link {
  color: #3ddcf7;
}
.alert-warning {
  background-color: rgba(255, 170, 0, 0.2) !important;
  border-color: rgba(255, 170, 0, 0.5) !important;
  color: #ffaa00;
}
.alert-warning-flex {
  display: flex;
}
.alert-item-center {
  margin: auto;
}
.alert-item-action-button {
  margin-left: auto;
}
.alert-warning-dark {
  color: #8a6d3b;
}
.alert-warning .alert-link {
  color: #ffaa00;
}
.alert-danger {
  background-color: rgba(239, 83, 80, 0.2) !important;
  border-color: rgba(239, 83, 80, 0.5) !important;
  color: #ef5350;
}
.alert-danger .alert-link {
  color: #ef5350;
}

/* List Group */
.list-group-item.active {
  background: rgba(152, 166, 173, 0.1);
  border-color: rgba(152, 166, 173, 0.3);
  color: #98a6ad;
  z-index: 2;
}
.list-group-item.active:hover {
  background: rgba(152, 166, 173, 0.1);
  border-color: rgba(152, 166, 173, 0.3);
  color: #98a6ad;
  z-index: 2;
}
.list-group-item.active:hover .list-group-item-text {
  color: #98a6ad;
}
.list-group-item.active:focus {
  background: rgba(152, 166, 173, 0.1);
  border-color: rgba(152, 166, 173, 0.3);
  color: #98a6ad;
  z-index: 2;
}
.list-group-item.active:focus .list-group-item-text {
  color: #98a6ad;
}
.list-group-item.active .list-group-item-text {
  color: #98a6ad;
}
.list-group-item.disabled,
.list-group-item.disabled:focus,
.list-group-item.disabled:hover {
  background: rgba(152, 166, 173, 0.2);
  border-color: rgba(152, 166, 173, 0.3);
  color: #98a6ad;
}
.list-group-item {
  border: 1px solid rgba(152, 166, 173, 0.25);
  border-radius: 0;
  padding: 10px 20px;
}
.list-group-item:first-child,
.list-group-item:last-child {
  border-radius: 0;
}
.list-group-item:hover {
  background: rgba(152, 166, 173, 0.1);
}
.list-group-item-heading {
  font-weight: 300;
}
.list-group-item.active > .badge {
  color: #f9cd48;
}
.nav-pills > .active > a > .badge {
  color: #f9cd48;
}
.has-success .form-control {
  border-color: #009886;
  box-shadow: none !important;
}
.has-warning .form-control {
  border-color: #ffaa00;
  box-shadow: none !important;
}
.has-error .form-control {
  border-color: #ef5350;
  box-shadow: none !important;
}
.input-group-addon {
  border-radius: 2px;
  border: 1px solid #ddd;
}
/* Tooltips */
.tooltip-inner {
  border-radius: 3px;
  padding: 3px;
}
.jqstooltip {
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  width: auto !important;
  height: auto !important;
}
/* Popover */
.popover {
  font-family: inherit;
  border: none;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  -moz-border-radius: 3px;
  background-clip: padding-box;
}
.popover .popover-title {
  background-color: transparent;
  color: #f9cd48;
  font-weight: 600;
}

.pr-50 {
  padding-right: 50px;
}

.panel {
  margin-bottom: 20px;
  background-color: #fff;
  border: 1px solid transparent;
  border-radius: 4px;
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}

.panel-body {
  padding: 15px;
}

.panel-heading {
  padding: 10px 15px;
  border-bottom: 1px solid transparent;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}

.panel-info {
  border-color: #bce8f1;
}

.panel-info > .panel-heading {
  color: #31708f;
  background-color: #d9edf7;
  border-color: #bce8f1;
}

.custom-date-picker .react-date-picker__wrapper {
  border: 1px solid #ddd !important;
  border-radius: 2px;
  font-size: 0.9rem;
  padding: 0.1rem;
}

.list-group-item {
  position: relative;
  display: block;
  padding: 10px 15px;
  margin-bottom: -1px;
  background-color: #fff;
  border: 1px solid #ddd;
}
.list-group-item:first-child {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}
.list-group-item:last-child {
  margin-bottom: 0;
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
}
a.list-group-item,
button.list-group-item {
  color: #555;
}
a.list-group-item .list-group-item-heading,
button.list-group-item .list-group-item-heading {
  color: #333;
}
a.list-group-item:hover,
button.list-group-item:hover,
a.list-group-item:focus,
button.list-group-item:focus {
  color: #555;
  text-decoration: none;
  background-color: #f5f5f5;
}
button.list-group-item {
  width: 100%;
  text-align: left;
}
.list-group-item.disabled,
.list-group-item.disabled:hover,
.list-group-item.disabled:focus {
  color: #777;
  cursor: not-allowed;
  background-color: #eee;
}
.list-group-item.disabled .list-group-item-heading,
.list-group-item.disabled:hover .list-group-item-heading,
.list-group-item.disabled:focus .list-group-item-heading {
  color: inherit;
}
.list-group-item.disabled .list-group-item-text,
.list-group-item.disabled:hover .list-group-item-text,
.list-group-item.disabled:focus .list-group-item-text {
  color: #777;
}
.list-group-item.active,
.list-group-item.active:hover,
.list-group-item.active:focus {
  z-index: 2;
  color: #fff;
  background-color: #337ab7;
  border-color: #337ab7;
}
.list-group-item.active .list-group-item-heading,
.list-group-item.active:hover .list-group-item-heading,
.list-group-item.active:focus .list-group-item-heading,
.list-group-item.active .list-group-item-heading > small,
.list-group-item.active:hover .list-group-item-heading > small,
.list-group-item.active:focus .list-group-item-heading > small,
.list-group-item.active .list-group-item-heading > .small,
.list-group-item.active:hover .list-group-item-heading > .small,
.list-group-item.active:focus .list-group-item-heading > .small {
  color: inherit;
}
.list-group-item.active .list-group-item-text,
.list-group-item.active:hover .list-group-item-text,
.list-group-item.active:focus .list-group-item-text {
  color: #c7ddef;
}
.list-group-item-success {
  color: #3c763d;
  background-color: #dff0d8;
}
a.list-group-item-success,
button.list-group-item-success {
  color: #3c763d;
}
a.list-group-item-success .list-group-item-heading,
button.list-group-item-success .list-group-item-heading {
  color: inherit;
}
a.list-group-item-success:hover,
button.list-group-item-success:hover,
a.list-group-item-success:focus,
button.list-group-item-success:focus {
  color: #3c763d;
  background-color: #d0e9c6;
}
a.list-group-item-success.active,
button.list-group-item-success.active,
a.list-group-item-success.active:hover,
button.list-group-item-success.active:hover,
a.list-group-item-success.active:focus,
button.list-group-item-success.active:focus {
  color: #fff;
  background-color: #3c763d;
  border-color: #3c763d;
}
.list-group-item-info {
  color: #31708f;
  background-color: #d9edf7;
}
a.list-group-item-info,
button.list-group-item-info {
  color: #31708f;
}
a.list-group-item-info .list-group-item-heading,
button.list-group-item-info .list-group-item-heading {
  color: inherit;
}
a.list-group-item-info:hover,
button.list-group-item-info:hover,
a.list-group-item-info:focus,
button.list-group-item-info:focus {
  color: #31708f;
  background-color: #c4e3f3;
}
a.list-group-item-info.active,
button.list-group-item-info.active,
a.list-group-item-info.active:hover,
button.list-group-item-info.active:hover,
a.list-group-item-info.active:focus,
button.list-group-item-info.active:focus {
  color: #fff;
  background-color: #31708f;
  border-color: #31708f;
}
.list-group-item-warning {
  color: #8a6d3b;
  background-color: #fcf8e3;
}
a.list-group-item-warning,
button.list-group-item-warning {
  color: #8a6d3b;
}
a.list-group-item-warning .list-group-item-heading,
button.list-group-item-warning .list-group-item-heading {
  color: inherit;
}
a.list-group-item-warning:hover,
button.list-group-item-warning:hover,
a.list-group-item-warning:focus,
button.list-group-item-warning:focus {
  color: #8a6d3b;
  background-color: #faf2cc;
}
a.list-group-item-warning.active,
button.list-group-item-warning.active,
a.list-group-item-warning.active:hover,
button.list-group-item-warning.active:hover,
a.list-group-item-warning.active:focus,
button.list-group-item-warning.active:focus {
  color: #fff;
  background-color: #8a6d3b;
  border-color: #8a6d3b;
}
.list-group-item-danger {
  color: #a94442;
  background-color: #f2dede;
}
a.list-group-item-danger,
button.list-group-item-danger {
  color: #a94442;
}
a.list-group-item-danger .list-group-item-heading,
button.list-group-item-danger .list-group-item-heading {
  color: inherit;
}
a.list-group-item-danger:hover,
button.list-group-item-danger:hover,
a.list-group-item-danger:focus,
button.list-group-item-danger:focus {
  color: #a94442;
  background-color: #ebcccc;
}
a.list-group-item-danger.active,
button.list-group-item-danger.active,
a.list-group-item-danger.active:hover,
button.list-group-item-danger.active:hover,
a.list-group-item-danger.active:focus,
button.list-group-item-danger.active:focus {
  color: #fff;
  background-color: #a94442;
  border-color: #a94442;
}
.list-group-item-heading {
  margin-top: 0;
  margin-bottom: 5px;
}
.list-group-item-text {
  margin-bottom: 0;
  line-height: 1.3;
}

.form-check-input {
  border-color: $sw-steel-blue-60
}

textarea.form-control {
  border-color: $sw-steel-blue-30
}

.invalid-tooltip {
  z-index: 1001
}
