/* ===========
Waves Effect
=============*/
/*!
* Waves v0.6.0
* http://fian.my.id/Waves
*
* Copyright 2014 <PERSON><PERSON><PERSON> E. Si<PERSON> and other contributors
* Released under the MIT license
* https://github.com/fians/Waves/blob/master/LICENSE
*/
.waves-effect {
    position: relative;
    cursor: pointer;
    display: inline-block;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    vertical-align: middle;
    z-index: 1;
    will-change: opacity, transform;
    -webkit-transition: all 0.3s ease-out;
    -moz-transition: all 0.3s ease-out;
    -o-transition: all 0.3s ease-out;
    -ms-transition: all 0.3s ease-out;
    transition: all 0.3s ease-out;
}
.waves-effect .waves-ripple {
    position: absolute;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    margin-top: -10px;
    margin-left: -10px;
    opacity: 0;
    background: rgba(0, 0, 0, 0.2);
    -webkit-transition: all 0.7s ease-out;
    -moz-transition: all 0.7s ease-out;
    -o-transition: all 0.7s ease-out;
    -ms-transition: all 0.7s ease-out;
    transition: all 0.7s ease-out;
    -webkit-transition-property: -webkit-transform, opacity;
    -moz-transition-property: -moz-transform, opacity;
    -o-transition-property: -o-transform, opacity;
    transition-property: transform, opacity;
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    -o-transform: scale(0);
    transform: scale(0);
    pointer-events: none;
}
.waves-effect.waves-light .waves-ripple {
    background-color: rgba(255, 255, 255, 0.45);
}
.waves-effect.waves-red .waves-ripple {
    background-color: rgba(244, 67, 54, 0.7);
}
.waves-effect.waves-yellow .waves-ripple {
    background-color: rgba(255, 235, 59, 0.7);
}
.waves-effect.waves-orange .waves-ripple {
    background-color: rgba(255, 152, 0, 0.7);
}
.waves-effect.waves-purple .waves-ripple {
    background-color: rgba(156, 39, 176, 0.7);
}
.waves-effect.waves-green .waves-ripple {
    background-color: rgba(76, 175, 80, 0.7);
}
.waves-effect.waves-teal .waves-ripple {
    background-color: rgba(0, 150, 136, 0.7);
}
.waves-effect.waves-primary .waves-ripple {
    background-color: rgba(249, 205, 72, 0.4);
}
.waves-notransition {
    -webkit-transition: none !important;
    -moz-transition: none !important;
    -o-transition: none !important;
    -ms-transition: none !important;
    transition: none !important;
}
.waves-circle {
    -webkit-transform: translateZ(0);
    -moz-transform: translateZ(0);
    -ms-transform: translateZ(0);
    -o-transform: translateZ(0);
    transform: translateZ(0);
    text-align: center;
    width: 2.5em;
    height: 2.5em;
    line-height: 2.5em;
    border-radius: 50%;
    -webkit-mask-image: none;
}
.waves-input-wrapper {
    border-radius: 0.2em;
    vertical-align: bottom;
}
.waves-input-wrapper .waves-button-input {
    position: relative;
    top: 0;
    left: 0;
    z-index: 1;
}
.waves-block {
    display: block;
}
/* ===========
Animation
=============*/
/* Bounce 1 */
@-webkit-keyframes cd-bounce-1 {
    0% {
        opacity: 0;
        -webkit-transform: scale(0.5);
    }
    60% {
        opacity: 1;
        -webkit-transform: scale(1.2);
    }
    100% {
        -webkit-transform: scale(1);
    }
}
@-moz-keyframes cd-bounce-1 {
    0% {
        opacity: 0;
        -moz-transform: scale(0.5);
    }
    60% {
        opacity: 1;
        -moz-transform: scale(1.2);
    }
    100% {
        -moz-transform: scale(1);
    }
}
@-o-keyframes cd-bounce-1 {
    0% {
        opacity: 0;
        -o-transform: scale(0.5);
    }
    60% {
        opacity: 1;
        -o-transform: scale(1.2);
    }
    100% {
        -o-transform: scale(1);
    }
}
@keyframes cd-bounce-1 {
    0% {
        opacity: 0;
        -webkit-transform: scale(0.5);
        -moz-transform: scale(0.5);
        -ms-transform: scale(0.5);
        -o-transform: scale(0.5);
        transform: scale(0.5);
    }
    60% {
        opacity: 1;
        -webkit-transform: scale(1.2);
        -moz-transform: scale(1.2);
        -ms-transform: scale(1.2);
        -o-transform: scale(1.2);
        transform: scale(1.2);
    }
    100% {
        opacity: 1;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
        transform: scale(1);
    }
}
/* Bounce 2 */
@-webkit-keyframes cd-bounce-2 {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-100px);
    }
    60% {
        opacity: 1;
        -webkit-transform: translateX(20px);
    }
    100% {
        -webkit-transform: translateX(0);
    }
}
@-moz-keyframes cd-bounce-2 {
    0% {
        opacity: 0;
        -moz-transform: translateX(-100px);
    }
    60% {
        opacity: 1;
        -moz-transform: translateX(20px);
    }
    100% {
        -moz-transform: translateX(0);
    }
}
@-o-keyframes cd-bounce-2 {
    0% {
        opacity: 0;
        -o-transform: translateX(-100px);
    }
    60% {
        opacity: 1;
        -o-transform: translateX(20px);
    }
    100% {
        opacity: 1;
        -o-transform: translateX(0);
    }
}
@keyframes cd-bounce-2 {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-100px);
        -moz-transform: translateX(-100px);
        -ms-transform: translateX(-100px);
        -o-transform: translateX(-100px);
        transform: translateX(-100px);
    }
    60% {
        opacity: 1;
        -webkit-transform: translateX(20px);
        -moz-transform: translateX(20px);
        -ms-transform: translateX(20px);
        -o-transform: translateX(20px);
        transform: translateX(20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        -moz-transform: translateX(0);
        -ms-transform: translateX(0);
        -o-transform: translateX(0);
        transform: translateX(0);
    }
}
/* Progressbar Animated */
@-webkit-keyframes animationProgress {
    from {
        width: 0;
    }
}
@keyframes animationProgress {
    from {
        width: 0;
    }
}
/* Portlets loader */
@-webkit-keyframes loaderAnimate {
    0% {
        -webkit-transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(220deg);
    }
}
@-moz-keyframes loaderAnimate {
    0% {
        -moz-transform: rotate(0deg);
    }
    100% {
        -moz-transform: rotate(220deg);
    }
}
@-o-keyframes loaderAnimate {
    0% {
        -o-transform: rotate(0deg);
    }
    100% {
        -o-transform: rotate(220deg);
    }
}
@keyframes loaderAnimate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(220deg);
    }
}
@-webkit-keyframes loaderAnimate2 {
    0% {
        box-shadow: inset #555 0 0 0 8px;
        -webkit-transform: rotate(-140deg);
    }
    50% {
        box-shadow: inset #555 0 0 0 2px;
    }
    100% {
        box-shadow: inset #555 0 0 0 8px;
        -webkit-transform: rotate(140deg);
    }
}
@-moz-keyframes loaderAnimate2 {
    0% {
        box-shadow: inset #555 0 0 0 8px;
        -moz-transform: rotate(-140deg);
    }
    50% {
        box-shadow: inset #555 0 0 0 2px;
    }
    100% {
        box-shadow: inset #555 0 0 0 8px;
        -moz-transform: rotate(140deg);
    }
}
@-o-keyframes loaderAnimate2 {
    0% {
        box-shadow: inset #555 0 0 0 8px;
        -o-transform: rotate(-140deg);
    }
    50% {
        box-shadow: inset #555 0 0 0 2px;
    }
    100% {
        box-shadow: inset #555 0 0 0 8px;
        -o-transform: rotate(140deg);
    }
}
@keyframes loaderAnimate2 {
    0% {
        box-shadow: inset #555 0 0 0 8px;
        -webkit-transform: rotate(-140deg);
        -moz-transform: rotate(-140deg);
        -ms-transform: rotate(-140deg);
        transform: rotate(-140deg);
    }
    50% {
        box-shadow: inset #555 0 0 0 2px;
    }
    100% {
        box-shadow: inset #555 0 0 0 8px;
        -webkit-transform: rotate(140deg);
        -moz-transform: rotate(140deg);
        -ms-transform: rotate(140deg);
        transform: rotate(140deg);
    }
}
@keyframes loaderAnimate2 {
    0% {
        box-shadow: inset #999 0 0 0 17px;
        transform: rotate(-140deg);
    }
    50% {
        box-shadow: inset #999 0 0 0 2px;
    }
    100% {
        box-shadow: inset #999 0 0 0 17px;
        transform: rotate(140deg);
    }
}
