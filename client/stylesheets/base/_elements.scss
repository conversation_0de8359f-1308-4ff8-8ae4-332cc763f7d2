@import "{}/client/stylesheets/base/colors";

hr.dark {
  border-color: #666666;
}

@mixin insetGreyHeading($marginTop) {
  color: rgba(153, 153, 153, 0.8);
  margin-top: $marginTop;
  text-align: center;
  text-shadow: 1px 4px 6px #fff, 0 0 0 #000, 1px 4px 3px #f5f3f3;
}

h1.stamped {
  @include insetGreyHeading(25px);
  width: 100%;
}

h3.stamped {
  @include insetGreyHeading(10px);
  font-size: 1.3em;
  font-style: italic;
  font-weight: 600;
  letter-spacing: 1px;
  margin: 10px 60px;
  text-align: left;
}

h5.stamped {
  @include insetGreyHeading(10px);
  font-size: 1em;
  font-style: italic;
  font-weight: 600;
  letter-spacing: 1px;
  margin: 10px 60px;
  text-align: left;
}

h3.manage-scores-stamped {
  @include insetGreyHeading(10px);
  font-size: 1.3em;
  font-style: italic;
  font-weight: 600;
  letter-spacing: 1px;
  text-align: center;
}

.btn-high {
  line-height: 44px;
}

.confetti-container {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.footer-block {
  color: $sw-steel-blue-60;
}

.btn-close-custom {
  position: absolute;
  right: 2px;
  top: 2px;
  background: none;
  border: none;
  margin: 0;
  padding: 0 5px 0 0;
  font-size: 2rem;
  line-height: 1;
  cursor: pointer;
}