div.overviewContainer {
  background: #f8f7f3;
  bottom: 0;
  left: 215px;
  padding: 10px 20px 25px;
  position: absolute;
  right: 0;
  top: 0;

  h1 {
    font-size: 2.5em;
    font-weight: 900;
    letter-spacing: 1px;
    margin-top: 10px;
    text-align: center;
  }

  h2 {
    font-size: 1.3em;
    font-weight: 900;
    margin: 10px 0 0;
  }

  p {
    font-size: 0.9em;
  }

  .chart {
    background: none;
    border-bottom: none;
    margin-top: 50px;

    .highcharts-container {
      border: none;
    }
  } /*-- /.chart --*/

  .conScreeningResults {
    .lstScreeningWindowSummary {
      font-weight: 900;
      list-style: none;
      margin-left: -25px;

      $fall: #f9bf68; /* orange */
      $winter: #70a1d9; /* blue */
      $spring: #aee57c; /* green */

      @mixin listBeforeStyling($color) {
        &:before {
          color: $color;
          content: "\f0c8";
          display: block;
          float: left;
          font-family: "fontAwesome";
          left: -12px;
          position: relative;
        }
      }

      li {
        font-size: 0.8em;
        font-weight: bold;

        &.pending {
          color: #bababa;
          font-weight: normal;
        } /*-- /.pending --*/

        &.fall {
          @include listBeforeStyling($fall);
          &.pending {
            @include listBeforeStyling(lighten($fall, 10%));
          }
        } /*-- /.fall --*/

        &.winter {
          @include listBeforeStyling($winter);
          &.pending {
            @include listBeforeStyling(lighten($winter, 10%));
          }
        } /*-- /.winter --*/

        &.spring {
          @include listBeforeStyling($spring);
          &.pending {
            @include listBeforeStyling(lighten($spring, 10%));
          }
        } /*-- /.spring --*/
      } /*-- /li --*/
    } /*-- /.lstScreeningWindowSummary --*/
  } /*-- /#screeningResults --*/

  .school-wide {
  } /*-- /.school-wide --*/

  .grade-wide {
  } /*-- /.grade-wide --*/
} /*-- /.overviewContainer --*/

div.conOverviewHeader {
  position: relative;
  margin-top: 0.5rem;

  h1 {
    font-size: 2em;
    font-weight: 700;
    margin: 0;
    text-align: left;
    line-height: 1;
  }

  .overview-stats {
    bottom: 0;
    position: absolute;
    right: 0;
    margin: 0;

    .stat {
      color: #21cbb3;
      font-size: 1.3em;

      small {
        color: #333333;
        font-size: 0.5em;
        font-weight: bold;
        margin: 0 25px 0 5px;
      }
    } /*-- /.stat --*/
  } /*-- /.overview-stats --*/
} /*-- /.conOverviewHeader --*/

div.conOverviewNavBar {
  list-style: none;
  margin: 40px auto 0;

  ul {
    list-style: none;

    li {
      float: left;
      margin: 0 15px;

      a {
        color: #888f95;
        font: 0.8em "Lato";
        padding: 0 5px 2px;

        &.current {
          $currentColor: #00ccb3;
          border-bottom: 3px solid $currentColor;
          color: $currentColor;
        }

        &:hover {
        }
      } /*-- /.a --*/
    } /*-- /.li --*/
  } /*-- /.ul --*/
} /*-- /.conOverviewNavBar --*/

div.conOverviewMain {
  bottom: 25px;
  left: 20px;
  overflow: auto;
  position: absolute;
  right: 20px;
  top: 110px;

  h2 {
    i {
      color: #1a63a2;
    }
  }

  section {
    margin-bottom: 50px;
  }
}

div.conScreeningNotice {
  background: #21cbb3; // Mint Green
  background-clip: padding-box;
  border: 2px solid #21cbb3; // Mint Green
  border-radius: 8px;
  margin: 6px 0 0;

  &.screeningWindowStarted {
    background: #fadd56; // Brown Yellow
    border: 2px solid #fadd56; // Brown Yellow

    div.conScreeningNotice-Heading {
      color: #a08103 !important; // Brown

      h2 {
        color: #a08103 !important; // Brown
      }
    }
  }

  div.conScreeningNotice-Heading {
    color: #ffffff;
    padding: 2px 2px 0 8px;

    h2 {
      color: #ffffff;
      float: left;
      font-size: 0.9em;
      font-weight: normal;
      line-height: 30px;
      margin: 0 10px;
    }

    button.btnNoticeAction {
      border: none;
      border-radius: 5px;
      float: right;
      font-size: 14px;
      line-height: 1.4;
      margin: 2px 2px 0 0;
      padding: 2px 8px !important;
      text-align: left;
      width: 150px;

      &:after {
        display: block;
        float: right;
        font: 15px "FontAwesome";
        margin: 4px 3px 0 7px;
      }

      &.btnStartScreening {
        background: #e9c93d;

        &:after {
          content: "\f0da";
        }
      } /*-- /.btnStartScreening --*/

      &.btnViewProgress {
        background: #7ae0d1;

        &:after {
          content: "\f0d7";
        }
      } /*-- /.btnViewProgress --*/
    } /*-- /.btnNoticeAction --*/

    div.iconCallout {
      float: left;
      margin-top: 0;
    }
  } /*-- /conScreeningNotice-Heading --*/

  &.closed {
    height: 38px;
    overflow: hidden;
  }

  &.opened {
    // background: #efede7;
    background: #eff9f7;
    border-radius: 8px;
    height: auto;

    div.conScreeningNotice-Heading {
      h2 {
        color: #21cbb3;
        font-size: 2em;
        font-weight: 900;
        margin: 20px 18px;
      }

      button.btnViewProgress {
        // background: #eff9f7;
        // border: #7ae0d1 dotted 2px;
        // color: #797979 !important;
        // font-size: 14px;
        // margin-top: -2px;
        // margin-right: -4px;
        // padding: 4px 8px 6px 8px !important;

        &:after {
          content: "\f0d8";
          margin-top: 3px;
        }
      }

      div.iconCallout {
        background: #dcefeb;
        border-radius: 40px;
        color: #797979;
        font-size: 3em;
        margin-top: 0;
        padding: 0 10px;
      }
    }

    div.conScreeningNotice-Content {
      float: left;
      margin: -15px 0 5px 100px;
    } /*-- /.conScreeningNotice-Content --*/

    div.conScreeningNotice-Progress {
      clear: both;
      margin: 0 20px 0;
      position: relative;

      &.isPrinting {
        display: flex;
        justify-content: center;
        align-content: center;
        flex-wrap: wrap;
        flex-grow: 1;
        &:after {
          display: none !important;
        }
      }

      &.progressBar {
        span.classesScreened {
          display: inline-block;
          font-size: 20px;
          font-style: italic;
          float: right;
          height: 25px;
          position: absolute;
          right: 5px;
          top: -2px;
        }

        .progress {
          background-color: #ededed;
          border-radius: 3px;
          height: 25px;
          margin-bottom: 0;

          .progress-bar {
            background-color: #21cbb3;
            color: #505458;
            font-size: 16px;
            font-style: italic;
            line-height: 25px;
          } /*-- /.progress-bar --*/
        } /*-- /.progress --*/
      } /*-- /.progressBar --*/

      &.circleCharts {
        .circle-chart {
          $circlSize: 150px;
          float: left;
          height: $circlSize;
          margin: 20px 1.35% 0;
          width: $circlSize;
          // width: $circlSize - 20;

          .circleChartTitle {
            .percent {
              display: block;
              font-size: 2em;
              font-weight: 400;
            }
          }
        } /*-- /.circle-chart --*/
        &.isPrinting {
          .circle-chart {
            $circlSize: 115px;
            height: $circlSize;
            width: $circlSize;

            .circleChartTitle {
              font-size: 0.8em;
            }
          }
        }
      } /*-- /.circleCharts --*/
    } /*-- /.conScreeningNotice-Progress --*/
  } /*-- /.opened --*/

  &.gradeLevelProgress {
    &.opened {
      .conScreeningNotice-Heading {
        h2 {
          font-size: 1.7em;
          margin: 20px 8px;
        }
        div.iconCallout {
          font-size: 1.6em;
          padding: 0 6px;
        }
      }
      .conScreeningNotice-Content {
        margin: -15px 0 0 60px;
      }
    }
  }

  .studentGroupProgress {
    ul {
      display: flex;
      flex-wrap: wrap;
      list-style: none;
      margin: 0 auto 30px;
      padding: 0;
      width: 90%;

      li {
        display: inline-block;
        flex-grow: 0;
        margin: 15px 10px 0 0;
        padding-left: 31px;
        width: calc(25% - 36px);
        padding-right: 15px;

        i.fa {
          color: #e20000;
          font-size: 1.2em;
          left: 7px;
          top: 8px;
          width: 20px;

          &.completed {
            color: #009886;
          }
          &.inactive {
            color: rgba(32, 32, 32, 0.21);
          }
        }
      }
    }
  } /*-- /ul.studentGroupProgress --*/
} /*-- /.conScreeningNotice --*/

.conOverviewContent {
  background: #ffffff;
  border: 1px solid #dddddd;
  border-radius: 0 0 3px 3px;
  padding: 25px;
}

.tblIntvSummaryTable {
  width: 100%;
  text-align: center;
  padding: 10px 5px;
  color: #555555;
  font-size: 12px;
  font-weight: normal;
  &.isPrinting {
    font-size: 10px;
  }
  @mixin cellStyles() {
    text-align: center;
    padding: 10px 5px;
  }
  .rowIndvSummaryHeading {
    div:first-child {
      text-align: left;

      &.text-center {
        text-align: center;
      }
    }
    margin-bottom: 10px;
  }

  .rowIndvSummary.classwideSummary {
    border-bottom: 1px solid #dddddd;
    //margin: 15px 2px 15px 2px;
    //padding: 10px 10px 10px 0;
    padding-bottom: 15px;
  }

  .individual-interventions-group {
    color: #555555;
    border-top: 1px solid #dddddd;
    border-bottom: 1px solid #dddddd;
    text-align: center;
    font-size: 16px;

    div:last-child {
      border-bottom: none;
    }

    .grade-separator {
      border-top: 1px solid #ddd;
    }

    .rowIndvSummary {
      div:first-child {
        text-align: left;

        &.text-center {
          text-align: center;
        }
      }
      text-align: center;
      margin: 0;
      padding: 6px 0;

      &.isPrinting {
        font-size: 75%;
      }

      small {
        color: #999999;
        display: block;
        text-align: center;
        font-size: 10px;
        font-weight: normal;
        &.text-danger {
          color: #ef5350;
        }
        &.text-underline {
          text-decoration: underline;
        }
      }
    }
    .rowIndvStudents {
      & > div:first-child {
        text-align: left;
        padding-left: 40px;
      }
      .rowIndvStudents-consistency {
        small {
          color: #999999;
          display: block;
          font-size: 10px;
          font-weight: normal;
        }
      }
      .rowIndvStudents-currentAssessment {
        font-size: 80%;
      }
      margin-top: 8px;
      margin-bottom: 8px;
    }
  }
}

.quickInfoSection .collapsed {
  li:nth-child(1n + 5) {
    display: none;
  }
}

.move-students-modal {
  .modal-header {
    border-width: 0;
    padding-bottom: 0;
  }
}

.list-group-header-static {
  &.list-group-item {
    padding: 5px 20px;
  }

  .list-group-item-heading {
    font-weight: bold;
  }

  &:hover {
    background: #fff;
  }
}

ul.vertical-centered li {
  line-height: 1.8em;

  i {
    line-height: 1.8em;
  }
}
button.red-hover:hover,
i.red-hover:hover {
  color: red !important;
  cursor: pointer;
}

button.blue-hover:hover,
i.blue-hover:hover {
  color: #337ab7 !important;
  cursor: pointer;
}

button.blue-hover:disabled {
  opacity: 0.6;
  border: none;
}

.inputAlignedWithLabel {
  width: 1em;
  height: 1em;
}

.alignWithInlineButtons {
  vertical-align: middle;
  padding: 20px;
}

div.conNewsBannerPlaceholder {
  height: 20px;
  visibility: hidden;
}

div.conNewsBanner {
  background: #fadd56;
  background-clip: padding-box;
  border: 2px solid #fadd56;
  border-radius: 8px;
  flex-grow: 1;
  height: 38px;
  overflow: hidden;
  display: inline;
  width: auto;
  left: 20px;
  right: 20px;
  z-index: 100;
  margin: 0 0 5px;
  padding: 3px;

  &.expanded {
    height: auto;

    .conNewsBanner-Heading {
      p {
        white-space: initial;
      }
    }
  }

  div.conNewsBanner-Heading {
    p {
      color: #e6cc4d;
      font-size: 2em;
      font-weight: 900;
      margin: 20px 18px;
    }
  }

  div.conNewsBanner-Heading {
    align-items: flex-start;
    color: #ffffff;
    padding: 2px 2px 0 8px;

    p {
      color: #ffffff;
      float: left;
      font-size: 0.9em;
      font-weight: normal;
      line-height: 30px;
      margin: 0;
      padding: 0 10px;
      width: calc(100% - 45px);
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }

    button.btnNoticeAction {
      border: none;
      border-radius: 5px;
      float: right;
      font-size: 14px;
      line-height: 1.4;
      margin: 2px 2px 0 0;
      padding: 2px 8px !important;
      text-align: left;
      width: 125px;

      &:after {
        display: block;
        float: right;
        font: 15px "FontAwesome";
        margin: 4px 3px 0 7px;
      }

      &.btnViewProgress {
        background: #7ae0d1;

        &:after {
          content: "\f0da";
        }
      }
    }

    button.btnTransparent {
      background-color: #fff0 !important;
      border-color: #fff0 !important;
    }

    div.iconCallout {
      float: left;
      margin-top: 0;
    }
  }
}

.news-data-admin-dashboard-offset {
  margin: -15px 20px 0 20px;
}

.program-evaluation-table-container {
  .rowIndvSummaryHeading {
    font-size: 80% !important;
  }

  .individual-interventions-group {
    font-size: 100% !important;
  }
}

.individual-interventions-group {
  .isPrinting & {
    border-bottom: none;
  }
}

.tblIntvSummaryTable.isPrinting {
  padding-bottom: 0;
}
