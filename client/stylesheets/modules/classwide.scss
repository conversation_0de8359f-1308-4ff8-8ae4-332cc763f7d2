.card-box.intervention-container {
  margin: 15px 15px 15px 5px;
  overflow: hidden;
  padding: 30px 0;
  .student-group-header {
    border-bottom: 1px solid #ccc;
    .student-group-nav {
      display: -webkit-flex; /* Safari */
      display: flex;
      // display: -webkit-inline-flex; /* Safari */
      // display: inline-flex;

      -webkit-flex-direction: row; /* Safari */
      flex-direction: row;

      -webkit-flex-wrap: nowrap; /* Safari */
      flex-wrap: nowrap;

      -webkit-justify-content: center; /* Safari */
      justify-content: center;
    }
  }
}

.intervention-content.classwide {
  border-bottom: 1px solid #ddd;
  padding: 15px 0 0;
  small {
    color: #bdbdbd;
    display: block;
    font-size: 80%;
    font-style: italic;
    font-weight: 300;
    margin: 0 0 5px;
    vertical-align: super;
  }
  h4 {
    margin-bottom: 20px;
  }
  .intervention-materials {
    display: inline;
    float: right;
  }
  span.roi {
    float: right;
    font-size: 0.75em;
    font-weight: normal;
    margin-right: 20px;
  }
}

.student-item {
  .student-name {
    min-width: 265px;
    &.after-screening {
      color: #ccc;
    }
  }
  td .remove-score,
  td .screening-complete {
    color: #ccc;
  }
  &:hover td {
    &.student-name {
      color: #797979;
      .fa-check-circle-o {
        color: #009886;
      }
    }
    input[disabled] {
      opacity: 1;
    }
    .screening-complete {
      color: #009886;
    }
    .score-recorded {
      color: #797979;
    }
    .remove-score {
      color: red;
    }
  }
}

.not-screened {
  font-size: 0.8em;
  font-weight: 300;
  line-height: 1.2em;
  padding: 0 1px;
  &:hover {
    color: #337ab7;
  }
}

.print-materials-buttons-row {
  min-height: 36px;
  line-height: 33px;
}

.print-materials-button {
  display: inline-flex;
  flex-direction: column;
  justify-content: center;
}

.print-materials-item {
  width: 100%;
}

.print-materials-link {
  display: block;
  clear: both;
  font-weight: normal;
  color: #333333;
  white-space: nowrap; // prevent links from randomly breaking onto new lines
}

#dropdownUpcomingMaterials {
  white-space: normal;
  &+ .dropdown-menu .dropdown {
    width: 100%;
  }
}

.sideways
{
  -ms-writing-mode: tb-rl;
  -webkit-writing-mode: vertical-rl;
  writing-mode: vertical-rl;
  transform: rotate(180deg);
  max-height: 300px;
  bottom: 1px;
  position: relative;
}

.dot {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  display: inline-block;
}

.dot-small {
  height: 15px;
  width: 15px;
  border-radius: 50%;
  display: inline-block;
}
