.select-with-ellipsis {
  select {
    min-height: 8em !important;
    resize: vertical;
    option {
      max-height: 10em;
      text-overflow: ellipsis;
      overflow: hidden;
      border-bottom: 1px solid #ccc;
      &:hover {
        overflow: visible;
        white-space: normal;
        word-wrap: break-word;
      }
    }
  }
}

.table-with-ellipsis {
  div {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    height: auto;
    max-height: 3em;
    line-height: 1em;
  }
  tr:hover{
    div{
      overflow: visible;
      white-space: normal !important;
      max-height: inherit;
      word-wrap: break-word;
    }
  }
}

.table-layout-fixed {
  table-layout: fixed;
}
