div.conPrintScreenAssessments {
  text-align: center;
  width: 100%;

  .print-cta {
    border: 1px solid #cccccc;
    border-radius: 4px;
    margin: 40px auto;
    padding: 0 15px 30px;
    position: relative;
    width: 325px;

    .fa-print {
      background: #fff;
      border: 2px solid #cecece;
      border-radius: 50%;
      display: block;
      font-size: 30px;
      margin: -27px auto 10px;
      padding: 10px 12px;
      width: 53px;
    }

    .spinner-full {
      margin: 10px auto 30px;
    }
  }
}

.screening-status {
  text-align: right;
  form {
    display: inline;
    input[disabled] {
      opacity: 0.7;
    }
  }
  h4 {
    color: #a8a8a8;
    margin: 0;
  }
  p {
    margin: 0;
  }
  .btn-link {
    color: #ccc;
    font-size: 0.65em;
  }
  .lstScoreInputs {
    padding: 0;

    li {
      position: relative;

      .help-block {
        font-weight: 900;
        font-size: 10px;
        line-height: 1.3em;
        padding: 8px 20px 0 20px;
        position: absolute;
        text-decoration: none;
        top: 30px;
        vertical-align: middle;
        width: 100%;

        &.text-success {
          font-weight: 300;
          color: #0dd006;
        }
        &.text-warning {
          white-space: nowrap;
          padding: 8px 0 0 0;
        }
      }
      input::-webkit-input-placeholder {
        /* Chrome/Opera/Safari */
        font-size: 0.8em;
      }
      input::-moz-placeholder {
        /* Firefox 19+ */
        font-size: 0.8em;
      }
      input:-ms-input-placeholder {
        /* IE 10+ */
        font-size: 0.8em;
      }
      input:-moz-placeholder {
        /* Firefox 18- */
        font-size: 0.8em;
      }
    }
  }
}

.screening-cols {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: end;

  li {
    display: inline-block;
    text-align: center;
    width: 100px;
    .form-control {
      display: inline-block;
      margin: 0;
      width: 90px;
    }
  }
}

.student-header {
  width: 30%;
}

.post-screening {
  float: right;
  list-style: none;
  margin: 0;
  padding: 0;
  li {
    color: #ccc;
    display: inline;
    margin: 0 10px;
    a {
      color: #ccc;
      display: inline-block;
      font-size: 0.85em;
      padding: 3px 5px;
      text-align: right;
    }
  }
}

.intervention-table.table {
  // max-width: 92%;
  margin: auto;
}

.score-status {
  text-align: right;
  .alert {
    padding: 10px 5px;
    text-align: center;
    .fa {
      margin-right: 5px;
    }
  }
}

.fixed-classwide-intervention-header {
  background: #fff;
  box-shadow: 0px 9px 9px -6px #b5b5b5;
  padding: 10px;
  z-index: 25;
}

.screening-table.table,
.intervention-table.table,
student-roster-table.table {
  border-top: 1px solid #f3f3f3;
  margin-top: 1em;
  > thead > tr > th li {
    font-size: 10px;
    line-height: 1.3em;
    padding: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: end;
  }
  > thead > tr > th.split-header-top li {
    justify-content: start;
  }
  > tbody > tr > td {
    vertical-align: middle;
  }
}

.screening-table {
  border-top: 0 !important;
  th {
    border-bottom: 0 !important;
  }

  .screening-student-table-header {
    border-bottom: 1px solid #ddd !important;
  }

  .screening-student-sort {
    padding-top: 0;
  }
  .split-header-top {
    text-align: right;
    border-bottom: 0;
    padding-bottom: 5px;

    li {
      font-weight: bold;
    }
  }
  .split-header-bottom {
    text-align: right;
    padding-top: 0 !important;
    padding-bottom: 5px;
    border-top: 0;

    li {
      font-weight: bold;
    }
  }
}

tr.student-item.active {
  td {
    background-color: rgba(255, 215, 0, 0.1) !important;
  }
  input:focus {
    border: 1px solid #ffd700;
    box-shadow: 1px 1px 2px -1px #dab802;
  }
}

// Individual screening
.individual-screening-item {
  h4 {
    line-height: 1.5em;
    margin: 0;
  }
  h5 {
    margin: 0;
  }
  .ind-screening-result {
    border: 1px solid #c4c4c4;
    border-radius: 3px;
    display: inline-block;
    float: right;
    font-weight: 300;
    margin-top: 10px;
    padding: 0px 5px;
  }
}

.fixed-screening-progress {
  background: #fff;
  padding: 10px 25px 20px;
  z-index: 25;
}

.measure-columns {
  background: #fff;
  box-shadow: 0px 9px 13px -6px #b5b5b5;
  padding: 0 3px 15px;
  top: 65px !important;
  z-index: 25;
}

.screening-results-btn {
  margin-top: 10px;
  margin-bottom: 10px;
}

.disable-border {
  border: 0 !important;
}

.force-classwide-intervention-btn {
  position: relative;
  margin-bottom: 10px;
  padding-top: 15px !important;
}

.prior-year-text {
  margin: 20px auto;
}
