.navbar.navbar-default.student-group-utility {
  background-color: transparent;
  border-color: transparent;
  margin-bottom: 0;
  margin-left: -30px;
  .container {
    padding: 0;
    width: 100%;
    .nav.navbar-nav {
      margin: 0 20px;
      li {
        &.menu-label {
          font-style: italic;
          padding: 0px 10px;
        }
        &.title {
          line-height: 1.65em;
          display: block;
          position: relative;
          border-left: 1px solid #ddd;
          margin: 10px 5px;
          padding: 0px 15px;
        }
        &.active > a {
          background-color: transparent;
        }
        a {
          color: #337ab7;
        }
      }
    }
  }
}

.student-group-layout-header {
  position: relative;
}

#group-name {
  line-height: 35px;
  margin: 0;
}

.profile-view {
  background: #f3f4f6;
  border: 1px solid #ddd;
  border-radius: 3px;

  header {
    background: #f3f4f6;
    margin-top: 0.5rem;
    padding: 5px 0;
    position: relative;

    .printContainer & {
      padding: 5px 10px;
    }

    h1.profile-name {
      display: inline-block;
      font-weight: 700;
      margin: -7px 0 0 0;

      .profile-name-info {
        display: inline-block;
        line-height: 12px;

        > span {
          color: #8f8f8f;
          font-size: 40%;
          font-weight: 400;
          margin: 0 0 0 20px;
        }

        .field-value {
          color: #505458;
          font-weight: bold;
        }

        span.intervention-stat {
          color: #d42626 !important;
          font-size: 24px;
          margin-left: 50px;

          label {
            color: #2c3f4d !important;
            //font-size: 40%;
            font-size: 15px;
            font-weight: 400;
            margin: 0 0 0 20px;
          }
        }
      }
    }

    h4 {
      color: #a8a8a8 !important;
      font-size: 0.8em;
      font-style: italic;
      font-weight: 400;
      margin: 2px;
      word-spacing: 18px;
    }

    .global-stats {
      float: right;

      .global-stat {
        display: inline-block;
        margin: 0 20px;
        text-align: center;
        min-width: 80px;

        h1 {
          color: #737c81 !important;
          font-weight: 800;
          line-height: 0.9em;
          margin: 0;
        }

        label {
          color: #a1abb3 !important;
          font-size: 0.7em;
          font-style: italic;
          font-weight: 500;
          line-height: 1.15em;
        }
      }
    }
    .ribbon-status {
      // background: #d6ebf8;
      // border: 1px solid #7297ae;
      background: #fbdc71;
      border: 1px solid #c5a641;
      color: #977400 !important;
      font-weight: 700;
      padding: 5px 20px 5px 10px;
      position: absolute;
      right: -35px;
      top: 0px;

      span {
        color: #333 !important;
        display: block;
        font-size: 0.7em;
        font-weight: 300;
        font-style: italic;
      }

      .edge {
        border-bottom: 10px solid transparent;
        // border-left: 10px solid #284e66;
        border-left: 10px solid #ad9545;
        height: 0;
        width: 0;
        position: absolute;
        right: 0;
        bottom: -11px;
      }
    }
  }
}

.activity-timeline {
  padding: 0 0 0 30px;
  h4 {
    color: #ccc !important;
    font-weight: 400;
  }
}

.conStudentList {
  padding: 0;
  h2 {
    font-size: 1.3em;
    font-weight: 900;
    margin: 20px 0 10px;
    border-bottom: 1px solid #ddd;
    i {
      color: #1a63a2 !important;
    }
    &.no-bottom-border {
      border-bottom: none;
    }
  }
  .header {
    font-size: 0.8em;
    text-align: center;
  }
}

.conStudentListContent {
  > section > div > .row {
    border-bottom: 1px solid #ddd;
  }
  > section > .row {
    border-bottom: 1px solid #ddd;
  }
}

.studentDetailContent {
  h3 {
    color: #505458 !important;
    font-size: 24px;
    padding: 0 10px;
    font-weight: 900;
  }

  span.roiGoal {
    float: right;
    font-size: 0.85em;
  }

  .growth-chart {
    clear: both;
  }

  section {
    clear: left;
  }

  h4 {
    color: #505458 !important;
    font-size: 18px;
    padding: 10px;
  }

  .growth-chart-area {
    &:last-child {
      margin-top: 3rem;
    }
  }
}

#conStudentActivityLog {
  .activity {
    list-style: none;
    margin: 5px 0;
    padding: 20px;
    li {
      border-left: 1px solid #ddd;
      line-height: 20px;
      margin: 0;
      padding: 0px 15px 40px;
      position: relative;
      &:last-child {
        border-left: none;
      }
      &:before {
        color: #d8d8d8 !important;
        content: "\f111";
        float: left;
        font-family: "FontAwesome";
        margin-left: -23px;
        margin-top: -2px;
      }
      h5 {
        color: #505458 !important;
        font-size: 16px;
        font-weight: 900;
        line-height: 19px;
        margin: 0 0 3px;
      }
      .activity-date {
        color: #495e6e !important;
        font-size: 15px;
        line-height: 1.2;
        margin: 0 0 5px;
      }
      p {
        font-size: 16px;
        line-height: 22px;
        color: #505458 !important;
        .score {
          font-style: italic;
          font-weight: 400;
        }
        .title {
          font-style: italic;
          font-weight: 400;
          text-decoration: underline;
        }
      }
    }
  }
}

.skills-overview {
  background: #f8f8f8;
  .skill-container {
    background: #f8f8f8;
    // border-left: 1px solid #ddd;
    padding: 15px;
  }
}

.chart {
  background: #f9f9f9;
  border-bottom: 1px solid #ddd;
  padding: 10px;
  .highcharts-container {
    border: 1px solid #ddd;
    max-width: 100% !important;
  }
}

.student-detail-chart {
  padding: 10px;
  .highcharts-container {
    border: 1px solid #ddd;
    max-width: 100% !important;
  }
}

.student-detail-pm-chart {
  clear: both;
  height: 400px;
  padding: 10px;
  .highcharts-container {
    border: 1px solid #ddd;
    max-width: 100% !important;
  }
}

ul.skill-list.nav-pills {
  border-right: 1px solid #ddd;
  list-style: none;
  margin: 10px 0;
  padding: 10px 0 15px;
  > li {
    &.active {
      > a {
        background: #f8f8f8;
        color: #555 !important;
        font-weight: 700;
      }
      .tab-arrow {
        display: inherit;
      }
    }
    > a {
      border-radius: 0;
      font-size: 0.8em;
      font-weight: 400;
      line-height: 20px;
      padding: 10px;
      vertical-align: top;
      .completion-date {
        color: #aaa !important;
        display: block;
        font-size: 0.8em;
        font-style: italic;
        font-weight: 300;
        line-height: 1em;
      }
      .completed-text {
        &.text-default {
          color: #aaa !important;
        }
        display: block;
        font-size: 0.8em;
        font-style: italic;
        font-weight: 300;
        line-height: 1em;
      }
    }
    .tab-arrow {
      background: #f8f8f8;
      display: none;
      height: 16px;
      width: 15px;
      position: absolute;
      right: -8px;
      top: 20px;
      -ms-transform: rotate(-45deg);
      -webkit-transform: rotate(-45deg);
      transform: rotate(-45deg);
      border-top: 1px solid #cecece;
      border-left: 1px solid #cecece;
      z-index: 9;
    }
  }
}

.site-selector {
  background: #dfebf2;
  border-bottom: 1px solid #bbb;
  color: #676767 !important;
  font-size: 0.7em;
  font-weight: 700;
  padding: 15px 10px 15px 15px;
}

.student-group-list {
  a.list-group-item {
    background: transparent;
    border: none;
    color: #676767 !important;
    font-size: 0.8em;
    font-weight: 300;
    &.active {
      background-color: #bfdbf5;
      border-right: 4px solid #0961cd;
      color: #0961cd !important;
      font-weight: 700;
    }
  }
}

.individual-recommendation-card {
  width: 330px;
  border: 1px solid #d8d8d8;
  border-radius: 6px;
  margin: 12px;
  overflow: hidden;
  position: relative;
  padding: 15px 12px 3px;
  & > input[type="checkbox"] {
    width: 15.57px;
    height: 16px;
    background-color: #ffffff;
    border: 1px solid #979797;
    border-radius: 2px;
    margin-right: 10px;
  }
  & > i.scheduled {
    color: #00ccb3 !important;
    font-size: 1.3em;
    margin-right: 6px;
  }
  & > span.student-name {
    width: 104px;
    height: 22px;
    font-size: 18px;
    font-weight: bold;
    line-height: 22px;
    color: #505458 !important;
  }
  & > .container {
    margin: 10px 0 15px;
    width: 100%;
    & > .row {
      line-height: 32px;
      box-shadow: inset 0 -1px 0 0 #dddddd;
      font-size: 0.8em;
    }
  }
  .reasonTitle {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}

.begin-individual-intervention-row {
  position: relative;
  margin-bottom: 10px;
  .begin-individual-intervention-box {
    margin: 0 auto;
    font-size: 14px;
    max-width: 500px;
    min-width: 500px;
    border: 1px solid #d8d8d8;
    border-radius: 6px;
    overflow: hidden;
    padding: 15px;
    & > .btn {
      margin: 5px auto;
    }
    & > .container {
      margin: 10px 0 15px 8px;
      & > .row {
        line-height: 32px;
        box-shadow: inset 0 -1px 0 0 #dddddd;
        font-size: 0.6em;
      }
    }
  }
}

.override-rules-button {
  position: absolute;
  bottom: 0;
  right: 0;
}

.recommendation-header {
  //  line-height: 32px;
}

.reasonRed {
  color: #d42626 !important;
}

.important-dog-ear {
  width: 72px;
  height: 52px;
  background-color: #00ccb3;
  position: absolute;
  top: -28px;
  right: -39px;
  padding: 0 0 0 20px;
  margin-bottom: -13px;
  transform: rotate(40deg);
  & > i {
    color: white;
    text-align: right;
    transform: rotate(-40deg);
    margin: 37px 0px 0px 8px;
  }
}

.important-dog-ear-icon {
  width: 16px !important;
  height: 23px !important;
  color: #ffffff !important;
  font-size: 0.75em !important;
  &.gold-icon {
    color: gold !important;
  }
}

#student-detail-intervention-progress {
  .fa {
    margin: 0 10px 0 0;
  }

  .fa.fa-dot-circle-o,
  .fa.fa-dot-circle-o:before {
    color: #00ccb3 !important;
  }
  .fa.fa-circle,
  .fa.fa-circle:before {
    color: #00ccb3 !important;
  }
  .current-skill {
    color: #888f95 !important;
    margin: 0 8px 0 0;
  }
  .roi {
    text-align: right;
    font-size: 0.85rem;
  }
  .student-roi {
    text-align: right;
    margin-top: -5px;
    font-size: 0.85rem;
  }
  label.current-skill {
    color: #00ccb3 !important;
    display: block;
    float: right;
    font-size: 0.7em;
    font-weight: 400;
    margin: 0 0 30px;
    text-align: left;
    width: 90%;
    cursor: pointer;
  }
  label.previous-skill {
    cursor: pointer;

    span {
      background-color: #00ccb3 !important;
      float: right;
      color: white;
      border-radius: 3px;
      padding-left: 5px;
      padding-right: 5px;
    }
  }
}

.studentListIndividualInterventionList {
  text-align: center;

  small {
    color: #999999 !important;
    display: block;
    font-size: 10px;
    font-weight: normal;
  }
  .intervention-progress {
    text-align: center;
    padding-top: 15px;
  }
  .row {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .row-individual-intervention-currentAssessment {
    font-size: 80%;
  }
}

.studentListClassRoster {
  .row {
    padding-top: 15px;
    padding-bottom: 15px;
  }
}

.student-display-id {
  color: #999999 !important;
  display: inline;
  font-size: 10px;
  font-weight: normal;
  margin-left: 10px;
}

div.profile-view .dropdown-menu {
  min-width: 300px;
}

.student-roster-table {
  .split-header-top {
    text-align: right;
    border-bottom: 0;
  }
  .split-header-bottom {
    text-align: right;
    padding-top: 0;
    padding-bottom: 5px;
    border-top: 0;
  }
}
