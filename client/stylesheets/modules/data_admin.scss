.entity-container {
  background: #fff;
  border: 1px solid #cecece;
  overflow: hidden;
}

.entity-header {
  background: #fbfbfb;
  border: 1px solid #cecece;
  border-bottom: none;
  padding: 1rem;
  h1 {
    border-bottom: 1px dotted #ccc;
    margin: 0;
  }
}

.district-details {
  float: left;
  padding: 27px 15px;
  width: 25%;
  section {
    h6 {
      border-bottom: 1px solid #8a8a8a;
      font-weight: 600;
      margin: 0;
      padding: 2px 5px;
      .list-action {
        color: #5cb85c;
        display: inline-block;
        float: right;
        font-size: 0.9em;
        font-weight: 300;
      }
    }
    ul {
      list-style: none;
      margin: 0;
      padding: 0;
      li {
        border-bottom: 1px solid #ddd;
        font-size: 0.8em;
        padding: 2px 5px;
      }
    }
  }
}

.school-list {
  display: -webkit-flex;
  display: flex;
  -webkit-flex-direction: row;
  flex-direction: row;
  -webkit-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;

  background: #f5f5f5;
  $border: 1px solid #cecece;
  border-left: $border;
  border-bottom: $border;
  float: right;
  min-height: 275px;
  margin-bottom: 10px;
  padding: 15px 10px 15px;
  width: 75%;
}

.support-view {
  width: 100%;
}

.school-item {
  background: #fff;
  box-shadow: 2px 2px 7px 0px #dedede;
  border: 1px solid #cecece;
  margin: 15px auto;
  padding: 10px;
  width: 32%;
  .school-name {
    border-bottom: 1px solid #cecece;
    margin-bottom: 5px;
    h4 {
      font-weight: 600;
      margin: 0;
      padding: 0 3px 2px;
      text-align: center;
    }
  }
  h5 {
    margin-top: 0;
  }
  h6 {
    color: #000;
    margin: 0;
  }

  ul {
    list-style: none;
    margin: 10px 0 15px 3px;
    padding: 0;
    li {
      font-size: 0.85em;
    }
  }
  .conCoachesList {
    border-top: 1px solid #cecece;
    padding: 5px;

    .lnkAddCoachUser {
      font-size: 0.6em;
      i {
        margin-left: 2px;
      }
    }

    ul {
      clear: both;
      margin-top: 20px;
    }
  }
}

.school-item-user {
  line-height: 0.85em;
  margin: 0 0 2px 0;
  padding: 0;
}

.conDataAdminsList {
  border-top: 1px solid #cecece;
  padding: 5px;

  ul {
    list-style: none;
    margin: 10px 0 15px 3px;
    padding: 0;
    li {
      font-size: 0.85em;
    }
  }

  .lnkAddDataAdminUser {
    font-size: 0.6em;
    i {
      margin-left: 2px;
    }
  }

  ul {
    clear: both;
    margin-top: 20px;
  }
}

.coaches-list {
  padding: 15px 0;
  h4 {
    border-bottom: 1px solid #ddd;
    font-weight: 300;
    padding: 2px 5px;
  }
  ul {
    list-style: none;
    margin: 0;
    padding: 0 5px;
    li a {
      font-size: 0.9em;
    }
  }
}

.student-groups {
  border: 1px solid #cecece;
  border-top: none;
  .nav.nav-tabs {
    background: #fbfbfb;
    padding: 0 1em;
    > li.active > a {
      border: 1px solid #cecece;
      border-bottom: none;
      top: 1px;
      z-index: 9;
    }
  }
  > .tab-content {
    border-top: 1px solid #cecece;
  }
}

.teacher-list.nav-pills {
  border-right: 1px solid #cecece;
  padding: 10px 0;
  > li {
    &.active > a {
      background: #fff;
      color: #434a54;
      font-weight: bold;
      padding-left: 25px;
      .tab-arrow {
        display: inherit;
      }
    }
    a {
      position: relative;
      .tab-arrow {
        background: #fff;
        display: none;
        height: 31px;
        width: 30px;
        position: absolute;
        right: -16px;
        top: 7px;
        -ms-transform: rotate(-45deg);
        -webkit-transform: rotate(-45deg);
        transform: rotate(-45deg);
        border-top: 1px solid #cecece;
        border-left: 1px solid #cecece;
      }
    }
  }
}

.table-title {
  border-bottom: 1px solid #ddd;
  font-weight: 700;
  padding: 0 15px 5px;
  sub {
    font-weight: 400;
    font-size: 0.6em;
    a {
      color: #84bd00;
    }
  }
  small {
    color: #777;
    float: right;
    padding: 10px 0 0;
  }
}

a.create-group {
  border-bottom: 1px dotted #ddd;
  color: #99cc32;
  display: block;
  margin: 5px 45px 5px 5px;
  padding: 10px;
  i {
    padding: 0 5px;
  }
  &:hover {
    background: #f3f3f3;
    border-radius: 5px;
  }
}

.group-table {
  tbody > tr > td {
    h4 {
      font-weight: 400;
      margin: 0;
    }
  }
}

.new-group-list {
  list-style: none;
  padding: 10px 20px;
  border-left: 1px dotted #ddd;
  li {
    .fa {
      color: #efefef;
      float: right;
      padding: 5px;
    }
    &:hover .fa {
      color: red;
      cursor: pointer;
    }
  }
}

.pull-bottom {
  margin-bottom: 0;
  margin-top: auto;
}

.scroll-y {
  overflow-y: auto;
}

.flex-container {
  display: flex;
}

.school-header {
  font-size: 1vw;
}

@media all and (max-width: 1400px) {
  .school-header {
    font-size: 14px;
  }
}

@media all and (min-width: 1700px) {
  .school-header {
    font-size: 17px;
  }
}

.manage-accounts-list-element {
  border: 2px solid #4991e6;
  padding: 0 10px 0 10px;
  border-radius: 8px;
  text-align: center;

  &:hover {
    cursor: pointer;
    background: #4ab0cd;
  }

  &.active {
    background: #5bc0de;
    font-weight: bold;
  }
  &.active > a {
    color: #fff !important;
  }
}

.manage-accounts-btn-list {
  padding-top: 10px;
  padding-bottom: 10px;
}

.archivedTeacherRow {
  background: #e7eff5;
}

.manage-students-row-input {
  display: inline !important;
  max-width: 150px;
}

.react-select-container input {
  padding: 10px 16px;
  font-size: 14px !important;
  line-height: 1.3333333;
  border-radius: 6px;
}

.react-select-container * {
  font-size: 14px;
}

.react-select__control {
  height: 46px;
}

.react-select__menu {
  z-index: 999 !important;
}

.inputFieldWithError {
  border-style: solid;
  border-color: red;
}
