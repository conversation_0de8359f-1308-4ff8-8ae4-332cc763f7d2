.high-school-summary-list {
  margin-left: 100px;
  .list-item {
    font-size: 20px;
    margin-bottom: 10px;
  }
}

.panel-group .panel {
  background: transparent;
  box-shadow: none;
  margin-bottom: 20px;
  h4 {
    color: #505458;
    font-weight: 900;
    margin: 0 10px;
  }
}

.panel-group .panel-heading {
  background-color: #fff;
  background-clip: padding-box;
  background-image: none;
  border: 1px solid #ccc !important;
  -webkit-border-radius: 0;
  border-radius: 0;
  -moz-border-radius: 0;
  padding: 0;
  transition: border ease-in 200ms;
  a {
    display: block;
    padding: 12px 10px;
  }
  &:hover {
    border: 1px solid #007eff !important;
  }
}

.panel-group .panel-body {
  background: #fafafa;
  border: 1px solid #ccc;
  margin: 0 auto;
  padding: 15px 10px 5px;
  width: 98%;
}

.class-stats {
  text-align: center;
  h4 {
    margin: 0 0 5px;
  }
  ul {
    border-top: 1px dotted #ddd;
    list-style: none;
    margin: 0;
    padding: 0;
    li {
      border-bottom: 1px dotted #dddddd;
      color: #878787;
      font-size: 0.85em;
    }
  }
}

.big-stat {
  text-align: center;
  h1 {
    margin: 5px 20px;
    padding: 15px;
  }
  span {
    color: #777;
    display: inline-block;
    font-size: 12px;
    font-weight: 300;
    line-height: 1em;
    text-align: center;
    width: 80%;
  }
}

.circle {
  width: 80px;
  .c100 {
    margin-bottom: 5px;
    > span {
      color: #777;
      font-size: 0.15em;
      line-height: 6.8em;
      width: 6.9em;
    }
  }
  label {
    color: #777;
    display: inline-block;
    font-size: 12px;
    line-height: 1em;
    text-align: center;
    width: 90%;
  }
}

.c100 {
  margin: 0;
}

.table.teacher tbody tr td {
  vertical-align: middle;
}

.table.teacher .intervention-progress {
  margin: 5px 0 0;
  height: 14px;
  .progress {
    height: 14px;
    .progress-bar {
      line-height: 14px;
    }
  }
}

// New Admin UI
.grade-list {
  background: #f6f6f6;
  border: 1px solid #ddd;
  border-bottom: none;
  & > li {
    & + li {
      margin: 0;
    }
    &.active > a {
      font-weight: 800;
    }
    &.active > a,
    &.active > a:focus,
    &.active > a:hover {
      background: #fff;
      color: #454545;
    }
    > a {
      border-bottom: 1px solid #ddd;
      border-radius: 0;
      font-weight: 400;
      margin: 0;
      padding: 10px 15px 15px;
    }
  }
  span.grade-averages {
    color: #555;
    display: block;
    font-size: 0.6em;
    font-weight: 300;
    line-height: 14px;
  }
}

.screening-results-grade-overview {
  display: -webkit-flex; /* Safari */
  display: flex;
  // display: -webkit-inline-flex; /* Safari */
  // display: inline-flex;

  -webkit-flex-direction: row; /* Safari */
  flex-direction: row;

  -webkit-flex-wrap: nowrap; /* Safari */
  flex-wrap: nowrap;

  -webkit-justify-content: space-between; /* Safari */
  justify-content: space-between;

  position: relative;
  margin: 20px 0;
  .screening-results-card-container {
    border-radius: 3px;
    display: inline-block;
    width: 30%;
    small.date {
      font-size: 0.65em;
      font-style: italic;
      float: right;
      padding-top: 5px;
    }
    .card {
      background: #f7f7f7;
      opacity: 0.45;
      min-height: 280px;
      &.active {
        opacity: 1;
      }
      h6 {
        background: #555;
        color: #fff;
        font-size: 0.8em;
        margin: 0 0 5px;
        padding: 10px;
      }
      .circle {
        display: inline-block;
        padding: 10px;
        width: 30%;
      }
      .stats {
        border-top: 1px solid #ddd;
        padding: 10px;
        vertical-align: top;
        display: -webkit-flex;
        display: flex;
        -webkit-flex-direction: row;
        flex-direction: row;
        -webkit-flex-wrap: nowrap;
        flex-wrap: nowrap;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        position: relative;
        margin: 0;
        li {
          display: inline-block;
          font-size: 0.7em;
          strong {
            font-size: 1.2em;
          }
        }
      }
    }
  }
}

// .group-container{
//     border-bottom: 1px solid #ddd;
//     padding: 15px;
//     .intervention-progress{
//         .progress{
//             height: 10px;
//             width: 100%;
//         }
//     }
// }

.group-data {
  ul {
    list-style: none;
    margin: 0;
    padding: 0;
    li {
      display: inline-block;
    }
  }
  .group-title {
    a {
      font-weight: 400;
      small {
        color: #555;
        font-size: 70%;
      }
    }
  }
  .group-stat {
    float: right;
    margin-left: 15px;
    label {
      font-size: 0.7em;
      font-weight: 300;
      margin-right: 5px;
    }
  }
}

.intervention-progress {
  display: block;
  .progress {
    margin: 0;
  }
  label {
    display: block;
    font-weight: 300;
    text-align: center;
  }
}
.input-date {
  max-width: 150px;
}

.input-short {
  max-width: 120px;
}

.input-xshort {
  max-width: 80px;
}

.input-xs {
  height: 24px;
  margin-top: 0 !important;
}

.input-group-date {
  max-width: 180px;
  .input-group-addon {
    padding: 0;
  }
}

.input-group-button {
  padding: 6px 12px;
}

.news-admin-dashboard-offset {
  position: relative;
  margin: 0 -10px;
}

.program-evaluation-break-page {
  page-break-inside: auto;
  break-after: page;
}

.program-evaluation-avoid-break-page {
  page-break-inside: avoid;
  break-after: avoid;
}

.program-evaluation-print-this-page {
  margin-top: 1px;
  margin-right: 20px;
}

.program-evaluation .chart {
  border: none ;
  clear: both;
  padding: 0;
  .highcharts-container {
    border: none;
    width: 100%;
  }
}

.district-reporting-school-card-group {
  grid-template-columns: minmax(0, 1fr) minmax(0, 1fr) minmax(0, 1fr);
}

.hr-dark {
  border-top: 2px solid #e5e5e5
}
