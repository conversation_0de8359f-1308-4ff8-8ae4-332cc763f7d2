.table-row-centered {
  th,
  td {
    text-align: center;
    vertical-align: middle !important;
  }

  input {
    margin: 0.4rem;
    text-align: center;

    &.withError {
      border-style: solid;
      border-color: red;
    }
  }

  input[type="color"] {
    padding: 0;
    border: 0;
    background: white;
  }

  button:disabled {
    background: #b5b5b5 !important;
    border-color: #b5b5b5 !important;
  }

  button {
    width: 100%;
  }

  &.message-header {
    height: 75px !important;
    font-size: 15px;
    font-weight: 500;

    th {
      vertical-align: bottom !important;
    }
  }
}

div.clickable-student {
  cursor: pointer;
  color: #4991e6;
}

.help-block {
  font-weight: 900;
  font-size: 10px;
  line-height: 1.3em;
  padding: 8px 20px 0 20px;
  position: absolute;
  text-decoration: none;
  left: 0;
  top: 30px;
  vertical-align: middle;
  width: 100%;

  &.text-warning {
    white-space: nowrap;
    padding: 8px 0 0 0;
  }
}

.help-block-school-item {
  font-weight: 900;
  font-size: 12px;
  line-height: 1.3em;
  display: block;
  text-decoration: none;
}

.school-item-edit {
  position: absolute;
  right: 5px;
  top: 2px;
}

.help-block-override {
  padding: 0;
  text-align: center;
  position: initial;
}
