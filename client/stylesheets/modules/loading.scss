.spinner-full {
  margin: 100px auto;
  width: 100%;
  text-align: center;
  font-size: 10px;
  position: relative;
}

.spinner-inline {
  display: inline-block;
  padding-right: 1ch;
}

.spinner-inline > span {
  font-size: 13px;
  padding-left: 5px;
  display: inline-block;
  vertical-align: text-bottom;
}

.spinner-full > span {
  font-size: 13px;
}

.spinner-inline > div,
.spinner-full > div {
  display: inline-block;
  height: 20px;
}

.spinner-inline > div > div,
.spinner-full > div > div {
  background-color: #b5b5b5;
  height: 100%;
  width: 6px;
  display: inline-block;
  vertical-align: text-bottom;

  -webkit-animation: sk-stretchdelay 1.2s infinite ease-in-out;
  animation: sk-stretchdelay 1.2s infinite ease-in-out;
}

.spinner-inline .rect2,
.spinner-full .rect2 {
  -webkit-animation-delay: -1.1s;
  animation-delay: -1.1s;
}

.spinner-inline .rect3,
.spinner-full .rect3 {
  -webkit-animation-delay: -1s;
  animation-delay: -1s;
}

.spinner-inline .rect4,
.spinner-full .rect4 {
  -webkit-animation-delay: -0.9s;
  animation-delay: -0.9s;
}

.spinner-inline .rect5,
.spinner-full .rect5 {
  -webkit-animation-delay: -0.8s;
  animation-delay: -0.8s;
}

@-webkit-keyframes sk-stretchdelay {
  0%,
  40%,
  100% {
    -webkit-transform: scaleY(0.4);
  }
  20% {
    -webkit-transform: scaleY(1);
  }
}

@keyframes sk-stretchdelay {
  0%,
  40%,
  100% {
    transform: scaleY(0.4);
    -webkit-transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
    -webkit-transform: scaleY(1);
  }
}
