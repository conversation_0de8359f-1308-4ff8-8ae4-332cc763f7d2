//@import url("https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@200;400;600&display=swap");
@import url("https://fonts.googleapis.com/css?family=Raleway:400,800,200");

@import "{}/client/stylesheets/base/colors";

//$font-sans-serif: "Source Sans Pro", "Calibri", sans-serif;
$font-sans-serif: "Lato", sans-serif;
$min-contrast-ratio: 2;
$body-color: $sw-dark-blue;
$primary: $sw-bright-blue;
$component-active-bg: #337ab7;
$btn-line-height: 1.43rem;
$dropdown-font-size: 0.875rem;
$dropdown-spacer: -0.075rem;
$dropdown-header-padding-y: 0;
$dropdown-divider-bg: #ddd;
$input-border-radius: 0;
$input-font-size: 1rem;
$input-padding-y-lg: 0.7rem;
$input-color: $sw-dark-blue;
$link-decoration: none;
$link-hover-decoration: underline;
$navbar-padding-y: 0;
$modal-md: 600px;
$modal-lg: 900px;
$table-border-width: 0.5px;
$table-group-separator-color: #ddd;

@import "{}/node_modules/bootstrap/scss/bootstrap.scss";

@import "{}/node_modules/font-awesome/scss/font-awesome";
$fa-font-path: "/fonts";
@import "{}/node_modules/font-awesome/scss/font-awesome.scss";

@import "{}/client/stylesheets/base/components";
@import "{}/client/stylesheets/base/core";
@import "{}/client/stylesheets/base/layout";
@import "{}/client/stylesheets/base/main-content-wrapper";
@import "{}/client/stylesheets/base/elements";
@import "{}/client/stylesheets/base/nav-middle-sub-tabbed";
@import "{}/client/stylesheets/base/nav-side-left";
@import "{}/client/stylesheets/base/nav-top";
@import "{}/client/stylesheets/base/responsive";
@import "{}/client/stylesheets/base/variables";
@import "{}/client/stylesheets/base/circle";
@import "{}/client/stylesheets/base/sloppy";
@import "{}/client/stylesheets/base/forms";
@import "{}/client/stylesheets/base/bootstrap-customizations";
@import "{}/client/stylesheets/base/animations";
@import "{}/client/stylesheets/base/helper-classes";

// May not need demo anymore?
@import "{}/client/stylesheets/base/demo";

@import "{}/client/stylesheets/base/_react-s-alert/default.scss";
@import "{}/client/stylesheets/base/_react-s-alert/effect-jelly.scss";
@import "{}/client/stylesheets/base/_react-s-alert/effect-slide.scss";
@import "{}/client/stylesheets/base/_react-s-alert/custom-override-tweaks.scss";

@import "{}/client/stylesheets/modules/common";
@import "{}/client/stylesheets/modules/admin_overview";

@import "{}/client/stylesheets/modules/screening_results";
@import "{}/client/stylesheets/modules/classwide";
@import "{}/client/stylesheets/modules/class-rules";
@import "{}/client/stylesheets/modules/classwide_guide";
@import "{}/client/stylesheets/modules/skill_graph";
@import "{}/client/stylesheets/modules/individual_intervention";
@import "{}/client/stylesheets/modules/loading-screen";
@import "{}/client/stylesheets/modules/upload";
@import "{}/client/stylesheets/modules/dashboard_admin";
@import "{}/client/stylesheets/modules/data_admin";
@import "{}/client/stylesheets/modules/onboarding";
@import "{}/client/stylesheets/modules/student_group";
@import "{}/client/stylesheets/modules/loading";
@import "{}/client/stylesheets/modules/radial-progress-bar";
@import "{}/client/stylesheets/modules/guide";
@import "{}/client/stylesheets/modules/video-tutorials";
@import "{}/client/stylesheets/modules/canvas";
@import "{}/client/stylesheets/modules/admin_growth";
@import "{}/client/stylesheets/modules/modals";
@import "{}/client/stylesheets/modules/assessment-printout";

@import "{}/client/stylesheets/state/animate";
@import "{}/client/stylesheets/state/processing_score";

@import "{}/client/stylesheets/base/print";
