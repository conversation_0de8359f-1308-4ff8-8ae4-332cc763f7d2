# SpringMath aka math-ninja
This is the SpringMath project repository. If you are reading this you must be one of the lucky few people out of the 7 billion people on this planet that get to work on this project.

## How to work on this repository

### Setup
> This is assuming you have a github account and have setup ssh keys. ([guide here](https://help.github.com/articles/generating-a-new-ssh-key-and-adding-it-to-the-ssh-agent/))

#### Setup Github and Local Repositories

First you need to fork the repo. Once you have your own fork of the code base, you can clone it to your local system. After it is cloned to your system, you will want to setup upstream references to the main repository. To this put `git remote <NAME_EMAIL>:edSpring/math-ninja.git` in your console when you are in the app's root directory. To verify that it is there you can run this command `git remote -v` . The out put will look like:
```
origin	**************:[githubUserName]/math-ninja.git (fetch)
origin	**************:[githubUserName]/math-ninja.git (push)
upstream	**************:edSpring/math-ninja.git (fetch)
upstream	**************:edSpring/math-ninja.git (push)
```

#### Setup Circle CI

You will need to log into cricleci.com with your github account. Go to `ADD PROJECTS` and build the math-ninja project.

Now you are ready to start working on the project.


##### Check List
1. [ ] Fork repo
2. [ ] Clone repo `<NAME_EMAIL>:[githubUserName]/math-ninja.git`
3. [ ] Set upstream repo `git remote <NAME_EMAIL>:edSpring/math-ninja.git`
4. [ ] Verify remote settings `git remote -v`

```
origin	**************:[githubUserName]/math-ninja.git (fetch)
origin	**************:[githubUserName]/math-ninja.git (push)
upstream	**************:edSpring/math-ninja.git (fetch)
upstream	**************:edSpring/math-ninja.git (push)
```

5. [ ] Add math-ninja to Circle CI projects.

### Running the app

>This was set up using nvm and external node 5.11.1 not Meteor's internal node version.

##### Meteor
**Before continuing please ask a peer for the settings json file.**

You will need to have node installed. A great way to manage this is [nvm](https://github.com/creationix/nvm).
- `brew install nvm` - install nvm via homebrew

Once you have the repo forked and cloned you will need to do an `npm install` in the root directory.
- `nvm install stable` - install the most recent stable version of npm.

Next we need to install dependacies for meteor. This can be done with the following:
- `meteor npm install` - search the setting json file for dependencies.

Once complete the following commands are available to get the app running in the way you need it for dev.
###### Running the app
- `npm run start` - run the app on localhost:3000

###### Running the app for the first time
You'll want to bootstrap in data if you're running the app for the first time. This will provide you some sample data as well as some user accounts so you can login locally. The bootstrap data is taken from the database specified in your settings.json file under ```BOOTSTRAP_FROM_DB```
- `npm run start:bootstrap` - run the app on localhost:3000 and bootstrap in some data

###### Running the unit tests
- `npm run test:client` - run unit test testing suite in the browser at [localhost:3100](http://localhost:3100)
- `npm run test:console` - run unit test testing suite in the console
- `npm run watch:test:console` - run test testing suite in the console and re-run when changes occur

###### Running the e2e tests
>Chimp is setup to use phantomjs this way it will not interrupt your work flow.

- `npm run chimp` - run chimp watching `@focus` tag on features. _Requires meteor app to be running on port 3000._
- `npm run test:chimp` - run chimp on all features.
- `npm run watch:chimp:console` - run meteor and run chimp watching `@focus` tag on features.

###### Compile the sass
- `npm run build:css` - compile `sass` into css at `client/bundle.css`
- `npm run watch:css` - watch sass files and compile the css file on changes\

###### Get nodemon help to print for help building more scripts
- `npm run watch:help`

### Workflow

To add changes, first go to jira and make sure you understand the issue that you are assigned to. Checkout the develop branch and pul down the most recent code base from upstream. Then make a new branch that look something like `[feature or bug]/edsp-[issue number]-small-description`.
>This format is not required but if we all use the same syntax then it is helpful. :) This is open to other ideas and options.

Once you have your branch start making your code changes. Preferably it would be awesome if you first adjust the current tests to write tests to reflect the work you want to complete. Then commit that work. Now you can write the code that gets your test to pass. Once you have your tests passing, commit this work. Finally, take some time to refactor your work.
> Again this would be the best practice workflow but some times we don't have time :(

Now that your new feature or bug fix is implemented, you can make a pull request. First pull down the upstream develop branch again so that you can manage and merge conflicts that might exist. Push the code up to your origin. Then go to githum.com and make the pull request there. When the tests that run on Circle CI pass then ask a team member to review the work and either merge the code or make suggestions.

##### Check List
> The following commit messages are just examples

1. [ ] Make sure you understand the jira issue
2. [ ] Checkout develop branch `git checkout develop`
3. [ ] Fetch upstream for changes `git fetch upstream`
4. [ ] Pull changes down from upstream develop `git pull upstream develop`
5. [ ] Create your new branch `git checkout -b [feature or bug]/edsp-[issue number]-small-description`
6. [ ] Write or adjust tests
7. [ ] Stage changes `git add .` or `git add -A` or `git add -all` (All do the same thing :) )
8. [ ] Commit tests `git commit -m 'edsp-[issue number] wrote tests to test that [feature description here]'`
9. [ ] Write code so tests pass
10. [ ] Stage changes `git add .` or `git add -A` or `git add -all` (All do the same thing :) )
11. [ ] Commit tests `git commit -m 'edsp-[issue number] added code that allowed [feature description here]'`
12. [ ] Refactor code
13. [ ] Stage changes `git add .` or `git add -A` or `git add -all` (All do the same thing :) )
14. [ ] Commit tests `git commit -m 'edsp-[issue number] refactor code that allowed [feature description`
15. [ ] Fetch upstream for changes `git fetch upstream`
16. [ ] Pull changes down from upstream develop `git pull upstream develop` here]'`

### About our testing

###### TDD (Mocha)
Intervention Advisor has unit tests set up through the practicalmeteor:mocha package and practicalmeteor:mocha-console-runner to run the tests in the console. This is to help with Test Driven Development (TDD).

###### BDD and E2E (Chimp and Cucumber) (Integration Testing?)
For Behavior Driven Development(BDD) Intervention Advisor uses Chimp and Cucumber, by Xolvio. This is end to end (E2E) testing.

###### Circle CI
For Continuous Integration, the math-ninja repo has a `circle.yml` set up ready to go. If the tests passes on circle it will deploy to the proper environment on Galaxy.

###### Other setup helpers
For css it has Sass set up to build and compile to css. It also has bootstrap 4 alpha as a starting place.

### TODOS

## How Continuous Deployment Works

> All environments are running on Galaxy. And the DB are managed by Mongo Director / Scale Grid

Deployment currently only happens to the Development server from the develop branch on github. It is triggered by a successful pull request being merged in to the branch.

The scripts to manage the deploy are located in `./.scripts/deploy.sh`

There are two files that need to be saved as environment varibales on Circle CI. This way they are encrypted and safe. One of these files is named `developMSJSON` and the other is `developMUSJSON`.
The `develop` part is sourced from the code via the name of the branch.
+ `MSJSON` is the `settings.json` file that is needed in the deploy process.
+ `MUSJSON` is the meteor user session json file that allows Circle CI to do the deployment.

To upload these files go to Circle CI, then navigate to the project. In the project there is project setting. With in project settings is a place to add environment variables.
Add the contents of those files here as env vars.

CircleCI/Galaxy tips:
+ the session token saved in `MUSJSON` expires periodically.  To create a new token, execute
> METEOR_SESSION_FILE=deployment_token.json meteor login

and enter the Galaxy deployment user's credentials. Compact the deployment_token into a single line, then paste that line into the developMUSJSON environment variable.

>tr -s '\n' ' ' < deployment_token.json

The following three files were use to set up Continuous Deployment.

+ http://coderchronicles.org/2016/03/15/deploying-a-meteor-app-to-galaxy/
+ https://medium.com/@natestrauser/migrating-meteor-apps-from-modulus-to-galaxy-with-continuous-deployment-from-codeship-aed2044cabd9#.2joe2w6jl
+ http://stackoverflow.com/questions/26349511/how-can-i-use-a-secure-file-in-a-circleci-build
