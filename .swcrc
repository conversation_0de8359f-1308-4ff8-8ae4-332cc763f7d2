{"jsc": {"target": "es2022", "baseUrl": ".", "paths": {"meteor/aldeed:simple-schema": ["./tests/stubs/aldeedSimpleSchema"], "meteor/*": ["./tests/stubs/*"], "highcharts/highstock": ["./tests/stubs/highcharts"], "^/*": ["*"]}, "parser": {"syntax": "ecmascript", "jsx": true, "dynamicImport": true}, "transform": {"react": {"pragma": "React.createElement", "pragmaFrag": "React.Fragment", "throwIfNamespace": true, "development": false, "useBuiltins": false}, "hidden": {"jest": true}}}, "module": {"type": "commonjs", "strictMode": true}}