const { defineConfig } = require("cypress");

module.exports = defineConfig({
  animationDistanceThreshold: 5,
  blockHosts: null,
  chromeWebSecurity: true,
  defaultCommandTimeout: 15000,
  downloadsFolder: "cypress/downloads",
  execTimeout: 60000,
  fileServerFolder: "",
  fixturesFolder: "tests/cypress/fixtures",
  hosts: null,
  modifyObstructiveCode: true,
  numTestsKeptInMemory: 100,
  pageLoadTimeout: 60000,
  port: null,
  requestTimeout: 10000,
  responseTimeout: 30000,
  retries: {
    runMode: 0,
    openMode: 0
  },
  screenshotsFolder: "cypress/screenshots",
  taskTimeout: 60000,
  trashAssetsBeforeRuns: true,
  userAgent: null,
  video: false,
  videoCompression: 32,
  videosFolder: "cypress/videos",
  viewportHeight: 1100,
  viewportWidth: 1200,
  waitForAnimations: true,
  watchForFileChanges: false,
  e2e: {
    // We've imported your old cypress plugins here.
    // You may want to clean this up later by importing these.
    setupNodeEvents(on, config) {
      require("cypress-terminal-report/src/installLogsPrinter")(on);
      require("./tests/cypress/plugins")(on, config);
    },
    env: {
      CURRENT_SCHOOL_YEAR: 2026,
      PREVIOUS_SCHOOL_YEAR: 2025,
      hideXhr: true
    },
    baseUrl: null,
    specPattern: "tests/cypress/integration/**/*.*",
    supportFile: "tests/cypress/support/index.js"
  }
});
