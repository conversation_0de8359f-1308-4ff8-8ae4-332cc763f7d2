#!/bin/bash
echo "######################"
echo "# RUNNING UNIT TESTS #"
echo "######################"

if [ "$CIRCLECI" == 'true' ]; then
    # <NAME_EMAIL>:"$CIRCLE_PROJECT_USERNAME"/local-for.git ./local
    mkdir -p .meteor/local
    cd .meteor
    <NAME_EMAIL>:edSpring/springmath-local-for-ci.git local
    cd ../
    BABEL_ENV=unittesting JEST_JUNIT_OUTPUT="/home/<USER>/testResults/jest/results.xml" jest --maxWorkers=2 --ci --reporters=default --reporters=jest-junit
else
    BABEL_ENV=unittesting jest
fi

UNIT_TEST_RESULT=$?

if [ "$UNIT_TEST_RESULT" == "0" ]; then
  echo -e "\e[32m#####################"
  echo -e "\e[32m# UNIT TESTS PASSED #"
  echo -e "\e[32m#####################"
else
  echo -e "\e[31m#####################"
  echo -e "\e[31m# UNIT TESTS FAILED #"
  echo -e "\e[31m#####################"
fi

exit $UNIT_TEST_RESULT
