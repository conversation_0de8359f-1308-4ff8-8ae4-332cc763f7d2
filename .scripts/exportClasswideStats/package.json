{"name": "Export_classwide_stats", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "npx jest", "start": "node ./index.js"}, "private": true, "engines": {"node": ">=10.12.0"}, "dependencies": {"exceljs": "^1.9.0", "json2csv": "^4.3.5", "lodash": "^4.17.11", "moment": "^2.24.0", "mongodb": "^3.1.13"}, "devDependencies": {"jest": "^24.1.0"}, "jest": {"rootDir": "./", "testMatch": ["**/__tests__/**/*.js?(x)", "**/?(*.)(tests).js?(x)"]}}