const moment = require("moment");
const _groupBy = require("lodash/groupBy");
const _findLast = require("lodash/findLast");
const _flatten = require("lodash/flatten");

const dayInMiliseconds = 1000 * 60 * 60 * 24;

const getValidScore = score => score.score;

function getClasswideInterventionStats({
  history,
  grade,
  studentIdsToLocalIds,
  gradeClasswideSequence,
  teacherLocalId,
  classSectionId,
  schoolId
}) {
  const resultsByAssessmentName = getHistoryItemsByAssessmentName(history);
  return _flatten(
    Object.entries(resultsByAssessmentName).map(([assessmentName, historyItems]) => {
      const { startingHistoryItem, endingHistoryItem } = getStartingAndEndingHistoryItem(historyItems);
      const numberOfScores = historyItems.length;
      const numberOfWeeksSpent =
        numberOfScores === 1 ? 1 : getNumberOfWeeksSpent({ startingHistoryItem, endingHistoryItem });
      const allStudentIdsTakingAssessment = getUniqueStudentIdsWithScore(historyItems);
      const studentScores = getAllStudentScores({
        historyItems,
        studentIds: allStudentIdsTakingAssessment,
        studentIdsToLocalIds
      });
      const classwideSequencePosition = gradeClasswideSequence.indexOf(assessmentName) + 1;
      return getAssessmentRows({
        studentScores,
        grade,
        numberOfWeeksSpent,
        assessmentName,
        classwideSequencePosition: classwideSequencePosition,
        teacherLocalId,
        classSectionId,
        schoolId
      });
    })
  );
}

function getAllStudentIdsWithScore(historyItems) {
  return historyItems.reduce((acc, cv) => {
    const studentIds = cv.assessmentResultMeasures[0].studentResults.map(sr => sr.studentId);
    return [...acc, ...studentIds];
  }, []);
}

function getUniqueStudentIdsWithScore(historyItems) {
  return Array.from(new Set(getAllStudentIdsWithScore(historyItems)));
}

function getHistoryItemsByAssessmentName(groupClasswideHistory) {
  return _groupBy(groupClasswideHistory, "assessmentName");
}

function getStudentScore(studentScores, studentId) {
  return studentScores.find(studentScore => studentScore.studentId === studentId).scores[0];
}

function getStudentScoresWithRoi(arr) {
  return arr.map(score => {
    const scoresWithValue = score.scores.filter(s => s.score);
    let roi = "N/A";
    if (scoresWithValue.length > 1) {
      const scoresList = scoresWithValue.map(s => s.score);
      const timestampsList = scoresWithValue.map(s => s.timestamp);
      roi = calculateRoI(timestampsList, scoresList);
    }
    return {
      ...score,
      ROI: roi,
      scores: score.scores.map(s => ({ score: s.score, scoreIndex: s.scoreIndex })) //removes lastModified prop
    };
  });
}

function getAllStudentScores({ historyItems, studentIds, studentIdsToLocalIds }) {
  const numberOfItems = historyItems.length;
  const studentAssessmentScores = historyItems.reduceRight((studentScores, historyItem, index) => {
    const timestamp = historyItem.whenEnded.on;
    const scoreIndex = Math.abs(numberOfItems - index); // reduceRight starts index from the end
    const singleItemStudentScores = studentIds.map(studentId => {
      const result = historyItem.assessmentResultMeasures[0].studentResults.find(sr => sr.studentId === studentId);
      return {
        studentId,
        studentLocalId: studentIdsToLocalIds[studentId],
        ROI: "N/A",
        scores: [
          {
            score: result ? result.score : null,
            scoreIndex,
            timestamp: timestamp
          }
        ]
      };
    });
    // IF IT'S FIRST ITERATION NO NEED TO DO LOOKUP IN studentScores
    if (!studentScores.length) {
      return singleItemStudentScores;
    }
    // IF NOT, ADD NEW SCORES TO CURRENT LIST
    return studentScores.map(ss => ({
      ...ss,
      scores: [...ss.scores, getStudentScore(singleItemStudentScores, ss.studentId)]
    }));
  }, []);
  return getStudentScoresWithRoi(studentAssessmentScores);
}

function getAssessmentRows({
  studentScores,
  grade,
  numberOfWeeksSpent,
  assessmentName,
  classwideSequencePosition,
  teacherLocalId,
  classSectionId,
  schoolId
}) {
  return studentScores.map(result => {
    let firstSkillScore = "N/A";
    let finalSkillScore = "N/A";
    const numberOfScoresWithValue = result.scores.filter(getValidScore).length;
    if (numberOfScoresWithValue) {
      firstSkillScore = result.scores.find(getValidScore).score;
      finalSkillScore = _findLast(result.scores, getValidScore).score;
    }
    const resultObj = {
      grade,
      "Local Student ID": result.studentLocalId,
      "Classwide Skill Name": assessmentName,
      "Classwide Sequence Position": classwideSequencePosition,
      "First Skill score": firstSkillScore,
      "Final Skill score": finalSkillScore,
      ROI: result.ROI,
      "Teacher ID": teacherLocalId,
      "Class Section ID": classSectionId,
      "School ID": schoolId,
      "Number of scores": numberOfScoresWithValue,
      "Number of weeks spent": numberOfWeeksSpent
    };
    result.scores.forEach(score => {
      const key = `Score #${score.scoreIndex}`;
      resultObj[key] = score.score || "N/A";
    });

    return resultObj;
  });
}

function getStartingAndEndingHistoryItem(historyItems) {
  const startingHistoryItem = historyItems[historyItems.length - 1];
  const endingHistoryItem = historyItems[0];
  return { startingHistoryItem, endingHistoryItem };
}

function getStartingAndEndingTimestamp({ startingHistoryItem, endingHistoryItem }) {
  const startTimestamp = startingHistoryItem.whenStarted.on;
  const endTimestamp = endingHistoryItem.whenEnded.on;
  return { startTimestamp, endTimestamp };
}

function getRoundedWeekDifference(endTimestamp, startTimestamp) {
  const timestampDifference = endTimestamp - startTimestamp;
  const dayDifference = timestampDifference / dayInMiliseconds;
  return dayDifference < 7 ? 1 : Math.floor(dayDifference / 7);
}

function getNumberOfWeeksSpent({ startingHistoryItem, endingHistoryItem }) {
  const { startTimestamp, endTimestamp } = getStartingAndEndingTimestamp({ startingHistoryItem, endingHistoryItem });
  return getRoundedWeekDifference(endTimestamp, startTimestamp);
}

// copy of imports/api/utilities/utilities.js implementation with slight modifications, the tests are in the project file
function calculateRoI(timestampsArray, scoresList) {
  // This calculation uses the 'Ordinary Least Squares Regression' formula for the ROI
  if (timestampsArray.length !== scoresList.length) {
    return "N/A";
  }
  const numItems = timestampsArray.length;

  if (numItems < 2) {
    return "N/A";
  }
  const expectedDateFormat = "ddd MMM DD YYYY HH:mm:ss";
  const datesArray = timestampsArray.map(t => moment(t).format(expectedDateFormat));

  const expectedFormats = [expectedDateFormat];
  const allValidDates = datesArray.every(d => moment(d, expectedFormats).isValid());
  if (!allValidDates) {
    // abort if any of the dates are invalid
    return "N/A";
  }
  const scoresArray = scoresList.map(x => Number(x));
  const allValidScores = scoresArray.every(s => typeof s === "number");
  if (!allValidScores) {
    // abort if any of the scores are non-numeric
    return "N/A";
  }

  if (numItems === 2) {
    // if there are only two data points calculate ROI only if the scores were taken on different days
    const firstDate = moment(new Date(datesArray[0]));
    const minHourDifferenceToDisplayRoiForTwoDataPoints = 24;
    const hourDifferenceBetweenTwoDates = Math.abs(firstDate.diff(moment(new Date(datesArray[1])), "hours"));
    if (hourDifferenceBetweenTwoDates < minHourDifferenceToDisplayRoiForTwoDataPoints) {
      return "N/A";
    }
  }

  const firstDateMoment = moment(datesArray[0], expectedFormats);
  const msPerWeek = 1000 * 60 * 60 * 24 * 7;
  const dateOffset = firstDateMoment.valueOf();
  const unixDateOffsets = datesArray.map(dt => (moment(dt, expectedFormats).valueOf() - dateOffset) / msPerWeek);

  const scoresSum = scoresArray.reduce((acc, val) => acc + parseInt(val), 0);
  const datesSum = unixDateOffsets.reduce((acc, val) => acc + val, 0);
  const datesSumSquare = datesSum * datesSum;
  const datesSquareSum = unixDateOffsets.reduce((acc, val) => acc + val * val, 0);

  const datesScoreSumProduct = unixDateOffsets.reduce((acc, val, index) => acc + val * parseInt(scoresArray[index]), 0);

  const numerator = datesScoreSumProduct - (scoresSum * datesSum) / numItems;
  const denominator = datesSquareSum - datesSumSquare / numItems;
  const olsRoi = numerator / denominator;

  return Number(olsRoi.toFixed(1));
}

module.exports = {
  getClasswideInterventionStats,
  getUniqueStudentIdsWithScore,
  getAllStudentScores,
  getStartingAndEndingHistoryItem,
  getRoundedWeekDifference,
  getNumberOfWeeksSpent
};
