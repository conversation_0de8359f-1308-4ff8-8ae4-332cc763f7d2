const {
  groupWithSimplifiedHistory,
  singleAssessmentItemsWithAbsentStudents,
  studentIdsToLocalIds,
  FIRST_STUDENT_ID,
  SECOND_STUDENT_ID,
  FIRST_STUDENT_LOCAL_ID,
  SECOND_STUDENT_LOCAL_ID,
  gradeClasswideSequence
} = require("./getClasswideInterventionData.testData");
const {
  getUniqueStudentIdsWithScore,
  getClasswideInterventionStats,
  getNumberOfWeeksSpent,
  getRoundedWeekDifference,
  getStartingAndEndingHistoryItem,
  getAllStudentScores
} = require("./getClasswideInterventionStats");

const GRADE = "05";
const TEACHER_LOCAL_ID = "TEACHER_LOCAL_ID";
const CLASS_SECTION_ID = "CLASS_SECTION_ID";
const SCHOOL_ID = "SCHOOL_ID";
const FIRST_SKILL_NAME = "Fact Families: Multiplication/Division 0-12";
const SECOND_SKILL_NAME = "Find the Least Common Denominator";
const THIRD_SKILL_NAME = "Simplify Fractions";

describe("getClasswideInterventionData", () => {
  it("should return collection of all students scores for each skill practiced", () => {
    //Assumption: The function gets only classwide items
    const result = getClasswideInterventionStats({
      history: groupWithSimplifiedHistory.history,
      grade: GRADE,
      studentIdsToLocalIds,
      gradeClasswideSequence,
      teacherLocalId: TEACHER_LOCAL_ID,
      classSectionId: CLASS_SECTION_ID,
      schoolId: SCHOOL_ID
    });

    const expectedResult = [
      {
        grade: GRADE,
        "Local Student ID": FIRST_STUDENT_LOCAL_ID,
        "Classwide Skill Name": FIRST_SKILL_NAME,
        "Classwide Sequence Position": gradeClasswideSequence.indexOf(FIRST_SKILL_NAME) + 1,
        "First Skill score": "47",
        "Final Skill score": "47",
        ROI: "N/A",
        "Number of scores": 1,
        "Number of weeks spent": 1,
        "Teacher ID": TEACHER_LOCAL_ID,
        "Class Section ID": CLASS_SECTION_ID,
        "School ID": SCHOOL_ID,
        "Score #1": "47"
      },
      {
        grade: GRADE,
        "Local Student ID": SECOND_STUDENT_LOCAL_ID,
        "Classwide Skill Name": FIRST_SKILL_NAME,
        "Classwide Sequence Position": gradeClasswideSequence.indexOf(FIRST_SKILL_NAME) + 1,
        "First Skill score": "72",
        "Final Skill score": "72",
        ROI: "N/A",
        "Number of scores": 1,
        "Number of weeks spent": 1,
        "Teacher ID": TEACHER_LOCAL_ID,
        "Class Section ID": CLASS_SECTION_ID,
        "School ID": SCHOOL_ID,
        "Score #1": "72"
      },
      {
        grade: GRADE,
        "Local Student ID": FIRST_STUDENT_LOCAL_ID,
        "Classwide Skill Name": SECOND_SKILL_NAME,
        "Classwide Sequence Position": gradeClasswideSequence.indexOf(SECOND_SKILL_NAME) + 1,
        "First Skill score": "8",
        "Final Skill score": "12",
        ROI: 1.4,
        "Number of scores": 2,
        "Number of weeks spent": 4,
        "Teacher ID": TEACHER_LOCAL_ID,
        "Class Section ID": CLASS_SECTION_ID,
        "School ID": SCHOOL_ID,
        "Score #1": "8",
        "Score #2": "12"
      },
      {
        grade: GRADE,
        "Local Student ID": SECOND_STUDENT_LOCAL_ID,
        "Classwide Skill Name": SECOND_SKILL_NAME,
        "Classwide Sequence Position": gradeClasswideSequence.indexOf(SECOND_SKILL_NAME) + 1,
        "First Skill score": "13",
        "Final Skill score": "18",
        ROI: 1.7,
        "Number of scores": 2,
        "Number of weeks spent": 4,
        "Teacher ID": TEACHER_LOCAL_ID,
        "Class Section ID": CLASS_SECTION_ID,
        "School ID": SCHOOL_ID,
        "Score #1": "13",
        "Score #2": "18"
      },
      {
        grade: GRADE,
        "Local Student ID": FIRST_STUDENT_LOCAL_ID,
        "Classwide Skill Name": THIRD_SKILL_NAME,
        "Classwide Sequence Position": gradeClasswideSequence.indexOf(THIRD_SKILL_NAME) + 1,
        "First Skill score": "14",
        "Final Skill score": "22",
        ROI: 1.7,
        "Number of scores": 4,
        "Number of weeks spent": 6,
        "Teacher ID": TEACHER_LOCAL_ID,
        "Class Section ID": CLASS_SECTION_ID,
        "School ID": SCHOOL_ID,
        "Score #1": "14",
        "Score #2": "16",
        "Score #3": "20",
        "Score #4": "22"
      },
      {
        grade: GRADE,
        "Local Student ID": SECOND_STUDENT_LOCAL_ID,
        "Classwide Skill Name": THIRD_SKILL_NAME,
        "Classwide Sequence Position": gradeClasswideSequence.indexOf(THIRD_SKILL_NAME) + 1,
        "First Skill score": "12",
        "Final Skill score": "23",
        ROI: 2.3,
        "Number of scores": 4,
        "Number of weeks spent": 6,
        "Teacher ID": TEACHER_LOCAL_ID,
        "Class Section ID": CLASS_SECTION_ID,
        "School ID": SCHOOL_ID,
        "Score #1": "12",
        "Score #2": "13",
        "Score #3": "18",
        "Score #4": "23"
      }
    ];
    // THE ORDER IS NOT IMPORTANT
    expect(expectedResult.length).toEqual(result.length);
    expectedResult.forEach(expectedItem => {
      expect(result).toContainEqual(expectedItem);
    });
  });
  it("should return N/A for student scores that were not taken (when student was absent) and modify other properties accordingly", () => {
    //(number of weeks spent should be the same for all items within the same student group)
    const result = getClasswideInterventionStats({
      history: singleAssessmentItemsWithAbsentStudents,
      grade: GRADE,
      studentIdsToLocalIds,
      gradeClasswideSequence,
      teacherLocalId: TEACHER_LOCAL_ID,
      classSectionId: CLASS_SECTION_ID,
      schoolId: SCHOOL_ID
    });

    const expectedResult = [
      {
        grade: GRADE,
        "Local Student ID": FIRST_STUDENT_LOCAL_ID,
        "Classwide Skill Name": THIRD_SKILL_NAME,
        "Classwide Sequence Position": gradeClasswideSequence.indexOf(THIRD_SKILL_NAME) + 1,
        "First Skill score": "16",
        "Final Skill score": "20",
        ROI: 4.6,
        "Number of scores": 2,
        "Number of weeks spent": 6,
        "Teacher ID": TEACHER_LOCAL_ID,
        "Class Section ID": CLASS_SECTION_ID,
        "School ID": SCHOOL_ID,
        "Score #1": "N/A",
        "Score #2": "16",
        "Score #3": "20",
        "Score #4": "N/A"
      },
      {
        grade: GRADE,
        "Local Student ID": SECOND_STUDENT_LOCAL_ID,
        "Classwide Skill Name": THIRD_SKILL_NAME,
        "Classwide Sequence Position": gradeClasswideSequence.indexOf(THIRD_SKILL_NAME) + 1,
        "First Skill score": "12",
        "Final Skill score": "23",
        ROI: 2.3,
        "Number of scores": 3,
        "Number of weeks spent": 6,
        "Teacher ID": TEACHER_LOCAL_ID,
        "Class Section ID": CLASS_SECTION_ID,
        "School ID": SCHOOL_ID,
        "Score #1": "12",
        "Score #2": "13",
        "Score #3": "N/A",
        "Score #4": "23"
      }
    ];
    expect(expectedResult.length).toEqual(result.length);
    expectedResult.forEach(expectedItem => {
      expect(result).toContainEqual(expectedItem);
    });
  });
});

describe("getNumberOfWeeksSpent", () => {
  it("should get the starting and ending dates from a single assessment history and return a number of weeks between them", () => {
    const singleAssessmentItems = groupWithSimplifiedHistory.history.filter(
      i => i.assessmentName === "Simplify Fractions"
    );
    const { startingHistoryItem, endingHistoryItem } = getStartingAndEndingHistoryItem(singleAssessmentItems);

    const result = getNumberOfWeeksSpent({ startingHistoryItem, endingHistoryItem });

    expect(result).toEqual(6);
  });
});

describe("getRoundedWeekDifference", () => {
  it("should return 1 week difference if the number of days between two timestamps is 13", () => {
    //13 days passed
    const startTimestamp = 1544213271219; // 2018-12-07
    const endTimestamp = 1545318655012; //2018-12-20

    const result = getRoundedWeekDifference(endTimestamp, startTimestamp);

    expect(result).toBe(1);
  });
  it("should return 2 week difference if the number of days between two timestamps is 14", () => {
    const startTimestamp = 1546297200000; // 2019-01-01
    const endTimestamp = 1547506800000; //2019-01-15

    const result = getRoundedWeekDifference(endTimestamp, startTimestamp);

    expect(result).toBe(2);
  });
  it("should return 1 week difference if the number of days between two timestamps is less than 7", () => {
    const startTimestamp = 1546297200000; // 2019-01-01

    const endTimestamps = [
      1546297200000, //same day
      1546383600000, //2019-01-02 1 day diff
      1546470000000, //2019-01-03 2 day diff
      1546902000000 //2019-01-08 7 day diff
    ];

    endTimestamps.forEach(endTimestamp => {
      const result = getRoundedWeekDifference(endTimestamp, startTimestamp);
      expect(result).toBe(1);
    });
  });
});

describe("getAllStudentsWithAtLeastOneScore", () => {
  it("should get all student ids that have at least one score in provided assessment items", () => {
    const singleAssessmentItems = groupWithSimplifiedHistory.history.filter(
      i => i.assessmentName === "Simplify Fractions"
    );
    const result = getUniqueStudentIdsWithScore(singleAssessmentItems);
    expect(result).toEqual([FIRST_STUDENT_ID, SECOND_STUDENT_ID]);
  });
});

describe("getAllStudentScores", () => {
  it("should return studentLocalId, ROI, and the list of scores for selected assessment", () => {
    const singleAssessmentItems = groupWithSimplifiedHistory.history.filter(
      i => i.assessmentName === "Simplify Fractions"
    );
    const studentIds = getUniqueStudentIdsWithScore(singleAssessmentItems);

    const result = getAllStudentScores({ historyItems: singleAssessmentItems, studentIds, studentIdsToLocalIds });

    expect(result).toEqual([
      {
        studentId: FIRST_STUDENT_ID,
        studentLocalId: FIRST_STUDENT_LOCAL_ID,
        ROI: 1.7,
        scores: [
          {
            score: "14",
            scoreIndex: 1
          },
          {
            score: "16",
            scoreIndex: 2
          },
          {
            score: "20",
            scoreIndex: 3
          },
          {
            score: "22",
            scoreIndex: 4
          }
        ]
      },
      {
        studentId: SECOND_STUDENT_ID,
        studentLocalId: SECOND_STUDENT_LOCAL_ID,
        ROI: 2.3,
        scores: [
          {
            score: "12",
            scoreIndex: 1
          },
          {
            score: "13",
            scoreIndex: 2
          },
          {
            score: "18",
            scoreIndex: 3
          },
          {
            score: "23",
            scoreIndex: 4
          }
        ]
      }
    ]);
  });
  it("should return null for each missing score (when student was absent)", () => {
    const singleAssessmentItems = groupWithSimplifiedHistory.history.filter(
      i => i.assessmentName === "Find the Least Common Denominator"
    );
    // remove one student's score from each assessment
    singleAssessmentItems[0].assessmentResultMeasures[0].studentResults.splice(0, 1);
    singleAssessmentItems[1].assessmentResultMeasures[0].studentResults.splice(1, 1);
    const studentIds = getUniqueStudentIdsWithScore(singleAssessmentItems);

    const result = getAllStudentScores({ historyItems: singleAssessmentItems, studentIds, studentIdsToLocalIds });

    expect(result).toEqual([
      {
        studentId: SECOND_STUDENT_ID,
        studentLocalId: SECOND_STUDENT_LOCAL_ID,
        ROI: "N/A",
        scores: [
          {
            score: null,
            scoreIndex: 1
          },
          {
            score: "18",
            scoreIndex: 2
          }
        ]
      },
      {
        studentId: FIRST_STUDENT_ID,
        studentLocalId: FIRST_STUDENT_LOCAL_ID,
        ROI: "N/A",
        scores: [
          {
            score: "8",
            scoreIndex: 1
          },
          {
            score: null,
            scoreIndex: 2
          }
        ]
      }
    ]);
  });
});
