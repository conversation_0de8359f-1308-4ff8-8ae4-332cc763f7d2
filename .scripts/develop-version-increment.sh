#!/bin/bash

git fetch --tags
git pull origin develop
NEW_TAG=''
OLD_TAG=$(git describe --abbrev=0 --tags)
MIDDLE=${OLD_TAG##*.}
MIDDLE=${MIDDLE%_*}
echo "$(($MIDDLE+1))"
FIRST=${OLD_TAG%.*}
echo "$FIRST"
LAST=''
if [[ $OLD_TAG == *'_'* ]]; then
    LAST=_${OLD_TAG#*_}
fi
echo "$LAST"
NEW_TAG="$FIRST.$(($MIDDLE+1))$LAST"
echo $NEW_TAG

# write out new version file content prior to publishing
cat <<EOF > ./imports/startup/client/version.js
// THIS FILE IS AUTOMATICALLY GENERATED - ATTEMPTS TO EDIT IT WILL BE OVERWRITTEN
const currentApplicationVersion = '$NEW_TAG';

const getCurrentApplicationVersion = () => (currentApplicationVersion);

export default getCurrentApplicationVersion;
EOF

if [ -f ./imports/startup/client/version.js ]; then
    CURRENT_VERSION=$(egrep -o "\d+\.\d+\.\d+\.\d+-?[^;\']*" ./imports/startup/client/version.js)
    echo "Current version on file: $CURRENT_VERSION"
fi

git tag -a -m "Tagging verion $NEW_TAG" "$NEW_TAG"
git push --follow-tags
