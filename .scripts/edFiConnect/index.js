// use node v >= 10.0.0

// The example below gets data from the Ed-Fi ODS API

// BASED ON: https://techdocs.ed-fi.org/display/ODSAPI31/Using+Scripting+Languages+to+Connect
// More Info: https://techdocs.ed-fi.org/display/ODSAPI3/API+Client+Developers%27+Guide
const axios = require("axios");

// TO BE REPLACED WITH REAL DATA
const serverUrl = "https://api.ed-fi.org/v3/api";
const edfiClientId = "RvcohKz9zHI4";
const edfiClientSecret = "E1iEFusaNf81xzCxwHfbolkC";
const orgDataLocation = `/data/v3/ed-fi`;
const itemsFetched = "/schools";

const authString = `Basic ${Buffer.from(edfiClientId + ":" + edfiClientSecret).toString("base64")}`;

run(itemsFetched).then(
  () => {
    console.log("\nCOMPLETE!");
  },
  e => {
    console.log("\nError:", e);
  }
);

async function getAuthToken() {
  const authInstance = axios.create({
    baseURL: serverUrl,
    timeout: 30000,
    headers: {
      "Content-Type": "application/json",
      Authorization: authString
    }
  });
  const {
    data: { access_token }
  } = await authInstance
    .post("/oauth/token", {
      grant_type: "client_credentials"
    })
    .catch(function(error) {
      console.log("getAuthToken error:", error);
    });
  return access_token;
}

async function run(itemsFetched) {
  const access_token = await getAuthToken();
  const authKey = `Bearer ${access_token}`;

  const fetchInstance = axios.create({
    baseURL: `${serverUrl}${orgDataLocation}`,
    timeout: 30000,
    headers: {
      Authorization: authKey
    }
  });

  const { data: fetchAllSchoolsData } = await fetchInstance.get(itemsFetched).catch(function(error) {
    console.log("error:", error);
  });
  console.log("Number of items found:", fetchAllSchoolsData.length, "\n");

  const idOfFirstItem = fetchAllSchoolsData[0].id;
  const { data: fetchSingleSite } = await fetchInstance.get(`${itemsFetched}/${idOfFirstItem}`).catch(function(error) {
    console.log("fetch single item error:", error);
  });
  console.log("First item:", fetchSingleSite);
}
