FROM ubuntu:22.04

ENV MONGO_MAJOR=8.0 \
    TZ=America/New_York

RUN apt-get update && apt-get install -y curl gnupg && rm -rf /var/lib/apt/lists/*

# Import the series-specific GPG key (no apt-key)
RUN curl -fsSL https://www.mongodb.org/static/pgp/server-${MONGO_MAJOR}.asc \
  | gpg --dearmor -o /usr/share/keyrings/mongodb-server-${MONGO_MAJOR}.gpg

# Add the series-specific repo; auto-detect Ubuntu codename
RUN . /etc/os-release && echo "deb [arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-${MONGO_MAJOR}.gpg] https://repo.mongodb.org/apt/ubuntu ${VERSION_CODENAME}/mongodb-org/${MONGO_MAJOR} multiverse" \
  > /etc/apt/sources.list.d/mongodb-org-${MONGO_MAJOR}.list

# Timezone (optional)
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Install MongoDB
RUN apt-get update && apt-get install -y mongodb-org netcat && rm -rf /var/lib/apt/lists/*

RUN mkdir -p /data/db
# Add replica set starting script
ADD ./initializeMongo.sh /
ADD ./startMongo.sh /
COPY ./meteor.tar /meteor.tar

# Initialize MongoDB - needs mongosh since MongDB v6
RUN /bin/bash /initializeMongo.sh

# Expose port #27017 from the container to the host
EXPOSE 27017
ENTRYPOINT ["./startMongo.sh"]
