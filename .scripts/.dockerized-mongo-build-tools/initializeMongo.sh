#!/usr/bin/env bash

/usr/bin/mongod --replSet "edspring" --bind_ip_all > $HOME/mongo.log 2>&1 &

until nc -z localhost 27017 ; do echo Waiting for MongoDB; sleep 1; done

mongosh --eval 'rs.initiate({
                 "_id" : "edspring",
                 "version" : 1,
                 "members" : [
                    {
                       "_id" : 1,
                       "host" : "127.0.0.1:27017"
                    }
                 ]
              })'
sleep 5
tar -xzvf /meteor.tar -C /
mongorestore -d meteor /dump/meteor --host=127.0.0.1 --port=27017
rm -rf /dump/meteor
