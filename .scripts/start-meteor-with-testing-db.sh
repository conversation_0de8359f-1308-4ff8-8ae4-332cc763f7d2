#!/bin/bash

# Setup mongo env
source .scripts/.variables/docker_db_variables.sh

LOG_DIR="/home/<USER>/testResults/logs"

[[ -d $LOG_DIR ]] || mkdir -p $LOG_DIR

echo -e "\t=> Initial Application start. `date +"%T"`"
echo -e "\t=> Initial Application start. `date +"%T"`" >> $LOG_DIR/meteor-log.txt
# METEOR_DISABLE_OPTIMISTIC_CACHING=1 - for CI only, value 0 used in development mode for faster rebuilding
# METEOR_PROFILE=100 # ms, enable profiler for meteor build and rebuild process
METEOR_DISABLE_OPTIMISTIC_CACHING=1 meteor --once --settings .scripts/settings-for-testing.json >> $LOG_DIR/meteor-log.txt &

TIMEFORMAT=%R
echo -ne "\033[2K=> Startup time: " # \033[2K erase line regardless of cursor position
time (
until grep -rnw $LOG_DIR/meteor-log.txt -e "App running at: http://localhost:3000/" > /dev/null
do
	sleep 1
done
)
