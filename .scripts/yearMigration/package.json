{"name": "yearmigration", "version": "1.0.0", "description": "Year migration script for organizations.", "main": "index.js", "scripts": {"build": "babel index.js -d dist", "start": "npm run build && node dist/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "MN", "devDependencies": {"@babel/cli": "^7.11.6", "@babel/core": "^7.11.6", "@babel/plugin-transform-runtime": "^7.11.5", "@babel/preset-env": "^7.11.5", "nodemon": "^2.0.4"}, "dependencies": {"@babel/runtime": "^7.11.2", "moment": "^2.29.0"}}