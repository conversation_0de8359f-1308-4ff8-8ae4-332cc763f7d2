import MongoClient from "mongodb";
import moment from "moment";

const MONGO_TARGET_URL = process.env.MONGO_TARGET_URL; // Provide mongo URL to target DB (demo)
const SOURCE_SCHOOL_YEAR = parseInt(process.env.SCHOOL_YEAR_FROM) || 2019;
const TARGET_SCHOOL_YEAR = parseInt(process.env.SCHOOL_YEAR_TO) || SOURCE_SCHOOL_YEAR;
const ORG_ID = process.env.ORGID || null;
const SITE_ID = process.env.SITEID || null;

const schoolYearDifference = TARGET_SCHOOL_YEAR - SOURCE_SCHOOL_YEAR;

async function run() {
  if (!MONGO_TARGET_URL) {
    console.log("MONGO_TARGET_URL not set in env");
    process.exit(1);
  }

  if (!ORG_ID) {
    console.log("ORG_ID not set in env");
    process.exit(1);
  }

  let baseQuery = { schoolYear: SOURCE_SCHOOL_YEAR };
  let queryForAddedRecords = { schoolYear: TARGET_SCHOOL_YEAR };

  if (ORG_ID) {
    baseQuery.orgid = ORG_ID;
    queryForAddedRecords.orgid = ORG_ID;
  }

  const client = await MongoClient.connect(MONGO_TARGET_URL, {
    useUnifiedTopology: true,
    tlsAllowInvalidCertificates: true
  });
  const db = await client.db();

  console.log("Processing benchmark windows...");
  const benchmarkWindows = await db
    .collection("BenchmarkWindows")
    .find(baseQuery)
    .toArray();

  console.log("benchmarkWindows found: ", benchmarkWindows.length);

  await db.collection("BenchmarkWindows").deleteMany(baseQuery);

  const migratedBenchmarkWindows = migrateBenchmarkWindows({
    benchmarkWindows,
    targetSchoolYear: TARGET_SCHOOL_YEAR,
    schoolYearDifference
  });

  await db.collection("BenchmarkWindows").insertMany(migratedBenchmarkWindows);

  console.log("Processing users...");
  const users = await db
    .collection("users")
    .find({ "profile.siteAccess.targetSchoolYear": SOURCE_SCHOOL_YEAR, "profile.orgid": ORG_ID })
    .toArray();

  console.log("users found: ", users.length);

  const migratedSiteAccessForUsers = migrateSiteAccessForUsers({ users, targetSchoolYear: TARGET_SCHOOL_YEAR });

  for (const [key, value] of Object.entries(migratedSiteAccessForUsers)) {
    db.collection("users").updateOne({ _id: key }, { $set: { "profile.siteAccess": value } });
  }

  console.log("Processing students...");
  const students = await db
    .collection("Students")
    .find(baseQuery)
    .toArray();

  console.log("students found: ", students.length);

  await db.collection("Students").deleteMany(baseQuery);

  const { migratedStudents, newStudentIds } = migrateStudents({
    students,
    targetSchoolYear: TARGET_SCHOOL_YEAR,
    schoolYearDifference
  });

  await db.collection("Students").insertMany(migratedStudents);

  console.log("Processing student groups...");
  const studentGroups = await db
    .collection("StudentGroups")
    .find(baseQuery)
    .toArray();

  console.log("studentGroups found: ", studentGroups.length);

  await db.collection("StudentGroups").deleteMany(baseQuery);

  const { migratedStudentGroups, newStudentGroupsIds } = migrateStudentGroups({
    studentGroups,
    newStudentIds,
    targetSchoolYear: TARGET_SCHOOL_YEAR,
    schoolYearDifference
  });

  await db.collection("StudentGroups").insertMany(migratedStudentGroups);

  console.log("Processing assessment results...");
  const assessmentResults = await db
    .collection("AssessmentResults")
    .find(baseQuery)
    .toArray();

  console.log("assessmentResults found: ", assessmentResults.length);

  await db.collection("AssessmentResults").deleteMany(baseQuery);

  const { migratedAssessmentResults, newAssessmentResultsIds } = migrateAssessmentResults({
    assessmentResults,
    newStudentIds,
    newStudentGroupsIds,
    targetSchoolYear: TARGET_SCHOOL_YEAR,
    schoolYearDifference
  });

  await db.collection("AssessmentResults").insertMany(migratedAssessmentResults);

  const addedStudents = await db
    .collection("Students")
    .find(queryForAddedRecords)
    .toArray();

  for (const student of addedStudents) {
    if (student.currentSkill) {
      await db.collection("Students").updateOne(
        { _id: student._id },
        {
          $set: {
            "currentSkill.assessmentResultId": newAssessmentResultsIds[student.currentSkill.assessmentResultId]
          }
        }
      );
    }
  }

  const addedStudentGroups = await db
    .collection("StudentGroups")
    .find(queryForAddedRecords)
    .toArray();

  const updatedNewStudentGroups = updateNewStudentGroups({ addedStudentGroups, newAssessmentResultsIds });

  for (const [key, value] of Object.entries(updatedNewStudentGroups)) {
    await db.collection("StudentGroups").updateOne(
      { _id: key },
      {
        $set: value
      }
    );
  }

  console.log("Processing student group enrollments...");

  const studentGroupEnrollments = await db
    .collection("StudentGroupEnrollments")
    .find(baseQuery)
    .toArray();

  console.log("studentGroupEnrollments found: ", studentGroupEnrollments.length);

  await db.collection("StudentGroupEnrollments").deleteMany(baseQuery);

  const migratedStudentGroupEnrollment = migrateStudentGroupEnrollment({
    studentGroupEnrollments,
    newStudentIds,
    newStudentGroupsIds,
    targetSchoolYear: TARGET_SCHOOL_YEAR,
    schoolYearDifference
  });
  await db.collection("StudentGroupEnrollments").insertMany(migratedStudentGroupEnrollment);

  let studentsBySkillQuery = {};
  if (SITE_ID) {
    studentsBySkillQuery = { siteId: SITE_ID };
  } else {
    const sites = await db
      .collection("Sites")
      .find({ orgid: ORG_ID }, { _id: 1 })
      .toArray();
    studentsBySkillQuery = { siteId: { $in: sites.map(site => site._id) } };
  }
  console.log("Processing students by skill...");

  const studentsBySkill = await db
    .collection("StudentsBySkill")
    .find(studentsBySkillQuery)
    .toArray();

  console.log("studentsBySkill found: ", studentsBySkill.length);

  const migratedStudentsBySkill = migrateStudentsBySkill({ studentsBySkill, newStudentIds });

  for (const [key, value] of Object.entries(migratedStudentsBySkill)) {
    await db.collection("StudentsBySkill").updateOne({ _id: key }, { $set: { ...value } });
  }

  console.log("Function reached the end successfully");
  process.exit(0);
}

try {
  run();
} catch (e) {
  console.log("Exception: ", e);
  process.exit(1);
}

function shiftYearOfDateObject(date, schoolYearDifference) {
  const shiftedDate = moment(date.on)
    .utc()
    .add(schoolYearDifference, "years");
  return { ...date, on: shiftedDate.valueOf(), date: shiftedDate.toDate() };
}

function migrateBenchmarkWindows({ benchmarkWindows, targetSchoolYear, schoolYearDifference }) {
  const migratedBenchamrkWindows = [];
  for (const benchmarkWindow of benchmarkWindows) {
    benchmarkWindow.schoolYear = targetSchoolYear;
    const newSchoolYear = benchmarkWindow.startDate.getUTCFullYear() + schoolYearDifference;
    benchmarkWindow.startDate.setUTCFullYear(newSchoolYear);
    benchmarkWindow.endDate.setUTCFullYear(newSchoolYear);

    benchmarkWindow._id = benchmarkWindow._id + targetSchoolYear;

    migratedBenchamrkWindows.push(benchmarkWindow);
  }
  return migratedBenchamrkWindows;
}

function migrateSiteAccessForUsers({ users, targetSchoolYear }) {
  const migratedUsersAccess = {};
  for (const user of users) {
    const { siteAccess } = user.profile;
    if (!siteAccess.some(e => e.schoolYear === targetSchoolYear)) {
      let newSiteAccess = [];
      siteAccess.forEach(siteAccessElem => {
        if (siteAccessElem.schoolYear === SOURCE_SCHOOL_YEAR) {
          let modifiedSiteAccess = Object.assign({}, siteAccessElem);
          modifiedSiteAccess.schoolYear = targetSchoolYear;
          newSiteAccess.push(modifiedSiteAccess);
        }
      });

      migratedUsersAccess[user._id] = newSiteAccess.concat(siteAccess);
    }
  }
  return migratedUsersAccess;
}

function migrateStudents({ students, targetSchoolYear, schoolYearDifference }) {
  const migratedStudents = [];
  const newStudentIds = {};

  for (const student of students) {
    const newId = student._id + targetSchoolYear;
    newStudentIds[student._id] = newId;
    student._id = newId;
    student.schoolYear = targetSchoolYear;
    if (student.created) {
      student.created = shiftYearOfDateObject(student.created, schoolYearDifference);
    }
    if (student.lastModified) {
      student.lastModified = shiftYearOfDateObject(student.lastModified, schoolYearDifference);
    }
    if (student.currentSkill) {
      student.currentSkill.whenStarted = shiftYearOfDateObject(student.currentSkill.whenStarted, schoolYearDifference);
    }
    if (student.history) {
      student.history = student.history.map(historyElem => {
        historyElem.assessmentResultId = `${historyElem.assessmentResultId}${targetSchoolYear}`;
        historyElem.assessmentResultMeasures.forEach(arm => {
          arm.studentResults.forEach(sr => {
            sr.studentId = `${sr.studentId}${targetSchoolYear}`;
          });
        });
        historyElem.whenStarted = shiftYearOfDateObject(historyElem.whenStarted, schoolYearDifference);
        historyElem.whenEnded = shiftYearOfDateObject(historyElem.whenEnded, schoolYearDifference);
        return historyElem;
      });
    }
    migratedStudents.push(student);
  }
  return { migratedStudents, newStudentIds };
}

function migrateStudentGroups({ studentGroups, newStudentIds, targetSchoolYear, schoolYearDifference }) {
  const migratedStudentGroups = [];
  const newStudentGroupsIds = {};
  for (const studentGroup of studentGroups) {
    const newId = studentGroup._id + targetSchoolYear;
    newStudentGroupsIds[studentGroup._id] = newId;
    studentGroup._id = newId;
    studentGroup.schoolYear = targetSchoolYear;
    const newHistory = [];
    if (studentGroup.currentClasswideSkill) {
      studentGroup.currentClasswideSkill.whenStarted = shiftYearOfDateObject(
        studentGroup.currentClasswideSkill.whenStarted,
        schoolYearDifference
      );
    }
    studentGroup.history &&
      studentGroup.history.forEach(historyElem => {
        if (historyElem.enrolledStudentIds) {
          historyElem.enrolledStudentIds = historyElem.enrolledStudentIds.map(
            oldEnrolledStudentId => newStudentIds[oldEnrolledStudentId]
          );
        }
        historyElem.assessmentResultMeasures &&
          historyElem.assessmentResultMeasures.forEach(assessmentResultMeasure => {
            if (assessmentResultMeasure.studentResults) {
              assessmentResultMeasure.studentResults = assessmentResultMeasure.studentResults.map(oldStudentResult => {
                let modifiedStudentResults = Object.assign({}, oldStudentResult);
                modifiedStudentResults.studentId = newStudentIds[oldStudentResult.studentId];
                return modifiedStudentResults;
              });
            }
          });

        if (historyElem.whenStarted) {
          historyElem.whenStarted = shiftYearOfDateObject(historyElem.whenStarted, schoolYearDifference);
        }
        historyElem.whenEnded = shiftYearOfDateObject(historyElem.whenEnded, schoolYearDifference);

        newHistory.push(historyElem);
      });
    studentGroup.history = newHistory;
    if (studentGroup.individualInterventionQueue) {
      studentGroup.individualInterventionQueue = studentGroup.individualInterventionQueue.map(
        studentId => newStudentIds[studentId] || studentId
      );
    }
    migratedStudentGroups.push(studentGroup);
  }
  return { migratedStudentGroups, newStudentGroupsIds };
}

function migrateAssessmentResults({
  assessmentResults,
  newStudentIds,
  newStudentGroupsIds,
  targetSchoolYear,
  schoolYearDifference
}) {
  const migratedAssessmentResults = [];
  let newAssessmentResultsIds = {};
  for (const assessmentResult of assessmentResults) {
    assessmentResult.schoolYear = targetSchoolYear;
    const newAssessmentResultsId = assessmentResult._id + targetSchoolYear;
    newAssessmentResultsIds[assessmentResult._id] = newAssessmentResultsId;
    assessmentResult._id = newAssessmentResultsId;
    if (assessmentResult.previousAssessmentResultId) {
      assessmentResult.previousAssessmentResultId = `${assessmentResult.previousAssessmentResultId}${targetSchoolYear}`;
    }

    if (assessmentResult.nextAssessmentResultId) {
      assessmentResult.nextAssessmentResultId = `${assessmentResult.nextAssessmentResultId}${targetSchoolYear}`;
    }
    if (assessmentResult.ruleResults) {
      assessmentResult.ruleResults.nextAssessmentResultId = `${assessmentResult.ruleResults.nextAssessmentResultId}${targetSchoolYear}`;
    }

    assessmentResult.studentGroupId = newStudentGroupsIds[assessmentResult.studentGroupId];
    if (assessmentResult.studentId) {
      assessmentResult.studentId = newStudentIds[assessmentResult.studentId];
    }

    assessmentResult.scores.forEach(score => {
      score.studentId = newStudentIds[score.studentId];
    });

    assessmentResult.measures &&
      assessmentResult.measures.forEach(measure => {
        measure.studentResults.forEach(studentResult => {
          studentResult.studentId = newStudentIds[studentResult.studentId];
        });
      });

    if (assessmentResult.classwideResults) {
      assessmentResult.classwideResults.studentIdsNotMeetingTarget = assessmentResult.classwideResults.studentIdsNotMeetingTarget.map(
        studentId => newStudentIds[studentId]
      );
    }

    if (assessmentResult.individualSkills) {
      assessmentResult.individualSkills.assessmentResultId =
        newAssessmentResultsIds[assessmentResult.individualSkills.assessmentResultId];
    }

    if (assessmentResult.lastScoreUpdatedAt) {
      const lastScoreUpdatedAt = moment(assessmentResult.lastScoreUpdatedAt)
        .utc()
        .add(schoolYearDifference, "years");
      assessmentResult.lastScoreUpdatedAt = lastScoreUpdatedAt.valueOf();
    }

    if (assessmentResult.created) {
      assessmentResult.created = shiftYearOfDateObject(assessmentResult.created, schoolYearDifference);
    }

    if (assessmentResult.lastModified) {
      assessmentResult.lastModified = shiftYearOfDateObject(assessmentResult.lastModified, schoolYearDifference);
    }
    migratedAssessmentResults.push(assessmentResult);
  }
  return { migratedAssessmentResults, newAssessmentResultsIds };
}

function migrateStudentGroupEnrollment({
  studentGroupEnrollments,
  newStudentIds,
  newStudentGroupsIds,
  targetSchoolYear,
  schoolYearDifference
}) {
  const migratedStudentGroupEnrollment = [];
  for (const studentGroupEnrollment of studentGroupEnrollments) {
    studentGroupEnrollment._id = studentGroupEnrollment._id + targetSchoolYear;
    studentGroupEnrollment.schoolYear = targetSchoolYear;
    studentGroupEnrollment.studentId = newStudentIds[studentGroupEnrollment.studentId];
    studentGroupEnrollment.studentGroupId = newStudentGroupsIds[studentGroupEnrollment.studentGroupId];
    studentGroupEnrollment.created = shiftYearOfDateObject(studentGroupEnrollment.created, schoolYearDifference);
    studentGroupEnrollment.lastModified = shiftYearOfDateObject(
      studentGroupEnrollment.lastModified,
      schoolYearDifference
    );

    migratedStudentGroupEnrollment.push(studentGroupEnrollment);
  }
  return migratedStudentGroupEnrollment;
}

function migrateStudentsBySkill({ studentsBySkill, newStudentIds }) {
  const migratedStudentsBySkill = {};
  for (const studentsBySkillElem of studentsBySkill) {
    studentsBySkillElem.studentsBelowInstructionalTarget = studentsBySkillElem.studentsBelowInstructionalTarget.map(
      studentId => newStudentIds[studentId] || studentId
    );
    studentsBySkillElem.studentsBelowMasteryTarget = studentsBySkillElem.studentsBelowMasteryTarget.map(
      studentId => newStudentIds[studentId] || studentId
    );
    studentsBySkillElem.studentsWithoutSkillHistory = studentsBySkillElem.studentsWithoutSkillHistory.map(
      studentId => newStudentIds[studentId] || studentId
    );
    migratedStudentsBySkill[studentsBySkillElem._id] = { ...studentsBySkillElem };
  }
  return migratedStudentsBySkill;
}

function updateNewStudentGroups({ addedStudentGroups, newAssessmentResultsIds }) {
  const studentGroups = {};

  for (const studentGroup of addedStudentGroups) {
    const updatedStudentGroupQuery = {};
    if (studentGroup.currentClasswideSkill) {
      studentGroup.currentClasswideSkill.assessmentResultId =
        newAssessmentResultsIds[studentGroup.currentClasswideSkill.assessmentResultId];
      updatedStudentGroupQuery["currentClasswideSkill.assessmentResultId"] =
        studentGroup.currentClasswideSkill.assessmentResultId;
    }
    studentGroup.history.forEach(historyElem => {
      historyElem.assessmentResultId = newAssessmentResultsIds[historyElem.assessmentResultId];
    });
    updatedStudentGroupQuery.history = studentGroup.history;
    if (studentGroup.currentAssessmentResultIds) {
      studentGroup.currentAssessmentResultIds = studentGroup.currentAssessmentResultIds.map(
        currentAssessmentResultId => newAssessmentResultsIds[currentAssessmentResultId]
      );
      updatedStudentGroupQuery.currentAssessmentResultIds = studentGroup.currentAssessmentResultIds;
    }
    studentGroups[studentGroup._id] = updatedStudentGroupQuery;
  }
  return studentGroups;
}
