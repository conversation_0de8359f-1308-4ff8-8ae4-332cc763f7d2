function onlyUnique(value, index, self) {
  return self.indexOf(value) === index;
}

var sgIds = db.AssessmentResults.find({ benchmarkPeriodId: "cjCMnZKARBJmG8suT", schoolYear: 2019, type: "benchmark" })
  .toArray()
  .map(function(x) {
    return x.studentGroupId;
  });
var sgs = db.StudentGroups.find({ _id: { $in: sgIds } }, { siteId: 1, orgid: 1 }).toArray();
var siteIds = sgs.map(function(s) {
  return s.siteId;
});
var uniqueSiteIds = siteIds.filter(onlyUnique);
printjson(uniqueSiteIds);
print(uniqueSiteIds.length);

uniqueSiteIds.forEach(function(siteId) {
  var hasSpringScreening = db.BenchmarkWindows.findOne(
    { schoolYear: 2019, benchmarkPeriodId: "cjCMnZKARBJmG8suT", siteId: siteId },
    { orgid: 1 }
  );
  printjson(hasSpringScreening);
  print(siteId, !!hasSpringScreening);
});
