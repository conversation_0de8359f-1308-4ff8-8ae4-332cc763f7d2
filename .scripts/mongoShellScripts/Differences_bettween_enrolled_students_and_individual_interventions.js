// FINDS IF ACTIVELY ENROLLED STUDENTS HAVE SCORES OPEN IN ACTIVE ASSESSMENTS

var groups = db.StudentGroups.find({orgid: "dJF9noggGuz6yNaw7", isActive: true, schoolYear: 2018}, {currentAssessmentResultIds: 1, _id: 1}).toArray()

groups.forEach(function(group) {
	var activelyEnrolledStudentIds = db.StudentGroupEnrollments.find({studentGroupId: group._id, isActive: true, schoolYear: 2018}, {studentId: 1}).toArray().map(function(s){return s.studentId}).sort();
	if(!group.currentAssessmentResultIds) return;
	var activeAssessments = db.AssessmentResults.find({_id: {$in: group.currentAssessmentResultIds}, type:  "individual"}, {studentId: 1, studentGroupId: 1}).toArray();
	if(!activeAssessments.length) return;
	printjson(activeAssessments)
	activeAssessments.forEach(function(ass) {
	  print("isEnrolled", activelyEnrolledStudentIds.includes(ass.studentId))
	})
})
