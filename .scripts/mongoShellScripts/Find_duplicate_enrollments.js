var organizations = db.Organizations.find({},{_id:1}).toArray().map(function(s){return s._id})
//printjson(organizations)

var page = 0;
var limit = 80;
var slicedOrganizations = organizations.slice(page * limit, page * limit + limit);
slicedOrganizations.forEach(function (organizationId) {
  print('orgid', organizationId, '\n\n');
  var activeEnrollments = db.StudentGroupEnrollments.find({orgid: organizationId, schoolYear: 2018, isActive: true}, {studentId: 1, grade: 1 }).sort({studentId: 1}).toArray() 
  var duplicates = [];
  activeEnrollments.forEach(function(enrollment, index) {
    if(index === activeEnrollments.length - 1) return;
    if(enrollment.studentId === activeEnrollments[index+1].studentId) {
    		duplicates.push(enrollment.studentId)
    }
  })
  printjson(duplicates)
});
