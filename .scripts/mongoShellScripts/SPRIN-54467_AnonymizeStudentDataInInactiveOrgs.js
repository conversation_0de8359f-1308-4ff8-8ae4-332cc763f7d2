function generateCleanUUID() {
  const raw = UUID().toString();
  const uuid = raw.match(/[a-f0-9\-]{36}/)[0];
  return uuid.replace(/[^a-zA-Z0-9]/g, '');
}

const inactiveOrganizations = db.Organizations.find({ isActive: false, _id: { $ne: "LyGDeCXKYkoiB9SCW" }}, { _id: 1 }).toArray();

inactiveOrganizations.forEach(org => {
  const students = db.Students.find({ orgid: org._id }, { _id: 1 }).toArray();

  const date = new Date("2000-01-01");
  const timestamp = date.getTime();

  const bulkOps = students.map(student => {
    const uId = generateCleanUUID();
    return {
      updateOne: {
        filter: { _id: student._id },
        update: {
          $set: {
            "identity.name.firstName": "N/A",
            "identity.name.lastName": "N/A",
            "identity.identification.localId": `fake${uId}`,
            "identity.identification.stateId": `fake${uId}`,
            "demographic.birthDate": "2000-01-01",
            "demographic.birthDateTimeStamp": timestamp
          }
        }
      }
    };
  });
  printjson(bulkOps.length)

  if (bulkOps.length > 0) {
    db.Students.bulkWrite(bulkOps, { ordered: false });
  }
});
