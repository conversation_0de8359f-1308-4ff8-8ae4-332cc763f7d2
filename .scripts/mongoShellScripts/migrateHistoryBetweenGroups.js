/*
{
	_id: "isolatedSectionId",
	studentGroupsWithSameName: [{ isActive: true }, { isActive: false }]
}

Without conflicts

Things to do:
Parse inactive group history
Remove all assessment results for active group (if has empty history)
Move group history, currentAssessmentResultIds to active group
	history item:
		update enrolledStudentIds to match currently enrolled
		assessmentResultMeasures:
			update studentResults studentId to match currently enrolled students and their first/last name
Update group name if needed to match sectionId
Remove isolatedSectionId field

Update assessment results studentIds
Fix OPEN assessment result
*/

const orgid = "XCDDj5FeAYaZzacYq";
const schoolYear = 2024;

const groups = db.StudentGroups.aggregate([
  { $match: { schoolYear, orgid } },
  // { $project: { name: 1, sectionId: 1, isActive: 1 } },
  { $sort: { "created.on": -1 } },
  { $group: { _id: "$sectionId", studentGroupsWithSameName: { $push: "$$ROOT" } } },
  {
    $match: {
      $or: [{ "studentGroupsWithSameName.isActive": true }, { "studentGroupsWithSameName.1": { $exists: true } }]
    }
  },
  {
    $match: {
      $or: [
        { "studentGroupsWithSameName.history.0": { $exists: true } },
        { "studentGroupsWithSameName.currentClasswideSkill": { $exists: true } }
      ]
    }
  }
  // {
  //     $project: {
  //         "studentGroupsWithSameName.history.assessmentResultMeasures": 0,
  //         "studentGroupsWithSameName.history.enrolledStudentIds": 0,
  //         "studentGroupsWithSameName.history.message": 0
  //     }
  // }
]).toArray();

// print(groups)
const groupsToSwitchPlace = [];
const groupsWithConflicts = [];
const groupsThatNeedManualWork = [];
const skippedGroups = [];

var ALPHABET = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
var ID_LENGTH = 17;
function generateId() {
  let rtn = "";
  for (let i = 0; i < ID_LENGTH; i++) {
    rtn += ALPHABET.charAt(Math.floor(Math.random() * ALPHABET.length));
  }
  return rtn;
}

groups.forEach((group, index) => {
  const sgs = group.studentGroupsWithSameName;
  if (sgs.length > 2) {
    groupsThatNeedManualWork.push(group);
  } else if (
    (sgs.find(s => s.isActive === false && s.history?.[0]) && sgs.find(s => s.isActive === true && !s.history?.[0])) ||
    (sgs.find(s => s.isActive === false && s.currentClasswideSkill) &&
      sgs.find(s => s.isActive === true && !s.history?.[0]))
  ) {
    groupsToSwitchPlace.push(group);
  } else if (
    (sgs[0]?.history && sgs[1]?.history) ||
    (sgs.find(s => s.isActive === true && s.currentClasswideSkill) &&
      sgs.find(s => s.isActive === false && s.history?.[0]))
  ) {
    groupsWithConflicts.push(group);
  } else {
    skippedGroups.push(group);
  }
});
print("Total number of groups: ", groups.length);
print("GroupsToSwitchPlace: ", groupsToSwitchPlace.length);
// print("GroupsWithConflicts: ", groupsWithConflicts.length);
if (groupsThatNeedManualWork.length) {
  print("GroupsThatNeedManualWork: ", groupsThatNeedManualWork.length);
}
if (skippedGroups.length) {
  print("SkippedGroups: ", skippedGroups.length);
}
// print(groupsToSwitchPlace);
// print(groupsWithConflicts);

const students = db.Students.aggregate([
  {
    $match: {
      schoolYear,
      orgid
    }
  },
  { $sort: { "created.on": -1 } },
  {
    $addFields: {
      localId: { $regexFind: { input: "$identity.identification.localId", regex: /(?<=-)[^-]+(?=-)|^[^-]+$/ } }
    }
  },
  {
    $group: {
      _id: { name: "$identity.name", grade: "$grade", localId: "$localId.match" },
      studentsWithSameName: { $push: "$$ROOT" }
    }
  },
  {
    $match: {
      "studentsWithSameName.1": { $exists: true }
    }
  }
]).toArray();
// print(students);
const studentIdByOldStudentId = {};
const studentsToUpdate = [];
const idsOfStudentsToRemove = [];
students.forEach(studentGrouping => {
  if (studentGrouping.studentsWithSameName.length > 2) {
    print(studentGrouping.studentsWithSameName);
  }
  const [studentToUpdate, studentToRemove] = studentGrouping.studentsWithSameName;
  studentIdByOldStudentId[studentToRemove._id] = studentToUpdate._id;
  studentsToUpdate.push(studentToUpdate);
  idsOfStudentsToRemove.push(studentToRemove._id);
});
// print(studentIdByOldStudentId);

groupsToSwitchPlace.forEach(groupSet => {
  const activeGroup = groupSet.studentGroupsWithSameName.find(s => s.isActive === true);
  const inactiveGroup = groupSet.studentGroupsWithSameName.find(s => s.isActive === false);
  const assessmentResultsToMove = db.AssessmentResults.find({ studentGroupId: inactiveGroup._id })
    .sort({
      "created.on": 1
    })
    .toArray();
  const activeGroupAssessmentResults = db.AssessmentResults.find({ studentGroupId: activeGroup._id }).toArray();
  const activeEnrollmentStudentIds = db.StudentGroupEnrollments.find(
    { studentGroupId: activeGroup._id, isActive: true },
    { studentId: 1 }
  )
    .toArray()
    .map(s => s.studentId);
  const openAssessmentResultIds = [];
  if (!activeGroupAssessmentResults.find(a => a.status === "COMPLETED")) {
    db.AssessmentResults.deleteMany({ studentGroupId: activeGroup._id, status: "OPEN" });
    const parsedAssessmentResults = assessmentResultsToMove.map(ar => {
      const assessmentIds = ar.assessmentIds;
      if (ar.status === "OPEN") {
        openAssessmentResultIds.push(ar._id);
        const scores = [];
        assessmentIds.forEach(aId => {
          activeEnrollmentStudentIds.forEach(sId => {
            scores.push({
              _id: generateId(),
              assessmentId: aId,
              orgid: activeGroup.orgid,
              siteId: activeGroup.siteId,
              status: "STARTED",
              studentId: sId
            });
          });
        });
        return { ...ar, scores, studentGroupId: activeGroup._id };
      } else {
        return {
          ...ar,
          studentGroupId: activeGroup._id,
          classwideResults: {
            ...ar.classwideResults,
            studentIdsNotMeetingTarget: ar.classwideResults.studentIdsNotMeetingTarget
              .map(id => studentIdByOldStudentId[id])
              .filter(f => f)
          },
          scores: ar.scores
            .map(s => ({
              ...s,
              studentId: studentIdByOldStudentId[s.studentId],
              siteId: activeGroup.siteId
            }))
            .filter(f => f.studentId),
          measures: ar.measures.map(measure => ({
            ...measure,
            studentResults: measure.studentResults
              .map(s => ({ ...s, studentId: studentIdByOldStudentId[s.studentId] }))
              .filter(f => f.studentId)
          }))
        };
      }
    });
    db.AssessmentResults.deleteMany({ _id: { $in: parsedAssessmentResults.map(a => a._id) } });
    parsedAssessmentResults.forEach(ar => {
      db.AssessmentResults.insertOne(ar);
    });
    const parsedHistory = inactiveGroup.history.map(historyItem => ({
      ...historyItem,
      enrolledStudentIds: historyItem.enrolledStudentIds.map(s => studentIdByOldStudentId[s]).filter(f => f),
      assessmentResultMeasures: historyItem.assessmentResultMeasures.map(arm => ({
        ...arm,
        studentResults: arm.studentResults
          .map(s => ({ ...s, studentId: studentIdByOldStudentId[s.studentId] }))
          .filter(f => f.studentId)
      }))
    }));
    db.StudentGroups.updateOne(
      { _id: activeGroup._id },
      {
        $set: {
          ...(openAssessmentResultIds.length ? { currentAssessmentResultIds: openAssessmentResultIds } : {}),
          ...(parsedHistory.length ? { history: parsedHistory } : {}),
          ...(inactiveGroup.currentClasswideSkill ? { currentClasswideSkill: inactiveGroup.currentClasswideSkill } : {})
        }
      }
    );
  }
});
