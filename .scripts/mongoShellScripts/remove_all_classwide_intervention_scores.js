(function () {
  // var studentGroupId = 'TEST_2018_3Uzgl2lfarSd';
  var studentGroupId = 'TEST_2018_WgOmA3gYzZkc';
  var schoolYear = 2018;
  var benchmarkPeriodId = ''; // leave empty to use the benchmark period id from the current classwide skill

  var studentGroup = db.StudentGroups.findOne({ _id: studentGroupId, 'currentClasswideSkill.benchmarkPeriodId': { $exists: true } });

  if (!studentGroup) return print('No student group with a classwide intervention and ' + studentGroupId + ' id');

  benchmarkPeriodId = benchmarkPeriodId || studentGroup.currentClasswideSkill.benchmarkPeriodId;

  var benchmarkAssessmentResult = db.AssessmentResults.findOne({
    studentGroupId: studentGroupId, type: 'benchmark', benchmarkPeriodId: benchmarkPeriodId, schoolYear: schoolYear,
  });

  if (!benchmarkAssessmentResult) return print('No benchmark assessment result found for the student group with ' + studentGroupId + ' id in the ' + benchmarkPeriodId + ' benchmark period and the ' + schoolYear + ' school year');

  var firstClasswideAssessmentResult = db.AssessmentResults.findOne({ previousAssessmentResultId: benchmarkAssessmentResult._id });

  var scores = firstClasswideAssessmentResult.scores.map(function (score) {
    score.status = 'STARTED';
    delete score.value;
    return score;
  });

  var now = new Date();
  var lastModified = {
    by: 'remove_all_classwide_intervention_scores_script',
    on: now.valueOf(),
    date: now,
  };

  db.AssessmentResults.update({ _id: firstClasswideAssessmentResult._id }, {
    $set: { status: 'OPEN', scores: scores, lastModified: lastModified },
    $unset: { classwideResults: '', measures: '', nextAssessmentResultId: '', ruleResults: '' },
  });

  var assessmentId = firstClasswideAssessmentResult.assessmentIds && firstClasswideAssessmentResult.assessmentIds[0];

  if (!assessmentId) return print('No assessment id for the classwide assessment result with ' + firstClasswideAssessmentResult._id + ' id');

  var assessment = db.Assessments.findOne({ _id: assessmentId }, {
    name: 1,
    'strands.name': 1,
    'strands.scores.externalId': 1,
    'strands.scores.targets.grade': 1,
    'strands.scores.targets.periods.benchmarkPeriodId': 1,
    'strands.scores.targets.periods.values': 1,
  });

  if (!assessment) return print('No assessment with the ' + assessmentId + ' id');

  var strandOverallScores = [];
  var scoreTargets = [];
  var targetPeriods = [];
  var assessmentTargets = [];
  assessment.strands.forEach(function (strand) {
    if (strand.name === 'Overall') {
      strandOverallScores = strand.scores;
      return;
    }
  });
  strandOverallScores.forEach(function (score) {
    if (score.externalId === 'number_correct') {
      scoreTargets = score.targets;
      return;
    }
  });
  scoreTargets.forEach(function (target) {
    if (target.grade === studentGroup.grade) {
      targetPeriods = target.periods;
      return;
    }
  });
  targetPeriods.forEach(function (period) {
    if (period.benchmarkPeriodId === benchmarkPeriodId) {
      assessmentTargets = period.values;
      return;
    }
  });

  assessmentTargets = assessmentTargets.map(function (target) {
    return NumberInt(target);
  });

  db.StudentGroups.update({ _id: studentGroupId }, {
    $set: {
      currentAssessmentResultIds: [firstClasswideAssessmentResult._id],
      'currentClasswideSkill.assessmentResultId': firstClasswideAssessmentResult._id,
      'currentClasswideSkill.assessmentId': assessmentId,
      'currentClasswideSkill.assessmentName': assessment.name,
      'currentClasswideSkill.targets': assessmentTargets,
      lastModified: lastModified,
    },
    $unset: { individualInterventionQueue: '' },
    $pull: { history: { type: { $ne: 'benchmark' }, benchmarkPeriodId: benchmarkPeriodId } },
  });

  db.AssessmentResults.remove({
    studentGroupId: studentGroupId,
    type: { $ne: 'benchmark' },
    benchmarkPeriodId: benchmarkPeriodId,
    previousAssessmentResultId: { $ne: benchmarkAssessmentResult._id },
  });

  print('Classwide intervention scores for student group with ' + studentGroupId + ' id have been removed');
})();
