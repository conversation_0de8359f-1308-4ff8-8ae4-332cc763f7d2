// Generate csv file for AssessmentScoresUpload collection
print(
  "StudentLocalID,StudentStateID,StudentLastName,StudentFirstName,AssessmentYear,StateAssessmentName,StateAssessmentScaleScore,StateAssessmentProficient,StateAssessmentPercentileScore,DistrictAssessmentName,DistrictAssessmentFallScaleScore,DistrictAssessmentFallProficient,DistrictAssessmentSpringScaleScore,DistrictAssessmentSpringProficient"
);

// NOTE(fmazur) - Update orgid and schoolYear
// NOTE(fmazur) - Run the script in mongoshell and save output as csv file
// NOTE(fmazur) - Import as super admin
const orgid = "zbcX9ytkEJvdogsZS";
const schoolYear = 2024;

const studentIds = db.StudentGroupEnrollments.find(
  { isActive: true, schoolYear, orgid },
  { studentId: 1, studentGroupId: 1, siteId: 1 }
)
  .sort({ siteId: 1, studentGroupId: 1 })
  .toArray()
  .map(sge => sge.studentId);
const students = db.Students.find({ _id: { $in: studentIds } }, { identity: 1 });

const proficient = ["YES", "NO"];
const scores = [25, 50, 75, 100];
const bools = [true, false];

function getRandom(array) {
  return array[Math.floor(Math.random() * array.length)];
}

function getShouldBeAbsent(totalStudents) {
  return Math.floor(Math.random() * totalStudents) <= totalStudents * 0.1;
}

students.forEach(student => {
  const {
    identification: { localId, stateId },
    name: { firstName, lastName }
  } = student.identity;
  const stateAssessmentName = `stateAssessmentName${Math.floor(Math.random() * 100)}`;
  const districtAssessmentName = `districtAssessmentName${Math.floor(Math.random() * 100)}`;
  const stateProficient = getRandom(proficient);
  const stateScore = stateProficient ? getRandom(scores) : 0;
  const districtProficientFall = getRandom(proficient);
  const districtScoreFall = districtProficientFall ? getRandom(scores) : 0;
  const districtProficientSpring = getRandom(proficient);
  const districtScoreSpring = districtProficientSpring ? getRandom(scores) : 0;
  const stateAssessmentPercentileScore = getRandom(scores);
  if (stateId[0] === "0") {
    return;
  }
  const shouldHaveStateProficiency = !getShouldBeAbsent(studentIds.length) && getRandom(bools);
  const shouldHaveDistrictFall = !getShouldBeAbsent(studentIds.length) && getRandom(bools);
  const shouldHaveDistrictSpring = !getShouldBeAbsent(studentIds.length) && getRandom(bools);

  print(
    `${localId},${stateId},"${lastName}","${firstName}",${schoolYear},${
      shouldHaveStateProficiency ? stateAssessmentName : ""
    },${shouldHaveStateProficiency ? stateScore : ""},${shouldHaveStateProficiency ? stateProficient : ""},${
      shouldHaveStateProficiency ? stateAssessmentPercentileScore : ""
    },${shouldHaveDistrictFall && shouldHaveDistrictSpring ? districtAssessmentName : ""},${
      shouldHaveDistrictFall && shouldHaveDistrictSpring ? districtScoreFall : ""
    },${shouldHaveDistrictFall && shouldHaveDistrictSpring ? districtProficientFall : ""},${
      shouldHaveDistrictFall && shouldHaveDistrictSpring ? districtScoreSpring : ""
    },${shouldHaveDistrictFall && shouldHaveDistrictSpring ? districtProficientSpring : ""}`
  );
});
