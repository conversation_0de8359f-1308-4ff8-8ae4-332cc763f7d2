const assessmentResultsSumsTo20 = db.AssessmentResults.find({
  grade: "05",
  type: "classwide",
  status: "OPEN",
  assessmentIds: "7arH8a6z3BAEdEARm",
  schoolYear: 2023
}).toArray();

const assessmentResultsSubtraction09 = db.AssessmentResults.find({
  grade: "05",
  type: "classwide",
  status: "OPEN",
  assessmentIds: "pexzg5K88e3XBHKYi",
  schoolYear: 2023
}).toArray();

const assessmentResultsSubtraction020 = db.AssessmentResults.find({
  grade: "05",
  type: "classwide",
  status: "OPEN",
  assessmentIds: "RAicL73Hwai6BSCPH",
  schoolYear: 2023
}).toArray();

const assessmentResultIdsByStudentGroup = [
  ...assessmentResultsSubtraction09,
  ...assessmentResultsSumsTo20,
  ...assessmentResultsSubtraction020
].reduce((a, c) => {
  (a[c.studentGroupId] = a[c.studentGroupId] || []).push(c._id);
  return a;
}, {});

const assessmentResultIdsSubtraction020 = assessmentResultsSubtraction020.map(ar => ar._id);

Object.entries(assessmentResultIdsByStudentGroup).forEach(([studentGroupId, assessmentResultIds]) => {
  if (assessmentResultIds.length === 2) {
    const assessmentResultIdToBeRemoved = assessmentResultIds.find(arId =>
      assessmentResultIdsSubtraction020.includes(arId)
    );
    const studentGroup = db.StudentGroups.findOne({ _id: studentGroupId }, { siteId: 1, currentClasswideSkill: 1 });
    printjson({
      studentGroupId,
      assessmentResultIds,
      assessmentResultIdToBeRemoved,
      siteId: studentGroup.siteId,
      currentClasswideSkill: studentGroup.currentClasswideSkill && studentGroup.currentClasswideSkill.assessmentId
    });
    db.AssessmentResults.remove({
      _id: assessmentResultIdToBeRemoved
    });
    db.StudentGroups.update(
      { _id: studentGroupId },
      { $pull: { currentAssessmentResultIds: assessmentResultIdToBeRemoved } },
      { upsert: false, multi: true }
    );
  }
});
