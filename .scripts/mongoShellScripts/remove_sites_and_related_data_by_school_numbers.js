// Mongodb script to remove sites and their related data by school number for given organization and school year
var schoolNumbers = ["119", "117", "105", "113", "109"];
var orgid = "evBRRkwmQsj7nWDhF";
var schoolYear = 2024;

var sites = db.Sites.find({ "stateInformation.schoolNumber": { $in: schoolNumbers }, schoolYear, orgid }).toArray();
var rosterImportIds = sites.map(s => s.rosterImportId);
var siteIds = sites.map(s => s._id);

print("Removing sites:");
sites.forEach(s => print(`  ${s.name} - ${s.stateInformation.schoolNumber} - "${s._id}"`));
db.Sites.deleteMany({ _id: { $in: siteIds } });

var studentGroupEnrollments = db.StudentGroupEnrollments.find({
  siteId: { $in: siteIds },
  orgid,
  schoolYear
}).toArray();
var sgeStudentIds = studentGroupEnrollments.map(sge => sge.studentId);
var sgeIds = studentGroupEnrollments.map(sge => sge._id);

print("Removing", sgeIds.length, "student group enrollments");
db.StudentGroupEnrollments.deleteMany({ _id: { $in: sgeIds } });

var studentIdsWithoutEnrollments = db.Students.aggregate([
  {
    $match: {
      schoolYear: 2024,
      orgid,
      _id: {
        $in: sgeStudentIds
      }
    }
  },
  {
    $lookup: {
      from: "StudentGroupEnrollments",
      let: { studentId: "$_id" },
      pipeline: [
        {
          $match: {
            $expr: {
              $eq: ["$studentId", "$$studentId"]
            }
          }
        }
      ],
      as: "enrollments"
    }
  },
  {
    $match: {
      enrollments: { $eq: [] }
    }
  },
  {
    $project: {
      _id: 1
    }
  }
])
  .toArray()
  .map(s => s._id);

print("Removing", studentIdsWithoutEnrollments.length, "students without enrollments for removed sites");
db.Students.deleteMany({ orgid, schoolYear, _id: { $in: studentIdsWithoutEnrollments } });

print("Removing students by skill for removed sites");
db.StudentsBySkill.deleteMany({ siteId: { $in: siteIds } });

var rosterImportItems = db.RosterImportItems.find({
  rosterImportId: { $in: rosterImportIds },
  orgid,
  "data.schoolID": { $in: schoolNumbers }
}).toArray();
var rIds = rosterImportItems.map(r => r._id);

print("Removing", rIds.length, "roster import items");
db.RosterImportItems.deleteMany({ _id: { $in: rIds } });

print("Removing site access for removed sites");
db.users.updateMany(
  { "profile.orgid": orgid, "profile.siteAccess.siteId": { $in: siteIds } },
  { $pull: { "profile.siteAccess": { siteId: { $in: siteIds } } } }
);

var studentGroups = db.StudentGroups.find({ orgid, schoolYear, siteId: { $in: siteIds } }).toArray();
var sgIds = studentGroups.map(sg => sg._id);

print("Removing", sgIds.length, "student groups");
db.StudentGroups.deleteMany({ _id: { $in: sgIds } });

var assessmentResults = db.AssessmentResults.find({ studentGroupId: { $in: sgIds }, orgid, schoolYear }).toArray();
var arIds = assessmentResults.map(a => a._id);

print("Removing", arIds.length, "assessment results");
db.AssessmentResults.deleteMany({ _id: { $in: arIds } });

var benchmarkWindows = db.BenchmarkWindows.find({ schoolYear, orgid, siteId: { $in: siteIds } }).toArray();
var bIds = benchmarkWindows.map(b => b._id);

print("Removing", bIds.length, "benchmark windows");
db.BenchmarkWindows.deleteMany({ _id: { $in: bIds } });
