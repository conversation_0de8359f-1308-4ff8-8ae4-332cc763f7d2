// https://jira.ties.k12.mn.us/browse/SPRIN-1356

(function() {
  var affectedGrade = "K";
  var currentSkillAssessmentIdToFindGroupsBy = "rpsroeZfWnDiTrCHp";
  var previousAssessmentIdThatTargetsWereRaised = "D23Q9jdshAnEWWcnr";
  var isPreview = true;

  var possibleGrades = ["K", "01", "02", "03", "04", "05", "06", "07", "08", "HS"];
  if (!possibleGrades.includes(affectedGrade)) {
    print("Wrong grade provided:", affectedGrade, "use one of: ");
    printjson(possibleGrades);
  }
  var currentSkillAssessmentToVerify = db.Assessments.findOne({
    _id: currentSkillAssessmentIdToFindGroupsBy
  });
  if (!currentSkillAssessmentToVerify) {
    print("Error, wrong value for currentSkillAssessmentToVerify");
    return;
  }

  var previousAssessmentThatTargetsWereRaised = db.Assessments.findOne({
    _id: previousAssessmentIdThatTargetsWereRaised
  });
  if (!previousAssessmentThatTargetsWereRaised) {
    print("Error, wrong value for previousAssessmentThatTargetsWereRaised");
    return;
  }

  var previousAssessmentName = previousAssessmentThatTargetsWereRaised.name;
  print(
    "Finding the updated targets for previous skill:",
    previousAssessmentName,
    "for grade:",
    affectedGrade,
    "...\n"
  );
  var assessmentTargets = previousAssessmentThatTargetsWereRaised.strands[0].scores[0].targets;
  var currentTargets = getTargetsToSetForGrade(affectedGrade);
  if (!currentTargets || !currentTargets.length) {
    print("Error finding default targets");
    return;
  }
  var instructionalTarget = currentTargets[0];
  var masteryTarget = currentTargets[1];
  print("Found targets. \nMastery:", masteryTarget, "\nInstructional:", instructionalTarget);

  print("\nSearching for groups with current skill set to:", currentSkillAssessmentToVerify.name, "...\n");
  var possiblyAffectedGroups = db.StudentGroups.find(
    {
      "currentClasswideSkill.assessmentId": currentSkillAssessmentIdToFindGroupsBy,
      schoolYear: 2019,
      isActive: true,
      grade: affectedGrade
    },
    {}
  ).toArray();
  print("Found", possiblyAffectedGroups.length, "possibly affected groups. \n");

  print("Filtering groups that have passed", previousAssessmentName, "scoring below", masteryTarget);
  var groupsThatNeedToBeBroughtBack = [];
  possiblyAffectedGroups.forEach(function(studentGroup) {
    var lastPassingHistoryItem = studentGroup.history.find(function(historyItem) {
      return historyItem.type === "classwide" && historyItem.assessmentId === previousAssessmentIdThatTargetsWereRaised;
    });

    if (!lastPassingHistoryItem) {
      print("Error finding history item for: ", previousAssessmentName, "in Student Group:", studentGroup.name);
      return;
    }
    var medianScore = lastPassingHistoryItem.assessmentResultMeasures[0].medianScore;
    var cutoffTarget = lastPassingHistoryItem.assessmentResultMeasures[0].cutoffTarget;
    if (cutoffTarget === masteryTarget) {
      print("No action needed for:", studentGroup.name);
      return;
    }

    if (medianScore < masteryTarget) {
      groupsThatNeedToBeBroughtBack.push(studentGroup);
    }
  });

  print("\nFound", groupsThatNeedToBeBroughtBack.length, "groups that need to be brought back to the previous skill");
  printGroupsWithAdditionalData(groupsThatNeedToBeBroughtBack);

  groupsThatNeedToBeBroughtBack.forEach(function(studentGroup) {
    print("\n\n******************", studentGroup.name, "**************************");
    var studentGroupId = studentGroup._id;

    var lastPassingClasswideIntervention = db.AssessmentResults.findOne({
      studentGroupId: studentGroupId,
      type: "classwide",
      assessmentIds: previousAssessmentIdThatTargetsWereRaised,
      status: "COMPLETED",
      "ruleResults.passed": true
    });
    if (!lastPassingClasswideIntervention) {
      return print(
        "No completed and passed classwide intervention for:",
        previousAssessmentName,
        "was found for the student group:",
        studentGroup.name
      );
    }

    var moreRecentClasswideInterventions = db.AssessmentResults.find({
      studentGroupId: studentGroupId,
      type: "classwide",
      "created.on": { $gt: lastPassingClasswideIntervention.created.on }
    }).toArray();
    print("Number of classwide interventions to be removed: " + moreRecentClasswideInterventions.length);

    var newTargets = currentTargets.map(function(target) {
      return NumberInt(target);
    });
    var currentClasswideSkill = {
      assessmentId: lastPassingClasswideIntervention.assessmentIds[0],
      assessmentName: lastPassingClasswideIntervention.measures[0].assessmentName,
      interventions: [],
      targets: newTargets,
      whenStarted: lastPassingClasswideIntervention.created,
      assessmentResultId: lastPassingClasswideIntervention._id,
      benchmarkPeriodId: lastPassingClasswideIntervention.benchmarkPeriodId,
      message: {
        additionalStudentsAddedToInterventionQueue: false,
        messageCode: "1",
        dismissed: false
      }
    };

    var moreRecentClasswideInterventionIds = moreRecentClasswideInterventions.map(function(intervention) {
      return intervention._id;
    });
    var idsOfClasswideInterventionToRemoveFromHistory = moreRecentClasswideInterventionIds.slice(0); //COPYING
    idsOfClasswideInterventionToRemoveFromHistory.push(lastPassingClasswideIntervention._id);

    var now = new Date();
    var lastModified = {
      by: "set_specified_classwide_intervention_as_active",
      on: now.valueOf(),
      date: now
    };

    if (!isPreview) {
      var studentGroupUpdate1 = db.StudentGroups.update(
        { _id: studentGroupId },
        {
          $pull: {
            currentAssessmentResultIds: { $in: moreRecentClasswideInterventionIds },
            history: { assessmentResultId: { $in: idsOfClasswideInterventionToRemoveFromHistory } }
          }
        }
      );
      print(
        "\nRemoving classwide interventions from history, currentClasswideSkill and currentAssessmentResultIds... ",
        studentGroupUpdate1.nModified ? "SUCCESS" : "FAILED"
      );

      var studentGroupUpdate2 = db.StudentGroups.update(
        { _id: studentGroupId },
        {
          $set: { currentClasswideSkill: currentClasswideSkill },
          $addToSet: { currentAssessmentResultIds: lastPassingClasswideIntervention._id }
        }
      );
      print(
        "\nSetting new currentClasswideSkill and currentAssessmentResultIds",
        studentGroupUpdate2.nModified ? "SUCCESS" : "FAILED"
      );

      var assessmentsUpdateResult = db.AssessmentResults.update(
        { _id: lastPassingClasswideIntervention._id },
        {
          $set: { lastModified: lastModified, status: "OPEN" },
          $unset: { classwideResults: "", measures: "", nextAssessmentResultId: "", ruleResults: "" }
        }
      );
      print(
        "\nBringing back the previous skill's last Assessment Result to active",
        assessmentsUpdateResult.nModified ? "SUCCESS" : "FAILED"
      );

      var removeResult = db.AssessmentResults.remove({ _id: { $in: moreRecentClasswideInterventionIds } });
      print(
        "\nRemoving the unneded classwide interventions from Assessment Results",
        removeResult.nRemoved ? "SUCCESS" : "FAILED"
      );

      print(
        "Classwide intervention for: ",
        previousAssessmentName,
        " for the student group",
        studentGroup.name,
        "has been set back to ACTIVE"
      );
    }
  });

  /******************************************** HELPERS ***************************************************/
  function getTargetsToSetForGrade(affectedGrade) {
    var allGradeTargets = assessmentTargets.filter(function(target) {
      return target.grade === affectedGrade;
    });
    if (!allGradeTargets.length) {
      print("Error! No targets found for grade. Exiting...");
      return;
    }
    var defaultTargets = allGradeTargets.filter(function(target) {
      return !target.assessmentType;
    })[0];
    var fallTargets = defaultTargets.periods.find(function(period) {
      return period.name === "Fall";
    });
    return fallTargets.values;
  }

  function printGroupsWithAdditionalData(studentGroups) {
    var sitesIdsAffected = studentGroups.map(function(sg) {
      return sg.siteId;
    });

    var sitesData = db.Sites.find({ _id: { $in: sitesIdsAffected } }, { name: 1, orgid: 1 }).toArray();

    var orgidsAffected = sitesData.map(function(s) {
      return s.orgid;
    });
    var orgsData = db.Organizations.find({ _id: { $in: orgidsAffected } }, { name: 1 }).toArray();
    var groupsWithExtraData = studentGroups.map(function(sg) {
      var siteName = sitesData.find(function(s) {
        return s._id === sg.siteId;
      }).name;
      var organizationName = orgsData.find(function(org) {
        return org._id === sg.orgid;
      }).name;
      return {
        groupId: sg._id,
        groupName: sg.name,
        siteName: siteName,
        siteId: sg.siteId,
        organizationName: organizationName,
        orgid: sg.orgid
      };
    });
    printjson(groupsWithExtraData);
  }
})();
