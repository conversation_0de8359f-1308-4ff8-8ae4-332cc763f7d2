// THE PURPOSE OF THIS SCRIPT IS TO CLEAR HISTORY AND CURRENT CLA<PERSON>WIDE SKILL OF PROVIDED studentGroupId
// THE SCRIPT (WHEN shouldClearStudents IS SET TO true) ALSO CLEARS THE HISTORY AND CURRENT SKILL OF STUDENTS CURRENTLY ENROLLED IN PROVIDED studentGroupId
// THE SCRIPT OUTPUTS REMOVED ASSESSMENT RESULT IDS IN CASE WE WANT TO ACCESS / BRING THEM BACK FROM BACKUPS IN FUTURE
(function() {
  var studentGroupId = "";
  var shouldClearStudents = false;

  if (!studentGroupId) {
    print("Invalid value provided for studentGroupId");
    return;
  }

  //CHECKING STUDENT GROUP
  var studentGroup = db.StudentGroups.findOne({ _id: studentGroupId });
  if (!studentGroup) {
    print("Student Group wasn't found, check provided studentGroupId");
    return;
  }

  //CLEARING STUDENTS HISTORY
  if (shouldClearStudents) {
    var studentEnrollmentsData = db.StudentGroupEnrollments.find({
      isActive: true,
      studentGroupId: studentGroupId
    }).map(function(enrollment) {
      return { enrollmentId: enrollment._id, studentId: enrollment.studentId };
    });

    //CHECKING STUDENTS
    var studentIdsForHistoryCleanup = studentEnrollmentsData.map(function(datum) {
      return datum.studentId;
    });

    var studentUpdateRes = db.Students.update(
      { _id: { $in: studentIdsForHistoryCleanup } },
      { $unset: { currentSkill: 1 }, $set: { history: [] } },
      { multi: true }
    ); //remove history and currentSkill
    print("Cleared current skill and history of " + studentUpdateRes.nModified + " currently enrolled students\n");

    var studentsARs = db.AssessmentResults.find(
      { studentId: { $in: studentIdsForHistoryCleanup } },
      { _id: 1, studentId: 1 }
    ).toArray();
    print("Individual ARs to remove:");
    printjson(studentsARs);
    var arIds = studentsARs.map(function(ar) {
      return ar._id;
    });

    var individualArsRemoveRes = db.AssessmentResults.remove({ _id: { $in: arIds } });
    print("Removed: ", individualArsRemoveRes.nRemoved, "individual ARs");

    var sgPullIndividualRes = db.StudentGroups.update(
      { _id: studentGroupId },
      { $pull: { currentAssessmentResultIds: { $in: arIds } } }
    );
    print(
      "Individual assessments were pulled from Student Group currentAssessmentResultIds: ",
      !!sgPullIndividualRes.nModified
    );
  }
  // STUDENT GROUP CLEANUP
  var groupCleanupRes = db.StudentGroups.update(
    { _id: studentGroupId },
    { $unset: { currentClasswideSkill: 1 }, $set: { history: [] } }
  );
  print("\nGroup history and skill cleanup successful: ", !!groupCleanupRes.nModified);
  // ASSESSMENT RESULTS
  var assessmentResultIdsToRemove = db.AssessmentResults.find(
    {
      studentGroupId: studentGroupId,
      type: { $in: ["benchmark", "classwide"] }
    },
    { _id: 1 }
  ).map(function(ar) {
    return ar._id;
  }); //REMOVE

  print("Found:", assessmentResultIdsToRemove.length, "ARs connected with StudentGroup (classwide or benchmark)");
  printjson(assessmentResultIdsToRemove);

  var sgArsCleanupRes = db.AssessmentResults.remove({ _id: { $in: assessmentResultIdsToRemove } });
  print("Removed: ", sgArsCleanupRes.nRemoved, "classwide ARs");

  var sgPullClasswideRes = db.StudentGroups.update(
    { _id: studentGroupId },
    { $pull: { currentAssessmentResultIds: { $in: assessmentResultIdsToRemove } } }
  );
  print(
    "Classwide assessments were pulled from Student Group currentAssessmentResultIds: ",
    !!sgPullClasswideRes.nModified
  );
})();
