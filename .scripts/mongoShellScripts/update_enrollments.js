var students = db.Students.find({
  orgid: "dJF9noggGuz6yNaw7",
  "identity.identification.stateId": {
    $in: [
      "1760070726",
      "1765415039",
      "1954725841",
      "2482848873",
      "2497282714",
      "2844043437",
      "3486250221",
      "3887551575",
      "5057132849",
      "5845611202",
      "7123453409",
      "7620901442",
      "7794846927",
      "8921764259",
      "9528985238",
      "9557716118",
      "9779133674",
      "9879698665"
    ]
  }
})
  .toArray()
  .map(function(s) {
    return s._id;
  });

var enrollments = db.StudentGroupEnrollments.find(
  { studentId: { $in: students }, isActive: true },
  { studentGroupId: 1, studentId: 1 }
).toArray();
printjson(enrollments);
enrollments.forEach(function(enrollment) {
  if (enrollment.studentGroupId === "QsB7kLBrErWmixSfC") {
    print("Updating...");
    db.StudentGroupEnrollments.update({ _id: enrollment._id }, { $set: { studentGroupId: "2a44ded2c1c44629a" } });
  }
});
