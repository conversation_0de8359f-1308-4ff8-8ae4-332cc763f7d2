var organizations = []; //ORGANIZATION IDS HERE

var page = 7;
var limit = 10;
var slicedOrganizations = organizations.slice(page * limit, page * limit + limit);
slicedOrganizations.forEach(function(organizationId) {
  print("orgid", organizationId, "\n\n");
  var activeEnrollments = db.StudentGroupEnrollments.find(
    { orgid: organizationId, schoolYear: 2018, isActive: true },
    { studentId: 1, grade: 1 }
  ).toArray();
  activeEnrollments.forEach(function(enrollment) {
    var sameGradeStudent = db.Students.findOne({ _id: enrollment.studentId, grade: enrollment.grade });
    if (!sameGradeStudent) {
      print("NotFound!!!");
      printjson(enrollment);
    }
  });
});
