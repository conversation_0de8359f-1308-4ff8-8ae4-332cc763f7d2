var thorpId = "QRg3dbak45J3HA48L";
var dansvilleId = "9FAqWsTwjDYegzDuM";
var forestHillsId = "iBXNr5aFz55mzz859";
var authorizedOrganizations = [dansvilleId, thorpId, forestHillsId];
var sgs = db.StudentGroups.find(
  {
    isActive: true,
    schoolYear: 2019,
    grade: { $in: ["HS"] },
    orgid: { $nin: authorizedOrganizations }
  },
  { name: 1, orgid: 1, siteId: 1, grade: 1 }
).toArray();
var orgIds = sgs.map(function(sg) {
  return sg.orgid;
});
var orgs = db.Organizations.find({ _id: { $in: orgIds } }, { name: 1 }).toArray();
var siteIds = sgs.map(function(sg) {
  return sg.siteId;
});
var sites = db.Sites.find({ _id: { $in: siteIds } }, { name: 1 }).toArray();

sgs.forEach(function(sg) {
  var numberOfActiveEnrollments = db.StudentGroupEnrollments.find({ studentGroupId: sg._id, isActive: true }).count();
  var org = orgs.find(function(org) {
    return org._id === sg.orgid;
  });
  var site = sites.find(function(site) {
    return site._id === sg.siteId;
  });
  var printData = {
    Organization: org.name,
    orgId: org._id,
    Site: site.name,
    siteId: site._id,
    "Group Name": sg.name,
    groupId: sg._id,
    Grade: sg.grade,
    "Actively Enrolled Students": numberOfActiveEnrollments
  };
  printjson(printData);
});
