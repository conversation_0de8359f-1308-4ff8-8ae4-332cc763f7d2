(function() {
  var orgid = ""; //to be provided e.g. 'test_organization_id'
  var siteId = ""; //to be provided e.g. 'test_site_id'
  var schoolYear = 2018; //to be provided e.g. 2018

  if (!orgid || !siteId || !schoolYear || !Number.isInteger(schoolYear)) {
    print("Invalid value provided for orgid, siteId or schoolYear");
    return;
  }
  //CHECKING SITE
  var site = db.Sites.findOne({ _id: siteId });
  if (site) {
    print("Site: " + site.name + " found\n");
  } else {
    print("Site not found\n Aborting...");
    return;
  }

  //CHECKING STUDENT GROUPS
  var groupIdsToRemove = db.StudentGroups.find({ siteId: siteId, schoolYear: schoolYear }).map(function(group) {
    return group._id;
  });

  if (groupIdsToRemove.length > 0) {
    print("Found " + groupIdsToRemove.length + " student groups to remove");
    var removedGroups = db.StudentGroups.remove({ _id: { $in: groupIdsToRemove } });
    print("Removed " + removedGroups.nRemoved + " student groups\n");
  } else {
    print("No groups related to provided siteId were found\n Aborting...");
    return;
  }

  //CHECKING STUDENT GROUP ENROLLMENTS
  var studentEnrollmentsData = db.StudentGroupEnrollments.find({
    isActive: true,
    studentGroupId: { $in: groupIdsToRemove }
  }).map(function(enrollment) {
    return { enrollmentId: enrollment._id, studentId: enrollment.studentId };
  });
  var studentEnrollmentsIdsToRemove = studentEnrollmentsData.map(function(datum) {
    return datum.enrollmentId;
  });

  if (studentEnrollmentsIdsToRemove.length > 0) {
    print("Found " + studentEnrollmentsIdsToRemove.length + " student enrollments to remove");
    var removedEnrollments = db.StudentGroupEnrollments.remove({
      $or: [
        {
          isActive: true,
          studentGroupId: { $in: groupIdsToRemove }
        },
        { siteId: siteId, schoolYear: schoolYear }
      ]
    });
    print("Removed " + removedEnrollments.nRemoved + " student enrollments\n");
  } else {
    print("No student enrollments related to provided siteId were found...\n");
  }

  //CHECKING STUDENTS
  var studentIdsToRemove = studentEnrollmentsData.map(function(datum) {
    return datum.studentId;
  });
  var students = db.Students.remove({ _id: { $in: studentIdsToRemove } }); //REMOVE
  print("Removed " + students.nRemoved + " students\n");

  // ASSESSMENT RESULTS
  var assessmentResult = db.AssessmentResults.remove({
    studentGroupId: { $in: groupIdsToRemove },
    schoolYear: schoolYear
  }); //REMOVE
  print("Removed " + assessmentResult.nRemoved + " AssessmentResults documents");

  // FUTURE USERS
  var futureUsers = db.FutureUsers.remove({ siteId: siteId, schoolYear: schoolYear });
  print("Removed " + futureUsers.nRemoved + " FutureUsers documents");

  //USERS
  var usersModified = db.users.update(
    {
      "profile.orgid": orgid,
      "profile.siteAccess": { $elemMatch: { role: "arbitraryIdteacher", siteId: siteId, schoolYear: schoolYear } }
    },
    { $pull: { "profile.siteAccess": { role: "arbitraryIdteacher", siteId: siteId, schoolYear: schoolYear } } },
    { multi: true }
  );
  print(
    "Removed access for " +
      usersModified.nModified +
      " user(s) for " +
      siteId +
      " siteId for " +
      schoolYear +
      " schoolYear"
  );
  print("\nDone!");
})();
