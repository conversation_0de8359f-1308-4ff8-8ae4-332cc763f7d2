// This script removes screening results based on provided benchmark period id
// If there were classwide or individual interventions created based on that screening result they will be removed
// The script won't remove classwide or individual interventions that were created based on screening scores from other than provided benchmark period id
// The script won't remove any other screening scores that were completed in other than provided benchmark period id

(function() {
  var orgid = ""; // to be provided e.g. 'test_organization_id'
  var studentGroupId = ""; // to be provided e.g. 'test_site_id'
  var schoolYear = 2018; // to be provided e.g. 2018
  var benchmarkPeriodId = "nEsbWokBWutTZFkTh"; // id of benchmarkPeriod

  if (!orgid || !studentGroupId || !schoolYear || !Number.isInteger(schoolYear) || !benchmarkPeriodId) {
    print("Invalid value provided for orgid, siteId, benchmarkPeriodId or schoolYear");
    return;
  }

  var studentGroup = db.StudentGroups.findOne({ _id: studentGroupId });
  if (!studentGroup || !studentGroup.history || studentGroup.history.length == 0) {
    print("Error! Provided studentGroup does not exist or does not have any score history");
    return;
  }

  var now = new Date();
  var lastModified = {
    by: "remove_screening_results_along_with_classwide_and_individual_int_script",
    on: now.valueOf(),
    date: now
  };

  if (studentGroup.currentAssessmentResultIds && studentGroup.currentAssessmentResultIds.length > 0) {
    // CASE FOR INDIVIDUAL INTERVENTIONS STARTED - Remove Assessment Results for ind. intervention and history of individual interventions in students history
    removeIndividualInterventionAssessmentsForCurrentBenchmarkPeriod();

    // CASE FOR CLASSWIDE INTERVENTIONS STARTED - Remove Assessment Results for classwide intervention and group history related to classwide interventions
    if (studentGroup.currentClasswideSkill && studentGroup.currentClasswideSkill.assessmentResultId) {
      removeClasswideInterventionsFromGroupAndAssessments();
    }
  }

  // Remove screening results
  removeScreeningResultFromGroupAndAssessments();

  print("\nDone!");

  function removeIndividualInterventionAssessmentsForCurrentBenchmarkPeriod() {
    var studentsIdsInIntervention = [];
    var currentIndividualAssessmentsToRemove = [];
    print("Currently active assessments: " + studentGroup.currentAssessmentResultIds);
    db.AssessmentResults.find({ _id: { $in: studentGroup.currentAssessmentResultIds } }).forEach(function(assessment) {
      if (
        assessment.type == "individual" &&
        assessment.benchmarkPeriodId == benchmarkPeriodId &&
        assessment.schoolYear == schoolYear
      ) {
        studentsIdsInIntervention.push(assessment.studentId);
        currentIndividualAssessmentsToRemove.push(assessment._id);
      }
    });
    print("Students with individual intervention active: " + studentsIdsInIntervention);
    print("Current individual AssessmentResults to remove: " + currentIndividualAssessmentsToRemove);

    if (currentIndividualAssessmentsToRemove.length === 0) {
      print("No individual assessments to remove found... ");
      return;
    }

    // Update studentGroup currentAssessmentResultIds to get rid of individual intervention assessments
    var updatedGroup = db.StudentGroups.update(
      { _id: studentGroupId },
      {
        $set: { lastModified: lastModified },
        $pullAll: { currentAssessmentResultIds: currentIndividualAssessmentsToRemove }
      }
    );
    print("CurrentAssessmentResultIds updated: " + (updatedGroup.nModified > 0 ? "YES!" : "NO!"));

    // Find all assessments results of students in active individual intervention and remove them
    var idsOfIndividualAssessmentsToRemove = db.AssessmentResults.find({
      studentId: { $in: studentsIdsInIntervention },
      benchmarkPeriodId: benchmarkPeriodId,
      type: "individual",
      schoolYear: schoolYear
    }).map(function(ass) {
      return ass._id;
    });

    print("Ids of individual assessments to remove: " + idsOfIndividualAssessmentsToRemove);
    var removedIndividualAssessments = db.AssessmentResults.remove({
      _id: { $in: idsOfIndividualAssessmentsToRemove }
    });
    print("Removed: " + removedIndividualAssessments.nRemoved + " individual assessments documents.");

    // Remove students' individual intervention entries from their history
    var updatedStudents = db.Students.update(
      { _id: { $in: studentsIdsInIntervention } },
      {
        $set: { lastModified: lastModified },
        $pull: { history: { assessmentResultId: { $in: idsOfIndividualAssessmentsToRemove } } }
      },
      { multi: true }
    );
    print("Removed " + updatedStudents.nModified + " items from students' history records");

    // Removing currentSkill data if it points to individual intervention in the provided benchmark period
    var removedCurrentSkills = 0;
    db.Students.find({ _id: { $in: studentsIdsInIntervention } }).forEach(function(student) {
      if (
        student.currentSkill &&
        idsOfIndividualAssessmentsToRemove.indexOf(student.currentSkill.assessmentResultId) > -1
      ) {
        var unsetCurrentSkill = db.Students.update(
          { _id: student._id },
          { $set: { lastModified: lastModified }, $unset: { currentSkill: 1 } }
        );
        removedCurrentSkills += unsetCurrentSkill.nModified;
      }
    });
    print("Removed current skills from: " + removedCurrentSkills + " students");
  }

  function removeClasswideInterventionsFromGroupAndAssessments() {
    var idsOfCurrentClasswideAssessmentsToRemove = db.AssessmentResults.find({
      studentGroupId: studentGroupId,
      type: "classwide",
      benchmarkPeriodId: benchmarkPeriodId,
      schoolYear: schoolYear
    }).map(function(ass) {
      return ass._id;
    });
    print("Current classwide interventions to remove: ", idsOfCurrentClasswideAssessmentsToRemove);
    db.AssessmentResults.remove({ _id: { $in: idsOfCurrentClasswideAssessmentsToRemove } });
    var shouldUnsetCurrentClasswideSkill = studentGroup.currentClasswideSkill.benchmarkPeriodId === benchmarkPeriodId;
    if (shouldUnsetCurrentClasswideSkill) {
      var unsetCurrentClasswideSkill = db.StudentGroups.update(
        { _id: studentGroupId },
        {
          $set: { lastModified: lastModified },
          $unset: { currentClasswideSkill: 1 }
        }
      );
      print(unsetCurrentClasswideSkill.nModified > 0 ? "Current classwide skill removed" : "");
    } else {
      print("Current classwide skill was NOT removed as it was created based on other benchmark scores");
    }

    var pullClasswideInterventionsOut = db.StudentGroups.update(
      { _id: studentGroupId },
      {
        $set: { lastModified: lastModified },
        $pullAll: { currentAssessmentResultIds: idsOfCurrentClasswideAssessmentsToRemove }
      }
    );
    print("CurrentAssessmentResultIds updated: " + (pullClasswideInterventionsOut.nModified > 0 ? "YES!" : "NO!"));

    var updatedHistoryItems = db.StudentGroups.update(
      { _id: studentGroupId },
      {
        $set: { lastModified: lastModified },
        $pull: { history: { assessmentResultId: { $in: idsOfCurrentClasswideAssessmentsToRemove } } }
      },
      { multi: true }
    );
    print(
      "Removed " + updatedHistoryItems.nModified + " items related to classwide interventions from group's history"
    );
  }

  function removeScreeningResultFromGroupAndAssessments() {
    var screeningToRemove = db.AssessmentResults.findOne({
      schoolYear: schoolYear,
      studentGroupId: studentGroupId,
      type: "benchmark",
      status: "COMPLETED",
      benchmarkPeriodId: benchmarkPeriodId
    });
    if (!screeningToRemove) {
      print("No screening results for provided benchmarkPeriodId found. Aborting...");
      return;
    }
    db.StudentGroups.update(
      { _id: studentGroupId },
      {
        $set: { lastModified: lastModified },
        $pull: { history: { assessmentResultId: screeningToRemove._id } }
      },
      { multi: true }
    );
    var removed = db.AssessmentResults.remove({ _id: screeningToRemove._id });
    print(removed.nRemoved > 0 ? "Benchmark screening assessment result removed" : "No benchmark document removed");
  }
})();
