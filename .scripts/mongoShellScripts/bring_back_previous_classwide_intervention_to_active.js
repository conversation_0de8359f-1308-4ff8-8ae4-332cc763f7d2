(function () {
  var studentGroupId = "TEST_2018_Wo4hoZGSehns"; //TEST GROUP ID
  var schoolYear = 2018;
  var benchmarkPeriodId = ''; // leave empty to use the benchmark period id from the current classwide skill

  var studentGroup = db.StudentGroups.findOne({ _id: studentGroupId });
  if (!studentGroup) return print('No student group with a classwide intervention and ' + studentGroupId + ' id');
  benchmarkPeriodId = benchmarkPeriodId || studentGroup.currentClasswideSkill.benchmarkPeriodId;

  var lastClasswideAssessmentResult = db.AssessmentResults.findOne({
    studentGroupId: studentGroupId, status: 'OPEN', type: 'classwide', benchmarkPeriodId: benchmarkPeriodId, schoolYear: schoolYear,
  });
  if (!lastClasswideAssessmentResult) return print('No classwide intervention assessment result found for the student group with ' + studentGroupId + ' id in the ' + benchmarkPeriodId + ' benchmark period and the ' + schoolYear + ' school year');

  var previousAssessmentResult = db.AssessmentResults.findOne({
    _id: lastClasswideAssessmentResult.previousAssessmentResultId
  });

  if(!previousAssessmentResult || previousAssessmentResult.type !== 'classwide') return print('The group doesn\'t have a previous classwide assessment result');

  var now = new Date();
  var lastModified = {
    by: 'bring_back_previous_classwide_intervention_to_active_script',
    on: now.valueOf(),
    date: now,
  };
  var newTargets = previousAssessmentResult.measures[0].targetScores.map(function(s) {return NumberInt(s)});

  var currentClasswideSkill = {
  		assessmentId : previousAssessmentResult.assessmentIds[0],
        assessmentName : previousAssessmentResult.measures[0].assessmentName,
        interventions : [],
        targets : newTargets,
        whenStarted : previousAssessmentResult.created,
        assessmentResultId : previousAssessmentResult._id,
        benchmarkPeriodId : previousAssessmentResult.benchmarkPeriodId,
        message : {
            additionalStudentsAddedToInterventionQueue : false,
            messageCode : "1",
            dismissed : false
        }
  }

  var studentGroupUpdate1 = db.StudentGroups.update({_id: studentGroupId}, {
  	$set: {currentClasswideSkill: currentClasswideSkill},
  	$pull: {currentAssessmentResultIds: lastClasswideAssessmentResult._id, history: { assessmentResultId: previousAssessmentResult._id } }
  })
  printjson(studentGroupUpdate1)

  var studentGroupUpdate2 = db.StudentGroups.update({_id: studentGroupId}, {
  	$addToSet: {currentAssessmentResultIds: previousAssessmentResult._id}
  })
  printjson(studentGroupUpdate2)

  var assessmentsUpdateResult = db.AssessmentResults.update({ _id: previousAssessmentResult._id }, {
    $set: { lastModified: lastModified, status: 'OPEN' },
    $unset: { classwideResults: '', measures: '', nextAssessmentResultId: '', ruleResults: '' },
  });
  printjson(assessmentsUpdateResult);

  var removeResult = db.AssessmentResults.remove({ _id: lastClasswideAssessmentResult._id});
  printjson(removeResult);

  print('Last classwide intervention scores for student group with ' + studentGroupId + ' id have been removed');
})();
