var activeEnrollments = db.StudentGroupEnrollments.find({
  studentGroupId: { $in: [] }, //TO BE PROVIDED
  isActive: true
}).toArray();
activeEnrollments.forEach(function(enrollment) {
  var previousEnrollment = db.StudentGroupEnrollments.findOne({
    rosterImportId: "EgJeA5XszyedXHZpc",
    studentGroupId: enrollment.studentGroupId,
    isActive: false,
    studentId: enrollment.studentId
  });
  if (previousEnrollment) {
    db.StudentGroupEnrollments.updateOne(
      { _id: previousEnrollment._id },
      { $set: { isActive: true, lastModified: previousEnrollment.created } }
    );
    db.StudentGroupEnrollments.deleteOne({ _id: enrollment._id });
  } else {
    print("NO");
  }
});
