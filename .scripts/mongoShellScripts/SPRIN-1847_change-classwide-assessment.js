var assessment = db.Assessments.findOne({ name: "2-Digit Multiply by 2-Digit with Regrouping" });
var assessmentGrowth = db.AssessmentGrowth.findOne({ grade: "04" });

var oldWinterToSpringClasswideAssessmentId = assessmentGrowth.winterToSpring[0].classwide;
var newWinterToSpringClasswideAssessmentId = assessment._id;

if (oldWinterToSpringClasswideAssessmentId !== newWinterToSpringClasswideAssessmentId) {
  db.Rules.update({ grade: "04" }, { $pull: { skills: { assessmentId: oldWinterToSpringClasswideAssessmentId } } });
  db.AssessmentGrowth.update(
    { grade: "04" },
    { $set: { "winterToSpring.0.classwide": newWinterToSpringClasswideAssessmentId } }
  );

  print(
    "Winter to Spring classwide assessment with id: " +
      oldWinterToSpringClasswideAssessmentId +
      " was replaced by assessment with id: " +
      newWinterToSpringClasswideAssessmentId
  );
} else {
  print(
    "Winter to Spring classwide assessment is already set to assessment with id: " +
      newWinterToSpringClasswideAssessmentId
  );
}
