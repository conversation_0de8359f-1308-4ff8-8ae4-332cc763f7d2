function mergeSites(sourceId, targetId, _orgid) {
  const sourceSiteToDelete = "" || sourceId; // TODO to be provided
  const destinationSiteToKeep = "" || targetId; // TODO to be provided
  const orgid = "" || _orgid; // TODO to be provided
  print("\n*******************************");
  const org = db.Organizations.findOne({ _id: orgid }, { name: 1 });
  print(`ORG: ${org.name} - ${orgid}`);

  // get source school student groups
  const studentGroupsToMove = db.StudentGroups.find(
    { orgid, siteId: sourceSiteToDelete },
    { _id: 1, name: 1, sectionId: 1, grade: 1, siteId: 1 }
  ).toArray();
  if (!studentGroupsToMove.length) {
    print("NO STUDENT GROUPS TO MOVE, ERROR!");
    return;
  }
  print("Student Groups that get moved:");
  printjson(studentGroupsToMove);
  const studentGroupIdsToMove = studentGroupsToMove.map(sg => sg._id);

  // verify source site
  const sourceSchool = db.Sites.findOne({ orgid, _id: sourceSiteToDelete }, { _id: 1, name: 1, stateInformation: 1 });
  if (!sourceSchool) {
    print("SOURCE SCHOOL NOT AVAILABLE OR NOT IN ORG , ERROR!");
    return;
  }
  print("Source school:");
  printjson(sourceSchool);

  // verify destination site
  const destSchool = db.Sites.findOne({ orgid, _id: destinationSiteToKeep }, { _id: 1, name: 1, stateInformation: 1 });
  if (!destSchool) {
    print("DESTINATION SCHOOL NOT AVAILABLE OR NOT IN ORG , ERROR!");
    return;
  }
  print("Destination school:");
  printjson(destSchool);

  // ENROLLMENTS
  const studentEnrollmentsAffected = db.StudentGroupEnrollments.find({
    studentGroupId: { $in: studentGroupIdsToMove }
  }).toArray();
  const studentsToMoveFromEnrollments = studentEnrollmentsAffected.map(se => se.studentId);
  let studentsToMove = new Set(studentsToMoveFromEnrollments);
  studentsToMove = [...studentsToMove];

  const studentEnrollmentsAffectedRes = db.StudentGroupEnrollments.updateMany(
    { studentGroupId: { $in: studentGroupIdsToMove } },
    { $set: { siteId: destinationSiteToKeep } }
  );
  print("Changed: ", studentEnrollmentsAffectedRes.modifiedCount, "enrollments");

  const sgsUpdateRes = db.StudentGroups.updateMany(
    { _id: { $in: studentGroupIdsToMove } },
    { $set: { siteId: destinationSiteToKeep } }
  );
  print("Changed: ", sgsUpdateRes.modifiedCount, "groups");

  // USERS
  const usersAffected = db.users
    .find({ "profile.orgid": orgid, "profile.siteAccess.siteId": sourceSiteToDelete })
    .toArray();
  usersAffected.forEach(user => {
    let updatedSiteAccess = user.profile.siteAccess;
    updatedSiteAccess.forEach(sa => {
      if (sa.siteId === sourceSiteToDelete) {
        sa.siteId = destinationSiteToKeep;
      }
    });
    //remove any dups this may have created
    updatedSiteAccess = updatedSiteAccess.filter(
      (v, i, a) =>
        a.findIndex(
          t =>
            t.isActive === v.isActive &&
            t.isDefault === v.isDefault &&
            t.role === v.role &&
            t.schoolYear.toString() === v.schoolYear.toString() &&
            t.siteId === v.siteId
        ) === i
    );
    const updateOp = db.users.updateOne({ _id: user._id }, { $set: { "profile.siteAccess": updatedSiteAccess } });
    print("updating teacher with _id:", user._id, "success:", updateOp.modifiedCount ? "Yes!" : "No!");
  });

  //BENCHMARK WINDOWS
  const benchmarkWindowsToMove = db.BenchmarkWindows.find(
    { orgid, siteId: sourceSiteToDelete },
    { _id: 1, schoolYear: 1, benchmarkPeriodId: 1, siteId: 1 }
  ).toArray();
  if (!benchmarkWindowsToMove.length) {
    print("Notice: No benchmark windows to move");
  } else {
    benchmarkWindowsToMove.forEach(bmw => {
      const existingDestinationBenchmarkWindow = db.BenchmarkWindows.findOne(
        {
          orgid,
          schoolYear: bmw.schoolYear,
          benchmarkPeriodId: bmw.benchmarkPeriodId,
          siteId: destinationSiteToKeep
        },
        { _id: 1, schoolYear: 1, benchmarkPeriodId: 1, siteId: 1 }
      );
      if (!existingDestinationBenchmarkWindow) {
        const updateOp = db.BenchmarkWindows.updateOne({ _id: bmw._id }, { $set: { siteId: destinationSiteToKeep } });
        print("updating BenchmarkWindow with _id:", bmw._id, "success:", updateOp.modifiedCount ? "Yes!" : "No!");
      } else {
        print(
          `WARNING: Unable to move benchmark window ${bmw._id} due to conflict with ${existingDestinationBenchmarkWindow._id}`
        );
      }
    });
  }

  //ASSESSMENT RESULTS
  const assessmentResultsAffected = db.AssessmentResults.find({
    studentGroupId: { $in: studentGroupIdsToMove }
  }).toArray(); // need to change scores[].siteId
  assessmentResultsAffected.forEach((ar, index) => {
    print("Updating AssessmentResults:", index);
    ar.scores.forEach(score => {
      if (score.siteId === sourceSiteToDelete) {
        score.siteId = destinationSiteToKeep;
      }
    });
    db.AssessmentResults.updateOne({ _id: ar._id }, { $set: { scores: ar.scores } });
  });

  //STUDENTS
  const stuUpdateRes = db.Students.updateMany(
    { _id: { $in: studentsToMove }, orgid, districtNumber: sourceSchool.stateInformation.districtNumber },
    { $set: { districtNumber: destSchool.stateInformation.districtNumber } }
  );
  print("Changed: ", stuUpdateRes.modifiedCount, "students");

  //StudentsBySkill
  const existingStudentsBySkill = db.StudentsBySkill.find({
    siteId: sourceSiteToDelete
  }).toArray();

  existingStudentsBySkill.forEach(studentsBySkill => {
    db.StudentsBySkill.update(
      { siteId: destinationSiteToKeep, assessmentGroupId: studentsBySkill.assessmentGroupId },
      {
        $addToSet: {
          studentsBelowInstructionalTarget: {
            $each: studentsBySkill.studentsBelowInstructionalTarget
          },
          studentsBelowMasteryTarget: {
            $each: studentsBySkill.studentsBelowMasteryTarget
          },
          studentsWithoutSkillHistory: {
            $each: studentsBySkill.studentsWithoutSkillHistory
          }
        }
      },
      { upsert: true }
    );
  });

  const studentsBySkillIdsToRemove = existingStudentsBySkill.map(sbs => sbs._id);
  // get rid of unused StudentsBySkill
  db.StudentsBySkill.deleteMany({ _id: { $in: studentsBySkillIdsToRemove } });

  //SITE
  const sourceSchoolDeleted = db.Sites.findOne({ orgid, _id: sourceSiteToDelete });
  print("Source school deleted:");
  printjson(sourceSchoolDeleted);
  const deleteOp = db.Sites.deleteOne({ orgid, _id: sourceSiteToDelete });
  print("Deleting site with _id:", sourceSiteToDelete, "success:", deleteOp.deletedCount ? "Yes!" : "No!");
}

// source: target
// const targetIdBySourceId = {
// };

function stripLeadingZeros(id) {
  let normalizedId = id;
  while (normalizedId.charAt(0) === "0" && normalizedId.length > 1) {
    normalizedId = normalizedId.substr(1);
  }
  return normalizedId;
}

function getNormalizedId(id) {
  let normalizedId = id;
  // eslint-disable-next-line no-restricted-globals
  while (isNaN(normalizedId.charAt(normalizedId.length - 1)) && normalizedId.length > 1) {
    normalizedId = normalizedId.slice(0, -1);
  }
  normalizedId = stripLeadingZeros(normalizedId);

  return normalizedId;
}

Object.entries(targetIdBySourceId).forEach(([sourceId, targetId]) => {
  // TODO(fmazur) - Use proper orgid
  const orgid = "";
  if (!orgid) {
    print("No orgid specified. Exiting...");
    return;
  }
  // db.Sites.updateMany({ orgid, schoolYear: 2023 }, { $set: { "stateInformation.districtNumber": "Q7" } });
  db.Sites.find({ orgid })
    .toArray()
    .forEach(site => {
      // NOTE(fmazur) - remove _old_date suffix when merging autorostering schools
      // const schoolNumber = site.stateInformation.schoolNumber.replace(/_old_\d{4}-\d{2}-\d{2}$/, "");
      const schoolNumber = site.stateInformation.schoolNumber;
      db.Sites.updateOne(
        { _id: site._id },
        {
          $set: {
            "stateInformation.districtNumber": getNormalizedId(orgid),
            "stateInformation.schoolNumber": schoolNumber,
            "stateInformation.localSchoolNumber": schoolNumber
          }
        }
      );
    });
  mergeSites(sourceId, targetId, orgid);
});
