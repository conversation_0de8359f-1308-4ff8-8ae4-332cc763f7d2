var studentsAffected = db.Students.find(
  {
    "currentSkill.benchmarkPeriodId": "nEsbWokBWutTZFkTh",
    "history.benchmarkPeriodId": "8S52Gz5o85hRkECgq",
    schoolYear: 2019,
    _id: {
      $nin: ["HT3Q6564TJDhPz7Eo", "cqBweTc9LxJ7w9cGP", "RwQev6gXDLiauKw6N", "Fyv3pPoAgrgrjKmEr", "8xutYq2kaskHMa6rr"]
    }
  },
  { identity: 1, orgid: 1, grade: 1 }
).toArray();
var studentIds = studentsAffected.map(function(s) {
  return s._id;
});
var enrollments = db.StudentGroupEnrollments.find({
  studentId: { $in: studentIds },
  isActive: true,
  schoolYear: 2019
}).toArray();
var siteIds = enrollments.map(function(e) {
  return e.siteId;
});
var orgids = enrollments.map(function(e) {
  return e.orgid;
});
var sgIds = enrollments.map(function(e) {
  return e.studentGroupId;
});

var sites = db.Sites.find({ _id: { $in: siteIds } }, { name: 1 }).toArray();
var organizations = db.Organizations.find({ _id: { $in: orgids } }, { name: 1 }).toArray();
var studentGroups = db.StudentGroups.find({ _id: { $in: sgIds } }, { name: 1 }).toArray();

enrollments.forEach(function(enrollment, index) {
  var student = studentsAffected.find(function(s) {
    return enrollment.studentId === s._id;
  });
  var studentName = "" + student.identity.name.firstName + " " + student.identity.name.lastName;
  var studentGrade = student.grade;
  var organizationName = organizations.find(function(s) {
    return enrollment.orgid === s._id;
  }).name;
  var siteName = sites.find(function(s) {
    return enrollment.siteId === s._id;
  }).name;
  var studentGroupName = studentGroups.find(function(s) {
    return enrollment.studentGroupId === s._id;
  }).name;
  print(
    index + 1,
    ".\n",
    "Student Name: ",
    studentName,
    "(",
    enrollment.studentId,
    ")",
    "\n Grade: ",
    studentGrade,
    ".\n",
    "Org:",
    organizationName,
    "\n",
    "Site: ",
    siteName,
    "(",
    enrollment.siteId,
    ")",
    "\n",
    "Group: ",
    studentGroupName,
    "(",
    enrollment.studentGroupId,
    ")"
  );
});
