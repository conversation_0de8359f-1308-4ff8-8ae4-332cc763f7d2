//SPRIN-1332
(function() {
  var benchmarkAssessmentId = "j3zAzRRuqXCfh3zFs";

  var benchmarkAssessment = db.Assessments.findOne({
    _id: benchmarkAssessmentId
  });
  print("Benchmark Assessment:", benchmarkAssessment.name);
  var interventionAssessmentId = "N4vDf85G4rsQp7FPF";
  var interventionAssessment = db.Assessments.findOne({
    _id: interventionAssessmentId
  });
  print("\nIntervention Assessment", interventionAssessment.name);

  var studentsAffected = db.Students.find({
    grade: "06",
    "currentSkill.benchmarkAssessmentId": "j3zAzRRuqXCfh3zFs",
    "currentSkill.assessmentId": "N4vDf85G4rsQp7FPF",
    schoolYear: 2019
  }).toArray();
  print("\nNumber of students affected:", studentsAffected.length);

  var studentIds = studentsAffected.map(function(s) {
    return s._id;
  });

  var enrollments = db.StudentGroupEnrollments.find(
    {
      studentId: { $in: studentIds },
      isActive: true
    },
    { studentId: 1, siteId: 1, studentGroupId: 1 }
  ).toArray();
  var affectedStudentGroupIdsAndSiteIds = enrollments.map(function(e) {
    return { studentGroupId: e.studentGroupId, siteId: e.siteId };
  });
  var studentGroupIds = affectedStudentGroupIdsAndSiteIds.map(function(i) {
    return i.studentGroupId;
  });
  var studentGroups = db.StudentGroups.find(
    {
      _id: {
        $in: studentGroupIds
      }
    },
    { name: 1, currentAssessmentResultIds: 1 }
  ).toArray();

  var siteIds = affectedStudentGroupIdsAndSiteIds.map(function(i) {
    return i.siteId;
  });
  var sites = db.Sites.find(
    {
      _id: {
        $in: siteIds
      }
    },
    { name: 1 }
  ).toArray();

  var now = new Date();
  var lastModified = {
    by: "SPRIN_1332_fix_script",
    on: now.valueOf(),
    date: now
  };

  studentsAffected.forEach(function(student, index) {
    var firstName = student.identity.name.firstName;
    var lastName = student.identity.name.lastName;
    var studentId = student._id;
    var enrollment = enrollments.find(function(e) {
      return e.studentId == studentId;
    });

    var siteId = enrollment.siteId;
    var schoolName = sites.find(function(s) {
      return s._id === siteId;
    }).name;
    var groupId = enrollment.studentGroupId;
    var group = studentGroups.find(function(sg) {
      return sg._id === groupId;
    });
    var groupName = group.name;

    var numberOfHistoryItems = student.history.length;
    var historyItemsToBeRemoved = student.history.filter(function(historyItem) {
      return (
        historyItem.assessmentId === interventionAssessmentId &&
        historyItem.benchmarkAssessmentId === benchmarkAssessmentId
      );
    });
    var numberOfHistoryItemsWithFaultyIntervention = historyItemsToBeRemoved.length;

    print("\n", index, ".");
    print(firstName, lastName, "(_id:", studentId, ")", "\nschool: ", schoolName, "\ngroup:", groupName);
    // print("Number of history items:", numberOfHistoryItems);
    print("Number of history items to be removed:", numberOfHistoryItemsWithFaultyIntervention);
    if (numberOfHistoryItemsWithFaultyIntervention) {
      var benchmarkScoresToBeRemoved = [];
      var interventionScoresToBeRemoved = [];
      historyItemsToBeRemoved.forEach(function(item) {
        if (item.assessmentResultMeasures.length === 1) {
          var interventionScore = item.assessmentResultMeasures[0].studentScores[0];
          interventionScoresToBeRemoved.push(interventionScore);
        } else if (item.assessmentResultMeasures.length === 2) {
          var interventionScore = item.assessmentResultMeasures.find(function(am) {
            return am.assessmentId === interventionAssessmentId;
          }).studentScores[0];
          var benchmarkScore = item.assessmentResultMeasures.find(function(am) {
            return am.assessmentId === benchmarkAssessmentId;
          }).studentScores[0];
          benchmarkScoresToBeRemoved.push(benchmarkScore);
          interventionScoresToBeRemoved.push(interventionScore);
        }
      });

      var printableInterventionScores = interventionScoresToBeRemoved.length
        ? interventionScoresToBeRemoved.join(", ")
        : "none";
      print("Recorded Intervention/Drilldown Scores for Division 0-5 to be removed: ", printableInterventionScores);
      var printableBenchmarkScores = benchmarkScoresToBeRemoved.length ? benchmarkScoresToBeRemoved.join(", ") : "none";
      print(
        "Recorded Goal Skill Scores for Add/Subtract Fractions with Unlike Denominators to be removed: ",
        printableBenchmarkScores
      );
    }

    //REMOVING AR DOCUMENTS
    print("Removing Assessment Result documents and current skill...");
    var currentSkillARId = student.currentSkill.assessmentResultId;
    var assessmentResultsToBeRemoved = historyItemsToBeRemoved.map(function(i) {
      return i.assessmentResultId;
    });
    assessmentResultsToBeRemoved.push(currentSkillARId);
    var removedIndividualAssessments = db.AssessmentResults.remove({
      _id: { $in: assessmentResultsToBeRemoved }
    });
    print("Removed: " + removedIndividualAssessments.nRemoved + " individual assessments result documents.");

    //PULLING ITEMS FROM HISTORY
    print("Pulling out history items...");
    var historyUpdated = db.Students.update(
      { _id: studentId },
      {
        $set: { lastModified: lastModified },
        $pull: {
          history: {
            assessmentResultId: { $in: assessmentResultsToBeRemoved }
          }
        }
      }
    );
    print("Student history updated: ", historyUpdated.nModified ? "YES" : "NO");

    //REMOVING currentSkill AR id from StudentGroup
    print("Removing current skill AR id...");
    var removeCurrentSkillARFromGroup = db.StudentGroups.update(
      { _id: groupId },
      { $pull: { currentAssessmentResultIds: currentSkillARId } }
    );
    print("CurrentAssessmentResultIds updated: " + (removeCurrentSkillARFromGroup.nModified > 0 ? "YES!" : "NO!"));

    // SETTING NEW CURRENT SKILL AND currentAssessmentResultIds
    print("Updating currentSkill and currentAssessmentResultIds...");
    var desiredAssessmentId = "Ld5focRLdaoPfDkef"; //Division 0-9
    var previousAssessmentHistoryItem = student.history.find(function(item) {
      return item.assessmentId === desiredAssessmentId && item.benchmarkAssessmentId === benchmarkAssessmentId;
    });
    if (previousAssessmentHistoryItem) {
      var newCurrentAssessmentResultId = previousAssessmentHistoryItem.assessmentResultId;
      var updatedCurrentSkill = {
        benchmarkAssessmentId: "j3zAzRRuqXCfh3zFs",
        benchmarkAssessmentName: "Add/Subtract Fractions with Unlike Denominators",
        benchmarkAssessmentTargets: [NumberInt(3), NumberInt(6), NumberInt(300)],
        assessmentId: desiredAssessmentId,
        assessmentName: "Division 0-9",
        assessmentTargets: [NumberInt(40), NumberInt(80), NumberInt(300)],
        interventions: [],
        assessmentResultId: newCurrentAssessmentResultId,
        whenStarted: previousAssessmentHistoryItem.whenStarted,
        benchmarkPeriodId: "8S52Gz5o85hRkECgq",
        message: {
          messageCode: "61",
          dismissed: false
        }
      };

      var setOperation = { lastModified: lastModified, status: "OPEN" };
      var assessmentBroughtBackToOpen = db.AssessmentResults.findOne(
        { _id: newCurrentAssessmentResultId },
        { studentGroupId: 1 }
      );
      if (assessmentBroughtBackToOpen.studentGroupId !== groupId) {
        print("************** STUDENT WAS ENROLLED IN OTHER GROUP ***************");
        setOperation.studentGroupId = groupId;
      }

      var assessmentBroughtBackToOpenUpdate = db.AssessmentResults.update(
        { _id: newCurrentAssessmentResultId },
        {
          $set: setOperation,
          $unset: {
            classwideResults: "",
            measures: "",
            nextAssessmentResultId: "",
            ruleResults: ""
          }
        }
      );
      print("Division 0-9 was set back to open: ", assessmentBroughtBackToOpenUpdate.nModified ? "TRUE!" : "FALSE");

      var currentSkillUpdated = db.Students.update(
        { _id: studentId },
        {
          $set: {
            lastModified: lastModified,
            currentSkill: updatedCurrentSkill
          },
          $pull: {
            history: {
              assessmentResultId: newCurrentAssessmentResultId
            }
          }
        }
      );
      print("Student currentSkill updated: ", currentSkillUpdated.nModified ? "YES" : "NO");

      var addNewCurrentSkillToSg = db.StudentGroups.update(
        { _id: groupId },
        {
          $addToSet: {
            currentAssessmentResultIds: newCurrentAssessmentResultId
          }
        }
      );
      print("CurrentAssessmentResultIds updated: " + (addNewCurrentSkillToSg.nModified > 0 ? "YES!" : "NO!"));
    } else {
      print("\n ERROR: COULD NOT FIND THE Division 0-9 item!!!!!");
    }
  });
})();
