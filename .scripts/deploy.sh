echo "#######################"
echo "# PREPARING TO DEPLOY #"
echo "#######################"
SHOULD_DEPLOY="false"
THIS_BRANCH="NULL"
THIS_TAG="NULL"
REPO_DIRECTORY=$(pwd)/.deploy
echo "####################################"
echo "# REPO DIRECTORY IS "$REPO_DIRECTORY
echo "####################################"

echo "##########################"
echo "# EVALUATING LOCAL OR CI #"
echo "##########################"

echo "CIRCLE_BRANCH = $CIRCLE_BRANCH"
echo "CIRCLE_PROJECT_USERNAME = $CIRCLE_PROJECT_USERNAME"
if [ "$CIRCLECI" == "true" ] && [ "$CIRCLE_BRANCH" == "develop" ] && [ "$CIRCLE_PROJECT_USERNAME" == "edSpring" ]; then
  echo "################"
  echo "# ON CIRCLE CI #"
  echo "################"

  SHOULD_DEPLOY="true"
  THIS_BRANCH=$CIRCLE_BRANCH
  THIS_TAG=$CIRCLE_TAG

else
  echo "############"
  echo "# ON LOCAL #"
  echo "############"

  if [ "$MANUAL_DEPLOY" == "true" ]; then
    SHOULD_DEPLOY=$MANUAL_DEPLOY
  fi
fi

echo "##########################"
echo "#   EVALUATION RESULTS   #"
echo "##########################"
echo "#                         "
echo "# SHOULD DEPLOY? "$SHOULD_DEPLOY" "
echo "# ON BRANCH "$THIS_BRANCH" "
echo "# WITH TAGE "$THIS_TAG" "
echo "# REPO DIRECTORY "$REPO_DIRECTORY
echo "#                    "
echo "######################"


if [ "$SHOULD_DEPLOY" == "true" ]; then
  echo "#############"
  echo "# DEPLOYING #"
  echo "#############"

  echo "#######################"
  echo "# DEPLOYING TO SERVER #"
  echo "#######################"

  #Add deploy snippets here
  #increment version number and update the version file
  ./.scripts/develop-version-increment.sh
  # creates a js file with the conttents of WINNER
  eval echo \$$THIS_BRANCH"MUSJSON" >  deployment_token.json
  DEPLOY_HOSTNAME=ties.galaxy-deploy.meteor.com METEOR_SESSION_FILE=deployment_token.json meteor deploy app.dev.springmath.org --owner ties
  DEPLOY_RESULT=$?

  echo "#####################################"
  echo "# ASSIGN RESULTS TO DEPLOY_RESULT "$DEPLOY_RESULT" #"
  echo "#####################################"

  if [ "$DEPLOY_RESULT" == "0" ]; then
    echo "####################################"
    echo "# DEPLOYMENT FINISHED SUCCESSFULLY #"
    echo "####################################"
  else
    echo "#####################"
    echo "# DEPLOYMENT FAILED #"
    echo "#####################"
  fi

  exit $DEPLOY_RESULT
else
  echo "#################"
  echo "# NOT DEPLOYING #"
  echo "#################"
  exit 0
fi
