#!/bin/bash

# Modify spec test results
for FILE in ~/testResults/cypress/results.*.xml
do
  if [ ! -f "$FILE" ]; then
      echo "No files found to modify"
      exit 0
  fi
  echo -e "\t=> Parsing: $FILE"
  SPEC_TIME=$(grep "name=\"Mocha Tests\"" $FILE | sed -e 's/.*time=\(.*\) tests.*/\1/')
  SPEC_FILE_PATH=$(grep "name=\"Root Suite\"" $FILE | sed -e 's/.*file=\(.*\) time.*/\1/')
  PARSED_NAME=$(echo "file=$SPEC_FILE_PATH" | sed 's/\//\\\//g')
  NEW_FILE=$(cat $FILE | \
  sed 's/\sfile="[^"]*"//g' | \
  sed "s/name=\"Mocha Tests\"/name=\"Mocha Tests\" $PARSED_NAME/g" | \
  sed "s/<testsuite\s/<testsuite $PARSED_NAME /g" | \
  sed "s/time=\"0.0000\"/time=$SPEC_TIME/g")
  printf "%s" "$NEW_FILE" > "$FILE"
done
