//REQUIRES MONGODB 3.0 AND NODE >= 8
const MongoClient = require("mongodb").MongoClient;
const sourceUrl = "mongodb://localhost:5001";
// const targetUrl = "mongodb://localhost:3001";
const dbTarget = "springmathapp";
const localTargetDb = "meteor";
const benchmarkAssessmentId = "j3zAzRRuqXCfh3zFs";
const interventionAssessmentId = "N4vDf85G4rsQp7FPF";

const targetUrl = ""; // production URL

let targetConnection;
let sourceConnection;

async function run() {
  targetConnection = await MongoClient.connect(targetUrl);
  console.log(`\nConnected successfully to Target DB`);
  sourceConnection = await MongoClient.connect(sourceUrl);
  console.log("\nConnected successfully to Source DB");

  const targetDB = targetConnection.db(dbTarget);
  const targetAR = targetDB.collection("AssessmentResults");
  const targetStudents = targetDB.collection("Students");
  const targetStudentGroupEnrollments = targetDB.collection("StudentGroupEnrollments");

  const sourceDB = sourceConnection.db(localTargetDb);
  const sourceAR = sourceDB.collection("AssessmentResults");
  const sourceStudents = sourceDB.collection("Students");

  const studentsAffected = await sourceStudents
    .find({
      grade: "06",
      "currentSkill.benchmarkAssessmentId": benchmarkAssessmentId,
      "currentSkill.assessmentId": interventionAssessmentId,
      schoolYear: 2019
    })
    .toArray();
  console.log("\nNumber of students affected:", studentsAffected.length);

  const studentIds = studentsAffected.map(function(s) {
    return s._id;
  });

  const enrollments = await targetStudentGroupEnrollments
    .find(
      {
        studentId: { $in: studentIds },
        isActive: true,
        schoolYear: 2019
      },
      { projection: { studentId: 1, siteId: 1, studentGroupId: 1 } }
    )
    .toArray();

  for (student of studentsAffected) {
    const { firstName, lastName } = student.identity.name;
    const studentId = student._id;
    const studentGroupId = enrollments.find(e => e.studentId === studentId).studentGroupId;

    console.log("\n\n****************************************************");
    console.log(`Working on: ${firstName} ${lastName} (${studentId}) enrolled in group: ${studentGroupId}`);

    //FIND HISTORY ITEMS THAT GOT REMOVED
    const historyItemsToBeRemoved = student.history.filter(
      historyItem =>
        historyItem.assessmentId === interventionAssessmentId &&
        historyItem.benchmarkAssessmentId === benchmarkAssessmentId
    );
    const numberOfHistoryItemsWithFaultyIntervention = historyItemsToBeRemoved.length;
    console.log("\nNumber of history items that got removed:", numberOfHistoryItemsWithFaultyIntervention);

    //GET ARs THAT GOT REMOVED
    const assessmentResultIdsToBeRemoved = historyItemsToBeRemoved.map(i => i.assessmentResultId);

    // ADD MISSING ITEMS TO THE HISTORY
    const targetStudent = await targetStudents.findOne({ _id: studentId });
    const currentStudentHistory = targetStudent.history;
    currentStudentHistory.push(...historyItemsToBeRemoved);
    currentStudentHistory.sort((a, b) => b.whenEnded.on - a.whenEnded.on);

    // PREPARE ARs
    console.log("\nARs to be brought back: ", assessmentResultIdsToBeRemoved);
    const removedAssessmentResults = await sourceAR.find({ _id: { $in: assessmentResultIdsToBeRemoved } }).toArray();
    const updatedAssessmentResults = removedAssessmentResults.map(ar => {
      const { previousAssessmentResultId, nextAssessmentResultId, ...rest } = ar;
      rest.studentGroupId = studentGroupId;
      delete rest.ruleResults.nextAssessmentResultId;
      return rest;
    });

    //UPDATES
    const historyUpdateRes = await targetStudents.updateOne(
      { _id: studentId },
      { $set: { history: currentStudentHistory } }
    );
    console.log("\nhistoryUpdateRes, number modified", historyUpdateRes.result.nModified);
    if (updatedAssessmentResults.length) {
      const insertRes = await targetAR.insertMany(updatedAssessmentResults);
      console.log("\ninsertRes, number inserted", insertRes.result.n);
    }
  }

  await targetConnection.close();
  await sourceConnection.close();
}

run().catch(async e => {
  if (e.err) {
    console.log("e", e.err);
  } else {
    console.log("e", e);
  }
  console.log("closing connections");
  await targetConnection.close();
  await sourceConnection.close();
});
