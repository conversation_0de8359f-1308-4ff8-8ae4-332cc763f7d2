// TODO(fmazur) - check jest version in school scrubber package.json
const faker = jest.createMockFromModule("faker");

let dateBetween;
function __setDateBetween(newDate) {
  dateBetween = newDate;
}

function getDateBetween() {
  return dateBetween;
}

let firstName, lastName, uuid;
function __setFirstName(newFirstName) {
  firstName = newFirstName;
}

function __setLastName(newLastName) {
  lastName = newLastName;
}

function __setUuid(newUuid) {
  uuid = newUuid;
}

function getLastName() {
  return lastName;
}

function getFirstName() {
  return firstName;
}

function getUuid() {
  return uuid;
}

faker.__setDateBetween = __setDateBetween;
faker.date.between = getDateBetween;

faker.__setFirstName = __setFirstName;
faker.__setLastName = __setLastName;
faker.__setUuid = __setUuid;
faker.name.firstName = getFirstName;
faker.name.lastName = getLastName;
faker.random.uuid = getUuid;

module.exports = faker;
