{"name": "school-scrubber", "version": "0.0.1", "description": "", "main": "schoolScrubber.js", "scripts": {"test": "jest", "start": "babel-node schoolScrubber.js", "migrateData": "babel-node migrateData.js", "migrateUsers": "babel-node migrateUsers.js"}, "author": "", "license": "ISC", "dependencies": {"babel-cli": "^6.24.1", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "babel-preset-stage-2": "^6.24.1", "lodash": "^4.17.10", "minimist": "^1.2.6", "mongodb": "^2.2.26"}, "devDependencies": {"faker": "^4.1.0", "jest": "^23.4.2"}, "jest": {"rootDir": "./", "collectCoverageFrom": ["*.js*"], "coverageDirectory": ".coverage/", "collectCoverage": false, "testURL": "http://localhost"}}