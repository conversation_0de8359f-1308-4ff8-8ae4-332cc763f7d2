import {
  BENCHMARK_ASSESSMENT_RESULT_ID,
  CLASSWIDE_ASSESSMENT_RESULT_ID,
  GRADE,
  GROUP_ID,
  INDIVIDUAL_ASSESSMENT_RESULT_ID,
  NEW_ORG_ID,
  ORG_ID,
  SCHOOL_YEAR,
  SITE_ID,
  STUDENT_BIRTHDATE,
  STUDENT_FIRSTNAME,
  STUDENT_ID,
  STUDENT_LASTNAME,
  STUDENT_MIDDLENAME,
  USER,
  UUID
} from "./constants";

export const exampleSites = [
  {
    _id: SITE_ID,
    orgid: ORG_ID,
    schoolYear: SCHOOL_YEAR,
    stateInformation: {
      districtNumber: "1",
      districtName: "Pre anonymization district",
      schoolNumber: "1",
      localSchoolNumber: "1"
    },
    name: "Pre anonymization name",
    grades: ["01", "02", "03", "04", "05", "06", "07", "08"],
    isVisible: true
  }
];

export const expectedSites = [
  {
    orgid: NEW_ORG_ID,
    schoolYear: SCHOOL_YEAR,
    stateInformation: {
      districtNumber: "4",
      districtName: "Demo school",
      schoolNumber: "10001",
      localSchoolNumber: "10001"
    },
    name: "Historical Data 10001",
    grades: ["01", "02", "03", "04", "05", "06", "07", "08"],
    isVisible: true
  }
];

export const exampleStudents = [
  {
    _id: STUDENT_ID,
    orgid: ORG_ID,
    schoolYear: SCHOOL_YEAR,
    districtNumber: "1",
    grade: GRADE,
    demographic: {
      birthDate: "2005-12-03",
      ethnicity: "",
      gender: "",
      gt: "",
      sped: "",
      ell: "",
      title: "",
      birthDateTimeStamp: 1133568000000.0
    },
    identity: {
      name: {
        firstName: "PreName",
        lastName: "PreLastName",
        middleName: "PreMiddleName"
      },
      identification: {
        localId: "301",
        stateId: "1301"
      }
    },
    history: [
      {
        benchmarkAssessmentId: "5JREydmmbAnRT5usy",
        benchmarkAssessmentName: "Add 2-Digit with Regrouping",
        benchmarkAssessmentTargets: [8, 15, 300],
        assessmentId: "bhkwFktTpjmag7EvF",
        assessmentName: "Add 2-Digit w/o Regrouping",
        assessmentTargets: [8, 16, 300],
        interventions: [],
        assessmentResultId: INDIVIDUAL_ASSESSMENT_RESULT_ID,
        benchmarkPeriodId: "cjCMnZKARBJmG8suT",
        message: {
          messageCode: "51",
          dismissed: false
        },
        assessmentResultMeasures: [
          {
            assessmentId: "bhkwFktTpjmag7EvF",
            assessmentName: "Add 2-Digit w/o Regrouping",
            cutoffTarget: 16,
            targetScores: [8, 16, 300],
            medianScore: 19,
            studentScores: [19],
            percentMeetingTarget: 100,
            numberMeetingTarget: 1,
            totalStudentsAssessed: 1,
            studentResults: [
              {
                studentId: STUDENT_ID,
                status: "COMPLETE",
                firstName: "John",
                lastName: "Verde",
                score: "19",
                meetsTarget: true,
                individualRuleOutcome: "above"
              }
            ]
          }
        ],
        type: "individual"
      }
    ],
    currentSkill: {
      benchmarkAssessmentId: "aqikjxjsFL3bwTfSm",
      benchmarkAssessmentName: "Fact Families: Multiplication/Division 0-12",
      benchmarkAssessmentTargets: [28, 56, 225],
      assessmentId: "hCWWDaZE2fusxuSJ9",
      assessmentName: "Multiplication 0-12",
      assessmentTargets: [23, 46, 300],
      interventions: [],
      assessmentResultId: INDIVIDUAL_ASSESSMENT_RESULT_ID,
      whenStarted: {
        by: "",
        on: 1495113112629.0,
        date: "2017-05-18T13:11:52.629+0000"
      },
      benchmarkPeriodId: "8S52Gz5o85hRkECgq",
      message: {
        messageCode: "51",
        dismissed: false
      }
    }
  }
];

export const expectedStudents = [
  {
    bak_id: "preStudentId",
    orgid: NEW_ORG_ID,
    schoolYear: SCHOOL_YEAR,
    districtNumber: "1",
    grade: GRADE,
    demographic: {
      birthDate: STUDENT_BIRTHDATE.toISOString().slice(0, 10),
      ethnicity: "",
      gender: "",
      gt: "",
      sped: "",
      ell: "",
      title: "",
      birthDateTimeStamp: STUDENT_BIRTHDATE.valueOf()
    },
    identity: {
      name: {
        firstName: STUDENT_FIRSTNAME,
        lastName: STUDENT_LASTNAME,
        middleName: STUDENT_MIDDLENAME
      },
      identification: {
        localId: `l${UUID.slice(0, 5)}`,
        stateId: `s${UUID.slice(0, 8)}`
      }
    },
    history: [
      expect.objectContaining({
        benchmarkAssessmentId: "5JREydmmbAnRT5usy",
        benchmarkAssessmentName: "Add 2-Digit with Regrouping",
        benchmarkAssessmentTargets: [8, 15, 300],
        assessmentId: "bhkwFktTpjmag7EvF",
        assessmentName: "Add 2-Digit w/o Regrouping",
        assessmentTargets: [8, 16, 300],
        interventions: [],
        benchmarkPeriodId: "cjCMnZKARBJmG8suT",
        message: {
          messageCode: "51",
          dismissed: false
        },
        assessmentResultMeasures: [
          {
            assessmentId: "bhkwFktTpjmag7EvF",
            assessmentName: "Add 2-Digit w/o Regrouping",
            cutoffTarget: 16,
            targetScores: [8, 16, 300],
            medianScore: 19,
            studentScores: [19],
            percentMeetingTarget: 100,
            numberMeetingTarget: 1,
            totalStudentsAssessed: 1,
            studentResults: [
              expect.objectContaining({
                status: "COMPLETE",
                firstName: STUDENT_FIRSTNAME,
                lastName: STUDENT_LASTNAME,
                score: "19",
                meetsTarget: true,
                individualRuleOutcome: "above"
              })
            ]
          }
        ],
        type: "individual"
      })
    ],
    currentSkill: expect.objectContaining({
      benchmarkAssessmentId: "aqikjxjsFL3bwTfSm",
      benchmarkAssessmentName: "Fact Families: Multiplication/Division 0-12",
      benchmarkAssessmentTargets: [28, 56, 225],
      assessmentId: "hCWWDaZE2fusxuSJ9",
      assessmentName: "Multiplication 0-12",
      assessmentTargets: [23, 46, 300],
      interventions: [],
      whenStarted: {
        by: "",
        on: 1495113112629.0,
        date: "2017-05-18T13:11:52.629+0000"
      },
      benchmarkPeriodId: "8S52Gz5o85hRkECgq",
      message: {
        messageCode: "51",
        dismissed: false
      }
    })
  }
];

export const exampleGroups = [
  {
    _id: GROUP_ID,
    orgid: ORG_ID,
    siteId: SITE_ID,
    grade: GRADE,
    type: "CLASS",
    ownerIds: [USER._id],
    isActive: true,
    sectionId: "preSectionId", //"fqaFT_06_2"
    name: "preGroupName", //"Test 06#2 (6ss1D-fqaFT_06_2-)"
    schoolYear: SCHOOL_YEAR,
    history: [
      {
        type: "benchmark",
        benchmarkPeriodId: "cjCMnZKARBJmG8suT",
        assessmentResultId: BENCHMARK_ASSESSMENT_RESULT_ID,
        assessmentResultMeasures: [
          {
            assessmentId: "7arH8a6z3BAEdEARm",
            assessmentName: "Sums to 20",
            cutoffTarget: 11,
            targetScores: [11, 22, 299],
            medianScore: 0,
            studentScores: [0, 0, 0, 0, 0],
            percentMeetingTarget: 0,
            numberMeetingTarget: 0,
            totalStudentsAssessed: 5,
            studentResults: [
              {
                studentId: STUDENT_ID,
                status: "COMPLETE",
                firstName: "John",
                lastName: "Verde",
                score: "0",
                meetsTarget: false,
                individualRuleOutcome: "below"
              }
            ],
            benchmarkPeriodId: "cjCMnZKARBJmG8suT",
            grade: GRADE,
            assessmentResultType: "benchmark"
          },
          {
            assessmentId: "RAicL73Hwai6BSCPH",
            assessmentName: "Subtraction 0-20",
            cutoffTarget: 17,
            targetScores: [17, 33, 300],
            medianScore: 0,
            studentScores: [0, 0, 0, 0, 0],
            percentMeetingTarget: 0,
            numberMeetingTarget: 0,
            totalStudentsAssessed: 5,
            studentResults: [
              {
                studentId: STUDENT_ID,
                status: "COMPLETE",
                firstName: "John",
                lastName: "Verde",
                score: "0",
                meetsTarget: false,
                individualRuleOutcome: "below"
              }
            ]
          },
          {
            assessmentId: "FxYDJWbMdT8QiZwcD",
            assessmentName: "Fact Fams: Add/Subt 0-9",
            cutoffTarget: 20,
            targetScores: [20, 40, 300],
            medianScore: 0,
            studentScores: [0, 0, 0, 0, 0],
            percentMeetingTarget: 0,
            numberMeetingTarget: 0,
            totalStudentsAssessed: 5,
            studentResults: [
              {
                studentId: STUDENT_ID,
                status: "COMPLETE",
                firstName: "John",
                lastName: "Verde",
                score: "0",
                meetsTarget: false,
                individualRuleOutcome: "below"
              }
            ]
          }
        ],
        enrolledStudentIds: [STUDENT_ID]
      },
      {
        assessmentId: "7arH8a6z3BAEdEARm",
        assessmentName: "Sums to 20",
        interventions: [],
        targets: [11, 22, 300],
        assessmentResultId: CLASSWIDE_ASSESSMENT_RESULT_ID,
        benchmarkPeriodId: "nEsbWokBWutTZFkTh",
        message: {
          additionalStudentsAddedToInterventionQueue: false,
          messageCode: "2",
          dismissed: false
        },
        assessmentResultMeasures: [
          {
            assessmentId: "7arH8a6z3BAEdEARm",
            assessmentName: "Sums to 20",
            cutoffTarget: 22,
            targetScores: [11, 22, 300],
            medianScore: 99,
            studentScores: [99, 99, 99, 99, 99],
            percentMeetingTarget: 100,
            numberMeetingTarget: 5,
            totalStudentsAssessed: 5,
            studentResults: [
              {
                studentId: STUDENT_ID,
                status: "COMPLETE",
                firstName: "John",
                lastName: "Verde",
                score: "99",
                meetsTarget: true,
                individualRuleOutcome: "above"
              }
            ],
            benchmarkPeriodId: "nEsbWokBWutTZFkTh",
            grade: GRADE,
            assessmentResultType: "classwide"
          }
        ],
        type: "classwide",
        enrolledStudentIds: [STUDENT_ID]
      }
    ],
    currentAssessmentResultIds: [CLASSWIDE_ASSESSMENT_RESULT_ID],
    currentClasswideSkill: {
      assessmentId: "yxBaWDvbNjBJLcaHQ",
      assessmentName: "Sums to 6",
      interventions: [],
      targets: [20, 40, 300],
      whenStarted: {
        by: "",
        on: 1532619115050.0,
        date: "2018-07-26T15:31:55.050+0000"
      },
      assessmentResultId: CLASSWIDE_ASSESSMENT_RESULT_ID,
      benchmarkPeriodId: "cjCMnZKARBJmG8suT",
      message: {
        additionalStudentsAddedToInterventionQueue: false,
        messageCode: "1",
        dismissed: false
      }
    }
  }
];

export const expectedGroups = [
  {
    bak_id: "preGroupId",
    orgid: NEW_ORG_ID,
    grade: GRADE,
    type: "CLASS",
    ownerIds: [USER._id],
    isActive: true,
    sectionId: `sec${UUID.slice(0, 8)}`, //"fqaFT_06_2"
    name: `AnonymousLastName's grade`, //"Test 06#2 (fqaFT_06_2)"
    schoolYear: SCHOOL_YEAR,
    history: [
      {
        type: "benchmark",
        benchmarkPeriodId: "cjCMnZKARBJmG8suT",
        assessmentResultMeasures: [
          {
            assessmentId: "7arH8a6z3BAEdEARm",
            assessmentName: "Sums to 20",
            cutoffTarget: 11,
            targetScores: [11, 22, 299],
            medianScore: 0,
            studentScores: [0, 0, 0, 0, 0],
            percentMeetingTarget: 0,
            numberMeetingTarget: 0,
            totalStudentsAssessed: 5,
            studentResults: [
              expect.objectContaining({
                status: "COMPLETE",
                firstName: STUDENT_FIRSTNAME,
                lastName: STUDENT_LASTNAME,
                score: "0",
                meetsTarget: false,
                individualRuleOutcome: "below"
              })
            ],
            benchmarkPeriodId: "cjCMnZKARBJmG8suT",
            grade: GRADE,
            assessmentResultType: "benchmark"
          },
          {
            assessmentId: "RAicL73Hwai6BSCPH",
            assessmentName: "Subtraction 0-20",
            cutoffTarget: 17,
            targetScores: [17, 33, 300],
            medianScore: 0,
            studentScores: [0, 0, 0, 0, 0],
            percentMeetingTarget: 0,
            numberMeetingTarget: 0,
            totalStudentsAssessed: 5,
            studentResults: [
              expect.objectContaining({
                status: "COMPLETE",
                firstName: STUDENT_FIRSTNAME,
                lastName: STUDENT_LASTNAME,
                score: "0",
                meetsTarget: false,
                individualRuleOutcome: "below"
              })
            ]
          },
          {
            assessmentId: "FxYDJWbMdT8QiZwcD",
            assessmentName: "Fact Fams: Add/Subt 0-9",
            cutoffTarget: 20,
            targetScores: [20, 40, 300],
            medianScore: 0,
            studentScores: [0, 0, 0, 0, 0],
            percentMeetingTarget: 0,
            numberMeetingTarget: 0,
            totalStudentsAssessed: 5,
            studentResults: [
              expect.objectContaining({
                status: "COMPLETE",
                firstName: STUDENT_FIRSTNAME,
                lastName: STUDENT_LASTNAME,
                score: "0",
                meetsTarget: false,
                individualRuleOutcome: "below"
              })
            ]
          }
        ]
      },
      {
        assessmentId: "7arH8a6z3BAEdEARm",
        assessmentName: "Sums to 20",
        interventions: [],
        targets: [11, 22, 300],
        benchmarkPeriodId: "nEsbWokBWutTZFkTh",
        message: {
          additionalStudentsAddedToInterventionQueue: false,
          messageCode: "2",
          dismissed: false
        },
        assessmentResultMeasures: [
          {
            assessmentId: "7arH8a6z3BAEdEARm",
            assessmentName: "Sums to 20",
            cutoffTarget: 22,
            targetScores: [11, 22, 300],
            medianScore: 99,
            studentScores: [99, 99, 99, 99, 99],
            percentMeetingTarget: 100,
            numberMeetingTarget: 5,
            totalStudentsAssessed: 5,
            studentResults: [
              expect.objectContaining({
                status: "COMPLETE",
                firstName: STUDENT_FIRSTNAME,
                lastName: STUDENT_LASTNAME,
                score: "99",
                meetsTarget: true,
                individualRuleOutcome: "above"
              })
            ],
            benchmarkPeriodId: "nEsbWokBWutTZFkTh",
            grade: GRADE,
            assessmentResultType: "classwide"
          }
        ],
        type: "classwide"
      }
    ],
    currentAssessmentResultIds: [],
    currentClasswideSkill: {
      assessmentId: "yxBaWDvbNjBJLcaHQ",
      assessmentName: "Sums to 6",
      interventions: [],
      targets: [20, 40, 300],
      whenStarted: {
        by: "",
        on: 1532619115050.0,
        date: "2018-07-26T15:31:55.050+0000"
      },
      benchmarkPeriodId: "cjCMnZKARBJmG8suT",
      message: {
        additionalStudentsAddedToInterventionQueue: false,
        messageCode: "1",
        dismissed: false
      }
    }
  }
];

export const exampleStudentGroupEnrollments = [
  {
    _id: "preSGEId",
    orgid: ORG_ID,
    siteId: SITE_ID,
    grade: GRADE,
    studentGroupId: GROUP_ID,
    studentId: STUDENT_ID,
    isActive: true,
    schoolYear: SCHOOL_YEAR,
    giftedAndTalented: false,
    ELL: false,
    IEP: false,
    title1: false,
    freeReducedLunch: "no"
  }
];

export const expectedStudentGroupEnrollments = [
  {
    orgid: NEW_ORG_ID,
    grade: GRADE,
    isActive: true,
    schoolYear: SCHOOL_YEAR,
    giftedAndTalented: false,
    ELL: false,
    IEP: false,
    title1: false,
    freeReducedLunch: "no"
  }
];

export const exampleAssessmentResults = [
  {
    _id: BENCHMARK_ASSESSMENT_RESULT_ID,
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    schoolYear: SCHOOL_YEAR,
    status: "COMPLETED",
    studentGroupId: GROUP_ID,
    type: "benchmark",
    grade: GRADE,
    orgid: ORG_ID,
    scores: [
      {
        _id: "preScore1",
        assessmentId: "bhkwFktTpjmag7EvF",
        orgid: ORG_ID,
        siteId: SITE_ID,
        status: "COMPLETE",
        studentId: STUDENT_ID,
        value: "1"
      },
      {
        _id: "preScore2",
        assessmentId: "TWCTEasrvMv7HFMRt",
        orgid: ORG_ID,
        siteId: SITE_ID,
        status: "COMPLETE",
        studentId: STUDENT_ID,
        value: "6"
      },
      {
        _id: "preScore3",
        assessmentId: "RbWZKaoYXgtkJiXdb",
        orgid: ORG_ID,
        siteId: SITE_ID,
        status: "COMPLETE",
        studentId: STUDENT_ID,
        value: "11"
      }
    ],
    assessmentIds: ["bhkwFktTpjmag7EvF", "TWCTEasrvMv7HFMRt", "RbWZKaoYXgtkJiXdb"],
    classwideResults: {
      percentMeetingTarget: 0,
      percentAtRisk: 100,
      totalStudentsMeetingAllTargets: 0,
      totalStudentsAssessedOnAllMeasures: 1,
      studentIdsNotMeetingTarget: [STUDENT_ID]
    },
    measures: [
      {
        assessmentId: "bhkwFktTpjmag7EvF",
        assessmentName: "Add 2-Digit w/o Regrouping",
        cutoffTarget: 8,
        targetScores: [8, 16, 300],
        medianScore: 3,
        studentScores: [3],
        percentMeetingTarget: 0,
        numberMeetingTarget: 0,
        totalStudentsAssessed: 5,
        studentResults: [
          {
            studentId: STUDENT_ID,
            status: "COMPLETE",
            firstName: "John",
            lastName: "Verde",
            score: "3",
            meetsTarget: false,
            individualRuleOutcome: "below"
          }
        ],
        benchmarkPeriodId: "nEsbWokBWutTZFkTh",
        grade: GRADE,
        assessmentResultType: "benchmark"
      },
      {
        assessmentId: "TWCTEasrvMv7HFMRt",
        assessmentName: "2-digit subtraction w/o regrouping",
        cutoffTarget: 12,
        targetScores: [12, 23, 300],
        medianScore: 8,
        studentScores: [8],
        percentMeetingTarget: 0,
        numberMeetingTarget: 0,
        totalStudentsAssessed: 5,
        studentResults: [
          {
            studentId: STUDENT_ID,
            status: "COMPLETE",
            firstName: "John",
            lastName: "Verde",
            score: "8",
            meetsTarget: false,
            individualRuleOutcome: "below"
          }
        ]
      },
      {
        assessmentId: "RbWZKaoYXgtkJiXdb",
        assessmentName: "Quantity Compare for Sums & Differences to 20",
        cutoffTarget: 15,
        targetScores: [15, 26, 300],
        medianScore: 13,
        studentScores: [13],
        percentMeetingTarget: 20,
        numberMeetingTarget: 1,
        totalStudentsAssessed: 5,
        studentResults: [
          {
            studentId: STUDENT_ID,
            status: "COMPLETE",
            firstName: "John",
            lastName: "Verde",
            score: "13",
            meetsTarget: false,
            individualRuleOutcome: "below"
          }
        ]
      }
    ],
    nextAssessmentResultId: "77YQGmeBC6YdayLfD",
    ruleResults: {
      passed: false,
      nextSkill: {
        assessmentId: "7arH8a6z3BAEdEARm",
        assessmentName: "Sums to 20",
        interventions: [],
        targets: [11, 22, 300]
      }
    }
  },
  {
    _id: INDIVIDUAL_ASSESSMENT_RESULT_ID,
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    orgid: ORG_ID,
    schoolYear: SCHOOL_YEAR,
    status: "COMPLETED",
    studentGroupId: GROUP_ID,
    studentId: STUDENT_ID,
    type: "individual",
    previousAssessmentResultId: "KCMAW9DoQhoK4Fopp",
    grade: GRADE,
    rootRuleId: "e422f42ed220c1",
    scores: [
      {
        _id: "PjfdKJfeNdmr4X6Xj",
        assessmentId: "XSdsR5SMeczFaEfHW",
        orgid: ORG_ID,
        siteId: SITE_ID,
        status: "COMPLETE",
        studentId: STUDENT_ID,
        value: "40"
      },
      {
        _id: "NN84KJfreTCvWiKox",
        assessmentId: "oCNYMYX3WE8xhRhZR",
        orgid: ORG_ID,
        siteId: SITE_ID,
        status: "STARTED",
        studentId: STUDENT_ID
      }
    ],
    assessmentIds: ["XSdsR5SMeczFaEfHW", "oCNYMYX3WE8xhRhZR"],
    individualSkills: {
      benchmarkAssessmentId: "oCNYMYX3WE8xhRhZR",
      benchmarkAssessmentName: "Multiplication 0-9",
      benchmarkAssessmentTargets: [13, 26, 300],
      assessmentId: "XSdsR5SMeczFaEfHW",
      assessmentName: "Multiplication 0-5",
      assessmentTargets: [16, 31, 300],
      interventions: [],
      assessmentResultId: "bP9ByjpvfCz57sK6M"
    },
    classwideResults: {
      percentMeetingTarget: null,
      percentAtRisk: 100,
      totalStudentsMeetingAllTargets: 1,
      totalStudentsAssessedOnAllMeasures: 0,
      studentIdsNotMeetingTarget: []
    },
    measures: [
      {
        assessmentId: "XSdsR5SMeczFaEfHW",
        assessmentName: "Multiplication 0-5",
        cutoffTarget: 31,
        targetScores: [16, 31, 300],
        medianScore: 40,
        studentScores: [40],
        percentMeetingTarget: 100,
        numberMeetingTarget: 1,
        totalStudentsAssessed: 1,
        studentResults: [
          {
            studentId: STUDENT_ID,
            status: "COMPLETE",
            firstName: "John",
            lastName: "Verde",
            score: "40",
            meetsTarget: true,
            individualRuleOutcome: "above"
          }
        ]
      }
    ],
    nextAssessmentResultId: "ReBrrrH9NRvGbW4md",
    ruleResults: {
      passed: true,
      nextSkill: {
        assessmentId: "oCNYMYX3WE8xhRhZR",
        assessmentName: "Multiplication 0-9",
        interventions: [
          {
            interventionId: "56attvmgjtrjMsFif",
            interventionLabel: "Intervention Adviser - Cover Copy and Compare",
            interventionAbbrv: "CCC"
          },
          {
            interventionId: "JC4A2Jx6gKfqLe9LF",
            interventionLabel: "Intervention Adviser - Guided Practice",
            interventionAbbrv: "GP"
          }
        ]
      },
      nextAssessmentResultId: "ReBrrrH9NRvGbW4md"
    }
  },
  {
    _id: CLASSWIDE_ASSESSMENT_RESULT_ID,
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    orgid: ORG_ID,
    schoolYear: SCHOOL_YEAR,
    status: "COMPLETED",
    studentGroupId: GROUP_ID,
    type: "classwide",
    previousAssessmentResultId: "77YQGmeBC6YdayLfD",
    grade: GRADE,
    assessmentIds: ["7arH8a6z3BAEdEARm"],
    scores: [
      {
        _id: "wfWSYJjGoDdPR5mCN",
        assessmentId: "7arH8a6z3BAEdEARm",
        orgid: ORG_ID,
        siteId: SITE_ID,
        status: "COMPLETE",
        studentId: STUDENT_ID,
        value: "99"
      }
    ],
    classwideResults: {
      percentMeetingTarget: 100,
      percentAtRisk: 0,
      totalStudentsMeetingAllTargets: 5,
      totalStudentsAssessedOnAllMeasures: 5,
      studentIdsNotMeetingTarget: []
    },
    measures: [
      {
        assessmentId: "7arH8a6z3BAEdEARm",
        assessmentName: "Sums to 20",
        cutoffTarget: 22,
        targetScores: [11, 22, 300],
        medianScore: 99,
        studentScores: [99, 99, 99, 99, 99],
        percentMeetingTarget: 100,
        numberMeetingTarget: 5,
        totalStudentsAssessed: 5,
        studentResults: [
          {
            studentId: STUDENT_ID,
            status: "COMPLETE",
            firstName: "John",
            lastName: "Verde",
            score: "99",
            meetsTarget: true,
            individualRuleOutcome: "above"
          }
        ],
        benchmarkPeriodId: "nEsbWokBWutTZFkTh",
        grade: GRADE,
        assessmentResultType: "classwide"
      }
    ],
    nextAssessmentResultId: "9j7qHK7rE5nECG8fi",
    ruleResults: {
      passed: true,
      nextSkill: {
        assessmentId: "pexzg5K88e3XBHKYi",
        assessmentName: "Subtraction 0-9",
        interventions: [],
        targets: [20, 40, 225]
      }
    }
  }
];

export const expectedAssessmentResults = [
  {
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    schoolYear: SCHOOL_YEAR,
    status: "COMPLETED",
    type: "benchmark",
    grade: GRADE,
    orgid: NEW_ORG_ID,
    scores: [
      expect.objectContaining({
        assessmentId: "bhkwFktTpjmag7EvF",
        orgid: NEW_ORG_ID,
        status: "COMPLETE",
        value: "1"
      }),
      expect.objectContaining({
        assessmentId: "TWCTEasrvMv7HFMRt",
        orgid: NEW_ORG_ID,
        status: "COMPLETE",
        value: "6"
      }),
      expect.objectContaining({
        assessmentId: "RbWZKaoYXgtkJiXdb",
        orgid: NEW_ORG_ID,
        status: "COMPLETE",
        value: "11"
      })
    ],
    assessmentIds: ["bhkwFktTpjmag7EvF", "TWCTEasrvMv7HFMRt", "RbWZKaoYXgtkJiXdb"],
    classwideResults: expect.objectContaining({
      percentMeetingTarget: 0,
      percentAtRisk: 100,
      totalStudentsMeetingAllTargets: 0,
      totalStudentsAssessedOnAllMeasures: 1,
      studentIdsNotMeetingTarget: [expect.anything()]
    }),
    measures: [
      {
        assessmentId: "bhkwFktTpjmag7EvF",
        assessmentName: "Add 2-Digit w/o Regrouping",
        cutoffTarget: 8,
        targetScores: [8, 16, 300],
        medianScore: 3,
        studentScores: [3],
        percentMeetingTarget: 0,
        numberMeetingTarget: 0,
        totalStudentsAssessed: 5,
        studentResults: [
          expect.objectContaining({
            status: "COMPLETE",
            firstName: STUDENT_FIRSTNAME,
            lastName: STUDENT_LASTNAME,
            score: "3",
            meetsTarget: false,
            individualRuleOutcome: "below"
          })
        ],
        benchmarkPeriodId: "nEsbWokBWutTZFkTh",
        grade: GRADE,
        assessmentResultType: "benchmark"
      },
      {
        assessmentId: "TWCTEasrvMv7HFMRt",
        assessmentName: "2-digit subtraction w/o regrouping",
        cutoffTarget: 12,
        targetScores: [12, 23, 300],
        medianScore: 8,
        studentScores: [8],
        percentMeetingTarget: 0,
        numberMeetingTarget: 0,
        totalStudentsAssessed: 5,
        studentResults: [
          expect.objectContaining({
            status: "COMPLETE",
            firstName: STUDENT_FIRSTNAME,
            lastName: STUDENT_LASTNAME,
            score: "8",
            meetsTarget: false,
            individualRuleOutcome: "below"
          })
        ]
      },
      {
        assessmentId: "RbWZKaoYXgtkJiXdb",
        assessmentName: "Quantity Compare for Sums & Differences to 20",
        cutoffTarget: 15,
        targetScores: [15, 26, 300],
        medianScore: 13,
        studentScores: [13],
        percentMeetingTarget: 20,
        numberMeetingTarget: 1,
        totalStudentsAssessed: 5,
        studentResults: [
          expect.objectContaining({
            status: "COMPLETE",
            firstName: STUDENT_FIRSTNAME,
            lastName: STUDENT_LASTNAME,
            score: "13",
            meetsTarget: false,
            individualRuleOutcome: "below"
          })
        ]
      }
    ],
    nextAssessmentResultId: "77YQGmeBC6YdayLfD",
    ruleResults: {
      passed: false,
      nextSkill: {
        assessmentId: "7arH8a6z3BAEdEARm",
        assessmentName: "Sums to 20",
        interventions: [],
        targets: [11, 22, 300]
      }
    }
  },
  {
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    orgid: NEW_ORG_ID,
    schoolYear: SCHOOL_YEAR,
    status: "COMPLETED",
    type: "individual",
    previousAssessmentResultId: "KCMAW9DoQhoK4Fopp",
    grade: GRADE,
    rootRuleId: "e422f42ed220c1",
    scores: [
      expect.objectContaining({
        assessmentId: "XSdsR5SMeczFaEfHW",
        orgid: NEW_ORG_ID,
        status: "COMPLETE",
        value: "40"
      }),
      expect.objectContaining({
        assessmentId: "oCNYMYX3WE8xhRhZR",
        orgid: NEW_ORG_ID,
        status: "STARTED"
      })
    ],
    assessmentIds: ["XSdsR5SMeczFaEfHW", "oCNYMYX3WE8xhRhZR"],
    individualSkills: expect.objectContaining({
      benchmarkAssessmentId: "oCNYMYX3WE8xhRhZR",
      benchmarkAssessmentName: "Multiplication 0-9",
      benchmarkAssessmentTargets: [13, 26, 300],
      assessmentId: "XSdsR5SMeczFaEfHW",
      assessmentName: "Multiplication 0-5",
      assessmentTargets: [16, 31, 300],
      interventions: []
    }),
    classwideResults: {
      percentMeetingTarget: null,
      percentAtRisk: 100,
      totalStudentsMeetingAllTargets: 1,
      totalStudentsAssessedOnAllMeasures: 0,
      studentIdsNotMeetingTarget: []
    },
    measures: [
      {
        assessmentId: "XSdsR5SMeczFaEfHW",
        assessmentName: "Multiplication 0-5",
        cutoffTarget: 31,
        targetScores: [16, 31, 300],
        medianScore: 40,
        studentScores: [40],
        percentMeetingTarget: 100,
        numberMeetingTarget: 1,
        totalStudentsAssessed: 1,
        studentResults: [
          expect.objectContaining({
            status: "COMPLETE",
            firstName: STUDENT_FIRSTNAME,
            lastName: STUDENT_LASTNAME,
            score: "40",
            meetsTarget: true,
            individualRuleOutcome: "above"
          })
        ]
      }
    ],
    nextAssessmentResultId: "ReBrrrH9NRvGbW4md",
    ruleResults: {
      passed: true,
      nextSkill: {
        assessmentId: "oCNYMYX3WE8xhRhZR",
        assessmentName: "Multiplication 0-9",
        interventions: [
          {
            interventionId: "56attvmgjtrjMsFif",
            interventionLabel: "Intervention Adviser - Cover Copy and Compare",
            interventionAbbrv: "CCC"
          },
          {
            interventionId: "JC4A2Jx6gKfqLe9LF",
            interventionLabel: "Intervention Adviser - Guided Practice",
            interventionAbbrv: "GP"
          }
        ]
      },
      nextAssessmentResultId: "ReBrrrH9NRvGbW4md"
    }
  },
  {
    benchmarkPeriodId: "nEsbWokBWutTZFkTh",
    orgid: NEW_ORG_ID,
    schoolYear: SCHOOL_YEAR,
    status: "COMPLETED",
    type: "classwide",
    previousAssessmentResultId: "77YQGmeBC6YdayLfD",
    grade: GRADE,
    assessmentIds: ["7arH8a6z3BAEdEARm"],
    scores: [
      expect.objectContaining({
        assessmentId: "7arH8a6z3BAEdEARm",
        orgid: NEW_ORG_ID,
        status: "COMPLETE",
        value: "99"
      })
    ],
    classwideResults: {
      percentMeetingTarget: 100,
      percentAtRisk: 0,
      totalStudentsMeetingAllTargets: 5,
      totalStudentsAssessedOnAllMeasures: 5,
      studentIdsNotMeetingTarget: []
    },
    measures: [
      {
        assessmentId: "7arH8a6z3BAEdEARm",
        assessmentName: "Sums to 20",
        cutoffTarget: 22,
        targetScores: [11, 22, 300],
        medianScore: 99,
        studentScores: [99, 99, 99, 99, 99],
        percentMeetingTarget: 100,
        numberMeetingTarget: 5,
        totalStudentsAssessed: 5,
        studentResults: [
          expect.objectContaining({
            status: "COMPLETE",
            firstName: STUDENT_FIRSTNAME,
            lastName: STUDENT_LASTNAME,
            score: "99",
            meetsTarget: true,
            individualRuleOutcome: "above"
          })
        ],
        benchmarkPeriodId: "nEsbWokBWutTZFkTh",
        grade: GRADE,
        assessmentResultType: "classwide"
      }
    ],
    nextAssessmentResultId: "9j7qHK7rE5nECG8fi",
    ruleResults: {
      passed: true,
      nextSkill: {
        assessmentId: "pexzg5K88e3XBHKYi",
        assessmentName: "Subtraction 0-9",
        interventions: [],
        targets: [20, 40, 225]
      }
    }
  }
];

export const exampleBenchmarkWindows = [
  {
    _id: "QPF5GFMPCvECJbbkQ",
    schoolYear: SCHOOL_YEAR,
    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
    siteId: SITE_ID,
    startDate: "2016-08-01T00:00:00.000+0000",
    endDate: "2016-12-31T00:00:00.000+0000",
    orgid: ORG_ID
  }
];

export const expectedBenchmarkWindows = [
  {
    schoolYear: SCHOOL_YEAR,
    benchmarkPeriodId: "8S52Gz5o85hRkECgq",
    startDate: "2016-08-01T00:00:00.000+0000",
    endDate: "2016-12-31T00:00:00.000+0000"
  }
];
