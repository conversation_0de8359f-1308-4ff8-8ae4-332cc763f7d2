/*
  INTRODUCTION

  IMPORTANT: USE THIS SCRIPT ONLY IN DEMO

  This is a script for migrating data from Sandbox for Coordinators - 2/13/2019 (_id: 5c64256fec10e6d46a0fc992) to DEMO_2/13/2019 (_id: 5c64260e78df14d4e049f6c1)
  The script affects:
   - BenchmarkWindows
   - Students
   - StudentGroups
   - StudentGroupEnrollments
   - AssessmentResults

   This script uses the same helper functions as in schoolScrubber but without anonymizing any data

   USAGE

   1. Run npm install in the schoolScrubber directory
   2. Set env variables
      a) MONGO_URL -- this is where the migration will occur
   3. Run the script with the following command
      npm run migrateData -- --sourceOrgId=ORG_ID_OF_SANDBOX --targetOrgId=ORG_ID_OF_DEMO --targetSchoolYear=2019(or any other year)
   4. The data can be accessed with the same usernames and passwords

   Note: The script will exit upon error
 */

const minimist = require("minimist");
import {
  scrubAssessmentResults,
  scrubBenchmarkWindows,
  scrubGroupsWithoutAssessmentResultsDependencies,
  scrubStudentGroupEnrollments,
  scrubStudentGroupPropertiesDependentOnAssessmentResults,
  scrubStudentPropertiesDependentOnAssessmentResults,
  scrubStudentsWithoutAssessmentResultsDependencies,
  removeHelperKeysFor
} from "./utils/anonymizationHelpers";

const MONGO_URL = process.env.MONGO_URL; // mongo url for demo db

const MongoClient = require("mongodb").MongoClient;
const chalk = require("chalk");

async function run() {
  if (!MONGO_URL) {
    console.log(chalk.red("MONGO_URL not set in env"));
    process.exit(0);
  }
  const argv = minimist(process.argv.slice(2));
  const { sourceOrgId, targetOrgId, targetSchoolYear } = argv;

  if (!sourceOrgId) {
    console.log(chalk.red("Please specify an Organization to copy from, pass organization Id as a parameter"));
    process.exit(0);
  }

  if (!targetOrgId) {
    console.log(
      chalk.red(
        "Please specify a destination organization for this migration script, pass organization Id as a parameter"
      )
    );
    process.exit(0);
  }

  if (!targetSchoolYear) {
    console.log(
      chalk.red("Please make sure to target a current school year by passing it as targetSchoolYear parameter.")
    );
    process.exit(0);
  }

  let assessmentResultsToInsert = [];
  let benchmarkWindowsToInsert = [];
  let studentGroupEnrollmentsToInsert = [];
  let studentGroupsToInsert = [];
  let studentsToInsert = [];

  let assessmentResultsToRemove = [];
  let benchmarkWindowsToRemove = [];
  let studentGroupEnrollmentsToRemove = [];
  let studentGroupsToRemove = [];
  let studentsToRemove = [];

  const dbConnection = await MongoClient.connect(MONGO_URL);
  console.log(`Connected successfully to target DB`);

  const organizationDb = dbConnection.collection("Organizations");

  const sourceOrganization = await organizationDb.findOne({ _id: sourceOrgId });
  if (!sourceOrganization) {
    console.log(chalk.red("No Organization with given sourceOrgId found."));
    process.exit(0);
  }

  const targetOrganization = await organizationDb.findOne({ _id: targetOrgId });
  if (!targetOrganization) {
    console.log(chalk.red("No Organization with given targetOrgId found."));
    process.exit(0);
  }

  const assessmentResultsDb = dbConnection.collection("AssessmentResults");
  const benchmarkWindowsDb = dbConnection.collection("BenchmarkWindows");
  const studentGroupEnrollmentsDb = dbConnection.collection("StudentGroupEnrollments");
  const studentGroupsDb = dbConnection.collection("StudentGroups");
  const studentsDb = dbConnection.collection("Students");
  const sitesDb = dbConnection.collection("Sites");
  const usersDb = dbConnection.collection("users");

  const demoCoachId = "demo_coach_id";
  const demoUser = await usersDb.findOne({ _id: demoCoachId });

  const migrationIterationsPromises = [];
  const relevantSiteAccess = demoUser.profile.siteAccess.filter(sa => sa.schoolYear === targetSchoolYear);
  relevantSiteAccess.forEach(({ schoolYear }) => {
    migrationIterationsPromises.push(
      new Promise(async (resolve, reject) => {
        try {
          // first, get data from target org
          const targetOrgBaseQuery = { orgid: targetOrgId, schoolYear };

          const target_existingAssessmentResults = await assessmentResultsDb.find(targetOrgBaseQuery).toArray();
          const target_existingBenchmarkWindows = await benchmarkWindowsDb.find(targetOrgBaseQuery).toArray();
          const target_existingStudentGroupEnrollments = await studentGroupEnrollmentsDb
            .find(targetOrgBaseQuery)
            .toArray();
          const target_existingStudentGroups = await studentGroupsDb.find(targetOrgBaseQuery).toArray();
          const target_existingStudents = await studentsDb.find(targetOrgBaseQuery).toArray();
          let target_existingSites = await sitesDb.find({ orgid: targetOrgId }).toArray();

          // second, get data from source org
          const sourceOrgBaseQuery = { orgid: sourceOrgId, schoolYear };

          const sourceAssessmentResults = await assessmentResultsDb.find(sourceOrgBaseQuery).toArray();
          const sourceBenchmarkWindows = await benchmarkWindowsDb.find(sourceOrgBaseQuery).toArray();
          const sourceStudentGroupEnrollments = await studentGroupEnrollmentsDb.find(sourceOrgBaseQuery).toArray();
          const sourceStudentGroups = await studentGroupsDb.find(sourceOrgBaseQuery).toArray();
          const sourceStudents = await studentsDb.find(sourceOrgBaseQuery).toArray();
          const sourceSite = await sitesDb.findOne({ orgid: sourceOrgId });

          // third, generate new ids and dependencies for the new set of data without anonymizing student names, etc
          target_existingSites = target_existingSites.map(site => ({
            // here we will use the existing sites with a link to new data
            ...site,
            bak_id: sourceSite._id
          }));

          const shouldAnonymize = false;
          const scrubbedBenchmarkWindows = scrubBenchmarkWindows(sourceBenchmarkWindows, target_existingSites);
          const scrubbedStudentsWithoutAssessmentResultsDependencies = scrubStudentsWithoutAssessmentResultsDependencies(
            { students: sourceStudents, newOrgId: targetOrgId, shouldAnonymize: shouldAnonymize }
          );

          const scrubbedGroupsWithoutAssessmentResultsDependencies = scrubGroupsWithoutAssessmentResultsDependencies({
            groups: sourceStudentGroups,
            anonymizedSites: target_existingSites,
            shouldAnonymize,
            customUser: demoUser
          });
          const scrubbedStudentGroupEnrollments = scrubStudentGroupEnrollments(
            sourceStudentGroupEnrollments,
            scrubbedStudentsWithoutAssessmentResultsDependencies,
            scrubbedGroupsWithoutAssessmentResultsDependencies
          );
          const scrubbedAssessmentResults = scrubAssessmentResults(
            sourceAssessmentResults,
            scrubbedStudentsWithoutAssessmentResultsDependencies,
            scrubbedGroupsWithoutAssessmentResultsDependencies
          );
          const completelyScrubbedGroups = scrubStudentGroupPropertiesDependentOnAssessmentResults(
            scrubbedGroupsWithoutAssessmentResultsDependencies,
            scrubbedAssessmentResults,
            scrubbedStudentsWithoutAssessmentResultsDependencies
          );
          const completelyScrubbedStudents = scrubStudentPropertiesDependentOnAssessmentResults(
            scrubbedStudentsWithoutAssessmentResultsDependencies,
            scrubbedAssessmentResults
          );

          // fourth, add new data to cumulative insert arrays
          studentsToInsert = [...studentsToInsert, ...completelyScrubbedStudents];
          studentGroupsToInsert = [...studentGroupsToInsert, ...completelyScrubbedGroups];
          assessmentResultsToInsert = [...assessmentResultsToInsert, ...scrubbedAssessmentResults];
          studentGroupEnrollmentsToInsert = [...studentGroupEnrollmentsToInsert, ...scrubbedStudentGroupEnrollments];
          benchmarkWindowsToInsert = [...benchmarkWindowsToInsert, ...scrubbedBenchmarkWindows];

          // fifth, add old documents to cumulative remove arrays
          assessmentResultsToRemove = [...assessmentResultsToRemove, ...target_existingAssessmentResults];
          benchmarkWindowsToRemove = [...benchmarkWindowsToRemove, ...target_existingBenchmarkWindows];
          studentGroupEnrollmentsToRemove = [
            ...studentGroupEnrollmentsToRemove,
            ...target_existingStudentGroupEnrollments
          ];
          studentGroupsToRemove = [...studentGroupsToRemove, ...target_existingStudentGroups];
          studentsToRemove = [...studentsToRemove, ...target_existingStudents];

          resolve();
        } catch (e) {
          console.log("Error: ", e);
          reject();
        }
      })
    );
  });

  await Promise.all(migrationIterationsPromises);

  // sixth, remove helper keys
  const studentsWithoutHelperKeys = removeHelperKeysFor(studentsToInsert);
  const groupsWithoutHelperKeys = removeHelperKeysFor(studentGroupsToInsert);
  const assessmentResultsWithoutHelperKeys = removeHelperKeysFor(assessmentResultsToInsert);
  const studentGroupEnrollmentsWithoutHelperKeys = removeHelperKeysFor(studentGroupEnrollmentsToInsert);

  // seventh, insert new data to db
  if (assessmentResultsWithoutHelperKeys.length) {
    try {
      await assessmentResultsDb.insertMany(assessmentResultsWithoutHelperKeys);
    } catch (error) {
      console.log("Inserting data to assessmentResultsDb failed:", error);
      process.exit(0);
    }
  }
  if (benchmarkWindowsToInsert.length) {
    try {
      await benchmarkWindowsDb.insertMany(benchmarkWindowsToInsert);
    } catch (error) {
      console.log("Inserting data to benchmarkWindowsDb failed:", error);
      process.exit(0);
    }
  }
  if (studentGroupEnrollmentsWithoutHelperKeys.length) {
    try {
      await studentGroupEnrollmentsDb.insertMany(studentGroupEnrollmentsWithoutHelperKeys);
    } catch (error) {
      console.log("Inserting data to studentGroupEnrollmentsDb failed:", error);
      process.exit(0);
    }
  }
  if (groupsWithoutHelperKeys.length) {
    try {
      await studentGroupsDb.insertMany(groupsWithoutHelperKeys);
    } catch (error) {
      console.log("Inserting data to studentGroupsDb failed:", error);
      process.exit(0);
    }
  }
  if (studentsWithoutHelperKeys.length) {
    try {
      await studentsDb.insertMany(studentsWithoutHelperKeys);
    } catch (error) {
      console.log("Inserting data to studentsDb failed:", error);
      process.exit(0);
    }
  }

  console.log("Successfully inserted new data to db");

  // eighth, remove relevant documents from target org
  try {
    await assessmentResultsDb.remove({ _id: { $in: assessmentResultsToRemove.map(ar => ar._id) } });
  } catch (e) {
    console.log("Removing data from assessmentResultsDb failed: ", e);
  }
  try {
    await benchmarkWindowsDb.remove({ _id: { $in: benchmarkWindowsToRemove.map(bw => bw._id) } });
  } catch (e) {
    console.log("Removing data from benchmarkWindowsDb failed: ", e);
  }
  try {
    await studentGroupEnrollmentsDb.remove({ _id: { $in: studentGroupEnrollmentsToRemove.map(sge => sge._id) } });
  } catch (e) {
    console.log("Removing data from studentGroupEnrollmentsDb failed: ", e);
  }
  try {
    await studentGroupsDb.remove({ _id: { $in: studentGroupsToRemove.map(sg => sg._id) } });
  } catch (e) {
    console.log("Removing data from studentGroupsDb failed: ", e);
  }
  try {
    await studentsDb.remove({ _id: { $in: studentsToRemove.map(stu => stu._id) } });
  } catch (e) {
    console.log("Removing data from studentsDb failed: ", e);
  }

  console.log("Successfully removed old data from db");

  dbConnection.close();
  process.exit(0);
}

run();
