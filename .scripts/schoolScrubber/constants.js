export const BIRTHDATE_RANGE_START = "2002-01-01";
export const BIRTHDATE_RANGE_END = "2013-12-31";

export const STUDENT_BIRTHDATE = new Date(Date.UTC(2007, 1, 1));

export const STUDENT_FIRSTNAME = "AnonymousName";
export const STUDENT_LASTNAME = "AnonymousLastName";
export const STUDENT_MIDDLENAME = STUDENT_FIRSTNAME;

export const LOCAL_ID = "1234";
export const STATE_ID = "12345";

export const UUID = "186d89e2-this-part-does-notmatter";

export const ORG_ID = "preOrgId";
export const SITE_ID = "preAnonymizationId";
export const SCHOOL_YEAR = 2019;
export const SCHOOL_NAME = "Historical Data";
export const SCHOOL_NUMBER_OFFSET = 10000;
export const GRADE = "06";
export const STUDENT_ID = "preStudentId";
export const GROUP_ID = "preGroupId";
export const DISTRICT_ID = "4";
export const DISTRICT_NAME = "Demo school";

export const BENCHMARK_ASSESSMENT_RESULT_ID = "benchmarkARId";
export const CLASSWIDE_ASSESSMENT_RESULT_ID = "classwideARId";
export const INDIVIDUAL_ASSESSMENT_RESULT_ID = "individualARId";

export const NEW_ORG_ID = "newOrgId";

export const USER = {
  _id: "demo_admin_user_id",
  createdAt: null,
  services: {
    password: {
      bcrypt: "$2a$10$/a3ZYVws0EbfU7AzXDJIA.43tCYkSKtx33OKBPjKyv76DZOSQl596"
    },
    resume: {
      loginTokens: []
    }
  },
  emails: [
    {
      address: "<EMAIL>",
      verified: false
    }
  ],
  profile: {
    onboarded: true,
    orgid: ORG_ID,
    localId: "local_admin_user_id",
    siteAccess: [],
    name: {
      first: "Demo",
      last: "User",
      middle: "Admin"
    },
    created: {
      by: "TEST",
      on: 1476740305430.0,
      date: new Date("2016-11-22T21:32:51.453Z")
    },
    lastModified: {
      by: "TEST",
      on: 1476740305430.0,
      date: new Date("2016-11-22T21:32:51.453Z")
    }
  },

  warn: false
};
