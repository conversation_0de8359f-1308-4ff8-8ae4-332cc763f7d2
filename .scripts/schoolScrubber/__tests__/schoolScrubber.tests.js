import {
  exampleAssessmentResults,
  exampleBenchmarkWindows,
  exampleGroups,
  exampleSites,
  exampleStudentGroupEnrollments,
  exampleStudents,
  expectedAssessmentResults,
  expectedBenchmarkWindows,
  expectedGroups,
  expectedSites,
  expectedStudentGroupEnrollments,
  expectedStudents
} from "../schoolScrubberStubs";

const { omit } = require("lodash");

import {
  scrubAssessmentResults,
  scrubGroupsWithoutAssessmentResultsDependencies,
  anonymizeSites,
  scrubStudentGroupEnrollments,
  scrubStudentsWithoutAssessmentResultsDependencies,
  scrubStudentGroupPropertiesDependentOnAssessmentResults,
  scrubBenchmarkWindows,
  scrubStudentPropertiesDependentOnAssessmentResults,
  anonymizeUsers
} from "../utils/anonymizationHelpers";
import { NEW_ORG_ID, STUDENT_FIRSTNAME, STUDENT_LASTNAME, USER, UUID } from "../constants";

jest.mock("faker");
const faker = require("faker");

describe("School scrubber helpers", () => {
  beforeAll(() => {
    faker.__setFirstName(STUDENT_FIRSTNAME);
    faker.__setLastName(STUDENT_LASTNAME);
    faker.__setUuid(UUID);
  });

  describe("anonymizeSites", () => {
    it("should return anonymized sites", () => {
      const anonymizedSites = anonymizeSites(exampleSites, NEW_ORG_ID);

      expect(anonymizedSites[0]._id).not.toEqual(exampleSites[0]._id);
      expect(anonymizedSites).toEqual(expect.arrayContaining([expect.objectContaining(expectedSites[0])]));
    });
  });

  describe("anonymizeBenchmakrWindows", () => {
    it("should return anonymized benchmarkWindows", () => {
      const anonymizedSites = anonymizeSites(exampleSites, NEW_ORG_ID);
      const anonymizedBenchmarkWindows = scrubBenchmarkWindows(exampleBenchmarkWindows, anonymizedSites);

      expect(anonymizedBenchmarkWindows[0]._id).not.toEqual(exampleBenchmarkWindows[0]._id);
      expect(anonymizedBenchmarkWindows[0].orgid).toEqual(anonymizedSites[0].orgid);
      expect(anonymizedBenchmarkWindows[0].siteId).toEqual(anonymizedSites[0]._id);
      expect(anonymizedBenchmarkWindows).toEqual(
        expect.arrayContaining([expect.objectContaining(expectedBenchmarkWindows[0])])
      );
    });
  });

  describe("anonymizeStudents", () => {
    it("should return anonymized students with the right site ids", () => {
      const anonymizedStudents = scrubStudentsWithoutAssessmentResultsDependencies({
        students: exampleStudents,
        newOrgId: NEW_ORG_ID
      });
      const anonymizedStudentsWithoutARDependencies = omit(anonymizedStudents[0], ["_id", "history", "currentSkill"]);
      const expectedStudentsWithoutARDependencies = omit(expectedStudents[0], ["history", "currentSkill"]);

      expect(anonymizedStudentsWithoutARDependencies._id).not.toEqual(exampleStudents[0]._id);
      expect(anonymizedStudentsWithoutARDependencies).toEqual(
        expect.objectContaining(expectedStudentsWithoutARDependencies)
      );
    });
  });

  describe("scrubGroupsWithoutAssessmentResultsDependencies", () => {
    it("should return anonymized groups", () => {
      const anonymizedSites = anonymizeSites(exampleSites, NEW_ORG_ID);
      const anonymizedUsers = anonymizeUsers({
        users: [USER],
        NEW_ORG_ID,
        schoolYear: 2019,
        sites: anonymizedSites,
        anonymizedSites
      });
      const anonymizedGroups = scrubGroupsWithoutAssessmentResultsDependencies({
        groups: exampleGroups,
        anonymizedUsers,
        anonymizedSites
      });
      //history is not anonymized at this point
      expect(anonymizedGroups[0]._id).not.toEqual(exampleGroups[0]._id);
      expect(anonymizedGroups[0].siteId).toEqual(anonymizedSites[0]._id);
      expect(anonymizedGroups[0].ownerIds).toEqual([anonymizedUsers[0]._id]);
      expect(anonymizedGroups[0].name).toEqual(`AnonymousLastName's grade (${anonymizedGroups[0].sectionId})`);
      const anonymizedGroupWithoutARDependencies = omit(anonymizedGroups[0], [
        "_id",
        "history",
        "currentClasswideSkill",
        "currentAssessmentResultIds",
        "siteId",
        "ownerIds",
        "name"
      ]);
      const expectedGroupWithoutARDependencies = omit(expectedGroups[0], [
        "history",
        "currentClasswideSkill",
        "currentAssessmentResultIds",
        "siteId",
        "ownerIds",
        "name"
      ]);
      expect(anonymizedGroupWithoutARDependencies).toEqual(expectedGroupWithoutARDependencies);
    });
  });

  describe("anonymize studentGroupEnrollments", () => {
    it("should return anonymized studentGroupEnrollments", () => {
      const anonymizedSites = anonymizeSites(exampleSites, NEW_ORG_ID);
      const anonymizedUsers = anonymizeUsers({
        users: [USER],
        NEW_ORG_ID,
        schoolYear: 2019,
        sites: anonymizedSites,
        anonymizedSites
      });
      const anonymizedGroups = scrubGroupsWithoutAssessmentResultsDependencies({
        groups: exampleGroups,
        anonymizedUsers,
        anonymizedSites
      });
      const anonymizedStudents = scrubStudentsWithoutAssessmentResultsDependencies({
        students: exampleStudents,
        newOrgId: NEW_ORG_ID
      });
      const anonymizedStudentGroupEnrollments = scrubStudentGroupEnrollments(
        exampleStudentGroupEnrollments,
        anonymizedStudents,
        anonymizedGroups
      );

      expect(anonymizedStudentGroupEnrollments[0]._id).not.toEqual(exampleStudentGroupEnrollments[0]._id);
      expect(anonymizedStudentGroupEnrollments[0].siteId).toEqual(anonymizedGroups[0].siteId);
      expect(anonymizedStudentGroupEnrollments[0].studentGroupId).toEqual(anonymizedGroups[0]._id);
      expect(anonymizedStudentGroupEnrollments[0].studentId).toEqual(anonymizedStudents[0]._id);
      expect(anonymizedStudentGroupEnrollments).toEqual(
        expect.arrayContaining([expect.objectContaining(expectedStudentGroupEnrollments[0])])
      );
    });
  });

  describe("anonymize assessmentResults", () => {
    it("should return anonymized assessmentResults", () => {
      const anonymizedSites = anonymizeSites(exampleSites, NEW_ORG_ID);
      const anonymizedUsers = anonymizeUsers({
        users: [USER],
        orgid: NEW_ORG_ID,
        schoolYear: 2019,
        sites: anonymizedSites,
        anonymizedSites
      });
      const anonymizedGroups = scrubGroupsWithoutAssessmentResultsDependencies({
        groups: exampleGroups,
        anonymizedUsers,
        anonymizedSites
      });
      const anonymizedStudents = scrubStudentsWithoutAssessmentResultsDependencies({
        students: exampleStudents,
        newOrgId: NEW_ORG_ID
      });
      const anonymizedAssessmentResults = scrubAssessmentResults(
        exampleAssessmentResults,
        anonymizedStudents,
        anonymizedGroups
      );
      const benchmarkAssessmentResult = anonymizedAssessmentResults.find(result => result.type === "benchmark");
      const individualAssessmentResult = anonymizedAssessmentResults.find(result => result.type === "individual");
      const classwideAssessmentResult = anonymizedAssessmentResults.find(result => result.type === "classwide");

      // type: benchmark specific
      expect(benchmarkAssessmentResult._id).not.toEqual(exampleAssessmentResults[0]._id);
      expect(benchmarkAssessmentResult.studentGroupId).toEqual(anonymizedGroups[0]._id);
      expect(benchmarkAssessmentResult.scores[0]._id).not.toEqual(exampleAssessmentResults[0].scores[0]._id);
      expect(benchmarkAssessmentResult.scores[0].siteId).toEqual(anonymizedGroups[0].siteId);
      expect(benchmarkAssessmentResult.scores[0].studentId).toEqual(anonymizedStudents[0]._id);
      expect(benchmarkAssessmentResult.scores[1]._id).not.toEqual(exampleAssessmentResults[0].scores[1]._id);
      expect(benchmarkAssessmentResult.scores[1].siteId).toEqual(anonymizedGroups[0].siteId);
      expect(benchmarkAssessmentResult.scores[1].studentId).toEqual(anonymizedStudents[0]._id);
      expect(benchmarkAssessmentResult.scores[2]._id).not.toEqual(exampleAssessmentResults[0].scores[2]._id);
      expect(benchmarkAssessmentResult.scores[2].siteId).toEqual(anonymizedGroups[0].siteId);
      expect(benchmarkAssessmentResult.scores[2].studentId).toEqual(anonymizedStudents[0]._id);
      expect(benchmarkAssessmentResult.classwideResults.studentIdsNotMeetingTarget).toContain(
        anonymizedStudents[0]._id
      );
      expect(benchmarkAssessmentResult.measures[0].studentResults[0].studentId).toEqual(anonymizedStudents[0]._id);
      expect(benchmarkAssessmentResult.measures[1].studentResults[0].studentId).toEqual(anonymizedStudents[0]._id);
      expect(benchmarkAssessmentResult.measures[2].studentResults[0].studentId).toEqual(anonymizedStudents[0]._id);
      expect(benchmarkAssessmentResult).toEqual(expect.objectContaining(expectedAssessmentResults[0]));

      //type: individual specific
      expect(individualAssessmentResult._id).not.toEqual(exampleAssessmentResults[0]._id);
      expect(individualAssessmentResult.individualSkills.assessmentResultId).toEqual(individualAssessmentResult._id);
      expect(individualAssessmentResult.studentGroupId).toEqual(anonymizedGroups[0]._id);
      expect(individualAssessmentResult.studentId).toEqual(anonymizedStudents[0]._id);
      expect(individualAssessmentResult.scores[0]._id).not.toEqual(exampleAssessmentResults[1].scores[0]._id);
      expect(individualAssessmentResult.scores[0].siteId).toEqual(anonymizedGroups[0].siteId);
      expect(individualAssessmentResult.scores[0].studentId).toEqual(anonymizedStudents[0]._id);
      expect(individualAssessmentResult.scores[1]._id).not.toEqual(exampleAssessmentResults[1].scores[1]._id);
      expect(individualAssessmentResult.scores[1].siteId).toEqual(anonymizedGroups[0].siteId);
      expect(individualAssessmentResult.scores[1].studentId).toEqual(anonymizedStudents[0]._id);
      expect(individualAssessmentResult.measures[0].studentResults[0].studentId).toEqual(anonymizedStudents[0]._id);
      expect(individualAssessmentResult).toEqual(expect.objectContaining(expectedAssessmentResults[1]));

      //type: classwide specific
      expect(classwideAssessmentResult._id).not.toEqual(exampleAssessmentResults[2]._id);
      expect(classwideAssessmentResult.studentGroupId).toEqual(anonymizedGroups[0]._id);
      expect(classwideAssessmentResult.scores[0]._id).not.toEqual(exampleAssessmentResults[2].scores[0]._id);
      expect(classwideAssessmentResult.scores[0].siteId).toEqual(anonymizedGroups[0].siteId);
      expect(classwideAssessmentResult.scores[0].studentId).toEqual(anonymizedStudents[0]._id);
      expect(classwideAssessmentResult.measures[0].studentResults[0].studentId).toEqual(anonymizedStudents[0]._id);
      expect(classwideAssessmentResult).toEqual(expect.objectContaining(expectedAssessmentResults[2]));
    });
  });
  describe("anonymize studentGroupPropertiesDependentOnAssessmentResults", () => {
    it("should properly anonymize properties dependent on assessmentResults", () => {
      const anonymizedSites = anonymizeSites(exampleSites, NEW_ORG_ID);
      const anonymizedUsers = anonymizeUsers({
        users: [USER],
        orgid: NEW_ORG_ID,
        schoolYear: 2019,
        sites: anonymizedSites,
        anonymizedSites
      });
      const anonymizedGroups = scrubGroupsWithoutAssessmentResultsDependencies({
        groups: exampleGroups,
        anonymizedUsers,
        anonymizedSites
      });
      const anonymizedStudents = scrubStudentsWithoutAssessmentResultsDependencies({
        students: exampleStudents,
        newOrgId: NEW_ORG_ID
      });
      const anonymizedAssessmentResults = scrubAssessmentResults(
        exampleAssessmentResults,
        anonymizedStudents,
        anonymizedGroups
      );
      const completelyAnonymizedGroups = scrubStudentGroupPropertiesDependentOnAssessmentResults(
        anonymizedGroups,
        anonymizedAssessmentResults,
        anonymizedStudents
      );
      const benchmarkAssessmentResult = anonymizedAssessmentResults.find(result => result.type === "benchmark");
      const classwideAssessmentResult = anonymizedAssessmentResults.find(result => result.type === "classwide");
      const benchmarkHistory = completelyAnonymizedGroups[0].history.find(
        assessmentResult => assessmentResult.type === "benchmark"
      );
      const classwideHistory = completelyAnonymizedGroups[0].history.find(
        assessmentResult => assessmentResult.type === "classwide"
      );

      expect(benchmarkHistory.assessmentResultId).toEqual(benchmarkAssessmentResult._id);
      expect(benchmarkHistory.enrolledStudentIds).toContain(anonymizedStudents[0]._id);
      expect(benchmarkHistory.assessmentResultMeasures[0].studentResults[0].studentId).toEqual(
        anonymizedStudents[0]._id
      );
      expect(benchmarkHistory.assessmentResultMeasures[1].studentResults[0].studentId).toEqual(
        anonymizedStudents[0]._id
      );
      expect(benchmarkHistory.assessmentResultMeasures[2].studentResults[0].studentId).toEqual(
        anonymizedStudents[0]._id
      );
      expect(benchmarkHistory).toEqual(expect.objectContaining(expectedGroups[0].history[0]));

      expect(classwideHistory.assessmentResultId).toEqual(classwideAssessmentResult._id);
      expect(classwideHistory.enrolledStudentIds).toContain(anonymizedStudents[0]._id);
      expect(classwideHistory.assessmentResultMeasures[0].studentResults[0].studentId).toEqual(
        anonymizedStudents[0]._id
      );
      expect(classwideHistory).toEqual(expect.objectContaining(expectedGroups[0].history[1]));

      expect(completelyAnonymizedGroups[0].currentAssessmentResultIds).toContain(classwideAssessmentResult._id);
      expect(completelyAnonymizedGroups[0].currentClasswideSkill.assessmentResultId).toEqual(
        classwideAssessmentResult._id
      );
      expect(completelyAnonymizedGroups[0].currentClasswideSkill).toEqual(
        expect.objectContaining(expectedGroups[0].currentClasswideSkill)
      );
    });
  });

  describe("anonymize studentPropertiesDependentOnAssessmentResults", () => {
    it("should properly anonymize properties dependent on assessmentResults", () => {
      const anonymizedSites = anonymizeSites(exampleSites, NEW_ORG_ID);
      const anonymizedUsers = anonymizeUsers({
        users: [USER],
        orgid: NEW_ORG_ID,
        schoolYear: 2019,
        sites: anonymizedSites,
        anonymizedSites
      });
      const anonymizedGroups = scrubGroupsWithoutAssessmentResultsDependencies({
        groups: exampleGroups,
        anonymizedUsers,
        anonymizedSites
      });
      const anonymizedStudents = scrubStudentsWithoutAssessmentResultsDependencies({
        students: exampleStudents,
        newOrgId: NEW_ORG_ID
      });
      const anonymizedAssessmentResults = scrubAssessmentResults(
        exampleAssessmentResults,
        anonymizedStudents,
        anonymizedGroups
      );
      const completelyAnonymizedStudents = scrubStudentPropertiesDependentOnAssessmentResults(
        anonymizedStudents,
        anonymizedAssessmentResults
      );
      const individualAssessmentResult = anonymizedAssessmentResults.find(result => result.type === "individual");

      const studentHistory = completelyAnonymizedStudents[0].history[0];

      expect(studentHistory.assessmentResultId).toEqual(individualAssessmentResult._id);
      expect(completelyAnonymizedStudents[0].currentSkill.assessmentResultId).toEqual(individualAssessmentResult._id);
      expect(studentHistory.assessmentResultMeasures[0].studentResults[0].studentId).toEqual(anonymizedStudents[0]._id);

      expect(completelyAnonymizedStudents[0]).toEqual(expect.objectContaining(expectedStudents[0]));
    });
  });
});
